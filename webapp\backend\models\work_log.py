from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from backend.database import Base


class WorkLog(Base):
    """
    Modello SQLAlchemy per la tabella work_logs.
    Gestisce i log di lavoro per il sistema di produttività.
    """
    __tablename__ = "work_logs"

    id = Column(Integer, primary_key=True, index=True)
    operator_id = Column(Integer, ForeignKey("responsabili.id_responsabile"), nullable=False)
    cable_type_id = Column(Integer, ForeignKey("tipologie_cavi.id_tipologia"), nullable=True)
    activity_type = Column(String(50), nullable=False)  # 'Posa', 'Collegamento', 'Certificazione'
    sub_activity_detail = Column(String(255), nullable=True)
    environmental_conditions = Column(String(50), nullable=False, default='Normale')  # 'Normale', '<PERSON><PERSON>', 'In Altezza', 'Esterno'
    tools_used = Column(String(50), nullable=False, default='Manuale')  # 'Manuale', 'Automatico'
    quantity = Column(Float, nullable=False)  # metri per 'Posa' o unità per 'Collegamento'/'Certificazione'
    start_timestamp = Column(DateTime(timezone=True), nullable=False)
    end_timestamp = Column(DateTime(timezone=True), nullable=False)
    duration_minutes = Column(Integer, nullable=True)  # calcolato automaticamente
    number_of_operators_on_task = Column(Integer, nullable=False, default=1)
    notes = Column(Text, nullable=True)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)
    created_at = Column(DateTime, nullable=False, default=func.now())

    # Relazioni
    operator = relationship("Responsabile", backref="work_logs")
    cable_type = relationship("TipologiaCavo", backref="work_logs")
    cantiere = relationship("Cantiere", backref="work_logs")

    def __repr__(self):
        return f"<WorkLog(id={self.id}, operator_id={self.operator_id}, activity='{self.activity_type}', quantity={self.quantity})>"

    @property
    def calculated_duration_minutes(self):
        """Calcola la durata in minuti dal timestamp di inizio e fine."""
        if self.start_timestamp and self.end_timestamp:
            delta = self.end_timestamp - self.start_timestamp
            return int(delta.total_seconds() / 60)
        return None

    @property
    def productivity_per_hour(self):
        """Calcola la produttività oraria (quantità/ora)."""
        if self.duration_minutes and self.duration_minutes > 0:
            hours = self.duration_minutes / 60
            return self.quantity / hours
        return None

    @property
    def productivity_per_person_per_hour(self):
        """Calcola la produttività per persona per ora."""
        if self.productivity_per_hour and self.number_of_operators_on_task > 0:
            return self.productivity_per_hour / self.number_of_operators_on_task
        return None

    @property
    def total_man_hours(self):
        """Calcola le ore-uomo totali."""
        if self.duration_minutes:
            hours = self.duration_minutes / 60
            return hours * self.number_of_operators_on_task
        return None
