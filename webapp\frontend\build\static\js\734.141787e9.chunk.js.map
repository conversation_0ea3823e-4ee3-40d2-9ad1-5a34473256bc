{"version": 3, "file": "static/js/734.141787e9.chunk.js", "mappings": "gNASO,MAAMA,EAAa,CAExBC,MAAOC,EAAAA,EAAYD,MACnBE,OAAQD,EAAAA,EAAYC,OACpBC,eAAgBF,EAAAA,EAAYE,eAG5BC,YAAaC,EAAAA,EAAgBC,cAC7BC,YAAaF,EAAAA,EAAgBE,YAC7BC,eAAgBH,EAAAA,EAAgBG,eAChCC,eAAgBJ,EAAAA,EAAgBI,eAGhCC,QAASC,EAAAA,EAAYD,QACrBE,QAASD,EAAAA,EAAYC,QACrBC,WAAYF,EAAAA,EAAYE,WACxBC,WAAYH,EAAAA,EAAYG,WACxBC,WAAYJ,EAAAA,EAAYI,WACxBC,aAAcL,EAAAA,EAAYK,aAG1BC,kBAAmBC,EAAAA,EAAsBD,kBACzCE,kBAAmBD,EAAAA,EAAsBC,kBACzCC,qBAAsBF,EAAAA,EAAsBE,qBAC5CC,qBAAsBH,EAAAA,EAAsBG,qBAC5CC,qBAAsBJ,EAAAA,EAAsBI,qBAG5CC,aAAcL,EAAAA,EAAsBK,aACpCC,gBAAiBN,EAAAA,EAAsBM,gBACvCC,gBAAiBP,EAAAA,EAAsBO,gBACvCC,gBAAiBR,EAAAA,EAAsBQ,gBAGvCC,aAAcC,EAAAA,EAAiBD,aAC/BE,aAAcD,EAAAA,EAAiBC,aAC/BC,aAAcF,EAAAA,EAAiBE,aAC/BC,aAAcH,EAAAA,EAAiBG,aAG/BC,iBAAkBC,EAAAA,QAAaD,iBAC/BE,YAAaD,EAAAA,QAAaC,YAG1BC,WAAYC,EAAAA,EAAcD,W,oRCkO5B,QA1PA,SAA2BE,GAAoD,IAAnD,eAAEC,EAAc,OAAEC,EAAM,SAAEC,EAAQ,WAAEC,GAAYJ,EAC1E,MAAOK,EAAwBC,IAA6BC,EAAAA,EAAAA,UAAS,OAC9DC,EAAmBC,IAAwBF,EAAAA,EAAAA,WAAS,IACpDG,EAAkBC,IAAuBJ,EAAAA,EAAAA,WAAS,IAClDK,EAAwBC,IAA6BN,EAAAA,EAAAA,UAAS,OAC9DO,EAASC,IAAcR,EAAAA,EAAAA,WAAS,GAkCjCS,EAAcC,GACbA,EACE,IAAIC,KAAKD,GAAYE,mBAAmB,SADvB,IAIpBC,EAAsBC,IAC1B,IAAKA,EAAQ,MAAO,UACpB,MAAMC,EAAWC,WAAWF,GAC5B,OAAIC,GAAY,IAAY,UACxBA,GAAY,IAAY,UACrB,OAAO,EAGhB,OAA8B,IAA1BrB,EAAeuB,QAEfC,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CAACC,GAAI,CAAEC,EAAG,EAAGC,UAAW,UAAWC,SAAA,EACvCC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,KAAKC,MAAM,iBAAgBJ,SAAC,oCAGhDC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,QAAQC,MAAM,iBAAiBP,GAAI,CAAEQ,GAAI,GAAIL,SAAC,gFAQtEL,EAAAA,EAAAA,MAAAW,EAAAA,SAAA,CAAAN,SAAA,EACEC,EAAAA,EAAAA,KAACM,EAAAA,EAAc,CAACC,UAAWZ,EAAAA,EAAMI,UAC/BL,EAAAA,EAAAA,MAACc,EAAAA,EAAK,CAAAT,SAAA,EACJC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,UACRL,EAAAA,EAAAA,MAACgB,EAAAA,EAAQ,CAAAX,SAAA,EACPC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,yBACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,eACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,iBACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,eACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,YACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,iBACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,4BACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,qBACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,mBAGvBC,EAAAA,EAAAA,KAACY,EAAAA,EAAS,CAAAb,SACP7B,EAAe2C,KAAKC,IACnBpB,EAAAA,EAAAA,MAACgB,EAAAA,EAAQ,CAA8BK,OAAK,EAAAhB,SAAA,EAC1CC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,QAAQc,WAAW,OAAMjB,SAC1Ce,EAAKG,wBAGVjB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,QAAQgB,WAAW,YAAWnB,SAC/Ce,EAAKK,aAGVnB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,SAAEe,EAAKM,gBAAkB,OACnCpB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,SAAEe,EAAKO,cAAgB,OACjCrB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,SAAEd,EAAW6B,EAAKQ,wBAC5BtB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,SAAEe,EAAKS,cAAgB,OACjCvB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRC,EAAAA,EAAAA,KAACwB,EAAAA,EAAI,CACHC,MAAOX,EAAKY,mBAAqB,IACjCvB,MAAOd,EAAmByB,EAAKY,mBAC/BC,KAAK,aAGT3B,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,SACPe,EAAKc,mBAAqBd,EAAKc,mBAAmBC,QAAQ,GAAK,OAElE7B,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRL,EAAAA,EAAAA,MAACoC,EAAAA,EAAG,CAAClC,GAAI,CAAEmC,QAAS,OAAQC,IAAK,IAAMjC,SAAA,EACrCC,EAAAA,EAAAA,KAACiC,EAAAA,EAAU,CACTN,KAAK,QACLO,QAASA,IA1GHC,WACxB,IACEnD,GAAW,GACX,MAAMoD,QAAgBzG,EAAAA,WAAWoB,kBAAkBsB,EAAYgE,EAAeC,mBAC9E/D,EAA0B6D,GAC1B1D,GAAqB,EACvB,CAAE,MAAO6D,GACPC,QAAQD,MAAM,uCAAwCA,EACxD,CAAC,QACCvD,GAAW,EACb,GAgGiCyD,CAAkB3B,GACjC4B,MAAM,sBAAqB3C,UAE3BC,EAAAA,EAAAA,KAAC2C,EAAAA,EAAc,CAACC,SAAS,aAE3B5C,EAAAA,EAAAA,KAACiC,EAAAA,EAAU,CACTN,KAAK,QACLO,QAASA,IAAM/D,EAAO2C,GACtB4B,MAAM,WAAU3C,UAEhBC,EAAAA,EAAAA,KAAC6C,EAAAA,EAAQ,CAACD,SAAS,aAErB5C,EAAAA,EAAAA,KAACiC,EAAAA,EAAU,CACTN,KAAK,QACLO,QAASA,KA1G3BpD,EA0GmDgC,QAzGnDlC,GAAoB,IA0GF8D,MAAM,UACNvC,MAAM,QAAOJ,UAEbC,EAAAA,EAAAA,KAAC8C,EAAAA,EAAU,CAACF,SAAS,mBA/Cd9B,EAAKwB,6BA0D5B5C,EAAAA,EAAAA,MAACqD,EAAAA,EAAM,CACLC,KAAMvE,EACNwE,QAASA,IAAMvE,GAAqB,GACpCwE,SAAS,KACTC,WAAS,EAAApD,SAAA,EAETL,EAAAA,EAAAA,MAAC0D,EAAAA,EAAW,CAAArD,SAAA,CAAC,2BACoC,OAAtBzB,QAAsB,IAAtBA,OAAsB,EAAtBA,EAAwB2C,uBAEnDjB,EAAAA,EAAAA,KAACqD,EAAAA,EAAa,CAAAtD,SACXzB,IACCoB,EAAAA,EAAAA,MAAC4D,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAG5D,GAAI,CAAEQ,GAAI,GAAIL,SAAA,EACxCL,EAAAA,EAAAA,MAAC4D,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAE5D,SAAA,EACvBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,YAAYC,MAAM,iBAAgBJ,SAAC,uBAGvDL,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,IAAEzB,EAAuB6C,YAC9DzB,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,eAAmB,IAAEzB,EAAuB8C,gBAAkB,QAClF1B,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,IAAEzB,EAAuB+C,cAAgB,QAC9E3B,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,cAAkB,IAAEzB,EAAuBsF,0BAA4B,QAC3FlE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,YAAgB,IAAEzB,EAAuBuF,wBAA0B,QACvFnE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,mBAAuB,IAAEzB,EAAuBwF,oBAAsB,QAC1FpE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,WAAe,IAAEzB,EAAuByF,0BAA4B,WAG1FrE,EAAAA,EAAAA,MAAC4D,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAE5D,SAAA,EACvBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,YAAYC,MAAM,iBAAgBJ,SAAC,iCAGvDL,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,uBAAwB,IAAEzB,EAAuB2C,uBACrEvB,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,UAAc,IAAEd,EAAWX,EAAuBgD,yBACtE5B,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,eAAmB,IAAEzB,EAAuBiD,cAAgB,QAChF7B,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,wBAA4B,IAAEzB,EAAuBsD,mBAAqB,GAAGtD,EAAuBsD,mBAAmBC,QAAQ,OAAS,WAG9JnC,EAAAA,EAAAA,MAAC4D,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAE5D,SAAA,EACvBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,YAAYC,MAAM,iBAAgBJ,SAAC,oBAGvDL,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,mBAAoB,IAAEzB,EAAuB0F,mBAAqB,QACtFtE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,gBAAoB,IAAEzB,EAAuBoD,mBAAqB,IAAI,eAC1FhC,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,gBAAoB,IAAEzB,EAAuB2F,mBAAqB,WAGxFvE,EAAAA,EAAAA,MAAC4D,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAIC,GAAI,EAAE5D,SAAA,EACvBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,YAAYC,MAAM,iBAAgBJ,SAAC,yBAGtDzB,EAAuB4F,gBACtBxE,EAAAA,EAAAA,MAAAW,EAAAA,SAAA,CAAAN,SAAA,EACEL,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,UAAc,IAAEzB,EAAuB4F,mBAC3DxE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,WAAe,IAAEzB,EAAuB6F,iBAAmB,QAC/EzE,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,EAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,IAAEzB,EAAuB8F,mBAAqB,WAGrFpE,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAAAF,SAAEzB,EAAuB+F,sBAAwB,uBAI/D/F,EAAuBgG,OACtB5E,EAAAA,EAAAA,MAAC4D,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAG3D,SAAA,EAChBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,YAAYC,MAAM,iBAAgBJ,SAAC,UAGvDC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAAAF,SAAEzB,EAAuBgG,gBAM9CtE,EAAAA,EAAAA,KAACuE,EAAAA,EAAa,CAAAxE,UACZC,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CAACtC,QAASA,IAAMxD,GAAqB,GAAOqB,SAAC,iBAOxDL,EAAAA,EAAAA,MAACqD,EAAAA,EAAM,CACLC,KAAMrE,EACNsE,QAASA,IAAMrE,GAAoB,GAAOmB,SAAA,EAE1CC,EAAAA,EAAAA,KAACoD,EAAAA,EAAW,CAAArD,SAAC,2BACbL,EAAAA,EAAAA,MAAC2D,EAAAA,EAAa,CAAAtD,SAAA,EACZL,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,CAAC,mDAC6D,OAAtBlB,QAAsB,IAAtBA,OAAsB,EAAtBA,EAAwBoC,mBAAmB,QAE9FjB,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,QAAQC,MAAM,iBAAiBP,GAAI,CAAEQ,GAAI,GAAIL,SAAC,uDAIpEL,EAAAA,EAAAA,MAAC6E,EAAAA,EAAa,CAAAxE,SAAA,EACZC,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CAACtC,QAASA,IAAMtD,GAAoB,GAAOmB,SAAC,aAGnDC,EAAAA,EAAAA,KAACwE,EAAAA,EAAM,CACLtC,QArNkBC,UAC1B,IACEnD,GAAW,SACLrD,EAAAA,WAAWuB,qBAAqBmB,EAAYQ,EAAuByD,mBACzE1D,GAAoB,GACpBE,EAA0B,MAC1BV,GACF,CAAE,MAAOmE,GACPC,QAAQD,MAAM,4BAA8BA,EAC9C,CAAC,QACCvD,GAAW,EACb,GA2MQmB,MAAM,QACNsE,SAAU1F,EAAQgB,SACnB,oBAOX,C", "sources": ["services/apiService.js", "components/certificazioni/CertificazioniList.jsx"], "sourcesContent": ["// Servizio API unificato che espone tutti i servizi\nimport authService from './authService';\nimport cantieriService from './cantieriService';\nimport caviService from './caviService';\nimport certificazioneService from './certificazioneService';\nimport parcoCaviService from './parcoCaviService';\nimport excelService from './excelService';\nimport reportService from './reportService';\n\nexport const apiService = {\n  // Auth\n  login: authService.login,\n  logout: authService.logout,\n  getCurrentUser: authService.getCurrentUser,\n\n  // Cantieri\n  getCantieri: cantieriService.getMyCantieri,\n  getCantiere: cantieriService.getCantiere,\n  createCantiere: cantieriService.createCantiere,\n  deleteCantiere: cantieriService.deleteCantiere,\n\n  // Cavi\n  getCavi: caviService.getCavi,\n  getCavo: caviService.getCavo,\n  createCavo: caviService.createCavo,\n  updateCavo: caviService.updateCavo,\n  deleteCavo: caviService.deleteCavo,\n  aggiornaCavo: caviService.aggiornaCavo,\n\n  // Certificazioni\n  getCertificazioni: certificazioneService.getCertificazioni,\n  getCertificazione: certificazioneService.getCertificazione,\n  createCertificazione: certificazioneService.createCertificazione,\n  updateCertificazione: certificazioneService.updateCertificazione,\n  deleteCertificazione: certificazioneService.deleteCertificazione,\n\n  // Strumenti\n  getStrumenti: certificazioneService.getStrumenti,\n  createStrumento: certificazioneService.createStrumento,\n  updateStrumento: certificazioneService.updateStrumento,\n  deleteStrumento: certificazioneService.deleteStrumento,\n\n  // Parco Cavi\n  getParcoCavi: parcoCaviService.getParcoCavi,\n  createBobina: parcoCaviService.createBobina,\n  updateBobina: parcoCaviService.updateBobina,\n  deleteBobina: parcoCaviService.deleteBobina,\n\n  // Excel\n  generateTemplate: excelService.generateTemplate,\n  importExcel: excelService.importExcel,\n\n  // Reports\n  getReports: reportService.getReports\n};\n\nexport default apiService;\n", "import React, { useState } from 'react';\nimport {\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Typography,\n  Box,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button,\n  Grid\n} from '@mui/material';\nimport {\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Visibility as VisibilityIcon,\n  GetApp as DownloadIcon\n} from '@mui/icons-material';\n\nimport { apiService } from '../../services/apiService';\n\nfunction CertificazioniList({ certificazioni, onEdit, onDelete, cantiereId }) {\n  const [selectedCertificazione, setSelectedCertificazione] = useState(null);\n  const [showDetailsDialog, setShowDetailsDialog] = useState(false);\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [certificazioneToDelete, setCertificazioneToDelete] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  const handleViewDetails = async (certificazione) => {\n    try {\n      setLoading(true);\n      const details = await apiService.getCertificazione(cantiereId, certificazione.id_certificazione);\n      setSelectedCertificazione(details);\n      setShowDetailsDialog(true);\n    } catch (error) {\n      console.error('Errore nel caricamento dei dettagli:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteClick = (certificazione) => {\n    setCertificazioneToDelete(certificazione);\n    setShowDeleteDialog(true);\n  };\n\n  const handleDeleteConfirm = async () => {\n    try {\n      setLoading(true);\n      await apiService.deleteCertificazione(cantiereId, certificazioneToDelete.id_certificazione);\n      setShowDeleteDialog(false);\n      setCertificazioneToDelete(null);\n      onDelete();\n    } catch (error) {\n      console.error('Errore nell\\'eliminazione:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return '-';\n    return new Date(dateString).toLocaleDateString('it-IT');\n  };\n\n  const getIsolamentoColor = (valore) => {\n    if (!valore) return 'default';\n    const numValue = parseFloat(valore);\n    if (numValue >= 500) return 'success';\n    if (numValue >= 100) return 'warning';\n    return 'error';\n  };\n\n  if (certificazioni.length === 0) {\n    return (\n      <Paper sx={{ p: 3, textAlign: 'center' }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Nessuna certificazione trovata\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          Clicca su \"Nuova Certificazione\" per aggiungere la prima certificazione\n        </Typography>\n      </Paper>\n    );\n  }\n\n  return (\n    <>\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell><strong>N° Certificato</strong></TableCell>\n              <TableCell><strong>ID Cavo</strong></TableCell>\n              <TableCell><strong>Tipologia</strong></TableCell>\n              <TableCell><strong>Sezione</strong></TableCell>\n              <TableCell><strong>Data</strong></TableCell>\n              <TableCell><strong>Operatore</strong></TableCell>\n              <TableCell><strong>Isolamento (MΩ)</strong></TableCell>\n              <TableCell><strong>Lunghezza (m)</strong></TableCell>\n              <TableCell><strong>Azioni</strong></TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {certificazioni.map((cert) => (\n              <TableRow key={cert.id_certificazione} hover>\n                <TableCell>\n                  <Typography variant=\"body2\" fontWeight=\"bold\">\n                    {cert.numero_certificato}\n                  </Typography>\n                </TableCell>\n                <TableCell>\n                  <Typography variant=\"body2\" fontFamily=\"monospace\">\n                    {cert.id_cavo}\n                  </Typography>\n                </TableCell>\n                <TableCell>{cert.cavo_tipologia || '-'}</TableCell>\n                <TableCell>{cert.cavo_sezione || '-'}</TableCell>\n                <TableCell>{formatDate(cert.data_certificazione)}</TableCell>\n                <TableCell>{cert.id_operatore || '-'}</TableCell>\n                <TableCell>\n                  <Chip\n                    label={cert.valore_isolamento || '-'}\n                    color={getIsolamentoColor(cert.valore_isolamento)}\n                    size=\"small\"\n                  />\n                </TableCell>\n                <TableCell>\n                  {cert.lunghezza_misurata ? cert.lunghezza_misurata.toFixed(2) : '-'}\n                </TableCell>\n                <TableCell>\n                  <Box sx={{ display: 'flex', gap: 0.5 }}>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleViewDetails(cert)}\n                      title=\"Visualizza dettagli\"\n                    >\n                      <VisibilityIcon fontSize=\"small\" />\n                    </IconButton>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => onEdit(cert)}\n                      title=\"Modifica\"\n                    >\n                      <EditIcon fontSize=\"small\" />\n                    </IconButton>\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleDeleteClick(cert)}\n                      title=\"Elimina\"\n                      color=\"error\"\n                    >\n                      <DeleteIcon fontSize=\"small\" />\n                    </IconButton>\n                  </Box>\n                </TableCell>\n              </TableRow>\n            ))}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog Dettagli Certificazione */}\n      <Dialog\n        open={showDetailsDialog}\n        onClose={() => setShowDetailsDialog(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          Dettagli Certificazione {selectedCertificazione?.numero_certificato}\n        </DialogTitle>\n        <DialogContent>\n          {selectedCertificazione && (\n            <Grid container spacing={2} sx={{ mt: 1 }}>\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Informazioni Cavo\n                </Typography>\n                <Typography><strong>ID Cavo:</strong> {selectedCertificazione.id_cavo}</Typography>\n                <Typography><strong>Tipologia:</strong> {selectedCertificazione.cavo_tipologia || '-'}</Typography>\n                <Typography><strong>Sezione:</strong> {selectedCertificazione.cavo_sezione || '-'}</Typography>\n                <Typography><strong>Partenza:</strong> {selectedCertificazione.cavo_ubicazione_partenza || '-'}</Typography>\n                <Typography><strong>Arrivo:</strong> {selectedCertificazione.cavo_ubicazione_arrivo || '-'}</Typography>\n                <Typography><strong>Metri Teorici:</strong> {selectedCertificazione.cavo_metri_teorici || '-'}</Typography>\n                <Typography><strong>Stato:</strong> {selectedCertificazione.cavo_stato_installazione || '-'}</Typography>\n              </Grid>\n              \n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Informazioni Certificazione\n                </Typography>\n                <Typography><strong>N° Certificato:</strong> {selectedCertificazione.numero_certificato}</Typography>\n                <Typography><strong>Data:</strong> {formatDate(selectedCertificazione.data_certificazione)}</Typography>\n                <Typography><strong>Operatore:</strong> {selectedCertificazione.id_operatore || '-'}</Typography>\n                <Typography><strong>Lunghezza Misurata:</strong> {selectedCertificazione.lunghezza_misurata ? `${selectedCertificazione.lunghezza_misurata.toFixed(2)} m` : '-'}</Typography>\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Valori di Test\n                </Typography>\n                <Typography><strong>Continuità:</strong> {selectedCertificazione.valore_continuita || '-'}</Typography>\n                <Typography><strong>Isolamento:</strong> {selectedCertificazione.valore_isolamento || '-'} MΩ</Typography>\n                <Typography><strong>Resistenza:</strong> {selectedCertificazione.valore_resistenza || '-'}</Typography>\n              </Grid>\n\n              <Grid item xs={12} md={6}>\n                <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                  Strumento Utilizzato\n                </Typography>\n                {selectedCertificazione.strumento_nome ? (\n                  <>\n                    <Typography><strong>Nome:</strong> {selectedCertificazione.strumento_nome}</Typography>\n                    <Typography><strong>Marca:</strong> {selectedCertificazione.strumento_marca || '-'}</Typography>\n                    <Typography><strong>Modello:</strong> {selectedCertificazione.strumento_modello || '-'}</Typography>\n                  </>\n                ) : (\n                  <Typography>{selectedCertificazione.strumento_utilizzato || 'Non specificato'}</Typography>\n                )}\n              </Grid>\n\n              {selectedCertificazione.note && (\n                <Grid item xs={12}>\n                  <Typography variant=\"subtitle2\" color=\"text.secondary\">\n                    Note\n                  </Typography>\n                  <Typography>{selectedCertificazione.note}</Typography>\n                </Grid>\n              )}\n            </Grid>\n          )}\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowDetailsDialog(false)}>\n            Chiudi\n          </Button>\n        </DialogActions>\n      </Dialog>\n\n      {/* Dialog Conferma Eliminazione */}\n      <Dialog\n        open={showDeleteDialog}\n        onClose={() => setShowDeleteDialog(false)}\n      >\n        <DialogTitle>Conferma Eliminazione</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Sei sicuro di voler eliminare la certificazione {certificazioneToDelete?.numero_certificato}?\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n            Questa operazione non può essere annullata.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowDeleteDialog(false)}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleDeleteConfirm} \n            color=\"error\" \n            disabled={loading}\n          >\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </>\n  );\n}\n\nexport default CertificazioniList;\n"], "names": ["apiService", "login", "authService", "logout", "getCurrentUser", "getCantieri", "cantieriService", "getMyCantieri", "getCantiere", "createCantiere", "deleteCantiere", "get<PERSON><PERSON>", "caviService", "getCavo", "createCavo", "updateCavo", "deleteCavo", "aggiornaCavo", "getCertificazioni", "certificazioneService", "getCertificazione", "createCertificazione", "updateCertificazione", "deleteCertificazione", "getStrumenti", "createStrumento", "updateStrumento", "deleteStrumento", "getParcoCavi", "parcoCaviService", "createBobina", "updateBobina", "deleteBobina", "generateTemplate", "excelService", "importExcel", "getReports", "reportService", "_ref", "certificazioni", "onEdit", "onDelete", "cantiereId", "selectedCertificazione", "setSelectedCertificazione", "useState", "showDetailsDialog", "setShowDetailsDialog", "showDeleteDialog", "setShowDeleteDialog", "certificazioneToDelete", "setCertificazioneToDelete", "loading", "setLoading", "formatDate", "dateString", "Date", "toLocaleDateString", "getIsolamentoColor", "valore", "numValue", "parseFloat", "length", "_jsxs", "Paper", "sx", "p", "textAlign", "children", "_jsx", "Typography", "variant", "color", "mt", "_Fragment", "TableContainer", "component", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "map", "cert", "hover", "fontWeight", "numero_certificato", "fontFamily", "id_cavo", "cavo_tipologia", "cavo_sezione", "data_certificazione", "id_operatore", "Chip", "label", "valore_isolamento", "size", "<PERSON><PERSON><PERSON>_misurata", "toFixed", "Box", "display", "gap", "IconButton", "onClick", "async", "details", "certificazione", "id_certificazione", "error", "console", "handleViewDetails", "title", "VisibilityIcon", "fontSize", "EditIcon", "DeleteIcon", "Dialog", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Grid", "container", "spacing", "item", "xs", "md", "cavo_ubicazione_partenza", "cavo_ubicazione_arrivo", "cavo_metri_teorici", "cavo_stato_installazione", "valore_continuita", "valore_resistenza", "strumento_nome", "strumento_marca", "strumento_modello", "strumento_utilizzato", "note", "DialogActions", "<PERSON><PERSON>", "disabled"], "sourceRoot": ""}