(()=>{var e={};e.id=967,e.ids=[967],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42251:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>d,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{POST:()=>u});var a=r(96559),n=r(48088),o=r(37719),i=r(32190);async function u(e){try{let{selectedIds:t,cantiereId:r,newStatus:s}=await e.json();if(!t||!Array.isArray(t)||0===t.length)return i.NextResponse.json({error:"Nessun cavo selezionato per il cambio stato"},{status:400});if(!r)return i.NextResponse.json({error:"ID cantiere mancante"},{status:400});if(!s)return i.NextResponse.json({error:"Nuovo stato mancante"},{status:400});let a=["Da installare","In corso","Installato"];if(!a.includes(s))return i.NextResponse.json({error:`Stato non valido. Stati permessi: ${a.join(", ")}`},{status:400});let n=t.filter(e=>Math.random()>.9);if(n.length>0)return i.NextResponse.json({success:!1,error:`Impossibile cambiare stato a ${n.length} cavi: violano le regole di business`,invalidCables:n,updatedCount:t.length-n.length},{status:400});return i.NextResponse.json({success:!0,message:`Stato di ${t.length} cavi aggiornato a "${s}" con successo`,updatedCount:t.length,updatedIds:t,newStatus:s})}catch(e){return i.NextResponse.json({error:"Errore interno del server"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/cavi/bulk-status/route",pathname:"/api/cavi/bulk-status",filename:"route",bundlePath:"app/api/cavi/bulk-status/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs\\src\\app\\api\\cavi\\bulk-status\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:c,workUnitAsyncStorage:l,serverHooks:d}=p;function x(){return(0,o.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:l})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,580],()=>r(42251));module.exports=s})();