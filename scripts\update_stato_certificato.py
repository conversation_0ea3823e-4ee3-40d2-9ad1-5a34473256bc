#!/usr/bin/env python3
"""
Script per aggiornare lo stato dei certificati esistenti da 'BOZZA' a 'CONFORME'
"""

import sys
import os
import logging
import psycopg2

# Aggiungi il percorso del progetto
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configurazione logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def update_stato_certificato():
    """
    Aggiorna lo stato dei certificati esistenti da 'BOZZA' a 'CONFORME'
    """
    try:
        # Connessione al database
        conn = psycopg2.connect(
            host="localhost",
            database="cantieri",
            user="postgres",
            password="Taranto"
        )
        
        cursor = conn.cursor()
        
        logging.info("🔄 Inizio aggiornamento stato certificati...")
        
        # Verifica quanti record hanno stato 'BOZZA'
        cursor.execute("""
            SELECT COUNT(*) 
            FROM certificazionicavi 
            WHERE stato_certificato = 'BOZZA' OR stato_certificato IS NULL
        """)
        
        count_bozza = cursor.fetchone()[0]
        logging.info(f"📊 Trovati {count_bozza} certificati con stato 'BOZZA' o NULL")
        
        if count_bozza == 0:
            logging.info("✅ Nessun certificato da aggiornare")
            return True
        
        # Aggiorna tutti i record con stato 'BOZZA' o NULL a 'CONFORME'
        cursor.execute("""
            UPDATE certificazionicavi 
            SET stato_certificato = 'CONFORME'
            WHERE stato_certificato = 'BOZZA' OR stato_certificato IS NULL
        """)
        
        rows_updated = cursor.rowcount
        logging.info(f"✅ {rows_updated} certificati aggiornati da 'BOZZA' a 'CONFORME'")
        
        # Verifica il risultato
        cursor.execute("""
            SELECT stato_certificato, COUNT(*) 
            FROM certificazionicavi 
            GROUP BY stato_certificato
            ORDER BY stato_certificato
        """)
        
        logging.info("📊 Stato certificati dopo l'aggiornamento:")
        for stato, count in cursor.fetchall():
            logging.info(f"  - {stato or 'NULL'}: {count}")
        
        # Commit delle modifiche
        conn.commit()
        logging.info("✅ Aggiornamento completato con successo!")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        logging.error(f"❌ Errore durante l'aggiornamento: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

if __name__ == "__main__":
    logging.info("🚀 Avvio script aggiornamento stato certificati")
    success = update_stato_certificato()
    
    if success:
        logging.info("🎉 Script completato con successo!")
        sys.exit(0)
    else:
        logging.error("💥 Script fallito!")
        sys.exit(1)
