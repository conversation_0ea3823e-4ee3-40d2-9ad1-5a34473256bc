#!/usr/bin/env python3
"""
Test Integrazione Database per Sistema Gestione Password
Verifica che il sistema funzioni con i modelli esistenti del database.
"""

import os
import sys
from pathlib import Path
from datetime import datetime, timedelta

# Aggiungi il path del backend
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Carica le variabili d'ambiente
try:
    from dotenv import load_dotenv
    env_file = backend_dir / '.env'
    if env_file.exists():
        load_dotenv(env_file)
except ImportError:
    pass

def test_database_connection():
    """Testa la connessione al database."""
    print("\n🗄️  Test Connessione Database")
    print("=" * 50)
    
    try:
        from database import get_db, engine
        from sqlalchemy import text
        
        # Test connessione
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            if result.fetchone():
                print("✅ Connessione database funzionante")
                return True
            else:
                print("❌ Connessione database fallita")
                return False
                
    except Exception as e:
        print(f"❌ Errore connessione database: {e}")
        return False

def test_user_models():
    """Testa i modelli utente esistenti."""
    print("\n👥 Test Modelli Utente")
    print("=" * 50)
    
    try:
        from models.models import User, Cantiere
        from database import get_db
        from sqlalchemy.orm import Session
        
        # Ottieni una sessione database
        db_gen = get_db()
        db: Session = next(db_gen)
        
        # Test query utenti
        users_count = db.query(User).count()
        print(f"✅ Utenti nel database: {users_count}")
        
        # Test query cantieri
        cantieri_count = db.query(Cantiere).count()
        print(f"✅ Cantieri nel database: {cantieri_count}")
        
        # Test query utenti con email
        users_with_email = db.query(User).filter(User.email.isnot(None)).count()
        print(f"✅ Utenti con email: {users_with_email}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore modelli utente: {e}")
        return False

def test_password_reset_models():
    """Testa i modelli per il reset password."""
    print("\n🔄 Test Modelli Reset Password")
    print("=" * 50)
    
    try:
        from models.security_models import PasswordResetToken, SecurityEvent
        from database import get_db
        from sqlalchemy.orm import Session
        
        # Ottieni una sessione database
        db_gen = get_db()
        db: Session = next(db_gen)
        
        # Test creazione token reset
        test_token = PasswordResetToken(
            token_hash="test_hash_123",
            user_id=1,
            user_type="user",
            email="<EMAIL>",
            expires_at=datetime.utcnow() + timedelta(minutes=30),
            used=False
        )
        
        # Non salviamo realmente per non sporcare il database
        print("✅ Modello PasswordResetToken creato correttamente")
        
        # Test creazione evento sicurezza
        test_event = SecurityEvent(
            event_type="password_reset_request",
            user_id=1,
            user_type="user",
            ip_address="127.0.0.1",
            user_agent="test-agent",
            details={"email": "<EMAIL>"}
        )
        
        print("✅ Modello SecurityEvent creato correttamente")
        
        # Test query esistenti
        reset_tokens_count = db.query(PasswordResetToken).count()
        print(f"✅ Token reset nel database: {reset_tokens_count}")
        
        security_events_count = db.query(SecurityEvent).count()
        print(f"✅ Eventi sicurezza nel database: {security_events_count}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Errore modelli reset password: {e}")
        return False

def test_password_security_integration():
    """Testa l'integrazione con il sistema di sicurezza password."""
    print("\n🔒 Test Integrazione Sicurezza Password")
    print("=" * 50)
    
    try:
        from core.password_security import PasswordValidator, SecureTokenManager
        
        # Test validatore password
        validator = PasswordValidator()
        is_valid, message, score = validator.validate_password("Strong123!")
        print(f"✅ Validatore password: Valida={is_valid}, Score={score}")
        
        # Test gestore token
        token_manager = SecureTokenManager()
        token = token_manager.generate_reset_token(1, "<EMAIL>")
        print(f"✅ Token generato: {token[:20]}...")
        
        # Test verifica token
        is_valid_token, user_data = token_manager.verify_reset_token(token)
        print(f"✅ Verifica token: Valido={is_valid_token}")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore integrazione sicurezza: {e}")
        return False

def test_email_service_integration():
    """Testa l'integrazione con il servizio email."""
    print("\n📧 Test Integrazione Servizio Email")
    print("=" * 50)
    
    try:
        from core.email_service import EmailService
        
        # Test inizializzazione servizio
        email_service = EmailService()
        print("✅ Servizio email inizializzato")
        
        # Test invio email reset (modalità testing)
        success = email_service.send_password_reset_email(
            to_email="<EMAIL>",
            reset_link="https://example.com/reset?token=test123",
            user_name="Test User",
            user_type="utente"
        )
        
        if success:
            print("✅ Email reset password inviata (modalità testing)")
        else:
            print("❌ Errore invio email reset password")
            return False
        
        # Test invio notifica cambio password
        success = email_service.send_password_changed_notification(
            to_email="<EMAIL>",
            user_name="Test User",
            user_type="utente"
        )
        
        if success:
            print("✅ Email notifica cambio password inviata (modalità testing)")
        else:
            print("❌ Errore invio notifica cambio password")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore integrazione email: {e}")
        return False

def test_api_endpoints_integration():
    """Testa l'integrazione degli endpoint API."""
    print("\n🌐 Test Integrazione Endpoint API")
    print("=" * 50)
    
    try:
        # Importa gli endpoint per verificare che si carichino correttamente
        from api.password_management import router
        print("✅ Router password management caricato")
        
        # Verifica che gli endpoint siano registrati
        routes = [route.path for route in router.routes]
        expected_routes = [
            "/change-password",
            "/request-password-reset", 
            "/confirm-password-reset",
            "/validate-password",
            "/verify-reset-token"
        ]
        
        for expected_route in expected_routes:
            if expected_route in routes:
                print(f"✅ Endpoint {expected_route} registrato")
            else:
                print(f"❌ Endpoint {expected_route} mancante")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ Errore integrazione API: {e}")
        return False

def main():
    """Esegue tutti i test di integrazione database."""
    print("🧪 TEST INTEGRAZIONE DATABASE - GESTIONE PASSWORD")
    print("=" * 70)
    
    tests = [
        ("Connessione Database", test_database_connection),
        ("Modelli Utente", test_user_models),
        ("Modelli Reset Password", test_password_reset_models),
        ("Integrazione Sicurezza", test_password_security_integration),
        ("Integrazione Email", test_email_service_integration),
        ("Integrazione API", test_api_endpoints_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔄 Esecuzione: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ Errore durante {test_name}: {e}")
            results.append((test_name, False))
    
    # Risultati finali
    print("\n" + "=" * 70)
    print("📊 RISULTATI TEST INTEGRAZIONE")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    total = len(results)
    print(f"\n🎯 Risultato: {passed}/{total} test superati")
    
    if passed == total:
        print("🎉 Tutti i test di integrazione superati!")
        print("💡 Il sistema è completamente integrato e funzionante.")
        return True
    else:
        print("🔧 Alcuni test falliti. Verifica la configurazione database.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
