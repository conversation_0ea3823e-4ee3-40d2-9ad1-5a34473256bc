(()=>{var e={};e.id=562,e.ids=[562],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26134:(e,i,a)=>{"use strict";a.d(i,{UC:()=>ea,VY:()=>es,ZL:()=>ee,bL:()=>X,bm:()=>er,hE:()=>et,hJ:()=>ei,l9:()=>Q});var t=a(43210),s=a(70569),r=a(98599),n=a(11273),o=a(96963),l=a(65551),c=a(31355),d=a(32547),u=a(25028),m=a(46059),x=a(14163),p=a(1359),h=a(42247),b=a(63376),g=a(8730),v=a(60687),f="Dialog",[j,N]=(0,n.A)(f),[y,_]=j(f),w=e=>{let{__scopeDialog:i,children:a,open:s,defaultOpen:r,onOpenChange:n,modal:c=!0}=e,d=t.useRef(null),u=t.useRef(null),[m,x]=(0,l.i)({prop:s,defaultProp:r??!1,onChange:n,caller:f});return(0,v.jsx)(y,{scope:i,triggerRef:d,contentRef:u,contentId:(0,o.B)(),titleId:(0,o.B)(),descriptionId:(0,o.B)(),open:m,onOpenChange:x,onOpenToggle:t.useCallback(()=>x(e=>!e),[x]),modal:c,children:a})};w.displayName=f;var z="DialogTrigger",D=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,n=_(z,a),o=(0,r.s)(i,n.triggerRef);return(0,v.jsx)(x.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":J(n.open),...t,ref:o,onClick:(0,s.m)(e.onClick,n.onOpenToggle)})});D.displayName=z;var C="DialogPortal",[A,S]=j(C,{forceMount:void 0}),E=e=>{let{__scopeDialog:i,forceMount:a,children:s,container:r}=e,n=_(C,i);return(0,v.jsx)(A,{scope:i,forceMount:a,children:t.Children.map(s,e=>(0,v.jsx)(m.C,{present:a||n.open,children:(0,v.jsx)(u.Z,{asChild:!0,container:r,children:e})}))})};E.displayName=C;var T="DialogOverlay",I=t.forwardRef((e,i)=>{let a=S(T,e.__scopeDialog),{forceMount:t=a.forceMount,...s}=e,r=_(T,e.__scopeDialog);return r.modal?(0,v.jsx)(m.C,{present:t||r.open,children:(0,v.jsx)(R,{...s,ref:i})}):null});I.displayName=T;var F=(0,g.TL)("DialogOverlay.RemoveScroll"),R=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,s=_(T,a);return(0,v.jsx)(h.A,{as:F,allowPinchZoom:!0,shards:[s.contentRef],children:(0,v.jsx)(x.sG.div,{"data-state":J(s.open),...t,ref:i,style:{pointerEvents:"auto",...t.style}})})}),k="DialogContent",O=t.forwardRef((e,i)=>{let a=S(k,e.__scopeDialog),{forceMount:t=a.forceMount,...s}=e,r=_(k,e.__scopeDialog);return(0,v.jsx)(m.C,{present:t||r.open,children:r.modal?(0,v.jsx)(M,{...s,ref:i}):(0,v.jsx)(B,{...s,ref:i})})});O.displayName=k;var M=t.forwardRef((e,i)=>{let a=_(k,e.__scopeDialog),n=t.useRef(null),o=(0,r.s)(i,a.contentRef,n);return t.useEffect(()=>{let e=n.current;if(e)return(0,b.Eq)(e)},[]),(0,v.jsx)($,{...e,ref:o,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let i=e.detail.originalEvent,a=0===i.button&&!0===i.ctrlKey;(2===i.button||a)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=t.forwardRef((e,i)=>{let a=_(k,e.__scopeDialog),s=t.useRef(!1),r=t.useRef(!1);return(0,v.jsx)($,{...e,ref:i,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{e.onCloseAutoFocus?.(i),i.defaultPrevented||(s.current||a.triggerRef.current?.focus(),i.preventDefault()),s.current=!1,r.current=!1},onInteractOutside:i=>{e.onInteractOutside?.(i),i.defaultPrevented||(s.current=!0,"pointerdown"===i.detail.originalEvent.type&&(r.current=!0));let t=i.target;a.triggerRef.current?.contains(t)&&i.preventDefault(),"focusin"===i.detail.originalEvent.type&&r.current&&i.preventDefault()}})}),$=t.forwardRef((e,i)=>{let{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:n,onCloseAutoFocus:o,...l}=e,u=_(k,a),m=t.useRef(null),x=(0,r.s)(i,m);return(0,p.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(d.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:n,onUnmountAutoFocus:o,children:(0,v.jsx)(c.qW,{role:"dialog",id:u.contentId,"aria-describedby":u.descriptionId,"aria-labelledby":u.titleId,"data-state":J(u.open),...l,ref:x,onDismiss:()=>u.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(Z,{titleId:u.titleId}),(0,v.jsx)(Y,{contentRef:m,descriptionId:u.descriptionId})]})]})}),L="DialogTitle",P=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,s=_(L,a);return(0,v.jsx)(x.sG.h2,{id:s.titleId,...t,ref:i})});P.displayName=L;var U="DialogDescription",V=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,s=_(U,a);return(0,v.jsx)(x.sG.p,{id:s.descriptionId,...t,ref:i})});V.displayName=U;var q="DialogClose",G=t.forwardRef((e,i)=>{let{__scopeDialog:a,...t}=e,r=_(q,a);return(0,v.jsx)(x.sG.button,{type:"button",...t,ref:i,onClick:(0,s.m)(e.onClick,()=>r.onOpenChange(!1))})});function J(e){return e?"open":"closed"}G.displayName=q;var H="DialogTitleWarning",[K,W]=(0,n.q)(H,{contentName:k,titleName:L,docsSlug:"dialog"}),Z=({titleId:e})=>{let i=W(H),a=`\`${i.contentName}\` requires a \`${i.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${i.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${i.docsSlug}`;return t.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},Y=({contentRef:e,descriptionId:i})=>{let a=W("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return t.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");i&&a&&(document.getElementById(i)||console.warn(s))},[s,e,i]),null},X=w,Q=D,ee=E,ei=I,ea=O,et=P,es=V,er=G},27910:e=>{"use strict";e.exports=require("stream")},28227:(e,i,a)=>{Promise.resolve().then(a.bind(a,64435))},28354:e=>{"use strict";e.exports=require("util")},28907:(e,i,a)=>{Promise.resolve().then(a.bind(a,71902))},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56770:(e,i,a)=>{"use strict";a.d(i,{tU:()=>H,av:()=>Z,j7:()=>K,Xi:()=>W});var t=a(60687),s=a(43210),r=a(70569),n=a(11273),o=a(9510),l=a(98599),c=a(96963),d=a(14163),u=a(13495),m=a(65551),x=a(43),p="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},b="RovingFocusGroup",[g,v,f]=(0,o.N)(b),[j,N]=(0,n.A)(b,[f]),[y,_]=j(b),w=s.forwardRef((e,i)=>(0,t.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,t.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,t.jsx)(z,{...e,ref:i})})}));w.displayName=b;var z=s.forwardRef((e,i)=>{let{__scopeRovingFocusGroup:a,orientation:n,loop:o=!1,dir:c,currentTabStopId:g,defaultCurrentTabStopId:f,onCurrentTabStopIdChange:j,onEntryFocus:N,preventScrollOnEntryFocus:_=!1,...w}=e,z=s.useRef(null),D=(0,l.s)(i,z),C=(0,x.jH)(c),[A,E]=(0,m.i)({prop:g,defaultProp:f??null,onChange:j,caller:b}),[T,I]=s.useState(!1),F=(0,u.c)(N),R=v(a),k=s.useRef(!1),[O,M]=s.useState(0);return s.useEffect(()=>{let e=z.current;if(e)return e.addEventListener(p,F),()=>e.removeEventListener(p,F)},[F]),(0,t.jsx)(y,{scope:a,orientation:n,dir:C,loop:o,currentTabStopId:A,onItemFocus:s.useCallback(e=>E(e),[E]),onItemShiftTab:s.useCallback(()=>I(!0),[]),onFocusableItemAdd:s.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:s.useCallback(()=>M(e=>e-1),[]),children:(0,t.jsx)(d.sG.div,{tabIndex:T||0===O?-1:0,"data-orientation":n,...w,ref:D,style:{outline:"none",...e.style},onMouseDown:(0,r.m)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,r.m)(e.onFocus,e=>{let i=!k.current;if(e.target===e.currentTarget&&i&&!T){let i=new CustomEvent(p,h);if(e.currentTarget.dispatchEvent(i),!i.defaultPrevented){let e=R().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===A),...e].filter(Boolean).map(e=>e.ref.current),_)}}k.current=!1}),onBlur:(0,r.m)(e.onBlur,()=>I(!1))})})}),D="RovingFocusGroupItem",C=s.forwardRef((e,i)=>{let{__scopeRovingFocusGroup:a,focusable:n=!0,active:o=!1,tabStopId:l,children:u,...m}=e,x=(0,c.B)(),p=l||x,h=_(D,a),b=h.currentTabStopId===p,f=v(a),{onFocusableItemAdd:j,onFocusableItemRemove:N,currentTabStopId:y}=h;return s.useEffect(()=>{if(n)return j(),()=>N()},[n,j,N]),(0,t.jsx)(g.ItemSlot,{scope:a,id:p,focusable:n,active:o,children:(0,t.jsx)(d.sG.span,{tabIndex:b?0:-1,"data-orientation":h.orientation,...m,ref:i,onMouseDown:(0,r.m)(e.onMouseDown,e=>{n?h.onItemFocus(p):e.preventDefault()}),onFocus:(0,r.m)(e.onFocus,()=>h.onItemFocus(p)),onKeyDown:(0,r.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let i=function(e,i,a){var t;let s=(t=e.key,"rtl"!==a?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===i&&["ArrowLeft","ArrowRight"].includes(s))&&!("horizontal"===i&&["ArrowUp","ArrowDown"].includes(s)))return A[s]}(e,h.orientation,h.dir);if(void 0!==i){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let a=f().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===i)a.reverse();else if("prev"===i||"next"===i){"prev"===i&&a.reverse();let t=a.indexOf(e.currentTarget);a=h.loop?function(e,i){return e.map((a,t)=>e[(i+t)%e.length])}(a,t+1):a.slice(t+1)}setTimeout(()=>S(a))}}),children:"function"==typeof u?u({isCurrentTabStop:b,hasTabStop:null!=y}):u})})});C.displayName=D;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e,i=!1){let a=document.activeElement;for(let t of e)if(t===a||(t.focus({preventScroll:i}),document.activeElement!==a))return}var E=a(46059),T="Tabs",[I,F]=(0,n.A)(T,[N]),R=N(),[k,O]=I(T),M=s.forwardRef((e,i)=>{let{__scopeTabs:a,value:s,onValueChange:r,defaultValue:n,orientation:o="horizontal",dir:l,activationMode:u="automatic",...p}=e,h=(0,x.jH)(l),[b,g]=(0,m.i)({prop:s,onChange:r,defaultProp:n??"",caller:T});return(0,t.jsx)(k,{scope:a,baseId:(0,c.B)(),value:b,onValueChange:g,orientation:o,dir:h,activationMode:u,children:(0,t.jsx)(d.sG.div,{dir:h,"data-orientation":o,...p,ref:i})})});M.displayName=T;var B="TabsList",$=s.forwardRef((e,i)=>{let{__scopeTabs:a,loop:s=!0,...r}=e,n=O(B,a),o=R(a);return(0,t.jsx)(w,{asChild:!0,...o,orientation:n.orientation,dir:n.dir,loop:s,children:(0,t.jsx)(d.sG.div,{role:"tablist","aria-orientation":n.orientation,...r,ref:i})})});$.displayName=B;var L="TabsTrigger",P=s.forwardRef((e,i)=>{let{__scopeTabs:a,value:s,disabled:n=!1,...o}=e,l=O(L,a),c=R(a),u=q(l.baseId,s),m=G(l.baseId,s),x=s===l.value;return(0,t.jsx)(C,{asChild:!0,...c,focusable:!n,active:x,children:(0,t.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":m,"data-state":x?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:u,...o,ref:i,onMouseDown:(0,r.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(s)}),onKeyDown:(0,r.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(s)}),onFocus:(0,r.m)(e.onFocus,()=>{let e="manual"!==l.activationMode;x||n||!e||l.onValueChange(s)})})})});P.displayName=L;var U="TabsContent",V=s.forwardRef((e,i)=>{let{__scopeTabs:a,value:r,forceMount:n,children:o,...l}=e,c=O(U,a),u=q(c.baseId,r),m=G(c.baseId,r),x=r===c.value,p=s.useRef(x);return s.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(E.C,{present:n||x,children:({present:a})=>(0,t.jsx)(d.sG.div,{"data-state":x?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":u,hidden:!a,id:m,tabIndex:0,...l,ref:i,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&o})})});function q(e,i){return`${e}-trigger-${i}`}function G(e,i){return`${e}-content-${i}`}V.displayName=U;var J=a(4780);let H=M,K=s.forwardRef(({className:e,...i},a)=>(0,t.jsx)($,{ref:a,className:(0,J.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...i}));K.displayName=$.displayName;let W=s.forwardRef(({className:e,...i},a)=>(0,t.jsx)(P,{ref:a,className:(0,J.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...i}));W.displayName=P.displayName;let Z=s.forwardRef(({className:e,...i},a)=>(0,t.jsx)(V,{ref:a,className:(0,J.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...i}));Z.displayName=V.displayName},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},63503:(e,i,a)=>{"use strict";a.d(i,{Cf:()=>u,Es:()=>x,L3:()=>p,c7:()=>m,lG:()=>o,rr:()=>h,zM:()=>l});var t=a(60687);a(43210);var s=a(26134),r=a(11860),n=a(4780);function o({...e}){return(0,t.jsx)(s.bL,{"data-slot":"dialog",...e})}function l({...e}){return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...e})}function c({...e}){return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...e})}function d({className:e,...i}){return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...i})}function u({className:e,children:i,showCloseButton:a=!0,...o}){return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...o,children:[i,a&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(r.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function m({className:e,...i}){return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",e),...i})}function x({className:e,...i}){return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...i})}function p({className:e,...i}){return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",e),...i})}function h({className:e,...i}){return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",e),...i})}},64435:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>Q});var t=a(60687),s=a(43210),r=a(44493),n=a(29523),o=a(96834),l=a(89667),c=a(6211),d=a(91821),u=a(63213),m=a(76628),x=a(25653),p=a(62185);let h={DISPONIBILE:"Disponibile",IN_USO:"In uso",TERMINATA:"Terminata",OVER:"Over"},b=(e,i)=>e<0?h.OVER:0===e?h.TERMINATA:e<i?h.IN_USO:h.DISPONIBILE,g=e=>e===h.DISPONIBILE||e===h.IN_USO,v=e=>{switch(e){case h.DISPONIBILE:return"hover:bg-green-50";case h.IN_USO:return"hover:bg-yellow-50";case h.TERMINATA:return"hover:bg-red-50";case h.OVER:return"hover:bg-red-100";default:return"hover:bg-gray-50"}},f=e=>e!==h.OVER&&e!==h.TERMINATA,j=(e,i)=>i<=0?0:Math.max(0,Math.min(100,(i-e)/i*100)),N=e=>`${e.toFixed(1)}m`,y=e=>{switch(e){case h.DISPONIBILE:return"Bobina disponibile per nuove installazioni";case h.IN_USO:return"Bobina parzialmente utilizzata";case h.TERMINATA:return"Bobina completamente esaurita";case h.OVER:return"Bobina sovra-utilizzata (metri negativi)";default:return"Stato non definito"}};var _=a(63503),w=a(80013),z=a(96474),D=a(41862),C=a(93613);let A={numero_bobina:"",utility:"",tipologia:"",n_conduttori:"0",sezione:"",metri_totali:"",ubicazione_bobina:"TBD",fornitore:"TBD",n_DDT:"TBD",data_DDT:"",configurazione:"s"};function S({open:e,onClose:i,cantiereId:a,onSuccess:r,onError:o}){let[c,u]=(0,s.useState)(A),[m,x]=(0,s.useState)(!1),[h,b]=(0,s.useState)(""),[g,v]=(0,s.useState)("1"),[f,j]=(0,s.useState)(!1),[N,y]=(0,s.useState)(!0),[S,E]=(0,s.useState)(""),[T,I]=(0,s.useState)(!1),F=async()=>{if(a&&!(a<=0))try{let e=await p.Fw.getBobine(a);if(e&&e.length>0){let i=e.filter(e=>e.numero_bobina&&/^\d+$/.test(e.numero_bobina));if(i.length>0){let e=Math.max(...i.map(e=>parseInt(e.numero_bobina,10))),a=String(e+1);v(a),u(e=>({...e,numero_bobina:a}))}else v("1"),u(e=>({...e,numero_bobina:"1"}))}else v("1"),u(e=>({...e,numero_bobina:"1"}))}catch(e){v("1"),u(e=>({...e,numero_bobina:"1"}))}},R=(e,i)=>{u(a=>({...a,[e]:i})),b("")},k=async e=>{E(e),u(i=>({...i,configurazione:e})),I(!1),"s"===e?await F():u(e=>({...e,numero_bobina:""}))},O=()=>{if(!c.numero_bobina.trim())return"Il numero bobina \xe8 obbligatorio";if("m"===c.configurazione){let e=c.numero_bobina.trim();if(/[\s\\/:*?"<>|]/.test(e))return'Il numero bobina non pu\xf2 contenere spazi o caratteri speciali come \\ / : * ? " < > |'}if(!c.utility.trim())return"La utility \xe8 obbligatoria";if(!c.tipologia.trim())return"La tipologia \xe8 obbligatoria";if(!c.sezione.trim())return"La formazione \xe8 obbligatoria";if(!c.metri_totali.trim())return"I metri totali sono obbligatori";let e=parseFloat(c.metri_totali);return isNaN(e)||e<=0?"I metri totali devono essere un numero positivo":null},M=async()=>{let e=O();if(e)return void b(e);try{if(x(!0),b(""),!a||a<=0)throw Error("Cantiere non selezionato");let e={numero_bobina:c.numero_bobina.trim(),utility:c.utility.trim().toUpperCase(),tipologia:c.tipologia.trim().toUpperCase(),n_conduttori:"0",sezione:c.sezione.trim(),metri_totali:parseFloat(c.metri_totali),ubicazione_bobina:c.ubicazione_bobina.trim()||"TBD",fornitore:c.fornitore.trim()||"TBD",n_DDT:c.n_DDT.trim()||"TBD",data_DDT:c.data_DDT||null,configurazione:c.configurazione};await p.Fw.createBobina(a,e),r(`Bobina ${c.numero_bobina} creata con successo`),i()}catch(i){let e=i.response?.data?.detail||i.message||"Errore durante la creazione della bobina";e.includes("gi\xe0 presente nel cantiere")||e.includes("gi\xe0 esistente")?b(`⚠️ Bobina con numero ${c.numero_bobina} gi\xe0 esistente. Scegli un numero diverso.`):o(e)}finally{x(!1)}},B=()=>{m||(u(A),b(""),i())};return(0,t.jsx)(_.lG,{open:e,onOpenChange:B,children:(0,t.jsxs)(_.Cf,{className:"max-h-[95vh] overflow-y-auto",style:{width:"1000px !important",maxWidth:"95vw !important",minWidth:"1000px"},children:[(0,t.jsxs)(_.c7,{children:[(0,t.jsxs)(_.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(z.A,{className:"h-5 w-5"}),"Crea Nuova Bobina"]}),(0,t.jsx)(_.rr,{})]}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[f&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)(D.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Verifica configurazione..."})]}),T&&!f&&(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Seleziona configurazione per questo cantiere"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Questa scelta determiner\xe0 come verranno numerati tutti i futuri inserimenti di bobine in questo cantiere."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsx)(n.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>k("s"),children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Standard (s) - Numerazione automatica"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"I numeri bobina vengono generati automaticamente: 1, 2, 3..."})]})}),(0,t.jsx)(n.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>k("m"),children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Manuale (m) - Inserimento manuale"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Puoi inserire numeri personalizzati: A123, TEST01, ecc."})]})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[!T&&!f&&(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"numero_bobina",className:"text-right",children:"Bobina *"}),(0,t.jsx)(l.p,{id:"numero_bobina",value:c.numero_bobina,onChange:e=>R("numero_bobina",e.target.value),placeholder:"s"===c.configurazione?"Generato automaticamente":"Es: A123, TEST01",disabled:m||"s"===c.configurazione,className:`col-span-2 ${"s"===c.configurazione?"bg-gray-50":""}`})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"utility",className:"text-right",children:"Utility *"}),(0,t.jsx)(l.p,{id:"utility",value:c.utility,onChange:e=>R("utility",e.target.value),className:"col-span-2",placeholder:"Es: ENEL, TIM, OPEN FIBER",disabled:m})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"tipologia",className:"text-right",children:"Tipologia *"}),(0,t.jsx)(l.p,{id:"tipologia",value:c.tipologia,onChange:e=>R("tipologia",e.target.value),className:"col-span-2",placeholder:"Es: FO, RAME",disabled:m})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"sezione",className:"text-right",children:"Formazione *"}),(0,t.jsx)(l.p,{id:"sezione",value:c.sezione,onChange:e=>R("sezione",e.target.value),className:"col-span-2",placeholder:"Es: 9/125, 50/125, 1.5",disabled:m})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"metri_totali",className:"text-right",children:"Metri Totali *"}),(0,t.jsx)(l.p,{id:"metri_totali",type:"number",step:"0.1",min:"0",value:c.metri_totali,onChange:e=>R("metri_totali",e.target.value),className:"col-span-2",placeholder:"Es: 1000",disabled:m})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"ubicazione_bobina",className:"text-right",children:"Ubicazione"}),(0,t.jsx)(l.p,{id:"ubicazione_bobina",value:c.ubicazione_bobina,onChange:e=>R("ubicazione_bobina",e.target.value),className:"col-span-2",placeholder:"Es: Magazzino A, Cantiere",disabled:m})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"fornitore",className:"text-right",children:"Fornitore"}),(0,t.jsx)(l.p,{id:"fornitore",value:c.fornitore,onChange:e=>R("fornitore",e.target.value),className:"col-span-2",placeholder:"Es: Prysmian, Nexans",disabled:m})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"n_DDT",className:"text-right",children:"N\xb0 DDT"}),(0,t.jsx)(l.p,{id:"n_DDT",value:c.n_DDT,onChange:e=>R("n_DDT",e.target.value),className:"col-span-2",placeholder:"Es: DDT001",disabled:m})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(w.J,{htmlFor:"data_DDT",className:"text-right",children:"Data DDT"}),(0,t.jsx)(l.p,{id:"data_DDT",type:"date",value:c.data_DDT,onChange:e=>R("data_DDT",e.target.value),className:"col-span-2",disabled:m})]})]})]}),h&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(C.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:h})]})]}),(0,t.jsxs)(_.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:B,disabled:m||T,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:M,disabled:m||T||f,children:[m&&(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin"}),m?"Creando...":"Crea Bobina"]})]})]})})}var E=a(15079),T=a(19080),I=a(96882);function F({open:e,onClose:i,bobina:a,onSuccess:r,onError:o}){let{cantiere:c}=(0,u.A)(),[m,x]=(0,s.useState)({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),[h,b]=(0,s.useState)({}),[g,v]=(0,s.useState)({}),[f,j]=(0,s.useState)(!1),N=(e,i)=>{x(t=>{let s={...t,[e]:i};return"metri_totali"===e&&a&&(s.metri_residui=Math.max(0,(parseFloat(i)||0)-(a.metri_totali-a.metri_residui)).toString()),s})},y=()=>!!a&&("Over"===a.stato_bobina||"Disponibile"===a.stato_bobina),z=e=>!!a&&(!!["fornitore","ubicazione_bobina","n_DDT","data_DDT"].includes(e)||"Disponibile"===a.stato_bobina),A=async()=>{if(a&&c&&!(Object.keys(h).length>0)){if(!y())return void o("La bobina non pu\xf2 essere modificata nel suo stato attuale");try{j(!0);let e={utility:m.utility,tipologia:m.tipologia,sezione:m.sezione,metri_totali:parseFloat(m.metri_totali),ubicazione_bobina:m.ubicazione_bobina,fornitore:m.fornitore,n_DDT:m.n_DDT,data_DDT:m.data_DDT||null};await p.Fw.updateBobina(c.id_cantiere,a.id_bobina,e),r(`Bobina ${a.numero_bobina} aggiornata con successo`),i()}catch(e){o(e.response?.data?.detail||e.message||"Errore durante la modifica della bobina")}finally{j(!1)}}},S=()=>{f||(x({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),b({}),v({}),i())};return a?(0,t.jsx)(_.lG,{open:e,onOpenChange:S,children:(0,t.jsxs)(_.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(_.c7,{children:[(0,t.jsxs)(_.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(T.A,{className:"h-5 w-5"}),"Modifica Bobina"]}),(0,t.jsxs)(_.rr,{children:["Modifica i dati della bobina ",a.numero_bobina]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(d.Fc,{className:"border-blue-200 bg-blue-50",children:[(0,t.jsx)(I.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)(d.TN,{className:"text-blue-800",children:[(0,t.jsx)("div",{className:"font-semibold mb-1",children:"Condizioni per la modifica:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside text-sm space-y-1",children:[(0,t.jsx)("li",{children:'La bobina deve essere nello stato "Disponibile"'}),(0,t.jsx)("li",{children:"La bobina non deve essere associata a nessun cavo"}),(0,t.jsx)("li",{children:"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente"})]}),(0,t.jsxs)("div",{className:"mt-2 text-sm",children:[(0,t.jsx)("strong",{children:"Stato attuale:"})," ",a.stato_bobina]})]})]}),Object.keys(g).length>0&&(0,t.jsxs)(d.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsxs)(d.TN,{className:"text-amber-800",children:[(0,t.jsx)("div",{className:"font-semibold",children:"Attenzione:"}),(0,t.jsx)("ul",{className:"list-disc list-inside text-sm",children:Object.values(g).map((e,i)=>(0,t.jsx)("li",{children:e},i))})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"numero_bobina",children:"ID Bobina"}),(0,t.jsx)(l.p,{id:"numero_bobina",value:m.numero_bobina,disabled:!0,className:"bg-gray-100 font-semibold"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"utility",children:"Utility *"}),(0,t.jsx)(l.p,{id:"utility",value:m.utility,onChange:e=>N("utility",e.target.value),disabled:f||!z("utility"),className:h.utility?"border-red-500":""}),h.utility&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.utility})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"tipologia",children:"Tipologia *"}),(0,t.jsx)(l.p,{id:"tipologia",value:m.tipologia,onChange:e=>N("tipologia",e.target.value),disabled:f||!z("tipologia"),className:h.tipologia?"border-red-500":""}),h.tipologia&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.tipologia})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"sezione",children:"Formazione *"}),(0,t.jsx)(l.p,{id:"sezione",value:m.sezione,onChange:e=>N("sezione",e.target.value),disabled:f||!z("sezione"),className:h.sezione?"border-red-500":""}),h.sezione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.sezione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"metri_totali",children:"Metri Totali *"}),(0,t.jsx)(l.p,{id:"metri_totali",type:"number",value:m.metri_totali,onChange:e=>N("metri_totali",e.target.value),disabled:f||!z("metri_totali"),className:h.metri_totali?"border-red-500":"",step:"0.1",min:"0"}),h.metri_totali&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.metri_totali})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"metri_residui",children:"Metri Residui"}),(0,t.jsx)(l.p,{id:"metri_residui",type:"number",value:m.metri_residui,disabled:!0,className:"bg-gray-100"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"I metri residui non possono essere modificati direttamente"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"stato_bobina",children:"Stato Bobina"}),(0,t.jsxs)(E.l6,{value:m.stato_bobina,disabled:!0,children:[(0,t.jsx)(E.bq,{className:"bg-gray-100",children:(0,t.jsx)(E.yv,{})}),(0,t.jsxs)(E.gC,{children:[(0,t.jsx)(E.eb,{value:"Disponibile",children:"Disponibile"}),(0,t.jsx)(E.eb,{value:"In uso",children:"In uso"}),(0,t.jsx)(E.eb,{value:"Terminata",children:"Terminata"}),(0,t.jsx)(E.eb,{value:"Danneggiata",children:"Danneggiata"}),(0,t.jsx)(E.eb,{value:"Over",children:"Over"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"ubicazione_bobina",children:"Ubicazione Bobina"}),(0,t.jsx)(l.p,{id:"ubicazione_bobina",value:m.ubicazione_bobina,onChange:e=>N("ubicazione_bobina",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"fornitore",children:"Fornitore"}),(0,t.jsx)(l.p,{id:"fornitore",value:m.fornitore,onChange:e=>N("fornitore",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"n_DDT",children:"Numero DDT"}),(0,t.jsx)(l.p,{id:"n_DDT",value:m.n_DDT,onChange:e=>N("n_DDT",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(w.J,{htmlFor:"data_DDT",children:"Data DDT (YYYY-MM-DD)"}),(0,t.jsx)(l.p,{id:"data_DDT",type:"date",value:m.data_DDT,onChange:e=>N("data_DDT",e.target.value),disabled:f,className:h.data_DDT?"border-red-500":""}),h.data_DDT&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.data_DDT})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,t.jsx)(w.J,{htmlFor:"configurazione",children:"Modalit\xe0 Numerazione"}),(0,t.jsx)(l.p,{id:"configurazione",value:"s"===m.configurazione?"Automatica":"Manuale",disabled:!0,className:"bg-gray-100"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"s"===m.configurazione?"Numerazione progressiva automatica (1, 2, 3, ...)":"Inserimento manuale dell'ID bobina (es. A123, SPEC01, ...)"})]})]})]}),(0,t.jsxs)(_.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:S,disabled:f,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:A,disabled:f||Object.keys(h).length>0||!y(),className:"bg-mariner-600 hover:bg-mariner-700",children:[f&&(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})}):null}var R=a(88233),k=a(43649);function O({open:e,onClose:i,bobina:a,cantiereId:r,onSuccess:o,onError:l}){let[c,u]=(0,s.useState)(!1),m=async()=>{if(a)try{if(u(!0),!r||r<=0)throw Error("Cantiere non selezionato");let e=a.id_bobina.split("_B")[1],t=await p.Fw.deleteBobina(r,e),s=`Bobina ${a.numero_bobina} eliminata con successo`;t.data?.is_last_bobina&&(s+=". Era l'ultima bobina del cantiere."),o(s),i()}catch(e){l(e.response?.data?.detail||e.message||"Errore durante l'eliminazione della bobina")}finally{u(!1)}},x=()=>{c||i()};if(!a)return null;let b=a.stato_bobina===h.OVER,v=!b&&g(a.stato_bobina)&&a.metri_residui===a.metri_totali,f=b?{type:"error",title:"Bobina OVER - Eliminazione bloccata",message:"Le bobine in stato OVER non possono essere eliminate per preservare la tracciabilit\xe0 del sistema. Contattare l'amministratore per la gestione di bobine OVER."}:g(a.stato_bobina)?a.metri_residui!==a.metri_totali?{type:"error",title:"Bobina in uso",message:`La bobina ha ${a.metri_residui}m residui su ${a.metri_totali}m totali. Solo le bobine completamente disponibili possono essere eliminate.`}:{type:"warning",title:"Conferma eliminazione",message:"Questa operazione \xe8 irreversibile. La bobina verr\xe0 rimossa definitivamente dal parco cavi."}:{type:"error",title:"Eliminazione non consentita",message:`La bobina \xe8 in stato "${a.stato_bobina}" e non pu\xf2 essere eliminata. ${y(a.stato_bobina)}`};return(0,t.jsx)(_.lG,{open:e,onOpenChange:x,children:(0,t.jsxs)(_.Cf,{className:"max-w-md",children:[(0,t.jsxs)(_.c7,{children:[(0,t.jsxs)(_.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-5 w-5 text-red-600"}),"Elimina Bobina"]}),(0,t.jsxs)(_.rr,{children:["Stai per eliminare la bobina ",a.numero_bobina]})]}),(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsxs)("div",{className:"bg-slate-50 p-4 rounded-lg mb-4",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Dettagli bobina:"}),(0,t.jsxs)("div",{className:"text-sm space-y-1",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Bobina:"})," ",a.numero_bobina]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Utility:"})," ",a.utility]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",a.tipologia]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Sezione:"})," ",a.sezione]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Stato:"})," ",a.stato_bobina]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri:"})," ",a.metri_residui,"m / ",a.metri_totali,"m"]}),a.ubicazione_bobina&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Ubicazione:"})," ",a.ubicazione_bobina]})]})]}),(0,t.jsxs)(d.Fc,{variant:"error"===f.type?"destructive":"default",children:[(0,t.jsx)(k.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:f.title}),(0,t.jsx)(d.TN,{className:"mt-1",children:f.message})]})]})]}),(0,t.jsxs)(_.Es,{children:[(0,t.jsx)(n.$,{variant:"outline",onClick:x,disabled:c,children:"Annulla"}),(0,t.jsxs)(n.$,{variant:"destructive",onClick:m,disabled:c||!v,children:[c&&(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin"}),c?"Eliminando...":"Elimina Bobina"]})]})]})})}var M=a(56770),B=a(23361),$=a(99270);let L=(e,i)=>{let[a,t]=(0,s.useState)(e);return(0,s.useEffect)(()=>{let a=setTimeout(()=>{t(e)},i);return()=>{clearTimeout(a)}},[e,i]),a};function P({open:e,onClose:i,bobina:a,cantiereId:r,onSuccess:o,onError:c}){let[d,u]=(0,s.useState)(!1),[m,x]=(0,s.useState)(!1),[h,b]=(0,s.useState)(!1),[g,v]=(0,s.useState)([]),[f,j]=(0,s.useState)([]),[N,y]=(0,s.useState)([]),[w,z]=(0,s.useState)({}),[C,A]=(0,s.useState)({}),[S,E]=(0,s.useState)(""),T=L(S,300),[I,F]=(0,s.useState)("id_cavo"),[R,k]=(0,s.useState)("asc"),[O,P]=(0,s.useState)("all"),[U,V]=(0,s.useState)("all"),[q,G]=(0,s.useState)(""),[J,H]=(0,s.useState)(""),[K,W]=(0,s.useState)(1),[Z,Y]=(0,s.useState)(20),X=(0,s.useMemo)(()=>{let e=e=>e.filter(e=>{let i=!T||e.id_cavo.toLowerCase().includes(T.toLowerCase())||e.tipologia?.toLowerCase().includes(T.toLowerCase())||e.ubicazione_partenza?.toLowerCase().includes(T.toLowerCase())||e.ubicazione_arrivo?.toLowerCase().includes(T.toLowerCase()),a="all"===O||e.tipologia===O,t="all"===U||e.sezione===U,s=parseFloat(e.metri_teorici?.toString()||"0"),r=!q||s>=parseFloat(q),n=!J||s<=parseFloat(J);return i&&a&&t&&r&&n}),i=e=>[...e].sort((e,i)=>{let a,t;switch(I){case"id_cavo":a=e.id_cavo,t=i.id_cavo;break;case"metri_teorici":a=parseFloat(e.metri_teorici?.toString()||"0"),t=parseFloat(i.metri_teorici?.toString()||"0");break;case"tipologia":a=e.tipologia||"",t=i.tipologia||"";break;case"ubicazione_partenza":a=e.ubicazione_partenza||"",t=i.ubicazione_partenza||"";break;default:return 0}if("string"!=typeof a)return"asc"===R?a-t:t-a;{let e=a.localeCompare(t);return"asc"===R?e:-e}});return{compatibiliFiltrati:i(e(g)),incompatibiliFiltrati:i(e(f))}},[g,f,T,I,R,O,U,q,J]),Q=(0,s.useMemo)(()=>{let{compatibiliFiltrati:e,incompatibiliFiltrati:i}=X,a=(K-1)*Z,t=a+Z;return{compatibili:e.slice(a,t),incompatibili:i.slice(a,t),totalCompatibili:e.length,totalIncompatibili:i.length,totalPages:Math.ceil(Math.max(e.length,i.length)/Z)}},[X,K,Z]),ee=(0,s.useMemo)(()=>{let e=Object.values(w).reduce((e,i)=>e+parseFloat(i||"0"),0),i=a?.metri_residui||0,t=Object.entries(w).some(([e,a])=>parseFloat(a||"0")>i),s=N.some(e=>e._isIncompatible),r=t||a?.stato_bobina==="OVER",n=Math.max(0,e-i);return{metriTotaliSelezionati:e,metriResiduiBobina:i,isOverState:r,metriEccedenza:n,percentualeUtilizzo:i>0?e/i*100:0,hasSingleCavoOver:t,hasIncompatibleCavi:s}},[w,a,N]);(0,s.useMemo)(()=>Array.from(new Set([...g,...f].map(e=>e.tipologia).filter(Boolean))).sort(),[g,f]),(0,s.useMemo)(()=>Array.from(new Set([...g,...f].map(e=>e.sezione).filter(Boolean))).sort(),[g,f]);let ei=(e,i)=>{if(console.log("\uD83C\uDFAF AggiungiCaviDialogSimple: Toggle cavo:",{cavoId:e.id_cavo,isCompatible:i,metriTeorici:e.metri_teorici}),N.some(i=>i.id_cavo===e.id_cavo))y(i=>i.filter(i=>i.id_cavo!==e.id_cavo)),z(i=>{let a={...i};return delete a[e.id_cavo],a});else{let a={...e,_isIncompatible:!i};y(e=>[...e,a]),z(i=>({...i,[e.id_cavo]:"0"}))}},ea=(e,i)=>{a?.metri_residui,parseFloat(i||"0");let t=N.find(i=>i.id_cavo===e);t?._isIncompatible,A(i=>{let a={...i};return delete a[e],a}),z(a=>({...a,[e]:i}))},et=(0,s.useMemo)(()=>{let e=a?.metri_residui||0,i=!1,t=[],s=[],r=null;for(let a of N){let n=parseFloat(w[a.id_cavo]||"0");n>0?i?s.push(a.id_cavo):(e-n<0?(t.push(a.id_cavo),r=a.id_cavo,i=!0):t.push(a.id_cavo),e-=n):t.push(a.id_cavo)}return{metriResiduiSimulati:e,caviValidi:t,caviBloccati:s,bobinaGiaOver:i,cavoCheCausaOver:r}},[N,w,a?.metri_residui]),es=()=>et,er=async()=>{if(!r||!a)return;if(0===N.length)return void c("Nessun cavo selezionato");let e=N.filter(e=>{let i=w[e.id_cavo];return!i||""===i.trim()||isNaN(parseFloat(i))||0>parseFloat(i)});if(e.length>0)return void c(`Metri posati mancanti o non validi per: ${e.map(e=>e.id_cavo).join(", ")}`);try{b(!0);let{caviValidi:e,caviBloccati:t}=es(),s=N.filter(e=>{let i=parseFloat(w[e.id_cavo]||"0"),a=t.includes(e.id_cavo);return i>0&&!a});if(0===s.length)return void c("Nessun cavo valido da salvare (tutti bloccati o senza metri)");console.log("\uD83D\uDCBE AggiungiCaviDialogSimple: Preparazione salvataggio:",{caviSelezionati:N.length,caviValidi:e.length,caviBloccati:t.length,caviDaSalvare:s.length});let n=[],l=[],d=a?.metri_residui||0;for(let e of s)try{let i=w[e.id_cavo],t=parseFloat(i),s=d-t<0,o=e._isIncompatible||!1,l=s||o;console.log("⚡ AggiungiCaviDialogSimple: Aggiornamento cavo:",{metriPosati:t,metriResiduiCorrente:d,causaOver:s,isIncompatible:o,needsForceOver:l}),await p.At.updateMetriPosati(r,e.id_cavo,t,a.id_bobina,l),d-=t,n.push({cavo:e.id_cavo,metriPosati:t,success:!0,wasIncompatible:e._isIncompatible,wasForceOver:l})}catch(a){let i="Errore sconosciuto";if(a.response){let e=a.response.status,t=a.response.data;i=400===e?t?.message||t?.error||"Richiesta non valida (400)":404===e?"Cavo o bobina non trovati (404)":409===e?"Conflitto: cavo gi\xe0 assegnato o bobina non disponibile (409)":500===e?"Errore interno del server (500)":`Errore HTTP ${e}: ${t?.message||t?.error||"Errore del server"}`}else i=a.request?"Errore di connessione al server":a.message||"Errore di validazione";l.push({cavo:e.id_cavo,error:i})}if(0===l.length){let e=n.filter(e=>e.wasIncompatible).length,a=n.filter(e=>e.wasForceOver).length,t=`${n.length} cavi aggiornati con successo`;e>0&&(t+=` (${e} incompatibili)`),a>0&&(t+=` (${a} con force_over)`),o(t),i()}else c(`Errori: ${l.map(e=>`${e.cavo}: ${e.error}`).join(", ")}`)}catch(i){let e="Errore durante il salvataggio dei cavi";if(i.response){let a=i.response.status,t=i.response.data;e=400===a?`Errore di validazione: ${t?.message||t?.error||"Dati non validi"}`:401===a?"Sessione scaduta. Effettua nuovamente il login.":403===a?"Non hai i permessi per questa operazione":500===a?"Errore interno del server. Riprova pi\xf9 tardi.":`Errore del server (${a}): ${t?.message||t?.error||"Errore sconosciuto"}`}else e=i.request?"Impossibile contattare il server. Verifica la connessione.":i.message||"Errore imprevisto durante il salvataggio";c(e)}finally{b(!1)}},en=()=>{h||(y([]),z({}),A({}),i())};if(!a)return null;let eo=e=>{let i=e.match(/C\d+_B(\d+)/);return i?i[1]:e},el=(e,i)=>(console.log("\uD83D\uDCCB AggiungiCaviDialogSimple: Rendering lista cavi:",{isCompatible:i,caviLength:e.length,primi3Cavi:e.slice(0,3).map(e=>({id:e.id_cavo,tipologia:e.tipologia,sezione:e.sezione}))}),0===e.length)?(0,t.jsx)("div",{className:"h-[300px] flex items-center justify-center text-gray-500 border rounded",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(B.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,t.jsxs)("div",{children:["Nessun cavo ",i?"compatibile":"incompatibile"," disponibile"]}),(0,t.jsx)("div",{className:"text-xs mt-2 text-gray-400",children:i?`Cerca cavi con tipologia "${a?.tipologia}" e formazione "${a?.sezione}"`:"I cavi incompatibili hanno tipologia o formazione diverse"})]})}):(0,t.jsx)("div",{className:"space-y-1 h-[300px] overflow-y-auto border rounded p-2 w-full",children:e.map((e,a)=>{let s=N.some(i=>i.id_cavo===e.id_cavo),r=w[e.id_cavo]||"",{caviBloccati:n,cavoCheCausaOver:o}=et,l=s&&n.includes(e.id_cavo),c=s&&e.id_cavo===o;return(0,t.jsxs)("div",{className:`border rounded px-3 py-2 transition-colors ${l?"border-red-300 bg-red-50":c?"border-orange-300 bg-orange-50":s?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"}`,children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 w-full overflow-hidden",children:[(0,t.jsx)("input",{type:"checkbox",checked:s,onChange:a=>{ei(e,i)},className:"h-4 w-4 text-blue-600 border-gray-300 rounded flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0 overflow-hidden",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900 flex-shrink-0",children:e.id_cavo}),(0,t.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.tipologia}),(0,t.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.sezione}),(0,t.jsxs)("span",{className:"text-xs text-gray-600 flex-shrink-0",children:[e.metri_teorici,"m"]}),l&&(0,t.jsx)("span",{className:"text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"BLOCCATO"}),c&&(0,t.jsx)("span",{className:"text-xs bg-orange-100 text-orange-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"CAUSA OVER"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500 truncate min-w-0",children:[e.ubicazione_partenza||"N/A"," → ",e.ubicazione_arrivo||"N/A"]})]}),s&&(0,t.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,t.jsx)("label",{className:"text-xs text-gray-600",children:"m:"}),(0,t.jsx)("input",{type:"number",step:"0.1",min:"0",value:r,onChange:i=>{ea(e.id_cavo,i.target.value)},className:"w-16 px-1 py-1 border rounded text-xs",placeholder:"0"})]})]}),C[e.id_cavo]&&(0,t.jsx)("div",{className:"text-red-600 text-xs mt-1 ml-7",children:C[e.id_cavo]})]},e.id_cavo)})});return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(_.lG,{open:e,onOpenChange:en,children:(0,t.jsxs)(_.Cf,{className:"h-[85vh] w-full max-w-5xl overflow-hidden",style:{width:"950px !important",maxWidth:"95vw !important",minWidth:"850px"},children:[(0,t.jsxs)(_.c7,{className:"pb-0",children:[(0,t.jsxs)(_.L3,{className:"flex items-center gap-2 mb-0 text-lg",children:[(0,t.jsx)(B.A,{className:"h-5 w-5"}),"\uD83D\uDD25 NUOVO SISTEMA OVER - Aggiungi cavi alla bobina ",eo(a.id_bobina)]}),(0,t.jsx)(_.rr,{className:"mb-0 text-xs text-gray-600 mt-0",children:"Seleziona cavi e inserisci metri posati (SISTEMA AGGIORNATO)"})]}),(0,t.jsxs)("div",{className:"space-y-1 mt-2",children:[(0,t.jsx)("div",{className:"bg-gray-50 px-3 py-1 rounded text-sm",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("span",{children:[(0,t.jsxs)("strong",{children:["Bobina ",eo(a.id_bobina)]})," • ",a.tipologia," • ",a.sezione]}),(0,t.jsxs)("span",{children:["Residui: ",(0,t.jsxs)("strong",{children:[a.metri_residui,"m"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("span",{children:["Selezionati: ",(0,t.jsx)("strong",{children:N.length})," cavi • ",(0,t.jsxs)("strong",{children:[ee.metriTotaliSelezionati.toFixed(1),"m"]})]}),ee.isOverState&&(0,t.jsx)("span",{className:"text-red-600 font-medium",children:"OVER!"})]})]})}),(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)($.A,{className:"absolute left-2 top-2 h-4 w-4 text-gray-400"}),(0,t.jsx)(l.p,{placeholder:"Cerca cavi...",value:S,onChange:e=>E(e.target.value),className:"pl-8 h-8 text-sm"})]}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{y([]),z({}),A({})},disabled:0===N.length,className:"h-8 px-3 text-sm",children:"Reset"})]}),m&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(D.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento cavi..."})]}),!m&&(0,t.jsxs)(M.tU,{defaultValue:"compatibili",className:"w-full",children:[(0,t.jsxs)(M.j7,{className:"flex justify-center gap-6 bg-transparent border-0 h-auto p-0",children:[(0,t.jsxs)(M.Xi,{value:"compatibili",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-2 h-2 rounded-full bg-green-500"}),"Cavi Compatibili (",Q.totalCompatibili,")"]}),(0,t.jsxs)(M.Xi,{value:"incompatibili",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-2 h-2 rounded-full bg-orange-500"}),"Cavi Incompatibili (",Q.totalIncompatibili,")"]})]}),(0,t.jsx)(M.av,{value:"compatibili",className:"mt-4 w-full overflow-hidden",children:(0,t.jsx)("div",{className:"w-full overflow-hidden",children:el(Q.compatibili,!0)})}),(0,t.jsx)(M.av,{value:"incompatibili",className:"mt-4 w-full overflow-hidden",children:(0,t.jsx)("div",{className:"w-full overflow-hidden",children:el(Q.incompatibili,!1)})})]})]}),(0,t.jsxs)(_.Es,{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600",children:N.length>0?(()=>{let{metriResiduiSimulati:e,caviValidi:i,caviBloccati:s,cavoCheCausaOver:r}=et,n=N.reduce((e,i)=>e+parseFloat(w[i.id_cavo]||"0"),0),o=(a?.metri_residui||0)-e,l=N.filter(e=>e._isIncompatible).length;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{children:[N.length," cavi selezionati • ",n.toFixed(1),"m totali"]}),(0,t.jsxs)("div",{children:["✅ ",i.length," salvabili • ❌ ",s.length," bloccati"]}),(0,t.jsxs)("div",{children:["Usati: ",o.toFixed(1),"m / ",a?.metri_residui||0,"m",e<0&&r&&(0,t.jsxs)("span",{className:"text-orange-600 font-medium ml-2",children:["(OVER da ",r,": +",Math.abs(e).toFixed(1),"m)"]})]}),l>0&&(0,t.jsxs)("div",{className:"text-orange-600 font-medium",children:["⚠️ ",l," cavi incompatibili"]})]})})():(0,t.jsx)("div",{children:"Nessun cavo selezionato"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:en,disabled:h,children:"Annulla"}),(0,t.jsxs)(n.$,{onClick:er,disabled:h||0===N.length,className:ee.isOverState?"bg-orange-600 hover:bg-orange-700":"",children:[h&&(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin"}),h?"Salvataggio...":ee.isOverState?`Salva ${N.length} cavi (OVER)`:`Salva ${N.length} cavi`]})]})]})]})})})}var U=a(53411),V=a(5336),q=a(48730);function G({bobine:e,filteredBobine:i,className:a}){let n=(0,s.useMemo)(()=>{let a=e.length,t=i.length,s=i.filter(e=>"Disponibile"===e.stato_bobina).length,r=i.filter(e=>"In uso"===e.stato_bobina).length,n=i.filter(e=>"Terminata"===e.stato_bobina).length,o=i.filter(e=>"Over"===e.stato_bobina).length,l=i.reduce((e,i)=>e+(i.metri_totali||0),0),c=i.reduce((e,i)=>e+(i.metri_residui||0),0),d=l-c,u=l>0?Math.round(d/l*100):0;return{totalBobine:a,filteredCount:t,disponibili:s,inUso:r,terminate:n,over:o,metriTotali:l,metriResidui:c,metriUtilizzati:d,percentualeUtilizzo:u}},[e,i]);return(0,t.jsx)(r.Zp,{className:a,children:(0,t.jsxs)(r.Wu,{className:"p-1.5",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-1",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(U.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Bobine"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(T.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:n.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",n.totalBobine," bobine"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(V.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:n.disponibili}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"disponibili"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(q.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:n.inUso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in uso"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(k.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-red-700 text-sm",children:n.terminate}),(0,t.jsx)("div",{className:"text-xs text-red-600",children:"terminate"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(C.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-red-700 text-sm",children:n.over}),(0,t.jsx)("div",{className:"text-xs text-red-600",children:"over"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[n.metriUtilizzati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",n.metriTotali.toLocaleString(),"m"]})]})]})]}),n.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"Utilizzo Complessivo Bobine"}),(0,t.jsxs)("span",{className:`font-bold ${n.percentualeUtilizzo>=80?"text-amber-700":n.percentualeUtilizzo>=60?"text-orange-700":n.percentualeUtilizzo>=40?"text-yellow-700":"text-emerald-700"}`,children:[n.percentualeUtilizzo,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ease-in-out ${n.percentualeUtilizzo>=80?"bg-gradient-to-r from-amber-500 to-amber-600":n.percentualeUtilizzo>=60?"bg-gradient-to-r from-orange-500 to-orange-600":n.percentualeUtilizzo>=40?"bg-gradient-to-r from-yellow-500 to-yellow-600":"bg-gradient-to-r from-emerald-500 to-emerald-600"}`,style:{width:`${Math.min(n.percentualeUtilizzo,100)}%`}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Metri utilizzati vs totali disponibili"}),(0,t.jsxs)("span",{children:[n.metriResidui.toLocaleString(),"m residui"]})]})]})]})})}var J=a(62688);let H=(0,J.A)("file-up",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 12v6",key:"3ahymv"}],["path",{d:"m15 15-3-3-3 3",key:"15xj92"}]]),K=(0,J.A)("file-down",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M12 18v-6",key:"17g6i2"}],["path",{d:"m9 15 3 3 3-3",key:"1npd3o"}]]);var W=a(63143),Z=a(51215);function Y({children:e,items:i,onAction:a,disabled:r=!1}){let[n,o]=(0,s.useState)(!1),[l,c]=(0,s.useState)({x:0,y:0}),[d,u]=(0,s.useState)(null),m=(0,s.useRef)(null),x=(0,s.useRef)(null),p=e=>{e.disabled||(o(!1),a(e.action,d))},h=e=>{switch(e){case"warning":return"text-amber-600 hover:bg-amber-50";case"danger":return"text-red-600 hover:bg-red-50";default:return"text-gray-700 hover:bg-gray-100"}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{ref:x,onContextMenu:e=>{if(r)return;e.preventDefault(),e.stopPropagation(),x.current?.getBoundingClientRect();let a=e.clientX,t=e.clientY,s=40*i.length,n=window.innerWidth;c({x:a+200>n?a-200:a,y:t+s>window.innerHeight?t-s:t}),u(e.currentTarget.dataset),o(!0)},className:"w-full h-full",children:e}),n?(0,Z.createPortal)((0,t.jsx)("div",{ref:m,className:"fixed z-50 min-w-[200px] bg-white border border-gray-200 rounded-md shadow-lg py-1",style:{left:l.x,top:l.y},children:i.map((e,i)=>e.separator?(0,t.jsx)("div",{className:"border-t border-gray-200 my-1"},`separator-${i}`):(0,t.jsxs)("button",{className:`w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ${e.disabled?"text-gray-400 cursor-not-allowed":h(e.color)}`,onClick:()=>p(e),disabled:e.disabled,children:[e.icon&&(0,t.jsx)("span",{className:"w-4 h-4",children:e.icon}),(0,t.jsx)("span",{children:e.label})]},e.id))}),document.body):null]})}var X=a(15391);function Q(){let[e,i]=(0,s.useState)(""),[a,g]=(0,s.useState)("all"),[_,w]=(0,s.useState)([]),[A,E]=(0,s.useState)(!0),[T,I]=(0,s.useState)(""),{user:k,isLoading:M}=(0,u.A)(),{cantiereId:L,cantiere:U,isValidCantiere:J,isLoading:Z,error:Q}=(0,m.jV)(),[ee,ei]=(0,s.useState)(!1),[ea,et]=(0,s.useState)(!1),[es,er]=(0,s.useState)(!1),[en,eo]=(0,s.useState)(!1),[el,ec]=(0,s.useState)(null),[ed,eu]=(0,s.useState)(""),[em,ex]=(0,s.useState)(""),ep=async()=>{try{if(E(!0),I(""),!L||L<=0){I("Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine."),w([]);return}let e=await p.Fw.getBobine(L);w(e||[])}catch(e){I(e.response?.data?.detail||"Errore durante il caricamento delle bobine"),w([])}finally{E(!1)}},eh=e=>{ec(e),eo(!0)},eb=e=>{ec(e),et(!0)},eg=e=>{ec(e),er(!0)},ev=e=>{eu(e),ep()},ef=e=>{ex(e)},ej=()=>{eu("Funzione import in sviluppo")},eN=()=>{eu("Funzione export in sviluppo")},ey=(e,i,a)=>{let s=e||b(i,a),r=(0,X.t2)(s),n={disponibile:V.A,in_uso:q.A,terminata:C.A,over:C.A}[s?.toLowerCase()]||C.A;return(0,t.jsxs)(o.E,{className:`flex items-center gap-1 font-medium ${r.badge}`,title:y(s),children:[(0,t.jsx)(n,{className:`h-3 w-3 ${r.text}`}),s.toUpperCase()]})},e_=_.filter(i=>{let t=i.numero_bobina?.toLowerCase().includes(e.toLowerCase())||i.tipologia?.toLowerCase().includes(e.toLowerCase())||i.utility?.toLowerCase().includes(e.toLowerCase()),s=!0;if("all"!==a){let e=i.stato_bobina||b(i.metri_residui,i.metri_totali);switch(a){case"disponibile":s=e===h.DISPONIBILE;break;case"in_uso":s=e===h.IN_USO;break;case"esaurita":s=e===h.TERMINATA;break;case"over":s=e===h.OVER}}return t&&s});return(0,t.jsx)(x.u,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[T&&(0,t.jsxs)(d.Fc,{variant:"destructive",className:"mb-6",children:[(0,t.jsx)(C.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:T})]}),(0,t.jsx)(G,{bobine:_,filteredBobine:e_,className:"mb-6"}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)($.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(l.p,{placeholder:"Cerca per bobina, tipologia o utility...",value:e,onChange:e=>i(e.target.value),className:"w-full"})}),(0,t.jsx)("div",{className:"flex gap-2",children:["all","disponibile","in_uso","esaurita","over"].map(e=>(0,t.jsx)(n.$,{variant:a===e?"default":"outline",size:"sm",onClick:()=>g(e),children:"all"===e?"Tutte":"disponibile"===e?"Disponibili":"in_uso"===e?"In Uso":"esaurita"===e?"Esaurite":"Over"},e))})]})})]}),(0,t.jsx)(Y,{items:[{id:"import",label:"Importa Bobine",icon:(0,t.jsx)(H,{className:"h-4 w-4"}),action:"import",disabled:!L||L<=0},{id:"export",label:"Esporta Bobine",icon:(0,t.jsx)(K,{className:"h-4 w-4"}),action:"export",disabled:!L||L<=0},{id:"separator1",separator:!0},{id:"add_bobina",label:"Aggiungi Bobina",icon:(0,t.jsx)(z.A,{className:"h-4 w-4"}),action:"add_bobina",disabled:!L||L<=0}],onAction:(e,i)=>{switch(e){case"import":ej();break;case"export":eN();break;case"add_bobina":ei(!0)}},children:(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(r.ZB,{children:["Elenco Bobine (",e_.length,")"]}),(0,t.jsx)(r.BT,{children:"Gestione completa delle bobine con stato utilizzo e metrature. Clicca tasto destro per opzioni aggiuntive."})]}),(0,t.jsxs)(n.$,{size:"sm",onClick:()=>ei(!0),disabled:!L||L<=0,title:!L||L<=0?"Seleziona un cantiere per creare una bobina":"Crea nuova bobina",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-2"}),"Nuova Bobina"]})]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(c.XI,{children:[(0,t.jsx)(c.A0,{children:(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nd,{children:"Bobina"}),(0,t.jsx)(c.nd,{children:"Utility"}),(0,t.jsx)(c.nd,{children:"Tipologia"}),(0,t.jsx)(c.nd,{children:"Formazione"}),(0,t.jsx)(c.nd,{children:"Metrature"}),(0,t.jsx)(c.nd,{children:"Utilizzo"}),(0,t.jsx)(c.nd,{children:"Stato"}),(0,t.jsx)(c.nd,{children:"Ubicazione"}),(0,t.jsx)(c.nd,{children:"Azioni"})]})}),(0,t.jsx)(c.BF,{children:A?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 animate-spin"}),"Caricamento bobine..."]})})}):T?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)(C.A,{className:"h-4 w-4"}),T]})})}):0===e_.length?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna bobina trovata"})}):e_.map(e=>{let i=j(e.metri_residui,e.metri_totali),a=e.stato_bobina||b(e.metri_residui,e.metri_totali),s=v(a);return(0,t.jsxs)(c.Hj,{className:`transition-colors ${s}`,children:[(0,t.jsx)(c.nA,{className:"font-medium",children:e.numero_bobina||"-"}),(0,t.jsx)(c.nA,{children:e.utility||"-"}),(0,t.jsx)(c.nA,{children:e.tipologia||"-"}),(0,t.jsx)(c.nA,{children:(0,t.jsx)("div",{className:"text-sm font-medium",children:e.sezione||"-"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Residui: ",(0,t.jsx)("span",{className:"font-medium",children:N(e.metri_residui)})]}),(0,t.jsxs)("div",{className:"text-slate-500",children:["Totali: ",N(e.metri_totali)]})]})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"text-sm font-medium",children:[Math.round(i),"%"]})}),(0,t.jsx)(c.nA,{children:ey(e.stato_bobina,e.metri_residui,e.metri_totali)}),(0,t.jsx)(c.nA,{children:(0,t.jsx)(o.E,{variant:"outline",children:e.ubicazione_bobina||"Non specificata"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>eh(e),disabled:!f(a),title:a===h.OVER?"Bobina OVER - Non pu\xf2 accettare nuovi cavi":a===h.TERMINATA?"Bobina terminata - Non pu\xf2 accettare nuovi cavi":"Aggiungi cavo a bobina",className:f(a)?"":"opacity-50 cursor-not-allowed",children:(0,t.jsx)(B.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>eb(e),title:a===h.OVER?"Modifica bobina (limitata per bobine OVER)":"Modifica bobina",children:(0,t.jsx)(W.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>eg(e),disabled:a===h.OVER||a!==h.DISPONIBILE,title:a===h.OVER?"Bobina OVER - Non pu\xf2 essere eliminata":a!==h.DISPONIBILE?"Solo bobine disponibili possono essere eliminate":"Elimina bobina",className:a===h.OVER||a!==h.DISPONIBILE?"opacity-50 cursor-not-allowed":"",children:(0,t.jsx)(R.A,{className:"h-4 w-4"})})]})})]},e.id_bobina)})})]})})})]})}),ed&&(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,t.jsxs)(d.Fc,{className:"bg-green-50 border-green-200",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)(d.TN,{className:"text-green-800",children:ed})]})}),em&&(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(C.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:em})]})}),(0,t.jsx)(S,{open:ee,onClose:()=>ei(!1),cantiereId:L,onSuccess:ev,onError:ef}),(0,t.jsx)(F,{open:ea,onClose:()=>et(!1),bobina:el,cantiereId:L,onSuccess:e=>{eu(e),ep()},onError:e=>{ex(e)}}),(0,t.jsx)(O,{open:es,onClose:()=>er(!1),bobina:el,cantiereId:L,onSuccess:e=>{eu(e),ep()},onError:e=>{ex(e)}}),(0,t.jsx)(P,{open:en,onClose:()=>eo(!1),bobina:el,cantiereId:L,onSuccess:ev,onError:ef})]})})})}},71902:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\parco-cavi\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx","default")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},85902:(e,i,a)=>{"use strict";a.r(i),a.d(i,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var t=a(65239),s=a(48088),r=a(88170),n=a.n(r),o=a(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);a.d(i,l);let c={children:["",{children:["parco-cavi",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,71902)),"C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\CMS\\webapp-nextjs\\src\\app\\parco-cavi\\page.tsx"],u={require:a,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/parco-cavi/page",pathname:"/parco-cavi",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},88233:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96882:(e,i,a)=>{"use strict";a.d(i,{A:()=>t});let t=(0,a(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])}};var i=require("../../webpack-runtime.js");i.C(e);var a=e=>i(i.s=e),t=i.X(0,[447,991,658,462,400,818,639,109,653],()=>a(85902));module.exports=t})();