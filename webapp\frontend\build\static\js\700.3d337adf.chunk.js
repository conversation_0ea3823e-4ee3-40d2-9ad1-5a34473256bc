"use strict";(self.webpackChunkcms_frontend=self.webpackChunkcms_frontend||[]).push([[700,894],{1894:(e,t,n)=>{n.d(t,{apiService:()=>d});var i=n(8816),r=n(173),a=n(561),o=n(7079),l=n(9678),s=n(2043),c=n(184);const d={login:i.A.login,logout:i.A.logout,getCurrentUser:i.A.getCurrentUser,getCantieri:r.A.getMyCantieri,getCantiere:r.A.getCantiere,createCantiere:r.A.createCantiere,deleteCantiere:r.A.deleteCantiere,getCavi:a.A.getCavi,getCavo:a.A.getCavo,createCavo:a.A.createCavo,updateCavo:a.A.updateCavo,deleteCavo:a.A.deleteCavo,aggiornaCavo:a.A.aggiorna<PERSON>avo,getCertificazioni:o.A.getCertificazioni,getCertificazione:o.A.getCertificazione,createCertificazione:o.A.createCertificazione,updateCertificazione:o.A.updateCertificazione,deleteCertificazione:o.A.deleteCertificazione,getStrumenti:o.A.getStrumenti,createStrumento:o.A.createStrumento,updateStrumento:o.A.updateStrumento,deleteStrumento:o.A.deleteStrumento,getParcoCavi:l.A.getParcoCavi,createBobina:l.A.createBobina,updateBobina:l.A.updateBobina,deleteBobina:l.A.deleteBobina,generateTemplate:s.default.generateTemplate,importExcel:s.default.importExcel,getReports:c.A.getReports}},8700:(e,t,n)=>{n.r(t),n.d(t,{default:()=>y});var i=n(5043),r=n(3336),a=n(5865),o=n(9650),l=n(1806),s=n(4882),c=n(8076),d=n(39),A=n(3460),x=n(6446),u=n(3845),h=n(7392),m=n(35),j=n(6600),g=n(5316),C=n(9347),z=n(1906),v=n(4642),f=n(3560),p=n(3768),b=n(1894),S=n(579);const y=function(e){let{strumenti:t,onEdit:n,onDelete:y,cantiereId:_}=e;const[w,k]=(0,i.useState)(!1),[D,E]=(0,i.useState)(null),[B,M]=(0,i.useState)(!1),I=e=>e?new Date(e).toLocaleDateString("it-IT"):"-",N=e=>{if(!e)return!1;const t=new Date;return new Date(e)<t},T=e=>{if(!e)return!1;const t=new Date,n=new Date(e)-t,i=Math.ceil(n/864e5);return i<=30&&i>0};return 0===t.length?(0,S.jsxs)(r.A,{sx:{p:3,textAlign:"center"},children:[(0,S.jsx)(a.A,{variant:"h6",color:"text.secondary",children:"Nessuno strumento certificato trovato"}),(0,S.jsx)(a.A,{variant:"body2",color:"text.secondary",sx:{mt:1},children:'Clicca su "Nuovo Strumento" per aggiungere il primo strumento'})]}):(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(o.A,{component:r.A,children:(0,S.jsxs)(l.A,{children:[(0,S.jsx)(s.A,{children:(0,S.jsxs)(c.A,{children:[(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Nome"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Marca"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Modello"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"N\xb0 Serie"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Data Calibrazione"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Scadenza Calibrazione"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Stato"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Azioni"})})]})}),(0,S.jsx)(A.A,{children:t.map((e=>{const t=(i=e.data_scadenza_calibrazione,N(i)?{label:"Scaduto",color:"error"}:T(i)?{label:"In scadenza",color:"warning"}:{label:"Valido",color:"success"});var i;return(0,S.jsxs)(c.A,{hover:!0,children:[(0,S.jsx)(d.A,{children:(0,S.jsx)(a.A,{variant:"body2",fontWeight:"bold",children:e.nome})}),(0,S.jsx)(d.A,{children:e.marca}),(0,S.jsx)(d.A,{children:e.modello}),(0,S.jsx)(d.A,{children:(0,S.jsx)(a.A,{variant:"body2",fontFamily:"monospace",children:e.numero_serie})}),(0,S.jsx)(d.A,{children:I(e.data_calibrazione)}),(0,S.jsx)(d.A,{children:(0,S.jsxs)(x.A,{sx:{display:"flex",alignItems:"center",gap:1},children:[I(e.data_scadenza_calibrazione),N(e.data_scadenza_calibrazione)&&(0,S.jsx)(v.A,{color:"error",fontSize:"small"}),T(e.data_scadenza_calibrazione)&&(0,S.jsx)(v.A,{color:"warning",fontSize:"small"})]})}),(0,S.jsx)(d.A,{children:(0,S.jsx)(u.A,{label:t.label,color:t.color,size:"small"})}),(0,S.jsx)(d.A,{children:(0,S.jsxs)(x.A,{sx:{display:"flex",gap:.5},children:[(0,S.jsx)(h.A,{size:"small",onClick:()=>n(e),title:"Modifica",children:(0,S.jsx)(f.A,{fontSize:"small"})}),(0,S.jsx)(h.A,{size:"small",onClick:()=>(e=>{E(e),k(!0)})(e),title:"Elimina",color:"error",children:(0,S.jsx)(p.A,{fontSize:"small"})})]})})]},e.id_strumento)}))})]})}),(0,S.jsxs)(m.A,{open:w,onClose:()=>k(!1),children:[(0,S.jsx)(j.A,{children:"Conferma Eliminazione"}),(0,S.jsxs)(g.A,{children:[(0,S.jsxs)(a.A,{children:['Sei sicuro di voler eliminare lo strumento "',null===D||void 0===D?void 0:D.nome," ",null===D||void 0===D?void 0:D.marca," ",null===D||void 0===D?void 0:D.modello,'"?']}),(0,S.jsx)(a.A,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Questa operazione non pu\xf2 essere annullata. Lo strumento non potr\xe0 essere eliminato se \xe8 utilizzato in certificazioni esistenti."})]}),(0,S.jsxs)(C.A,{children:[(0,S.jsx)(z.A,{onClick:()=>k(!1),children:"Annulla"}),(0,S.jsx)(z.A,{onClick:async()=>{try{M(!0),await b.apiService.deleteStrumento(_,D.id_strumento),k(!1),E(null),y()}catch(e){console.error("Errore nell'eliminazione:",e)}finally{M(!1)}},color:"error",disabled:B,children:"Elimina"})]})]})]})}}}]);
//# sourceMappingURL=700.3d337adf.chunk.js.map