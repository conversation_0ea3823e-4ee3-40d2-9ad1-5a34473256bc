from sqlalchemy import Column, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Date, ForeignKey, DateTime
from sqlalchemy.orm import relationship
from datetime import datetime

from backend.database import Base

class User(Base):
    """
    Modello SQLAlchemy per la tabella utenti.
    Corrisponde alla tabella utenti nel database esistente.
    """
    __tablename__ = "utenti"

    id_utente = Column(Integer, primary_key=True, index=True)
    username = Column(String, unique=True, index=True, nullable=False)
    password = Column(String, nullable=False)
    password_plain = Column(String, nullable=True)  # Campo per memorizzare la password in chiaro
    ruolo = Column(String, nullable=False)
    data_scadenza = Column(Date, nullable=True)
    abilitato = Column(Boolean, default=True)
    created_by = Column(Integer, ForeignKey("utenti.id_utente"), nullable=True)

    # Nuovi campi per la gestione aziendale
    ragione_sociale = Column(String, nullable=True)
    indirizzo = Column(String, nullable=True)
    nazione = Column(String, nullable=True)
    email = Column(String, nullable=True)
    vat = Column(String, nullable=True)  # Partita IVA
    referente_aziendale = Column(String, nullable=True)

    # Campi per comunicazione
    telefono = Column(String, nullable=True)
    cellulare = Column(String, nullable=True)

    # Campi di sicurezza per reset password
    reset_token = Column(String(255), nullable=True)
    reset_token_expires = Column(DateTime, nullable=True)
    reset_token_used = Column(Boolean, default=False)

    # Campi per rate limiting e sicurezza
    failed_login_attempts = Column(Integer, default=0)
    locked_until = Column(DateTime, nullable=True)
    last_login = Column(DateTime, nullable=True)
    last_password_change = Column(DateTime, default=datetime.utcnow)

    # Campi per audit
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_activity = Column(DateTime, nullable=True)

    # Relazioni
    cantieri = relationship("Cantiere", back_populates="utente")
    created_users = relationship("User",
                               backref="creator",
                               remote_side=[id_utente])
