#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modulo per la gestione dei cantieri con PostgreSQL.
"""

import logging
import random
import string
import psycopg2
import bcrypt
from datetime import datetime
from typing import Tuple, Optional, Union, Any
from modules.database_pg import database_connection

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class CantieriManager:
    def __init__(self, db, current_user: Optional[str] = None):
        """
        Inizializza il CantieriManager.

        Args:
            db: Istanza del database
            current_user (str, optional): Username dell'utente corrente
        """
        self.db = db
        self.current_user = current_user

        if not self.current_user:
            logging.warning("CantieriManager inizializzato senza utente corrente")

    def _check_user_auth(self) -> bool:
        """Verifica se c'è un utente autenticato."""
        if not self.current_user:
            logging.error("❌ Operazione non autorizzata: nessun utente autenticato")
            return False
        return True

    def genera_codice_univoco_targa(self) -> str:
        """
        Genera un codice univoco tipo targa auto per un cantiere.

        Returns:
            str: Codice univoco nel formato C{2 lettere}{3 numeri}{2 lettere}.
        """
        while True:
            # Genera 2 lettere casuali (maiuscole)
            lettere_iniziali = ''.join(random.choices(string.ascii_uppercase, k=2))

            # Genera 3 numeri casuali
            numeri = ''.join(random.choices(string.digits, k=3))

            # Genera 2 lettere casuali (maiuscole)
            lettere_finali = ''.join(random.choices(string.ascii_uppercase, k=2))

            # Combina tutto nel formato tipo targa auto
            codice_univoco = f"C{lettere_iniziali}{numeri}{lettere_finali}"

            # Verifica se il codice esiste già nel database
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute('SELECT 1 FROM Cantieri WHERE codice_univoco = %s', (codice_univoco,))
                if not c.fetchone():
                    # Se il codice non esiste, restituiscilo
                    return codice_univoco

    def _get_cantiere_by_id(self, id_cantiere: int) -> Optional[Tuple[int, str, str]]:
        """
        Ottiene i dettagli di un cantiere dal database.

        Args:
            id_cantiere (int): ID del cantiere da cercare.

        Returns:
            Optional[Tuple[int, str, str]]: (id_cantiere, codice_univoco, nome_cantiere) se trovato, altrimenti None.
        """
        try:
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT c.id_cantiere, c.codice_univoco, c.commessa, u.username
                    FROM Cantieri c
                    JOIN Utenti u ON c.id_utente = u.id_utente
                    WHERE c.id_cantiere = %s
                ''', (id_cantiere,))
                result = c.fetchone()

                if result:
                    id_cantiere, codice_univoco, nome_cantiere, proprietario = result
                    if proprietario == self.current_user:
                        return id_cantiere, codice_univoco, nome_cantiere
                    else:
                        logging.warning(f"Utente {self.current_user} non ha accesso al cantiere {id_cantiere}")
                        return None
                else:
                    logging.warning(f"Cantiere {id_cantiere} non trovato")
                    return None
        except psycopg2.Error as e:
            logging.error(f"Errore nel database: {e}")
            return None


    def seleziona_cantiere(self) -> Tuple[Optional[int], Optional[str], Optional[str]]:
        """
        Gestisce l'interazione con l'utente per selezionare o creare un cantiere.
        """
        if not self._check_user_auth():
            return None, None, None

        print("\nI tuoi cantieri disponibili:")
        self.visualizza_cantieri()

        while True:
            scelta = input(
                "\nVuoi creare un nuovo cantiere (N), selezionarne uno esistente (E) o uscire (Q)? [n/e/q]: ").strip().lower()

            if scelta == 'q':
                print("\nOperazione annullata.")
                return None, None, None

            elif scelta == 'e':
                id_cantiere = input("\nInserisci l'ID del cantiere (o premi INVIO per annullare): ").strip()
                if not id_cantiere:
                    return None, None, None

                try:
                    id_cantiere = int(id_cantiere)
                except ValueError:
                    print("\n❌ L'ID deve essere un numero")
                    continue

                cantiere = self._get_cantiere_by_id(id_cantiere)
                if cantiere:
                    id_cantiere, codice_univoco, nome_cantiere = cantiere
                    print(f"\n✅ Selezionato cantiere: {nome_cantiere} (ID: {id_cantiere}, Codice: {codice_univoco})")
                    return id_cantiere, codice_univoco, nome_cantiere
                else:
                    print("\n❌ Cantiere non trovato o accesso negato")
                    continue

            elif scelta == 'n':
                # Creazione nuovo cantiere
                success, id_cantiere, nome_cantiere = self.crea_cantiere()
                if success and id_cantiere:
                    # Ottieni il codice univoco
                    with self.db.get_connection() as conn:
                        c = conn.cursor()
                        c.execute('SELECT codice_univoco FROM Cantieri WHERE id_cantiere = %s', (id_cantiere,))
                        result = c.fetchone()
                        if result:
                            codice_univoco = result[0]
                            return id_cantiere, codice_univoco, nome_cantiere
                return None, None, None

            else:
                print("\n❌ Scelta non valida. Inserisci n, e o q.")

    def crea_cantiere(self) -> Tuple[bool, Optional[int], Optional[str]]:
        """
        Crea un nuovo cantiere nel database.

        Returns:
            Tuple[bool, Optional[int], Optional[str]]: (successo, id_cantiere, nome_cantiere)
        """
        if not self._check_user_auth():
            return False, None, None

        try:
            print("\n=== CREAZIONE NUOVO CANTIERE ===")

            # Richiedi il nome del cantiere
            nome = input("Nome del cantiere: ").strip()
            if not nome:
                print("❌ Il nome del cantiere è obbligatorio!")
                return False, None, None

            # Richiedi la descrizione del cantiere (opzionale)
            descrizione = input("Descrizione (opzionale): ").strip()

            # Richiedi la password del cantiere
            password = input("Password del cantiere: ").strip()
            if not password:
                print("❌ La password del cantiere è obbligatoria!")
                return False, None, None

            # Genera un codice univoco per il cantiere
            codice_univoco = self.genera_codice_univoco_targa()

            # Hash della password
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

            # Ottieni l'ID dell'utente corrente
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute("SELECT id_utente FROM Utenti WHERE username = %s", (self.current_user,))
                result = c.fetchone()
                if not result:
                    print("❌ Utente non trovato!")
                    return False, None, None

                id_utente = result[0]

                # Inserisci il nuovo cantiere nel database
                c.execute('''
                    INSERT INTO Cantieri (nome, descrizione, data_creazione, password_cantiere, id_utente, codice_univoco)
                    VALUES (%s, %s, %s, %s, %s, %s)
                    RETURNING id_cantiere
                ''', (nome, descrizione, datetime.now(), hashed_password, id_utente, codice_univoco))

                id_cantiere = c.fetchone()[0]
                conn.commit()

                print(f"\n✅ Cantiere '{nome}' creato con successo!")
                print(f"📋 Codice univoco: {codice_univoco}")
                print(f"🔑 Password: {password}")
                print("\n⚠️ Conserva queste informazioni in un luogo sicuro!")

                return True, id_cantiere, nome

        except psycopg2.Error as e:
            logging.error(f"Errore nel database: {e}")
            print("❌ Si è verificato un errore durante la creazione del cantiere.")
            return False, None, None

    def visualizza_cantieri(self) -> bool:
        """
        Visualizza l'elenco dei cantieri dell'utente corrente.

        Returns:
            bool: True se l'operazione è riuscita, False altrimenti
        """
        if not self._check_user_auth():
            return False

        try:
            # Ottieni l'elenco dei cantieri dell'utente corrente
            with self.db.get_connection() as conn:
                c = conn.cursor()
                c.execute('''
                    SELECT c.id_cantiere, c.codice_univoco, c.commessa, c.descrizione, c.data_creazione
                    FROM Cantieri c
                    JOIN Utenti u ON c.id_utente = u.id_utente
                    WHERE u.username = %s
                    ORDER BY c.data_creazione DESC
                ''', (self.current_user,))
                cantieri = c.fetchall()

            if cantieri:
                print("\nI tuoi cantieri:")
                print(f"{'ID':<6} {'Codice':<15} {'Nome':<20} {'Descrizione':<30} {'Data Creazione':<20} {'Password':<15}")
                print("-" * 106)

                # Ottieni l'elenco dei cantieri con le informazioni di base
                for id_cantiere, codice_univoco, nome, descrizione, data_creazione in cantieri:
                    data_str = data_creazione.strftime("%d/%m/%Y %H:%M") if data_creazione else "N/D"
                    desc_trunc = (descrizione[:27] + '...') if descrizione and len(descrizione) > 30 else (descrizione or '')

                    # Per ogni cantiere, mostra la password come 'a' (per semplicità)
                    # Nella realtà, la password è la stessa dell'utente proprietario
                    password_display = "a"

                    print(f"{id_cantiere:<6} {codice_univoco:<15} {nome:<20} {desc_trunc:<30} {data_str:<20} {password_display:<15}")

                # Aggiungi una nota informativa sulle credenziali
                print("\nNota: Per accedere ai cantieri, usa il Codice come username e la password impostata durante la creazione.")
            else:
                print("Non hai ancora creato nessun cantiere.")

            return True

        except psycopg2.Error as e:
            logging.error(f"Errore nel database: {e}")
            print("❌ Si è verificato un errore durante il recupero dei cantieri.")
            return False

    def elimina_cantiere(self) -> bool:
        """
        Elimina un cantiere dal database.

        Returns:
            bool: True se l'operazione è riuscita, False altrimenti
        """
        if not self._check_user_auth():
            return False

        try:
            # Seleziona un cantiere
            id_cantiere, codice, nome = self.seleziona_cantiere()
            if not id_cantiere:
                return False

            # Chiedi conferma
            print(f"\n⚠️ Stai per eliminare il cantiere '{nome}' (ID: {id_cantiere}).")
            print("⚠️ Questa operazione eliminerà anche tutti i cavi e le certificazioni associate.")
            print("⚠️ Questa operazione è irreversibile!")

            conferma = input("\nSei sicuro di voler procedere? (s/n): ").strip().lower()
            if conferma != 's':
                print("Operazione annullata.")
                return False

            # Elimina il cantiere e tutti i dati associati
            with self.db.get_connection() as conn:
                c = conn.cursor()

                # Elimina le certificazioni dei cavi
                c.execute("DELETE FROM CertificazioniCavi WHERE id_cantiere = %s", (id_cantiere,))

                # Elimina i cavi
                c.execute("DELETE FROM Cavi WHERE id_cantiere = %s", (id_cantiere,))

                # Elimina il cantiere
                c.execute("DELETE FROM Cantieri WHERE id_cantiere = %s", (id_cantiere,))

                conn.commit()

                print(f"\n✅ Cantiere '{nome}' eliminato con successo!")
                return True

        except psycopg2.Error as e:
            logging.error(f"Errore nel database: {e}")
            print("❌ Si è verificato un errore durante l'eliminazione del cantiere.")
            return False
