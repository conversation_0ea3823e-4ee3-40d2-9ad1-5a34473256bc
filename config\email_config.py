"""
Configurazione per il sistema di notifiche email.
Modifica questi valori per configurare il tuo server SMTP.
"""

import os

# Configurazione SMTP
SMTP_CONFIG = {
    # Server SMTP (esempi comuni)
    'server': os.getenv('SMTP_SERVER', 'smtp.gmail.com'),  # Gmail
    # 'server': 'smtp.outlook.com',  # Outlook/Hotmail
    # 'server': 'smtp.libero.it',   # Libero
    # 'server': 'smtp.virgilio.it', # Virgilio
    
    'port': int(os.getenv('SMTP_PORT', '587')),  # Porta TLS standard
    
    # Credenziali (IMPORTANTE: usa variabili d'ambiente in produzione)
    'username': os.getenv('SMTP_USERNAME', ''),  # Il tuo indirizzo email
    'password': os.getenv('SMTP_PASSWORD', ''),  # Password o App Password
    
    # Indirizzo mittente (di solito uguale a username)
    'from_email': os.getenv('EMAIL_FROM', os.getenv('SMTP_USERNAME', '')),
    
    # Nome del mittente
    'from_name': os.getenv('EMAIL_FROM_NAME', 'Sistema CMS Cantieri'),
}

# Template email personalizzabili
EMAIL_TEMPLATES = {
    'comanda_creata': {
        'subject_template': 'Nuova Comanda {codice_comanda} - {tipo_comanda}',
        'body_template': '''
Gentile {responsabile_nome},

Le è stata assegnata una nuova comanda di lavoro:

🔧 DETTAGLI COMANDA:
• Codice Comanda: {codice_comanda}
• Tipo Lavoro: {tipo_comanda}
• Cantiere: {cantiere_nome}
• Descrizione: {descrizione}

📱 ISTRUZIONI:
1. Utilizzare il codice comanda {codice_comanda} nell'app mobile
2. Il codice è necessario per accedere ai dettagli del lavoro
3. Aggiornare lo stato dei lavori tramite l'app

⚠️ IMPORTANTE:
Conservare questo codice per accedere alla comanda dall'app mobile.

---
{from_name}
Data invio: {data_invio}
'''
    },
    
    'test_email': {
        'subject_template': 'Test Sistema Notifiche CMS',
        'body_template': '''
Questo è un messaggio di test dal sistema CMS.

Se ricevi questo messaggio, la configurazione email è corretta.

Data test: {data_invio}

---
{from_name}
'''
    }
}

# Istruzioni per la configurazione
SETUP_INSTRUCTIONS = """
CONFIGURAZIONE EMAIL - ISTRUZIONI:

1. GMAIL:
   - Server: smtp.gmail.com
   - Porta: 587
   - Abilita "Verifica in due passaggi" nel tuo account Google
   - Genera una "Password per le app" da usare al posto della password normale
   - Usa la password per le app nel campo SMTP_PASSWORD

2. OUTLOOK/HOTMAIL:
   - Server: smtp.outlook.com
   - Porta: 587
   - Usa le tue credenziali normali

3. ALTRI PROVIDER:
   - Consulta la documentazione del tuo provider email
   - Modifica server e porta di conseguenza

4. VARIABILI D'AMBIENTE (RACCOMANDATO):
   Crea un file .env nella root del progetto con:
   
   SMTP_SERVER=smtp.gmail.com
   SMTP_PORT=587
   SMTP_USERNAME=<EMAIL>
   SMTP_PASSWORD=tua-password-app
   EMAIL_FROM=<EMAIL>
   EMAIL_FROM_NAME=Sistema CMS Cantieri

5. TEST:
   Usa la funzione test_configurazione_email() per verificare la configurazione
"""

def get_smtp_config():
    """Restituisce la configurazione SMTP corrente."""
    return SMTP_CONFIG.copy()

def get_email_template(template_name):
    """Restituisce un template email specifico."""
    return EMAIL_TEMPLATES.get(template_name, EMAIL_TEMPLATES['comanda_creata'])

def is_configured():
    """Verifica se la configurazione email è completa."""
    return bool(SMTP_CONFIG['username'] and SMTP_CONFIG['password'])

def print_setup_instructions():
    """Stampa le istruzioni per la configurazione."""
    print(SETUP_INSTRUCTIONS)
