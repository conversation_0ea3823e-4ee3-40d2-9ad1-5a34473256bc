import sys
import os
import logging

# Aggiungi la directory principale al path per importare i moduli
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.database import get_db
from sqlalchemy import text

def update_passwords():
    """
    Aggiorna le password in chiaro per gli utenti esistenti.
    Imposta password predefinite per gli utenti esistenti.
    """
    try:
        # Ottieni una connessione al database
        db = next(get_db())

        # Imposta password predefinite per gli utenti esistenti
        # Admin: admin
        db.execute(text("UPDATE utenti SET password_plain = 'admin' WHERE ruolo = 'owner'"))

        # Utenti standard: password uguale allo username
        db.execute(text("UPDATE utenti SET password_plain = username WHERE ruolo = 'user' AND password_plain IS NULL"))

        # Utenti cantiere: password uguale a 'cantiere'
        db.execute(text("UPDATE utenti SET password_plain = 'cantiere' WHERE ruolo = 'cantieri_user' AND password_plain IS NULL"))

        # Commit delle modifiche
        db.commit()

        print("Password in chiaro aggiornate con successo per gli utenti esistenti.")
    except Exception as e:
        print(f"Errore durante l'aggiornamento delle password: {str(e)}")
        db.rollback()

if __name__ == "__main__":
    update_passwords()
