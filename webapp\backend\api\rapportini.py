from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from datetime import date, datetime

from ..core.security import get_current_active_user
from ..database import get_db
from ..models.user import User
from ..models.rapportino_lavoro import RapportinoLavoro
from ..models.comanda import Comanda
from ..models.cavo import Cavo
from ..schemas.comanda import (
    RapportinoLavoroRequest, RapportinoLavoroResponse, 
    DatiRevisioneRequest, RapportinoStampaRequest, RapportinoStampaResponse
)

router = APIRouter()


@router.post("/mobile/inserisci", response_model=RapportinoLavoroResponse, status_code=status.HTTP_201_CREATED)
async def inserisci_rapportino_mobile(
    rapportino: RapportinoLavoroRequest,
    db: Session = Depends(get_db)
):
    """
    Inserisce un nuovo rapportino di lavoro dalla mobile app.
    I dati vengono salvati in stato 'IN_ATTESA' per la revisione.
    """
    try:
        # Verifica che la comanda esista
        comanda = db.query(Comanda).filter(Comanda.codice_comanda == rapportino.codice_comanda).first()
        if not comanda:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Comanda {rapportino.codice_comanda} non trovata"
            )

        # Verifica che il cavo esista
        cavo = db.query(Cavo).filter(
            and_(Cavo.id_cavo == rapportino.id_cavo, Cavo.id_cantiere == comanda.id_cantiere)
        ).first()
        if not cavo:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Cavo {rapportino.id_cavo} non trovato nel cantiere"
            )

        # Crea il nuovo rapportino
        nuovo_rapportino = RapportinoLavoro(
            codice_comanda=rapportino.codice_comanda,
            id_cavo=rapportino.id_cavo,
            id_cantiere=comanda.id_cantiere,
            attivita_svolta=rapportino.attivita_svolta,
            bobina_utilizzata=rapportino.bobina_utilizzata,
            metri_posati=rapportino.metri_posati,
            ore_lavoro=rapportino.ore_lavoro,
            numero_componenti_squadra=rapportino.numero_componenti_squadra,
            note_lavoro=rapportino.note_lavoro,
            problemi_riscontrati=rapportino.problemi_riscontrati,
            data_lavoro=rapportino.data_lavoro or date.today(),
            responsabile=comanda.responsabile,
            stato_revisione="IN_ATTESA"
        )

        db.add(nuovo_rapportino)
        db.commit()
        db.refresh(nuovo_rapportino)

        return nuovo_rapportino

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'inserimento del rapportino: {str(e)}"
        )


@router.get("/in-attesa", response_model=List[RapportinoLavoroResponse])
async def ottieni_rapportini_in_attesa(
    id_cantiere: Optional[int] = Query(None, description="Filtra per cantiere"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene tutti i rapportini in attesa di revisione.
    """
    try:
        query = db.query(RapportinoLavoro).filter(RapportinoLavoro.stato_revisione == "IN_ATTESA")
        
        if id_cantiere:
            query = query.filter(RapportinoLavoro.id_cantiere == id_cantiere)
        
        rapportini = query.order_by(RapportinoLavoro.data_inserimento.desc()).all()
        return rapportini

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero dei rapportini: {str(e)}"
        )


@router.post("/revisiona", response_model=RapportinoLavoroResponse)
async def revisiona_rapportino(
    revisione: DatiRevisioneRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Revisiona un rapportino di lavoro (approva o rifiuta).
    Se approvato, applica i dati al sistema.
    """
    try:
        # Trova il rapportino
        rapportino = db.query(RapportinoLavoro).filter(
            RapportinoLavoro.id_rapportino == revisione.id_rapportino
        ).first()
        
        if not rapportino:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Rapportino non trovato"
            )

        if rapportino.stato_revisione != "IN_ATTESA":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Il rapportino è già stato revisionato"
            )

        # Aggiorna lo stato di revisione
        if revisione.azione == "APPROVA":
            rapportino.stato_revisione = "APPROVATO"
            
            # Applica eventuali correzioni
            if revisione.metri_posati_corretti is not None:
                rapportino.metri_posati_corretti = revisione.metri_posati_corretti
            if revisione.bobina_utilizzata_corretta:
                rapportino.bobina_utilizzata_corretta = revisione.bobina_utilizzata_corretta
            
            # TODO: Qui andrà la logica per applicare i dati al sistema
            # (aggiornamento stato cavi, metri installati, ecc.)
            rapportino.dati_applicati = True
            rapportino.data_applicazione = datetime.now()
            
        elif revisione.azione == "RIFIUTA":
            rapportino.stato_revisione = "RIFIUTATO"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Azione non valida. Usare 'APPROVA' o 'RIFIUTA'"
            )

        rapportino.data_revisione = datetime.now()
        rapportino.note_revisione = revisione.note_revisione
        rapportino.id_utente_revisore = current_user.id_utente

        db.commit()
        db.refresh(rapportino)

        return rapportino

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella revisione del rapportino: {str(e)}"
        )


@router.get("/comanda/{codice_comanda}", response_model=List[RapportinoLavoroResponse])
async def ottieni_rapportini_comanda(
    codice_comanda: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene tutti i rapportini per una specifica comanda.
    """
    try:
        rapportini = db.query(RapportinoLavoro).filter(
            RapportinoLavoro.codice_comanda == codice_comanda
        ).order_by(RapportinoLavoro.data_lavoro.desc()).all()
        
        return rapportini

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero dei rapportini: {str(e)}"
        )


@router.post("/genera-stampa", response_model=RapportinoStampaResponse)
async def genera_rapportino_stampa(
    richiesta: RapportinoStampaRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Genera un rapportino stampabile in formato PDF per uso senza mobile app.
    """
    try:
        # Verifica che la comanda esista
        comanda = db.query(Comanda).filter(Comanda.codice_comanda == richiesta.codice_comanda).first()
        if not comanda:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Comanda {richiesta.codice_comanda} non trovata"
            )

        # Importa il generatore di rapportini
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
        from modules.rapportino_stampa import genera_rapportino_stampa as genera_pdf

        # Genera il PDF
        percorso_file = genera_pdf(
            codice_comanda=richiesta.codice_comanda,
            formato_pagina=richiesta.formato_pagina,
            includi_dettagli_cavi=richiesta.includi_dettagli_cavi,
            includi_note_lavoro=richiesta.includi_note_lavoro
        )

        # Estrai il nome del file dal percorso
        nome_file = os.path.basename(percorso_file)
        url_pdf = f"/static/rapportini/{nome_file}"

        return RapportinoStampaResponse(
            url_pdf=url_pdf,
            nome_file=nome_file,
            data_generazione=date.today()
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella generazione del rapportino: {str(e)}"
        )


@router.get("/statistiche/{id_cantiere}")
async def ottieni_statistiche_rapportini(
    id_cantiere: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene statistiche sui rapportini per un cantiere.
    """
    try:
        stats = db.query(
            func.count(RapportinoLavoro.id_rapportino).label('totale'),
            func.sum(func.case([(RapportinoLavoro.stato_revisione == 'IN_ATTESA', 1)], else_=0)).label('in_attesa'),
            func.sum(func.case([(RapportinoLavoro.stato_revisione == 'APPROVATO', 1)], else_=0)).label('approvati'),
            func.sum(func.case([(RapportinoLavoro.stato_revisione == 'RIFIUTATO', 1)], else_=0)).label('rifiutati')
        ).filter(RapportinoLavoro.id_cantiere == id_cantiere).first()

        return {
            "totale_rapportini": stats.totale or 0,
            "in_attesa_revisione": stats.in_attesa or 0,
            "approvati": stats.approvati or 0,
            "rifiutati": stats.rifiutati or 0
        }

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel calcolo delle statistiche: {str(e)}"
        )
