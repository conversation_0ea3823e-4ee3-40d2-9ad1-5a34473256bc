"use strict";(self.webpackChunkcms_frontend=self.webpackChunkcms_frontend||[]).push([[108],{7108:(e,t,o)=>{o.d(t,{A:()=>ie});var n=o(8587),r=o(8168),a=o(5043),l=o(8387),i=o(8610),s=o(7266),p=o(5844),u=o(1052);const c=e=>{const t=a.useRef({});return a.useEffect((()=>{t.current=e})),t.current};var d=o(1782),g=o(6564);function f(e){return"undefined"!==typeof e.normalize?e.normalize("NFD").replace(/[\u0300-\u036f]/g,""):e}function h(e,t){for(let o=0;o<e.length;o+=1)if(t(e[o]))return o;return-1}const m=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{ignoreAccents:t=!0,ignoreCase:o=!0,limit:n,matchFrom:r="any",stringify:a,trim:l=!1}=e;return(e,i)=>{let{inputValue:s,getOptionLabel:p}=i,u=l?s.trim():s;o&&(u=u.toLowerCase()),t&&(u=f(u));const c=u?e.filter((e=>{let n=(a||p)(e);return o&&(n=n.toLowerCase()),t&&(n=f(n)),"start"===r?0===n.indexOf(u):n.indexOf(u)>-1})):e;return"number"===typeof n?c.slice(0,n):c}}(),b=e=>{var t;return null!==e.current&&(null==(t=e.current.parentElement)?void 0:t.contains(document.activeElement))},v=[];const x=function(e){const{unstable_isActiveElementInListbox:t=b,unstable_classNamePrefix:o="Mui",autoComplete:n=!1,autoHighlight:l=!1,autoSelect:i=!1,blurOnSelect:s=!1,clearOnBlur:f=!e.freeSolo,clearOnEscape:x=!1,componentName:A="useAutocomplete",defaultValue:y=(e.multiple?v:null),disableClearable:O=!1,disableCloseOnSelect:$=!1,disabled:I,disabledItemsFocusable:S=!1,disableListWrap:C=!1,filterOptions:k=m,filterSelectedOptions:P=!1,freeSolo:L=!1,getOptionDisabled:w,getOptionKey:R,getOptionLabel:T=e=>{var t;return null!=(t=e.label)?t:e},groupBy:N,handleHomeEndKeys:M=!e.freeSolo,id:D,includeInputInList:E=!1,inputValue:z,isOptionEqualToValue:F=(e,t)=>e===t,multiple:H=!1,onChange:W,onClose:j,onHighlightChange:V,onInputChange:B,onOpen:G,open:K,openOnFocus:q=!1,options:U,readOnly:_=!1,selectOnFocus:X=!e.freeSolo,value:J}=e,Q=(0,p.A)(D);let Y=T;Y=e=>{const t=T(e);return"string"!==typeof t?String(t):t};const Z=a.useRef(!1),ee=a.useRef(!0),te=a.useRef(null),oe=a.useRef(null),[ne,re]=a.useState(null),[ae,le]=a.useState(-1),ie=l?0:-1,se=a.useRef(ie),[pe,ue]=(0,u.A)({controlled:J,default:y,name:A}),[ce,de]=(0,u.A)({controlled:z,default:"",name:A,state:"inputValue"}),[ge,fe]=a.useState(!1),he=a.useCallback(((e,t)=>{if(!(H?pe.length<t.length:null!==t)&&!f)return;let o;if(H)o="";else if(null==t)o="";else{const e=Y(t);o="string"===typeof e?e:""}ce!==o&&(de(o),B&&B(e,o,"reset"))}),[Y,ce,H,B,de,f,pe]),[me,be]=(0,u.A)({controlled:K,default:!1,name:A,state:"open"}),[ve,xe]=a.useState(!0),Ae=!H&&null!=pe&&ce===Y(pe),ye=me&&!_,Oe=ye?k(U.filter((e=>!P||!(H?pe:[pe]).some((t=>null!==t&&F(e,t))))),{inputValue:Ae&&ve?"":ce,getOptionLabel:Y}):[],$e=c({filteredOptions:Oe,value:pe,inputValue:ce});a.useEffect((()=>{const e=pe!==$e.value;ge&&!e||L&&!e||he(null,pe)}),[pe,he,ge,$e.value,L]);const Ie=me&&Oe.length>0&&!_,Se=(0,d.A)((e=>{-1===e?te.current.focus():ne.querySelector(`[data-tag-index="${e}"]`).focus()}));a.useEffect((()=>{H&&ae>pe.length-1&&(le(-1),Se(-1))}),[pe,H,ae,Se]);const Ce=(0,d.A)((e=>{let{event:t,index:n,reason:r="auto"}=e;if(se.current=n,-1===n?te.current.removeAttribute("aria-activedescendant"):te.current.setAttribute("aria-activedescendant",`${Q}-option-${n}`),V&&V(t,-1===n?null:Oe[n],r),!oe.current)return;const a=oe.current.querySelector(`[role="option"].${o}-focused`);a&&(a.classList.remove(`${o}-focused`),a.classList.remove(`${o}-focusVisible`));let l=oe.current;if("listbox"!==oe.current.getAttribute("role")&&(l=oe.current.parentElement.querySelector('[role="listbox"]')),!l)return;if(-1===n)return void(l.scrollTop=0);const i=oe.current.querySelector(`[data-option-index="${n}"]`);if(i&&(i.classList.add(`${o}-focused`),"keyboard"===r&&i.classList.add(`${o}-focusVisible`),l.scrollHeight>l.clientHeight&&"mouse"!==r&&"touch"!==r)){const e=i,t=l.clientHeight+l.scrollTop,o=e.offsetTop+e.offsetHeight;o>t?l.scrollTop=o-l.clientHeight:e.offsetTop-e.offsetHeight*(N?1.3:0)<l.scrollTop&&(l.scrollTop=e.offsetTop-e.offsetHeight*(N?1.3:0))}})),ke=(0,d.A)((e=>{let{event:t,diff:o,direction:r="next",reason:a="auto"}=e;if(!ye)return;const l=function(e,t){if(!oe.current||e<0||e>=Oe.length)return-1;let o=e;for(;;){const n=oe.current.querySelector(`[data-option-index="${o}"]`),r=!S&&(!n||n.disabled||"true"===n.getAttribute("aria-disabled"));if(n&&n.hasAttribute("tabindex")&&!r)return o;if(o="next"===t?(o+1)%Oe.length:(o-1+Oe.length)%Oe.length,o===e)return-1}}((()=>{const e=Oe.length-1;if("reset"===o)return ie;if("start"===o)return 0;if("end"===o)return e;const t=se.current+o;return t<0?-1===t&&E?-1:C&&-1!==se.current||Math.abs(o)>1?0:e:t>e?t===e+1&&E?-1:C||Math.abs(o)>1?e:0:t})(),r);if(Ce({index:l,reason:a,event:t}),n&&"reset"!==o)if(-1===l)te.current.value=ce;else{const e=Y(Oe[l]);te.current.value=e;0===e.toLowerCase().indexOf(ce.toLowerCase())&&ce.length>0&&te.current.setSelectionRange(ce.length,e.length)}})),Pe=a.useCallback((()=>{if(!ye)return;const e=(()=>{if(-1!==se.current&&$e.filteredOptions&&$e.filteredOptions.length!==Oe.length&&$e.inputValue===ce&&(H?pe.length===$e.value.length&&$e.value.every(((e,t)=>Y(pe[t])===Y(e))):(e=$e.value,t=pe,(e?Y(e):"")===(t?Y(t):"")))){const e=$e.filteredOptions[se.current];if(e)return h(Oe,(t=>Y(t)===Y(e)))}var e,t;return-1})();if(-1!==e)return void(se.current=e);const t=H?pe[0]:pe;if(0!==Oe.length&&null!=t){if(oe.current)if(null==t)se.current>=Oe.length-1?Ce({index:Oe.length-1}):Ce({index:se.current});else{const e=Oe[se.current];if(H&&e&&-1!==h(pe,(t=>F(e,t))))return;const o=h(Oe,(e=>F(e,t)));-1===o?ke({diff:"reset"}):Ce({index:o})}}else ke({diff:"reset"})}),[Oe.length,!H&&pe,P,ke,Ce,ye,ce,H]),Le=(0,d.A)((e=>{(0,g.A)(oe,e),e&&Pe()}));a.useEffect((()=>{Pe()}),[Pe]);const we=e=>{me||(be(!0),xe(!0),G&&G(e))},Re=(e,t)=>{me&&(be(!1),j&&j(e,t))},Te=(e,t,o,n)=>{if(H){if(pe.length===t.length&&pe.every(((e,o)=>e===t[o])))return}else if(pe===t)return;W&&W(e,t,o,n),ue(t)},Ne=a.useRef(!1),Me=function(e,t){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"options",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"selectOption",r=t;if(H){r=Array.isArray(pe)?pe.slice():[];const e=h(r,(e=>F(t,e)));-1===e?r.push(t):"freeSolo"!==o&&(r.splice(e,1),n="removeOption")}he(e,r),Te(e,r,n,{option:t}),$||e&&(e.ctrlKey||e.metaKey)||Re(e,n),(!0===s||"touch"===s&&Ne.current||"mouse"===s&&!Ne.current)&&te.current.blur()},De=(e,t)=>{if(!H)return;""===ce&&Re(e,"toggleInput");let o=ae;-1===ae?""===ce&&"previous"===t&&(o=pe.length-1):(o+="next"===t?1:-1,o<0&&(o=0),o===pe.length&&(o=-1)),o=function(e,t){if(-1===e)return-1;let o=e;for(;;){if("next"===t&&o===pe.length||"previous"===t&&-1===o)return-1;const e=ne.querySelector(`[data-tag-index="${o}"]`);if(e&&e.hasAttribute("tabindex")&&!e.disabled&&"true"!==e.getAttribute("aria-disabled"))return o;o+="next"===t?1:-1}}(o,t),le(o),Se(o)},Ee=e=>{Z.current=!0,de(""),B&&B(e,"","clear"),Te(e,H?[]:null,"clear")},ze=e=>t=>{if(e.onKeyDown&&e.onKeyDown(t),!t.defaultMuiPrevented&&(-1!==ae&&-1===["ArrowLeft","ArrowRight"].indexOf(t.key)&&(le(-1),Se(-1)),229!==t.which))switch(t.key){case"Home":ye&&M&&(t.preventDefault(),ke({diff:"start",direction:"next",reason:"keyboard",event:t}));break;case"End":ye&&M&&(t.preventDefault(),ke({diff:"end",direction:"previous",reason:"keyboard",event:t}));break;case"PageUp":t.preventDefault(),ke({diff:-5,direction:"previous",reason:"keyboard",event:t}),we(t);break;case"PageDown":t.preventDefault(),ke({diff:5,direction:"next",reason:"keyboard",event:t}),we(t);break;case"ArrowDown":t.preventDefault(),ke({diff:1,direction:"next",reason:"keyboard",event:t}),we(t);break;case"ArrowUp":t.preventDefault(),ke({diff:-1,direction:"previous",reason:"keyboard",event:t}),we(t);break;case"ArrowLeft":De(t,"previous");break;case"ArrowRight":De(t,"next");break;case"Enter":if(-1!==se.current&&ye){const e=Oe[se.current],o=!!w&&w(e);if(t.preventDefault(),o)return;Me(t,e,"selectOption"),n&&te.current.setSelectionRange(te.current.value.length,te.current.value.length)}else L&&""!==ce&&!1===Ae&&(H&&t.preventDefault(),Me(t,ce,"createOption","freeSolo"));break;case"Escape":ye?(t.preventDefault(),t.stopPropagation(),Re(t,"escape")):x&&(""!==ce||H&&pe.length>0)&&(t.preventDefault(),t.stopPropagation(),Ee(t));break;case"Backspace":if(H&&!_&&""===ce&&pe.length>0){const e=-1===ae?pe.length-1:ae,o=pe.slice();o.splice(e,1),Te(t,o,"removeOption",{option:pe[e]})}break;case"Delete":if(H&&!_&&""===ce&&pe.length>0&&-1!==ae){const e=ae,o=pe.slice();o.splice(e,1),Te(t,o,"removeOption",{option:pe[e]})}}},Fe=e=>{fe(!0),q&&!Z.current&&we(e)},He=e=>{t(oe)?te.current.focus():(fe(!1),ee.current=!0,Z.current=!1,i&&-1!==se.current&&ye?Me(e,Oe[se.current],"blur"):i&&L&&""!==ce?Me(e,ce,"blur","freeSolo"):f&&he(e,pe),Re(e,"blur"))},We=e=>{const t=e.target.value;ce!==t&&(de(t),xe(!1),B&&B(e,t,"input")),""===t?O||H||Te(e,null,"clear"):we(e)},je=e=>{const t=Number(e.currentTarget.getAttribute("data-option-index"));se.current!==t&&Ce({event:e,index:t,reason:"mouse"})},Ve=e=>{Ce({event:e,index:Number(e.currentTarget.getAttribute("data-option-index")),reason:"touch"}),Ne.current=!0},Be=e=>{const t=Number(e.currentTarget.getAttribute("data-option-index"));Me(e,Oe[t],"selectOption"),Ne.current=!1},Ge=e=>t=>{const o=pe.slice();o.splice(e,1),Te(t,o,"removeOption",{option:pe[e]})},Ke=e=>{me?Re(e,"toggleInput"):we(e)},qe=e=>{e.currentTarget.contains(e.target)&&e.target.getAttribute("id")!==Q&&e.preventDefault()},Ue=e=>{e.currentTarget.contains(e.target)&&(te.current.focus(),X&&ee.current&&te.current.selectionEnd-te.current.selectionStart===0&&te.current.select(),ee.current=!1)},_e=e=>{I||""!==ce&&me||Ke(e)};let Xe=L&&ce.length>0;Xe=Xe||(H?pe.length>0:null!==pe);let Je=Oe;if(N){new Map;Je=Oe.reduce(((e,t,o)=>{const n=N(t);return e.length>0&&e[e.length-1].group===n?e[e.length-1].options.push(t):e.push({key:o,index:o,group:n,options:[t]}),e}),[])}return I&&ge&&He(),{getRootProps:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,r.A)({"aria-owns":Ie?`${Q}-listbox`:null},e,{onKeyDown:ze(e),onMouseDown:qe,onClick:Ue})},getInputLabelProps:()=>({id:`${Q}-label`,htmlFor:Q}),getInputProps:()=>({id:Q,value:ce,onBlur:He,onFocus:Fe,onChange:We,onMouseDown:_e,"aria-activedescendant":ye?"":null,"aria-autocomplete":n?"both":"list","aria-controls":Ie?`${Q}-listbox`:void 0,"aria-expanded":Ie,autoComplete:"off",ref:te,autoCapitalize:"none",spellCheck:"false",role:"combobox",disabled:I}),getClearProps:()=>({tabIndex:-1,type:"button",onClick:Ee}),getPopupIndicatorProps:()=>({tabIndex:-1,type:"button",onClick:Ke}),getTagProps:e=>{let{index:t}=e;return(0,r.A)({key:t,"data-tag-index":t,tabIndex:-1},!_&&{onDelete:Ge(t)})},getListboxProps:()=>({role:"listbox",id:`${Q}-listbox`,"aria-labelledby":`${Q}-label`,ref:Le,onMouseDown:e=>{e.preventDefault()}}),getOptionProps:e=>{let{index:t,option:o}=e;var n;const r=(H?pe:[pe]).some((e=>null!=e&&F(o,e))),a=!!w&&w(o);return{key:null!=(n=null==R?void 0:R(o))?n:Y(o),tabIndex:-1,role:"option",id:`${Q}-option-${t}`,onMouseMove:je,onClick:Be,onTouchStart:Ve,"data-option-index":t,"aria-disabled":a,"aria-selected":r}},id:Q,inputValue:ce,value:pe,dirty:Xe,expanded:ye&&ne,popupOpen:ye,focused:ge||-1!==ae,anchorEl:ne,setAnchorEl:re,focusedTag:ae,groupedOptions:Je}};var A=o(5953),y=o(4535),O=o(8206),$=o(6803),I=o(2532),S=o(2372);function C(e){return(0,S.Ay)("MuiListSubheader",e)}(0,I.A)("MuiListSubheader",["root","colorPrimary","colorInherit","gutters","inset","sticky"]);var k=o(579);const P=["className","color","component","disableGutters","disableSticky","inset"],L=(0,y.Ay)("li",{name:"MuiListSubheader",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[t.root,"default"!==o.color&&t[`color${(0,$.A)(o.color)}`],!o.disableGutters&&t.gutters,o.inset&&t.inset,!o.disableSticky&&t.sticky]}})((e=>{let{theme:t,ownerState:o}=e;return(0,r.A)({boxSizing:"border-box",lineHeight:"48px",listStyle:"none",color:(t.vars||t).palette.text.secondary,fontFamily:t.typography.fontFamily,fontWeight:t.typography.fontWeightMedium,fontSize:t.typography.pxToRem(14)},"primary"===o.color&&{color:(t.vars||t).palette.primary.main},"inherit"===o.color&&{color:"inherit"},!o.disableGutters&&{paddingLeft:16,paddingRight:16},o.inset&&{paddingLeft:72},!o.disableSticky&&{position:"sticky",top:0,zIndex:1,backgroundColor:(t.vars||t).palette.background.paper})})),w=a.forwardRef((function(e,t){const o=(0,O.b)({props:e,name:"MuiListSubheader"}),{className:a,color:s="default",component:p="li",disableGutters:u=!1,disableSticky:c=!1,inset:d=!1}=o,g=(0,n.A)(o,P),f=(0,r.A)({},o,{color:s,component:p,disableGutters:u,disableSticky:c,inset:d}),h=(e=>{const{classes:t,color:o,disableGutters:n,inset:r,disableSticky:a}=e,l={root:["root","default"!==o&&`color${(0,$.A)(o)}`,!n&&"gutters",r&&"inset",!a&&"sticky"]};return(0,i.A)(l,C,t)})(f);return(0,k.jsx)(L,(0,r.A)({as:p,className:(0,l.A)(h.root,a),ref:t,ownerState:f},g))}));w.muiSkipListHighlight=!0;const R=w;var T=o(3336),N=o(7392),M=o(3845),D=o(3138),E=o(1470),z=o(2766),F=o(6950),H=o(6871),W=o(2527);function j(e){return(0,S.Ay)("MuiAutocomplete",e)}const V=(0,I.A)("MuiAutocomplete",["root","expanded","fullWidth","focused","focusVisible","tag","tagSizeSmall","tagSizeMedium","hasPopupIcon","hasClearIcon","inputRoot","input","inputFocused","endAdornment","clearIndicator","popupIndicator","popupIndicatorOpen","popper","popperDisablePortal","paper","listbox","loading","noOptions","option","groupLabel","groupUl"]);var B,G,K=o(5849);const q=["autoComplete","autoHighlight","autoSelect","blurOnSelect","ChipProps","className","clearIcon","clearOnBlur","clearOnEscape","clearText","closeText","componentsProps","defaultValue","disableClearable","disableCloseOnSelect","disabled","disabledItemsFocusable","disableListWrap","disablePortal","filterOptions","filterSelectedOptions","forcePopupIcon","freeSolo","fullWidth","getLimitTagsText","getOptionDisabled","getOptionKey","getOptionLabel","isOptionEqualToValue","groupBy","handleHomeEndKeys","id","includeInputInList","inputValue","limitTags","ListboxComponent","ListboxProps","loading","loadingText","multiple","noOptionsText","onChange","onClose","onHighlightChange","onInputChange","onOpen","open","openOnFocus","openText","options","PaperComponent","PopperComponent","popupIcon","readOnly","renderGroup","renderInput","renderOption","renderTags","selectOnFocus","size","slotProps","value"],U=["ref"],_=["key"],X=["key"],J=(0,y.Ay)("div",{name:"MuiAutocomplete",slot:"Root",overridesResolver:(e,t)=>{const{ownerState:o}=e,{fullWidth:n,hasClearIcon:r,hasPopupIcon:a,inputFocused:l,size:i}=o;return[{[`& .${V.tag}`]:t.tag},{[`& .${V.tag}`]:t[`tagSize${(0,$.A)(i)}`]},{[`& .${V.inputRoot}`]:t.inputRoot},{[`& .${V.input}`]:t.input},{[`& .${V.input}`]:l&&t.inputFocused},t.root,n&&t.fullWidth,a&&t.hasPopupIcon,r&&t.hasClearIcon]}})({[`&.${V.focused} .${V.clearIndicator}`]:{visibility:"visible"},"@media (pointer: fine)":{[`&:hover .${V.clearIndicator}`]:{visibility:"visible"}},[`& .${V.tag}`]:{margin:3,maxWidth:"calc(100% - 6px)"},[`& .${V.inputRoot}`]:{[`.${V.hasPopupIcon}&, .${V.hasClearIcon}&`]:{paddingRight:30},[`.${V.hasPopupIcon}.${V.hasClearIcon}&`]:{paddingRight:56},[`& .${V.input}`]:{width:0,minWidth:30}},[`& .${D.A.root}`]:{paddingBottom:1,"& .MuiInput-input":{padding:"4px 4px 4px 0px"}},[`& .${D.A.root}.${E.A.sizeSmall}`]:{[`& .${D.A.input}`]:{padding:"2px 4px 3px 0"}},[`& .${z.A.root}`]:{padding:9,[`.${V.hasPopupIcon}&, .${V.hasClearIcon}&`]:{paddingRight:39},[`.${V.hasPopupIcon}.${V.hasClearIcon}&`]:{paddingRight:65},[`& .${V.input}`]:{padding:"7.5px 4px 7.5px 5px"},[`& .${V.endAdornment}`]:{right:9}},[`& .${z.A.root}.${E.A.sizeSmall}`]:{paddingTop:6,paddingBottom:6,paddingLeft:6,[`& .${V.input}`]:{padding:"2.5px 4px 2.5px 8px"}},[`& .${F.A.root}`]:{paddingTop:19,paddingLeft:8,[`.${V.hasPopupIcon}&, .${V.hasClearIcon}&`]:{paddingRight:39},[`.${V.hasPopupIcon}.${V.hasClearIcon}&`]:{paddingRight:65},[`& .${F.A.input}`]:{padding:"7px 4px"},[`& .${V.endAdornment}`]:{right:9}},[`& .${F.A.root}.${E.A.sizeSmall}`]:{paddingBottom:1,[`& .${F.A.input}`]:{padding:"2.5px 4px"}},[`& .${E.A.hiddenLabel}`]:{paddingTop:8},[`& .${F.A.root}.${E.A.hiddenLabel}`]:{paddingTop:0,paddingBottom:0,[`& .${V.input}`]:{paddingTop:16,paddingBottom:17}},[`& .${F.A.root}.${E.A.hiddenLabel}.${E.A.sizeSmall}`]:{[`& .${V.input}`]:{paddingTop:8,paddingBottom:9}},[`& .${V.input}`]:{flexGrow:1,textOverflow:"ellipsis",opacity:0},variants:[{props:{fullWidth:!0},style:{width:"100%"}},{props:{size:"small"},style:{[`& .${V.tag}`]:{margin:2,maxWidth:"calc(100% - 4px)"}}},{props:{inputFocused:!0},style:{[`& .${V.input}`]:{opacity:1}}},{props:{multiple:!0},style:{[`& .${V.inputRoot}`]:{flexWrap:"wrap"}}}]}),Q=(0,y.Ay)("div",{name:"MuiAutocomplete",slot:"EndAdornment",overridesResolver:(e,t)=>t.endAdornment})({position:"absolute",right:0,top:"50%",transform:"translate(0, -50%)"}),Y=(0,y.Ay)(N.A,{name:"MuiAutocomplete",slot:"ClearIndicator",overridesResolver:(e,t)=>t.clearIndicator})({marginRight:-2,padding:4,visibility:"hidden"}),Z=(0,y.Ay)(N.A,{name:"MuiAutocomplete",slot:"PopupIndicator",overridesResolver:(e,t)=>{let{ownerState:o}=e;return(0,r.A)({},t.popupIndicator,o.popupOpen&&t.popupIndicatorOpen)}})({padding:2,marginRight:-2,variants:[{props:{popupOpen:!0},style:{transform:"rotate(180deg)"}}]}),ee=(0,y.Ay)(A.A,{name:"MuiAutocomplete",slot:"Popper",overridesResolver:(e,t)=>{const{ownerState:o}=e;return[{[`& .${V.option}`]:t.option},t.popper,o.disablePortal&&t.popperDisablePortal]}})((e=>{let{theme:t}=e;return{zIndex:(t.vars||t).zIndex.modal,variants:[{props:{disablePortal:!0},style:{position:"absolute"}}]}})),te=(0,y.Ay)(T.A,{name:"MuiAutocomplete",slot:"Paper",overridesResolver:(e,t)=>t.paper})((e=>{let{theme:t}=e;return(0,r.A)({},t.typography.body1,{overflow:"auto"})})),oe=(0,y.Ay)("div",{name:"MuiAutocomplete",slot:"Loading",overridesResolver:(e,t)=>t.loading})((e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,padding:"14px 16px"}})),ne=(0,y.Ay)("div",{name:"MuiAutocomplete",slot:"NoOptions",overridesResolver:(e,t)=>t.noOptions})((e=>{let{theme:t}=e;return{color:(t.vars||t).palette.text.secondary,padding:"14px 16px"}})),re=(0,y.Ay)("div",{name:"MuiAutocomplete",slot:"Listbox",overridesResolver:(e,t)=>t.listbox})((e=>{let{theme:t}=e;return{listStyle:"none",margin:0,padding:"8px 0",maxHeight:"40vh",overflow:"auto",position:"relative",[`& .${V.option}`]:{minHeight:48,display:"flex",overflow:"hidden",justifyContent:"flex-start",alignItems:"center",cursor:"pointer",paddingTop:6,boxSizing:"border-box",outline:"0",WebkitTapHighlightColor:"transparent",paddingBottom:6,paddingLeft:16,paddingRight:16,[t.breakpoints.up("sm")]:{minHeight:"auto"},[`&.${V.focused}`]:{backgroundColor:(t.vars||t).palette.action.hover,"@media (hover: none)":{backgroundColor:"transparent"}},'&[aria-disabled="true"]':{opacity:(t.vars||t).palette.action.disabledOpacity,pointerEvents:"none"},[`&.${V.focusVisible}`]:{backgroundColor:(t.vars||t).palette.action.focus},'&[aria-selected="true"]':{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / ${t.vars.palette.action.selectedOpacity})`:(0,s.X4)(t.palette.primary.main,t.palette.action.selectedOpacity),[`&.${V.focused}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.hoverOpacity}))`:(0,s.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.hoverOpacity),"@media (hover: none)":{backgroundColor:(t.vars||t).palette.action.selected}},[`&.${V.focusVisible}`]:{backgroundColor:t.vars?`rgba(${t.vars.palette.primary.mainChannel} / calc(${t.vars.palette.action.selectedOpacity} + ${t.vars.palette.action.focusOpacity}))`:(0,s.X4)(t.palette.primary.main,t.palette.action.selectedOpacity+t.palette.action.focusOpacity)}}}}})),ae=(0,y.Ay)(R,{name:"MuiAutocomplete",slot:"GroupLabel",overridesResolver:(e,t)=>t.groupLabel})((e=>{let{theme:t}=e;return{backgroundColor:(t.vars||t).palette.background.paper,top:-8}})),le=(0,y.Ay)("ul",{name:"MuiAutocomplete",slot:"GroupUl",overridesResolver:(e,t)=>t.groupUl})({padding:0,[`& .${V.option}`]:{paddingLeft:24}}),ie=a.forwardRef((function(e,t){var o,s,p,u;const c=(0,O.b)({props:e,name:"MuiAutocomplete"}),{autoComplete:d=!1,autoHighlight:g=!1,autoSelect:f=!1,blurOnSelect:h=!1,ChipProps:m,className:b,clearIcon:v=B||(B=(0,k.jsx)(H.A,{fontSize:"small"})),clearOnBlur:y=!c.freeSolo,clearOnEscape:I=!1,clearText:S="Clear",closeText:C="Close",componentsProps:P={},defaultValue:L=(c.multiple?[]:null),disableClearable:w=!1,disableCloseOnSelect:R=!1,disabled:N=!1,disabledItemsFocusable:D=!1,disableListWrap:E=!1,disablePortal:z=!1,filterSelectedOptions:F=!1,forcePopupIcon:V="auto",freeSolo:ie=!1,fullWidth:se=!1,getLimitTagsText:pe=e=>`+${e}`,getOptionLabel:ue,groupBy:ce,handleHomeEndKeys:de=!c.freeSolo,includeInputInList:ge=!1,limitTags:fe=-1,ListboxComponent:he="ul",ListboxProps:me,loading:be=!1,loadingText:ve="Loading\u2026",multiple:xe=!1,noOptionsText:Ae="No options",openOnFocus:ye=!1,openText:Oe="Open",PaperComponent:$e=T.A,PopperComponent:Ie=A.A,popupIcon:Se=G||(G=(0,k.jsx)(W.A,{})),readOnly:Ce=!1,renderGroup:ke,renderInput:Pe,renderOption:Le,renderTags:we,selectOnFocus:Re=!c.freeSolo,size:Te="medium",slotProps:Ne={}}=c,Me=(0,n.A)(c,q),{getRootProps:De,getInputProps:Ee,getInputLabelProps:ze,getPopupIndicatorProps:Fe,getClearProps:He,getTagProps:We,getListboxProps:je,getOptionProps:Ve,value:Be,dirty:Ge,expanded:Ke,id:qe,popupOpen:Ue,focused:_e,focusedTag:Xe,anchorEl:Je,setAnchorEl:Qe,inputValue:Ye,groupedOptions:Ze}=x((0,r.A)({},c,{componentName:"Autocomplete"})),et=!w&&!N&&Ge&&!Ce,tt=(!ie||!0===V)&&!1!==V,{onMouseDown:ot}=Ee(),{ref:nt}=null!=me?me:{},rt=je(),{ref:at}=rt,lt=(0,n.A)(rt,U),it=(0,K.A)(at,nt),st=ue||(e=>{var t;return null!=(t=e.label)?t:e}),pt=(0,r.A)({},c,{disablePortal:z,expanded:Ke,focused:_e,fullWidth:se,getOptionLabel:st,hasClearIcon:et,hasPopupIcon:tt,inputFocused:-1===Xe,popupOpen:Ue,size:Te}),ut=(e=>{const{classes:t,disablePortal:o,expanded:n,focused:r,fullWidth:a,hasClearIcon:l,hasPopupIcon:s,inputFocused:p,popupOpen:u,size:c}=e,d={root:["root",n&&"expanded",r&&"focused",a&&"fullWidth",l&&"hasClearIcon",s&&"hasPopupIcon"],inputRoot:["inputRoot"],input:["input",p&&"inputFocused"],tag:["tag",`tagSize${(0,$.A)(c)}`],endAdornment:["endAdornment"],clearIndicator:["clearIndicator"],popupIndicator:["popupIndicator",u&&"popupIndicatorOpen"],popper:["popper",o&&"popperDisablePortal"],paper:["paper"],listbox:["listbox"],loading:["loading"],noOptions:["noOptions"],option:["option"],groupLabel:["groupLabel"],groupUl:["groupUl"]};return(0,i.A)(d,j,t)})(pt);let ct;if(xe&&Be.length>0){const e=e=>(0,r.A)({className:ut.tag,disabled:N},We(e));ct=we?we(Be,e,pt):Be.map(((t,o)=>{const a=e({index:o}),{key:l}=a,i=(0,n.A)(a,_);return(0,k.jsx)(M.A,(0,r.A)({label:st(t),size:Te},i,m),l)}))}if(fe>-1&&Array.isArray(ct)){const e=ct.length-fe;!_e&&e>0&&(ct=ct.splice(0,fe),ct.push((0,k.jsx)("span",{className:ut.tag,children:pe(e)},ct.length)))}const dt=ke||(e=>(0,k.jsxs)("li",{children:[(0,k.jsx)(ae,{className:ut.groupLabel,ownerState:pt,component:"div",children:e.group}),(0,k.jsx)(le,{className:ut.groupUl,ownerState:pt,children:e.children})]},e.key)),gt=Le||((e,t)=>{const{key:o}=e,a=(0,n.A)(e,X);return(0,k.jsx)("li",(0,r.A)({},a,{children:st(t)}),o)}),ft=(e,t)=>{const o=Ve({option:e,index:t});return gt((0,r.A)({},o,{className:ut.option}),e,{selected:o["aria-selected"],index:t,inputValue:Ye},pt)},ht=null!=(o=Ne.clearIndicator)?o:P.clearIndicator,mt=null!=(s=Ne.paper)?s:P.paper,bt=null!=(p=Ne.popper)?p:P.popper,vt=null!=(u=Ne.popupIndicator)?u:P.popupIndicator;return(0,k.jsxs)(a.Fragment,{children:[(0,k.jsx)(J,(0,r.A)({ref:t,className:(0,l.A)(ut.root,b),ownerState:pt},De(Me),{children:Pe({id:qe,disabled:N,fullWidth:!0,size:"small"===Te?"small":void 0,InputLabelProps:ze(),InputProps:(0,r.A)({ref:Qe,className:ut.inputRoot,startAdornment:ct,onClick:e=>{e.target===e.currentTarget&&ot(e)}},(et||tt)&&{endAdornment:(0,k.jsxs)(Q,{className:ut.endAdornment,ownerState:pt,children:[et?(0,k.jsx)(Y,(0,r.A)({},He(),{"aria-label":S,title:S,ownerState:pt},ht,{className:(0,l.A)(ut.clearIndicator,null==ht?void 0:ht.className),children:v})):null,tt?(0,k.jsx)(Z,(0,r.A)({},Fe(),{disabled:N,"aria-label":Ue?C:Oe,title:Ue?C:Oe,ownerState:pt},vt,{className:(0,l.A)(ut.popupIndicator,null==vt?void 0:vt.className),children:Se})):null]})}),inputProps:(0,r.A)({className:ut.input,disabled:N,readOnly:Ce},Ee())})})),Je?(0,k.jsx)(ee,(0,r.A)({as:Ie,disablePortal:z,style:{width:Je?Je.clientWidth:null},ownerState:pt,role:"presentation",anchorEl:Je,open:Ue},bt,{className:(0,l.A)(ut.popper,null==bt?void 0:bt.className),children:(0,k.jsxs)(te,(0,r.A)({ownerState:pt,as:$e},mt,{className:(0,l.A)(ut.paper,null==mt?void 0:mt.className),children:[be&&0===Ze.length?(0,k.jsx)(oe,{className:ut.loading,ownerState:pt,children:ve}):null,0!==Ze.length||ie||be?null:(0,k.jsx)(ne,{className:ut.noOptions,ownerState:pt,role:"presentation",onMouseDown:e=>{e.preventDefault()},children:Ae}),Ze.length>0?(0,k.jsx)(re,(0,r.A)({as:he,className:ut.listbox,ownerState:pt},lt,me,{ref:it,children:Ze.map(((e,t)=>ce?dt({key:e.key,group:e.group,children:e.options.map(((t,o)=>ft(t,e.index+o)))}):ft(e,t)))})):null]}))})):null]})}))}}]);
//# sourceMappingURL=108.e8180b24.chunk.js.map