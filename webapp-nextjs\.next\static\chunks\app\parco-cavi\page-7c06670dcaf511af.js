(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[562],{17313:(e,i,a)=>{"use strict";a.d(i,{Xi:()=>c,av:()=>d,j7:()=>o,tU:()=>r});var t=a(95155),s=a(12115),n=a(30064),l=a(59434);let r=n.bL,o=s.forwardRef((e,i)=>{let{className:a,...s}=e;return(0,t.jsx)(n.B8,{ref:i,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...s})});o.displayName=n.B8.displayName;let c=s.forwardRef((e,i)=>{let{className:a,...s}=e;return(0,t.jsx)(n.l9,{ref:i,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...s})});c.displayName=n.l9.displayName;let d=s.forwardRef((e,i)=>{let{className:a,...s}=e;return(0,t.jsx)(n.UC,{ref:i,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...s})});d.displayName=n.UC.displayName},30557:(e,i,a)=>{Promise.resolve().then(a.bind(a,58001))},54165:(e,i,a)=>{"use strict";a.d(i,{Cf:()=>m,Es:()=>x,L3:()=>b,c7:()=>u,lG:()=>r,rr:()=>g,zM:()=>o});var t=a(95155);a(12115);var s=a(15452),n=a(54416),l=a(59434);function r(e){let{...i}=e;return(0,t.jsx)(s.bL,{"data-slot":"dialog",...i})}function o(e){let{...i}=e;return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...i})}function c(e){let{...i}=e;return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...i})}function d(e){let{className:i,...a}=e;return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,l.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",i),...a})}function m(e){let{className:i,children:a,showCloseButton:r=!0,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,l.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",i),...o,children:[a,r&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(n.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function u(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,l.cn)("flex flex-col gap-2 text-center sm:text-left",i),...a})}function x(e){let{className:i,...a}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,l.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",i),...a})}function b(e){let{className:i,...a}=e;return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,l.cn)("text-lg leading-none font-semibold",i),...a})}function g(e){let{className:i,...a}=e;return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,l.cn)("text-muted-foreground text-sm",i),...a})}},58001:(e,i,a)=>{"use strict";a.r(i),a.d(i,{default:()=>Q});var t=a(95155),s=a(12115),n=a(66695),l=a(30285),r=a(26126),o=a(62523),c=a(85127),d=a(55365),m=a(40283),u=a(17522),x=a(13587),b=a(25731);let g={DISPONIBILE:"Disponibile",IN_USO:"In uso",TERMINATA:"Terminata",OVER:"Over"},h=(e,i)=>e<0?g.OVER:0===e?g.TERMINATA:e<i?g.IN_USO:g.DISPONIBILE,p=e=>e===g.DISPONIBILE||e===g.IN_USO,v=e=>{switch(e){case g.DISPONIBILE:return"hover:bg-green-50";case g.IN_USO:return"hover:bg-yellow-50";case g.TERMINATA:return"hover:bg-red-50";case g.OVER:return"hover:bg-red-100";default:return"hover:bg-gray-50"}},j=e=>e!==g.OVER&&e!==g.TERMINATA,f=(e,i)=>i<=0?0:Math.max(0,Math.min(100,(i-e)/i*100)),N=e=>"".concat(e.toFixed(1),"m"),_=e=>{switch(e){case g.DISPONIBILE:return"Bobina disponibile per nuove installazioni";case g.IN_USO:return"Bobina parzialmente utilizzata";case g.TERMINATA:return"Bobina completamente esaurita";case g.OVER:return"Bobina sovra-utilizzata (metri negativi)";default:return"Stato non definito"}};var y=a(54165),z=a(85057),w=a(84616),D=a(51154),S=a(85339);let C={numero_bobina:"",utility:"",tipologia:"",n_conduttori:"0",sezione:"",metri_totali:"",ubicazione_bobina:"TBD",fornitore:"TBD",n_DDT:"TBD",data_DDT:"",configurazione:"s"};function E(e){let{open:i,onClose:a,cantiereId:n,onSuccess:r,onError:c}=e,[m,u]=(0,s.useState)(C),[x,g]=(0,s.useState)(!1),[h,p]=(0,s.useState)(""),[v,j]=(0,s.useState)("1"),[f,N]=(0,s.useState)(!1),[_,E]=(0,s.useState)(!0),[T,A]=(0,s.useState)(""),[I,F]=(0,s.useState)(!1);(0,s.useEffect)(()=>{i&&n&&n>0&&(u(C),p(""),O())},[i,n]);let O=async()=>{if(n&&!(n<=0))try{N(!0);let e=await b.Fw.isFirstBobinaInsertion(n);E(e.is_first_insertion),e.is_first_insertion?(F(!0),A("")):(A(e.configurazione),u(i=>({...i,configurazione:e.configurazione})),F(!1),"s"===e.configurazione&&await B())}catch(e){E(!0),F(!0)}finally{N(!1)}},B=async()=>{if(n&&!(n<=0))try{let e=await b.Fw.getBobine(n);if(e&&e.length>0){let i=e.filter(e=>e.numero_bobina&&/^\d+$/.test(e.numero_bobina));if(i.length>0){let e=Math.max(...i.map(e=>parseInt(e.numero_bobina,10))),a=String(e+1);j(a),u(e=>({...e,numero_bobina:a}))}else j("1"),u(e=>({...e,numero_bobina:"1"}))}else j("1"),u(e=>({...e,numero_bobina:"1"}))}catch(e){j("1"),u(e=>({...e,numero_bobina:"1"}))}},L=(e,i)=>{u(a=>({...a,[e]:i})),p("")},M=async e=>{A(e),u(i=>({...i,configurazione:e})),F(!1),"s"===e?await B():u(e=>({...e,numero_bobina:""}))},k=()=>{if(!m.numero_bobina.trim())return"Il numero bobina \xe8 obbligatorio";if("m"===m.configurazione){let e=m.numero_bobina.trim();if(/[\s\\/:*?"<>|]/.test(e))return'Il numero bobina non pu\xf2 contenere spazi o caratteri speciali come \\ / : * ? " < > |'}if(!m.utility.trim())return"La utility \xe8 obbligatoria";if(!m.tipologia.trim())return"La tipologia \xe8 obbligatoria";if(!m.sezione.trim())return"La formazione \xe8 obbligatoria";if(!m.metri_totali.trim())return"I metri totali sono obbligatori";let e=parseFloat(m.metri_totali);return isNaN(e)||e<=0?"I metri totali devono essere un numero positivo":null},R=async()=>{let e=k();if(e)return void p(e);try{if(g(!0),p(""),!n||n<=0)throw Error("Cantiere non selezionato");let e={numero_bobina:m.numero_bobina.trim(),utility:m.utility.trim().toUpperCase(),tipologia:m.tipologia.trim().toUpperCase(),n_conduttori:"0",sezione:m.sezione.trim(),metri_totali:parseFloat(m.metri_totali),ubicazione_bobina:m.ubicazione_bobina.trim()||"TBD",fornitore:m.fornitore.trim()||"TBD",n_DDT:m.n_DDT.trim()||"TBD",data_DDT:m.data_DDT||null,configurazione:m.configurazione};await b.Fw.createBobina(n,e),r("Bobina ".concat(m.numero_bobina," creata con successo")),a()}catch(a){var i,t;let e=(null==(t=a.response)||null==(i=t.data)?void 0:i.detail)||a.message||"Errore durante la creazione della bobina";e.includes("gi\xe0 presente nel cantiere")||e.includes("gi\xe0 esistente")?p("⚠️ Bobina con numero ".concat(m.numero_bobina," gi\xe0 esistente. Scegli un numero diverso.")):c(e)}finally{g(!1)}},U=()=>{x||(u(C),p(""),a())};return(0,t.jsx)(y.lG,{open:i,onOpenChange:U,children:(0,t.jsxs)(y.Cf,{className:"max-h-[95vh] overflow-y-auto",style:{width:"1000px !important",maxWidth:"95vw !important",minWidth:"1000px"},children:[(0,t.jsxs)(y.c7,{children:[(0,t.jsxs)(y.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(w.A,{className:"h-5 w-5"}),"Crea Nuova Bobina"]}),(0,t.jsx)(y.rr,{})]}),(0,t.jsxs)("div",{className:"grid gap-4 py-4",children:[f&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-4",children:[(0,t.jsx)(D.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Verifica configurazione..."})]}),I&&!f&&(0,t.jsxs)("div",{className:"border rounded-lg p-4 bg-blue-50",children:[(0,t.jsx)("h4",{className:"font-medium mb-3",children:"Seleziona configurazione per questo cantiere"}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:"Questa scelta determiner\xe0 come verranno numerati tutti i futuri inserimenti di bobine in questo cantiere."}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-3",children:[(0,t.jsx)(l.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>M("s"),children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Standard (s) - Numerazione automatica"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"I numeri bobina vengono generati automaticamente: 1, 2, 3..."})]})}),(0,t.jsx)(l.$,{variant:"outline",className:"justify-start h-auto p-4",onClick:()=>M("m"),children:(0,t.jsxs)("div",{className:"text-left",children:[(0,t.jsx)("div",{className:"font-medium",children:"Manuale (m) - Inserimento manuale"}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:"Puoi inserire numeri personalizzati: A123, TEST01, ecc."})]})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[!I&&!f&&(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"numero_bobina",className:"text-right",children:"Bobina *"}),(0,t.jsx)(o.p,{id:"numero_bobina",value:m.numero_bobina,onChange:e=>L("numero_bobina",e.target.value),placeholder:"s"===m.configurazione?"Generato automaticamente":"Es: A123, TEST01",disabled:x||"s"===m.configurazione,className:"col-span-2 ".concat("s"===m.configurazione?"bg-gray-50":"")})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"utility",className:"text-right",children:"Utility *"}),(0,t.jsx)(o.p,{id:"utility",value:m.utility,onChange:e=>L("utility",e.target.value),className:"col-span-2",placeholder:"Es: ENEL, TIM, OPEN FIBER",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"tipologia",className:"text-right",children:"Tipologia *"}),(0,t.jsx)(o.p,{id:"tipologia",value:m.tipologia,onChange:e=>L("tipologia",e.target.value),className:"col-span-2",placeholder:"Es: FO, RAME",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"sezione",className:"text-right",children:"Formazione *"}),(0,t.jsx)(o.p,{id:"sezione",value:m.sezione,onChange:e=>L("sezione",e.target.value),className:"col-span-2",placeholder:"Es: 9/125, 50/125, 1.5",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"metri_totali",className:"text-right",children:"Metri Totali *"}),(0,t.jsx)(o.p,{id:"metri_totali",type:"number",step:"0.1",min:"0",value:m.metri_totali,onChange:e=>L("metri_totali",e.target.value),className:"col-span-2",placeholder:"Es: 1000",disabled:x})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"ubicazione_bobina",className:"text-right",children:"Ubicazione"}),(0,t.jsx)(o.p,{id:"ubicazione_bobina",value:m.ubicazione_bobina,onChange:e=>L("ubicazione_bobina",e.target.value),className:"col-span-2",placeholder:"Es: Magazzino A, Cantiere",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"fornitore",className:"text-right",children:"Fornitore"}),(0,t.jsx)(o.p,{id:"fornitore",value:m.fornitore,onChange:e=>L("fornitore",e.target.value),className:"col-span-2",placeholder:"Es: Prysmian, Nexans",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"n_DDT",className:"text-right",children:"N\xb0 DDT"}),(0,t.jsx)(o.p,{id:"n_DDT",value:m.n_DDT,onChange:e=>L("n_DDT",e.target.value),className:"col-span-2",placeholder:"Es: DDT001",disabled:x})]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 items-center gap-4",children:[(0,t.jsx)(z.J,{htmlFor:"data_DDT",className:"text-right",children:"Data DDT"}),(0,t.jsx)(o.p,{id:"data_DDT",type:"date",value:m.data_DDT,onChange:e=>L("data_DDT",e.target.value),className:"col-span-2",disabled:x})]})]})]}),h&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:h})]})]}),(0,t.jsxs)(y.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:U,disabled:x||I,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:R,disabled:x||I||f,children:[x&&(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin"}),x?"Creando...":"Crea Bobina"]})]})]})})}var T=a(59409),A=a(37108),I=a(81284);function F(e){let{open:i,onClose:a,bobina:n,onSuccess:r,onError:c}=e,{cantiere:u}=(0,m.A)(),[x,g]=(0,s.useState)({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),[h,p]=(0,s.useState)({}),[v,j]=(0,s.useState)({}),[f,N]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(i&&n){var e,a;g({numero_bobina:n.numero_bobina||"",utility:n.utility||"",tipologia:n.tipologia||"",sezione:n.sezione||"",metri_totali:(null==(e=n.metri_totali)?void 0:e.toString())||"",metri_residui:(null==(a=n.metri_residui)?void 0:a.toString())||"",stato_bobina:n.stato_bobina||"",ubicazione_bobina:n.ubicazione_bobina||"",fornitore:n.fornitore||"",n_DDT:n.n_DDT||"",data_DDT:n.data_DDT||"",configurazione:n.configurazione||""}),p({}),j({})}},[i,n]),(0,s.useEffect)(()=>{_()},[x]);let _=()=>{let e={};if(x.utility.trim()||(e.utility="Utility \xe8 obbligatoria"),x.tipologia.trim()||(e.tipologia="Tipologia \xe8 obbligatoria"),x.sezione.trim()||(e.sezione="Formazione \xe8 obbligatoria"),x.metri_totali.trim()){let i=parseFloat(x.metri_totali);(isNaN(i)||i<=0)&&(e.metri_totali="Inserire un valore numerico valido maggiore di 0")}else e.metri_totali="Metri totali sono obbligatori";x.data_DDT&&!/^\d{4}-\d{2}-\d{2}$/.test(x.data_DDT)&&(e.data_DDT="Formato data non valido (YYYY-MM-DD)"),p(e),j({})},w=(e,i)=>{g(a=>{let t={...a,[e]:i};return"metri_totali"===e&&n&&(t.metri_residui=Math.max(0,(parseFloat(i)||0)-(n.metri_totali-n.metri_residui)).toString()),t})},C=()=>!!n&&("Over"===n.stato_bobina||"Disponibile"===n.stato_bobina),E=e=>!!n&&(!!["fornitore","ubicazione_bobina","n_DDT","data_DDT"].includes(e)||"Disponibile"===n.stato_bobina),F=async()=>{if(n&&u&&!(Object.keys(h).length>0)){if(!C())return void c("La bobina non pu\xf2 essere modificata nel suo stato attuale");try{N(!0);let e={utility:x.utility,tipologia:x.tipologia,sezione:x.sezione,metri_totali:parseFloat(x.metri_totali),ubicazione_bobina:x.ubicazione_bobina,fornitore:x.fornitore,n_DDT:x.n_DDT,data_DDT:x.data_DDT||null};await b.Fw.updateBobina(u.id_cantiere,n.id_bobina,e),r("Bobina ".concat(n.numero_bobina," aggiornata con successo")),a()}catch(a){var e,i;c((null==(i=a.response)||null==(e=i.data)?void 0:e.detail)||a.message||"Errore durante la modifica della bobina")}finally{N(!1)}}},O=()=>{f||(g({numero_bobina:"",utility:"",tipologia:"",sezione:"",metri_totali:"",metri_residui:"",stato_bobina:"",ubicazione_bobina:"",fornitore:"",n_DDT:"",data_DDT:"",configurazione:""}),p({}),j({}),a())};return n?(0,t.jsx)(y.lG,{open:i,onOpenChange:O,children:(0,t.jsxs)(y.Cf,{className:"max-w-2xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsxs)(y.c7,{children:[(0,t.jsxs)(y.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(A.A,{className:"h-5 w-5"}),"Modifica Bobina"]}),(0,t.jsxs)(y.rr,{children:["Modifica i dati della bobina ",n.numero_bobina]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)(d.Fc,{className:"border-blue-200 bg-blue-50",children:[(0,t.jsx)(I.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)(d.TN,{className:"text-blue-800",children:[(0,t.jsx)("div",{className:"font-semibold mb-1",children:"Condizioni per la modifica:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside text-sm space-y-1",children:[(0,t.jsx)("li",{children:'La bobina deve essere nello stato "Disponibile"'}),(0,t.jsx)("li",{children:"La bobina non deve essere associata a nessun cavo"}),(0,t.jsx)("li",{children:"Se modifichi i metri totali, i metri residui verranno aggiornati automaticamente"})]}),(0,t.jsxs)("div",{className:"mt-2 text-sm",children:[(0,t.jsx)("strong",{children:"Stato attuale:"})," ",n.stato_bobina]})]})]}),Object.keys(v).length>0&&(0,t.jsxs)(d.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsxs)(d.TN,{className:"text-amber-800",children:[(0,t.jsx)("div",{className:"font-semibold",children:"Attenzione:"}),(0,t.jsx)("ul",{className:"list-disc list-inside text-sm",children:Object.values(v).map((e,i)=>(0,t.jsx)("li",{children:e},i))})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"numero_bobina",children:"ID Bobina"}),(0,t.jsx)(o.p,{id:"numero_bobina",value:x.numero_bobina,disabled:!0,className:"bg-gray-100 font-semibold"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"utility",children:"Utility *"}),(0,t.jsx)(o.p,{id:"utility",value:x.utility,onChange:e=>w("utility",e.target.value),disabled:f||!E("utility"),className:h.utility?"border-red-500":""}),h.utility&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.utility})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"tipologia",children:"Tipologia *"}),(0,t.jsx)(o.p,{id:"tipologia",value:x.tipologia,onChange:e=>w("tipologia",e.target.value),disabled:f||!E("tipologia"),className:h.tipologia?"border-red-500":""}),h.tipologia&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.tipologia})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"sezione",children:"Formazione *"}),(0,t.jsx)(o.p,{id:"sezione",value:x.sezione,onChange:e=>w("sezione",e.target.value),disabled:f||!E("sezione"),className:h.sezione?"border-red-500":""}),h.sezione&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.sezione})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"metri_totali",children:"Metri Totali *"}),(0,t.jsx)(o.p,{id:"metri_totali",type:"number",value:x.metri_totali,onChange:e=>w("metri_totali",e.target.value),disabled:f||!E("metri_totali"),className:h.metri_totali?"border-red-500":"",step:"0.1",min:"0"}),h.metri_totali&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.metri_totali})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"metri_residui",children:"Metri Residui"}),(0,t.jsx)(o.p,{id:"metri_residui",type:"number",value:x.metri_residui,disabled:!0,className:"bg-gray-100"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"I metri residui non possono essere modificati direttamente"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"stato_bobina",children:"Stato Bobina"}),(0,t.jsxs)(T.l6,{value:x.stato_bobina,disabled:!0,children:[(0,t.jsx)(T.bq,{className:"bg-gray-100",children:(0,t.jsx)(T.yv,{})}),(0,t.jsxs)(T.gC,{children:[(0,t.jsx)(T.eb,{value:"Disponibile",children:"Disponibile"}),(0,t.jsx)(T.eb,{value:"In uso",children:"In uso"}),(0,t.jsx)(T.eb,{value:"Terminata",children:"Terminata"}),(0,t.jsx)(T.eb,{value:"Danneggiata",children:"Danneggiata"}),(0,t.jsx)(T.eb,{value:"Over",children:"Over"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"ubicazione_bobina",children:"Ubicazione Bobina"}),(0,t.jsx)(o.p,{id:"ubicazione_bobina",value:x.ubicazione_bobina,onChange:e=>w("ubicazione_bobina",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"fornitore",children:"Fornitore"}),(0,t.jsx)(o.p,{id:"fornitore",value:x.fornitore,onChange:e=>w("fornitore",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"n_DDT",children:"Numero DDT"}),(0,t.jsx)(o.p,{id:"n_DDT",value:x.n_DDT,onChange:e=>w("n_DDT",e.target.value),disabled:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(z.J,{htmlFor:"data_DDT",children:"Data DDT (YYYY-MM-DD)"}),(0,t.jsx)(o.p,{id:"data_DDT",type:"date",value:x.data_DDT,onChange:e=>w("data_DDT",e.target.value),disabled:f,className:h.data_DDT?"border-red-500":""}),h.data_DDT&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:h.data_DDT})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,t.jsx)(z.J,{htmlFor:"configurazione",children:"Modalit\xe0 Numerazione"}),(0,t.jsx)(o.p,{id:"configurazione",value:"s"===x.configurazione?"Automatica":"Manuale",disabled:!0,className:"bg-gray-100"}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"s"===x.configurazione?"Numerazione progressiva automatica (1, 2, 3, ...)":"Inserimento manuale dell'ID bobina (es. A123, SPEC01, ...)"})]})]})]}),(0,t.jsxs)(y.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:O,disabled:f,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:F,disabled:f||Object.keys(h).length>0||!C(),className:"bg-mariner-600 hover:bg-mariner-700",children:[f&&(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})}):null}var O=a(62525),B=a(1243);function L(e){let{open:i,onClose:a,bobina:n,cantiereId:r,onSuccess:o,onError:c}=e,[m,u]=(0,s.useState)(!1),x=async()=>{var e,i,t;if(n)try{if(u(!0),!r||r<=0)throw Error("Cantiere non selezionato");let i=n.id_bobina.split("_B")[1],t=await b.Fw.deleteBobina(r,i),s="Bobina ".concat(n.numero_bobina," eliminata con successo");(null==(e=t.data)?void 0:e.is_last_bobina)&&(s+=". Era l'ultima bobina del cantiere."),o(s),a()}catch(e){c((null==(t=e.response)||null==(i=t.data)?void 0:i.detail)||e.message||"Errore durante l'eliminazione della bobina")}finally{u(!1)}},h=()=>{m||a()};if(!n)return null;let v=n.stato_bobina===g.OVER,j=!v&&p(n.stato_bobina)&&n.metri_residui===n.metri_totali,f=v?{type:"error",title:"Bobina OVER - Eliminazione bloccata",message:"Le bobine in stato OVER non possono essere eliminate per preservare la tracciabilit\xe0 del sistema. Contattare l'amministratore per la gestione di bobine OVER."}:p(n.stato_bobina)?n.metri_residui!==n.metri_totali?{type:"error",title:"Bobina in uso",message:"La bobina ha ".concat(n.metri_residui,"m residui su ").concat(n.metri_totali,"m totali. Solo le bobine completamente disponibili possono essere eliminate.")}:{type:"warning",title:"Conferma eliminazione",message:"Questa operazione \xe8 irreversibile. La bobina verr\xe0 rimossa definitivamente dal parco cavi."}:{type:"error",title:"Eliminazione non consentita",message:'La bobina \xe8 in stato "'.concat(n.stato_bobina,'" e non pu\xf2 essere eliminata. ').concat(_(n.stato_bobina))};return(0,t.jsx)(y.lG,{open:i,onOpenChange:h,children:(0,t.jsxs)(y.Cf,{className:"max-w-md",children:[(0,t.jsxs)(y.c7,{children:[(0,t.jsxs)(y.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(O.A,{className:"h-5 w-5 text-red-600"}),"Elimina Bobina"]}),(0,t.jsxs)(y.rr,{children:["Stai per eliminare la bobina ",n.numero_bobina]})]}),(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsxs)("div",{className:"bg-slate-50 p-4 rounded-lg mb-4",children:[(0,t.jsx)("h4",{className:"font-medium mb-2",children:"Dettagli bobina:"}),(0,t.jsxs)("div",{className:"text-sm space-y-1",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Bobina:"})," ",n.numero_bobina]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Utility:"})," ",n.utility]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",n.tipologia]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Sezione:"})," ",n.sezione]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Stato:"})," ",n.stato_bobina]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri:"})," ",n.metri_residui,"m / ",n.metri_totali,"m"]}),n.ubicazione_bobina&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Ubicazione:"})," ",n.ubicazione_bobina]})]})]}),(0,t.jsxs)(d.Fc,{variant:"error"===f.type?"destructive":"default",children:[(0,t.jsx)(B.A,{className:"h-4 w-4"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:f.title}),(0,t.jsx)(d.TN,{className:"mt-1",children:f.message})]})]})]}),(0,t.jsxs)(y.Es,{children:[(0,t.jsx)(l.$,{variant:"outline",onClick:h,disabled:m,children:"Annulla"}),(0,t.jsxs)(l.$,{variant:"destructive",onClick:x,disabled:m||!j,children:[m&&(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin"}),m?"Eliminando...":"Elimina Bobina"]})]})]})})}var M=a(17313),k=a(3493),R=a(47924);let U=(e,i)=>{let[a,t]=(0,s.useState)(e);return(0,s.useEffect)(()=>{let a=setTimeout(()=>{t(e)},i);return()=>{clearTimeout(a)}},[e,i]),a};function V(e){let{open:i,onClose:a,bobina:n,cantiereId:r,onSuccess:c,onError:d}=e,[m,u]=(0,s.useState)(!1),[x,g]=(0,s.useState)(!1),[h,p]=(0,s.useState)(!1),[v,j]=(0,s.useState)([]),[f,N]=(0,s.useState)([]),[_,z]=(0,s.useState)([]),[w,S]=(0,s.useState)({}),[C,E]=(0,s.useState)({}),[T,A]=(0,s.useState)(""),I=U(T,300),[F,O]=(0,s.useState)("id_cavo"),[B,L]=(0,s.useState)("asc"),[V,P]=(0,s.useState)("all"),[J,$]=(0,s.useState)("all"),[Y,G]=(0,s.useState)(""),[W,H]=(0,s.useState)(""),[q,Z]=(0,s.useState)(1),[X,Q]=(0,s.useState)(20),K=(0,s.useMemo)(()=>{let e=e=>e.filter(e=>{var i,a,t,s;let n=!I||e.id_cavo.toLowerCase().includes(I.toLowerCase())||(null==(i=e.tipologia)?void 0:i.toLowerCase().includes(I.toLowerCase()))||(null==(a=e.ubicazione_partenza)?void 0:a.toLowerCase().includes(I.toLowerCase()))||(null==(t=e.ubicazione_arrivo)?void 0:t.toLowerCase().includes(I.toLowerCase())),l="all"===V||e.tipologia===V,r="all"===J||e.sezione===J,o=parseFloat((null==(s=e.metri_teorici)?void 0:s.toString())||"0"),c=!Y||o>=parseFloat(Y),d=!W||o<=parseFloat(W);return n&&l&&r&&c&&d}),i=e=>[...e].sort((e,i)=>{let a,t;switch(F){case"id_cavo":a=e.id_cavo,t=i.id_cavo;break;case"metri_teorici":var s,n;a=parseFloat((null==(s=e.metri_teorici)?void 0:s.toString())||"0"),t=parseFloat((null==(n=i.metri_teorici)?void 0:n.toString())||"0");break;case"tipologia":a=e.tipologia||"",t=i.tipologia||"";break;case"ubicazione_partenza":a=e.ubicazione_partenza||"",t=i.ubicazione_partenza||"";break;default:return 0}if("string"!=typeof a)return"asc"===B?a-t:t-a;{let e=a.localeCompare(t);return"asc"===B?e:-e}});return{compatibiliFiltrati:i(e(v)),incompatibiliFiltrati:i(e(f))}},[v,f,I,F,B,V,J,Y,W]),ee=(0,s.useMemo)(()=>{let{compatibiliFiltrati:e,incompatibiliFiltrati:i}=K,a=(q-1)*X,t=a+X;return{compatibili:e.slice(a,t),incompatibili:i.slice(a,t),totalCompatibili:e.length,totalIncompatibili:i.length,totalPages:Math.ceil(Math.max(e.length,i.length)/X)}},[K,q,X]),ei=(0,s.useMemo)(()=>{let e=Object.values(w).reduce((e,i)=>e+parseFloat(i||"0"),0),i=(null==n?void 0:n.metri_residui)||0,a=Object.entries(w).some(e=>{let[a,t]=e;return parseFloat(t||"0")>i}),t=_.some(e=>e._isIncompatible),s=a||(null==n?void 0:n.stato_bobina)==="OVER",l=Math.max(0,e-i);return{metriTotaliSelezionati:e,metriResiduiBobina:i,isOverState:s,metriEccedenza:l,percentualeUtilizzo:i>0?e/i*100:0,hasSingleCavoOver:a,hasIncompatibleCavi:t}},[w,n,_]);(0,s.useMemo)(()=>Array.from(new Set([...v,...f].map(e=>e.tipologia).filter(Boolean))).sort(),[v,f]),(0,s.useMemo)(()=>Array.from(new Set([...v,...f].map(e=>e.sezione).filter(Boolean))).sort(),[v,f]);let ea=async()=>{if(r&&n)try{g(!0);let e=(await b.At.getCavi(r)).filter(e=>{var i,a;let t=parseFloat((null==(i=e.metratura_reale)?void 0:i.toString())||"0")||0,s=parseFloat((null==(a=e.metri_teorici)?void 0:a.toString())||"0")||0,n=!("Installato"===e.stato_installazione||t>0),l=3!==e.modificato_manualmente;return n&&l&&0===t&&s>0}),i=e.filter(e=>{let i=e.tipologia===n.tipologia&&String(e.sezione)===String(n.sezione);return console.log("\uD83D\uDD0D AggiungiCaviDialogSimple: Controllo compatibilit\xe0:",{cavoTip:e.tipologia,bobinaTip:n.tipologia,cavoSez:e.sezione,bobinaSez:n.sezione,cavoTipType:typeof e.tipologia,cavoSezType:typeof e.sezione,tipMatch:e.tipologia===n.tipologia,sezMatch:String(e.sezione)===String(n.sezione),cavoSezioneString:String(e.sezione),bobinaSezioneString:String(n.sezione),isCompatible:i}),i}),a=e.filter(e=>e.tipologia!==n.tipologia||String(e.sezione)!==String(n.sezione));i.length,j(i),N(a)}catch(e){d("Errore nel caricamento dei cavi: "+(e.message||"Errore sconosciuto"))}finally{g(!1)}};(0,s.useEffect)(()=>{console.log("\uD83D\uDD04 AggiungiCaviDialogSimple: Dialog reset:",{open:i,bobina:!!n,cantiereId:r,bobinaData:n}),i&&n&&r&&!(r<=0)&&(z([]),S({}),E({}),ea())},[i,n,r]);let et=(e,i)=>{if(console.log("\uD83C\uDFAF AggiungiCaviDialogSimple: Toggle cavo:",{cavoId:e.id_cavo,isCompatible:i,metriTeorici:e.metri_teorici}),_.some(i=>i.id_cavo===e.id_cavo))z(i=>i.filter(i=>i.id_cavo!==e.id_cavo)),S(i=>{let a={...i};return delete a[e.id_cavo],a});else{let a={...e,_isIncompatible:!i};z(e=>[...e,a]),S(i=>({...i,[e.id_cavo]:"0"}))}},es=(e,i)=>{null==n||n.metri_residui,parseFloat(i||"0");let a=_.find(i=>i.id_cavo===e);null==a||a._isIncompatible,E(i=>{let a={...i};return delete a[e],a}),S(a=>({...a,[e]:i}))},en=(0,s.useMemo)(()=>{let e=(null==n?void 0:n.metri_residui)||0,i=!1,a=[],t=[],s=null;for(let n of _){let l=parseFloat(w[n.id_cavo]||"0");l>0?i?t.push(n.id_cavo):(e-l<0?(a.push(n.id_cavo),s=n.id_cavo,i=!0):a.push(n.id_cavo),e-=l):a.push(n.id_cavo)}return{metriResiduiSimulati:e,caviValidi:a,caviBloccati:t,bobinaGiaOver:i,cavoCheCausaOver:s}},[_,w,null==n?void 0:n.metri_residui]),el=()=>en,er=async()=>{if(!r||!n)return;if(0===_.length)return void d("Nessun cavo selezionato");let e=_.filter(e=>{let i=w[e.id_cavo];return!i||""===i.trim()||isNaN(parseFloat(i))||0>parseFloat(i)});if(e.length>0)return void d("Metri posati mancanti o non validi per: ".concat(e.map(e=>e.id_cavo).join(", ")));try{p(!0);let{caviValidi:e,caviBloccati:i}=el(),t=_.filter(e=>{let a=parseFloat(w[e.id_cavo]||"0"),t=i.includes(e.id_cavo);return a>0&&!t});if(0===t.length)return void d("Nessun cavo valido da salvare (tutti bloccati o senza metri)");console.log("\uD83D\uDCBE AggiungiCaviDialogSimple: Preparazione salvataggio:",{caviSelezionati:_.length,caviValidi:e.length,caviBloccati:i.length,caviDaSalvare:t.length});let s=[],l=[],o=(null==n?void 0:n.metri_residui)||0;for(let e of t)try{let i=w[e.id_cavo],a=parseFloat(i),t=o-a<0,l=e._isIncompatible||!1,c=t||l;console.log("⚡ AggiungiCaviDialogSimple: Aggiornamento cavo:",{metriPosati:a,metriResiduiCorrente:o,causaOver:t,isIncompatible:l,needsForceOver:c}),await b.At.updateMetriPosati(r,e.id_cavo,a,n.id_bobina,c),o-=a,s.push({cavo:e.id_cavo,metriPosati:a,success:!0,wasIncompatible:e._isIncompatible,wasForceOver:c})}catch(a){let i="Errore sconosciuto";if(a.response){let e=a.response.status,t=a.response.data;i=400===e?(null==t?void 0:t.message)||(null==t?void 0:t.error)||"Richiesta non valida (400)":404===e?"Cavo o bobina non trovati (404)":409===e?"Conflitto: cavo gi\xe0 assegnato o bobina non disponibile (409)":500===e?"Errore interno del server (500)":"Errore HTTP ".concat(e,": ").concat((null==t?void 0:t.message)||(null==t?void 0:t.error)||"Errore del server")}else i=a.request?"Errore di connessione al server":a.message||"Errore di validazione";l.push({cavo:e.id_cavo,error:i})}if(0===l.length){let e=s.filter(e=>e.wasIncompatible).length,i=s.filter(e=>e.wasForceOver).length,t="".concat(s.length," cavi aggiornati con successo");e>0&&(t+=" (".concat(e," incompatibili)")),i>0&&(t+=" (".concat(i," con force_over)")),c(t),a()}else d("Errori: ".concat(l.map(e=>"".concat(e.cavo,": ").concat(e.error)).join(", ")))}catch(i){let e="Errore durante il salvataggio dei cavi";if(i.response){let a=i.response.status,t=i.response.data;e=400===a?"Errore di validazione: ".concat((null==t?void 0:t.message)||(null==t?void 0:t.error)||"Dati non validi"):401===a?"Sessione scaduta. Effettua nuovamente il login.":403===a?"Non hai i permessi per questa operazione":500===a?"Errore interno del server. Riprova pi\xf9 tardi.":"Errore del server (".concat(a,"): ").concat((null==t?void 0:t.message)||(null==t?void 0:t.error)||"Errore sconosciuto")}else e=i.request?"Impossibile contattare il server. Verifica la connessione.":i.message||"Errore imprevisto durante il salvataggio";d(e)}finally{p(!1)}},eo=()=>{h||(z([]),S({}),E({}),a())};if(!n)return null;let ec=e=>{let i=e.match(/C\d+_B(\d+)/);return i?i[1]:e},ed=(e,i)=>(console.log("\uD83D\uDCCB AggiungiCaviDialogSimple: Rendering lista cavi:",{isCompatible:i,caviLength:e.length,primi3Cavi:e.slice(0,3).map(e=>({id:e.id_cavo,tipologia:e.tipologia,sezione:e.sezione}))}),0===e.length)?(0,t.jsx)("div",{className:"h-[300px] flex items-center justify-center text-gray-500 border rounded",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(k.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,t.jsxs)("div",{children:["Nessun cavo ",i?"compatibile":"incompatibile"," disponibile"]}),(0,t.jsx)("div",{className:"text-xs mt-2 text-gray-400",children:i?'Cerca cavi con tipologia "'.concat(null==n?void 0:n.tipologia,'" e formazione "').concat(null==n?void 0:n.sezione,'"'):"I cavi incompatibili hanno tipologia o formazione diverse"})]})}):(0,t.jsx)("div",{className:"space-y-1 h-[300px] overflow-y-auto border rounded p-2 w-full",children:e.map((e,a)=>{let s=_.some(i=>i.id_cavo===e.id_cavo),n=w[e.id_cavo]||"",{caviBloccati:l,cavoCheCausaOver:r}=en,o=s&&l.includes(e.id_cavo),c=s&&e.id_cavo===r;return(0,t.jsxs)("div",{className:"border rounded px-3 py-2 transition-colors ".concat(o?"border-red-300 bg-red-50":c?"border-orange-300 bg-orange-50":s?"border-blue-500 bg-blue-50":"border-gray-200 hover:border-gray-300 hover:bg-gray-50"),children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 w-full overflow-hidden",children:[(0,t.jsx)("input",{type:"checkbox",checked:s,onChange:a=>{et(e,i)},className:"h-4 w-4 text-blue-600 border-gray-300 rounded flex-shrink-0"}),(0,t.jsxs)("div",{className:"flex items-center gap-2 flex-1 min-w-0 overflow-hidden",children:[(0,t.jsx)("span",{className:"font-medium text-gray-900 flex-shrink-0",children:e.id_cavo}),(0,t.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.tipologia}),(0,t.jsx)("span",{className:"text-xs bg-gray-100 px-1.5 py-0.5 rounded flex-shrink-0",children:e.sezione}),(0,t.jsxs)("span",{className:"text-xs text-gray-600 flex-shrink-0",children:[e.metri_teorici,"m"]}),o&&(0,t.jsx)("span",{className:"text-xs bg-red-100 text-red-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"BLOCCATO"}),c&&(0,t.jsx)("span",{className:"text-xs bg-orange-100 text-orange-700 px-1.5 py-0.5 rounded flex-shrink-0 font-medium",children:"CAUSA OVER"}),(0,t.jsxs)("span",{className:"text-xs text-gray-500 truncate min-w-0",children:[e.ubicazione_partenza||"N/A"," → ",e.ubicazione_arrivo||"N/A"]})]}),s&&(0,t.jsxs)("div",{className:"flex items-center gap-1 flex-shrink-0",children:[(0,t.jsx)("label",{className:"text-xs text-gray-600",children:"m:"}),(0,t.jsx)("input",{type:"number",step:"0.1",min:"0",value:n,onChange:i=>{es(e.id_cavo,i.target.value)},className:"w-16 px-1 py-1 border rounded text-xs",placeholder:"0"})]})]}),C[e.id_cavo]&&(0,t.jsx)("div",{className:"text-red-600 text-xs mt-1 ml-7",children:C[e.id_cavo]})]},e.id_cavo)})});return(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(y.lG,{open:i,onOpenChange:eo,children:(0,t.jsxs)(y.Cf,{className:"h-[85vh] w-full max-w-5xl overflow-hidden",style:{width:"950px !important",maxWidth:"95vw !important",minWidth:"850px"},children:[(0,t.jsxs)(y.c7,{className:"pb-0",children:[(0,t.jsxs)(y.L3,{className:"flex items-center gap-2 mb-0 text-lg",children:[(0,t.jsx)(k.A,{className:"h-5 w-5"}),"\uD83D\uDD25 NUOVO SISTEMA OVER - Aggiungi cavi alla bobina ",ec(n.id_bobina)]}),(0,t.jsx)(y.rr,{className:"mb-0 text-xs text-gray-600 mt-0",children:"Seleziona cavi e inserisci metri posati (SISTEMA AGGIORNATO)"})]}),(0,t.jsxs)("div",{className:"space-y-1 mt-2",children:[(0,t.jsx)("div",{className:"bg-gray-50 px-3 py-1 rounded text-sm",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("span",{children:[(0,t.jsxs)("strong",{children:["Bobina ",ec(n.id_bobina)]})," • ",n.tipologia," • ",n.sezione]}),(0,t.jsxs)("span",{children:["Residui: ",(0,t.jsxs)("strong",{children:[n.metri_residui,"m"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsxs)("span",{children:["Selezionati: ",(0,t.jsx)("strong",{children:_.length})," cavi • ",(0,t.jsxs)("strong",{children:[ei.metriTotaliSelezionati.toFixed(1),"m"]})]}),ei.isOverState&&(0,t.jsx)("span",{className:"text-red-600 font-medium",children:"OVER!"})]})]})}),(0,t.jsxs)("div",{className:"flex gap-2 items-center",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(R.A,{className:"absolute left-2 top-2 h-4 w-4 text-gray-400"}),(0,t.jsx)(o.p,{placeholder:"Cerca cavi...",value:T,onChange:e=>A(e.target.value),className:"pl-8 h-8 text-sm"})]}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{z([]),S({}),E({})},disabled:0===_.length,className:"h-8 px-3 text-sm",children:"Reset"})]}),x&&(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(D.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento cavi..."})]}),!x&&(0,t.jsxs)(M.tU,{defaultValue:"compatibili",className:"w-full",children:[(0,t.jsxs)(M.j7,{className:"flex justify-center gap-6 bg-transparent border-0 h-auto p-0",children:[(0,t.jsxs)(M.Xi,{value:"compatibili",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-2 h-2 rounded-full bg-green-500"}),"Cavi Compatibili (",ee.totalCompatibili,")"]}),(0,t.jsxs)(M.Xi,{value:"incompatibili",className:"tab-trigger flex items-center gap-2",children:[(0,t.jsx)("span",{className:"w-2 h-2 rounded-full bg-orange-500"}),"Cavi Incompatibili (",ee.totalIncompatibili,")"]})]}),(0,t.jsx)(M.av,{value:"compatibili",className:"mt-4 w-full overflow-hidden",children:(0,t.jsx)("div",{className:"w-full overflow-hidden",children:ed(ee.compatibili,!0)})}),(0,t.jsx)(M.av,{value:"incompatibili",className:"mt-4 w-full overflow-hidden",children:(0,t.jsx)("div",{className:"w-full overflow-hidden",children:ed(ee.incompatibili,!1)})})]})]}),(0,t.jsxs)(y.Es,{className:"flex justify-between items-center",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600",children:_.length>0?(()=>{let{metriResiduiSimulati:e,caviValidi:i,caviBloccati:a,cavoCheCausaOver:s}=en,l=_.reduce((e,i)=>e+parseFloat(w[i.id_cavo]||"0"),0),r=((null==n?void 0:n.metri_residui)||0)-e,o=_.filter(e=>e._isIncompatible).length;return(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{children:[_.length," cavi selezionati • ",l.toFixed(1),"m totali"]}),(0,t.jsxs)("div",{children:["✅ ",i.length," salvabili • ❌ ",a.length," bloccati"]}),(0,t.jsxs)("div",{children:["Usati: ",r.toFixed(1),"m / ",(null==n?void 0:n.metri_residui)||0,"m",e<0&&s&&(0,t.jsxs)("span",{className:"text-orange-600 font-medium ml-2",children:["(OVER da ",s,": +",Math.abs(e).toFixed(1),"m)"]})]}),o>0&&(0,t.jsxs)("div",{className:"text-orange-600 font-medium",children:["⚠️ ",o," cavi incompatibili"]})]})})():(0,t.jsx)("div",{children:"Nessun cavo selezionato"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(l.$,{variant:"outline",onClick:eo,disabled:h,children:"Annulla"}),(0,t.jsxs)(l.$,{onClick:er,disabled:h||0===_.length,className:ei.isOverState?"bg-orange-600 hover:bg-orange-700":"",children:[h&&(0,t.jsx)(D.A,{className:"mr-2 h-4 w-4 animate-spin"}),h?"Salvataggio...":ei.isOverState?"Salva ".concat(_.length," cavi (OVER)"):"Salva ".concat(_.length," cavi")]})]})]})]})})})}var P=a(72713),J=a(40646),$=a(14186);function Y(e){let{bobine:i,filteredBobine:a,className:l}=e,r=(0,s.useMemo)(()=>{let e=i.length,t=a.length,s=a.filter(e=>"Disponibile"===e.stato_bobina).length,n=a.filter(e=>"In uso"===e.stato_bobina).length,l=a.filter(e=>"Terminata"===e.stato_bobina).length,r=a.filter(e=>"Over"===e.stato_bobina).length,o=a.reduce((e,i)=>e+(i.metri_totali||0),0),c=a.reduce((e,i)=>e+(i.metri_residui||0),0),d=o-c,m=o>0?Math.round(d/o*100):0;return{totalBobine:e,filteredCount:t,disponibili:s,inUso:n,terminate:l,over:r,metriTotali:o,metriResidui:c,metriUtilizzati:d,percentualeUtilizzo:m}},[i,a]);return(0,t.jsx)(n.Zp,{className:l,children:(0,t.jsxs)(n.Wu,{className:"p-1.5",children:[(0,t.jsx)("div",{className:"flex items-center justify-between mb-1",children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(P.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Bobine"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(A.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:r.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",r.totalBobine," bobine"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(J.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:r.disponibili}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"disponibili"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)($.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:r.inUso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in uso"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(B.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-red-700 text-sm",children:r.terminate}),(0,t.jsx)("div",{className:"text-xs text-red-600",children:"terminate"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-red-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(S.A,{className:"h-3.5 w-3.5 text-red-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-red-700 text-sm",children:r.over}),(0,t.jsx)("div",{className:"text-xs text-red-600",children:"over"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[r.metriUtilizzati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",r.metriTotali.toLocaleString(),"m"]})]})]})]}),r.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"Utilizzo Complessivo Bobine"}),(0,t.jsxs)("span",{className:"font-bold ".concat(r.percentualeUtilizzo>=80?"text-amber-700":r.percentualeUtilizzo>=60?"text-orange-700":r.percentualeUtilizzo>=40?"text-yellow-700":"text-emerald-700"),children:[r.percentualeUtilizzo,"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-in-out ".concat(r.percentualeUtilizzo>=80?"bg-gradient-to-r from-amber-500 to-amber-600":r.percentualeUtilizzo>=60?"bg-gradient-to-r from-orange-500 to-orange-600":r.percentualeUtilizzo>=40?"bg-gradient-to-r from-yellow-500 to-yellow-600":"bg-gradient-to-r from-emerald-500 to-emerald-600"),style:{width:"".concat(Math.min(r.percentualeUtilizzo,100),"%")}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Metri utilizzati vs totali disponibili"}),(0,t.jsxs)("span",{children:[r.metriResidui.toLocaleString(),"m residui"]})]})]})]})})}var G=a(83744),W=a(23837),H=a(13717),q=a(47650);function Z(e){let{children:i,items:a,onAction:n,disabled:l=!1}=e,[r,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)({x:0,y:0}),[m,u]=(0,s.useState)(null),x=(0,s.useRef)(null),b=(0,s.useRef)(null);(0,s.useEffect)(()=>{let e=e=>{x.current&&!x.current.contains(e.target)&&o(!1)},i=e=>{"Escape"===e.key&&o(!1)};return r&&(document.addEventListener("mousedown",e),document.addEventListener("keydown",i)),()=>{document.removeEventListener("mousedown",e),document.removeEventListener("keydown",i)}},[r]);let g=e=>{e.disabled||(o(!1),n(e.action,m))},h=e=>{switch(e){case"warning":return"text-amber-600 hover:bg-amber-50";case"danger":return"text-red-600 hover:bg-red-50";default:return"text-gray-700 hover:bg-gray-100"}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{ref:b,onContextMenu:e=>{var i;if(l)return;e.preventDefault(),e.stopPropagation(),null==(i=b.current)||i.getBoundingClientRect();let t=e.clientX,s=e.clientY,n=40*a.length,r=window.innerWidth;d({x:t+200>r?t-200:t,y:s+n>window.innerHeight?s-n:s}),u(e.currentTarget.dataset),o(!0)},className:"w-full h-full",children:i}),r?(0,q.createPortal)((0,t.jsx)("div",{ref:x,className:"fixed z-50 min-w-[200px] bg-white border border-gray-200 rounded-md shadow-lg py-1",style:{left:c.x,top:c.y},children:a.map((e,i)=>e.separator?(0,t.jsx)("div",{className:"border-t border-gray-200 my-1"},"separator-".concat(i)):(0,t.jsxs)("button",{className:"w-full px-3 py-2 text-left text-sm flex items-center gap-2 transition-colors ".concat(e.disabled?"text-gray-400 cursor-not-allowed":h(e.color)),onClick:()=>g(e),disabled:e.disabled,children:[e.icon&&(0,t.jsx)("span",{className:"w-4 h-4",children:e.icon}),(0,t.jsx)("span",{children:e.label})]},e.id))}),document.body):null]})}var X=a(63743);function Q(){let[e,i]=(0,s.useState)(""),[a,p]=(0,s.useState)("all"),[y,z]=(0,s.useState)([]),[C,T]=(0,s.useState)(!0),[A,I]=(0,s.useState)(""),{user:B,isLoading:M}=(0,m.A)(),{cantiereId:U,cantiere:P,isValidCantiere:q,isLoading:Q,error:K}=(0,u.jV)(),[ee,ei]=(0,s.useState)(!1),[ea,et]=(0,s.useState)(!1),[es,en]=(0,s.useState)(!1),[el,er]=(0,s.useState)(!1),[eo,ec]=(0,s.useState)(null),[ed,em]=(0,s.useState)(""),[eu,ex]=(0,s.useState)("");(0,s.useEffect)(()=>{q&&U&&U>0&&!Q?(console.log("\uD83C\uDFD7️ ParcoCaviPage: Caricamento bobine per cantiere:",U),eb()):Q||q||(console.warn("\uD83C\uDFD7️ ParcoCaviPage: Cantiere non valido, reset dati"),z([]),I(K||"Nessun cantiere selezionato"))},[U,q,Q,K]);let eb=async()=>{try{if(T(!0),I(""),!U||U<=0){I("Cantiere non selezionato. Seleziona un cantiere per visualizzare le bobine."),z([]);return}let e=await b.Fw.getBobine(U);z(e||[])}catch(a){var e,i;I((null==(i=a.response)||null==(e=i.data)?void 0:e.detail)||"Errore durante il caricamento delle bobine"),z([])}finally{T(!1)}};(0,s.useEffect)(()=>{if(ed){let e=setTimeout(()=>em(""),5e3);return()=>clearTimeout(e)}},[ed]),(0,s.useEffect)(()=>{if(eu){let e=setTimeout(()=>ex(""),5e3);return()=>clearTimeout(e)}},[eu]);let eg=e=>{ec(e),er(!0)},eh=e=>{ec(e),et(!0)},ep=e=>{ec(e),en(!0)},ev=e=>{em(e),eb()},ej=e=>{ex(e)},ef=()=>{em("Funzione import in sviluppo")},eN=()=>{em("Funzione export in sviluppo")},e_=(e,i,a)=>{let s=e||h(i,a),n=(0,X.t2)(s),l={disponibile:J.A,in_uso:$.A,terminata:S.A,over:S.A}[null==s?void 0:s.toLowerCase()]||S.A;return(0,t.jsxs)(r.E,{className:"flex items-center gap-1 font-medium ".concat(n.badge),title:_(s),children:[(0,t.jsx)(l,{className:"h-3 w-3 ".concat(n.text)}),s.toUpperCase()]})},ey=y.filter(i=>{var t,s,n;let l=(null==(t=i.numero_bobina)?void 0:t.toLowerCase().includes(e.toLowerCase()))||(null==(s=i.tipologia)?void 0:s.toLowerCase().includes(e.toLowerCase()))||(null==(n=i.utility)?void 0:n.toLowerCase().includes(e.toLowerCase())),r=!0;if("all"!==a){let e=i.stato_bobina||h(i.metri_residui,i.metri_totali);switch(a){case"disponibile":r=e===g.DISPONIBILE;break;case"in_uso":r=e===g.IN_USO;break;case"esaurita":r=e===g.TERMINATA;break;case"over":r=e===g.OVER}}return l&&r});return(0,t.jsx)(x.u,{children:(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[A&&(0,t.jsxs)(d.Fc,{variant:"destructive",className:"mb-6",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:A})]}),(0,t.jsx)(Y,{bobine:y,filteredBobine:ey,className:"mb-6"}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(R.A,{className:"h-5 w-5"}),"Ricerca e Filtri"]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsx)(o.p,{placeholder:"Cerca per bobina, tipologia o utility...",value:e,onChange:e=>i(e.target.value),className:"w-full"})}),(0,t.jsx)("div",{className:"flex gap-2",children:["all","disponibile","in_uso","esaurita","over"].map(e=>(0,t.jsx)(l.$,{variant:a===e?"default":"outline",size:"sm",onClick:()=>p(e),children:"all"===e?"Tutte":"disponibile"===e?"Disponibili":"in_uso"===e?"In Uso":"esaurita"===e?"Esaurite":"Over"},e))})]})})]}),(0,t.jsx)(Z,{items:[{id:"import",label:"Importa Bobine",icon:(0,t.jsx)(G.A,{className:"h-4 w-4"}),action:"import",disabled:!U||U<=0},{id:"export",label:"Esporta Bobine",icon:(0,t.jsx)(W.A,{className:"h-4 w-4"}),action:"export",disabled:!U||U<=0},{id:"separator1",separator:!0},{id:"add_bobina",label:"Aggiungi Bobina",icon:(0,t.jsx)(w.A,{className:"h-4 w-4"}),action:"add_bobina",disabled:!U||U<=0}],onAction:(e,i)=>{switch(e){case"import":ef();break;case"export":eN();break;case"add_bobina":ei(!0)}},children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(n.ZB,{children:["Elenco Bobine (",ey.length,")"]}),(0,t.jsx)(n.BT,{children:"Gestione completa delle bobine con stato utilizzo e metrature. Clicca tasto destro per opzioni aggiuntive."})]}),(0,t.jsxs)(l.$,{size:"sm",onClick:()=>ei(!0),disabled:!U||U<=0,title:!U||U<=0?"Seleziona un cantiere per creare una bobina":"Crea nuova bobina",children:[(0,t.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Nuova Bobina"]})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(c.XI,{children:[(0,t.jsx)(c.A0,{children:(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nd,{children:"Bobina"}),(0,t.jsx)(c.nd,{children:"Utility"}),(0,t.jsx)(c.nd,{children:"Tipologia"}),(0,t.jsx)(c.nd,{children:"Formazione"}),(0,t.jsx)(c.nd,{children:"Metrature"}),(0,t.jsx)(c.nd,{children:"Utilizzo"}),(0,t.jsx)(c.nd,{children:"Stato"}),(0,t.jsx)(c.nd,{children:"Ubicazione"}),(0,t.jsx)(c.nd,{children:"Azioni"})]})}),(0,t.jsx)(c.BF,{children:C?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(D.A,{className:"h-4 w-4 animate-spin"}),"Caricamento bobine..."]})})}):A?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-red-600",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),A]})})}):0===ey.length?(0,t.jsx)(c.Hj,{children:(0,t.jsx)(c.nA,{colSpan:9,className:"text-center py-8 text-slate-500",children:"Nessuna bobina trovata"})}):ey.map(e=>{let i=f(e.metri_residui,e.metri_totali),a=e.stato_bobina||h(e.metri_residui,e.metri_totali),s=v(a);return(0,t.jsxs)(c.Hj,{className:"transition-colors ".concat(s),children:[(0,t.jsx)(c.nA,{className:"font-medium",children:e.numero_bobina||"-"}),(0,t.jsx)(c.nA,{children:e.utility||"-"}),(0,t.jsx)(c.nA,{children:e.tipologia||"-"}),(0,t.jsx)(c.nA,{children:(0,t.jsx)("div",{className:"text-sm font-medium",children:e.sezione||"-"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Residui: ",(0,t.jsx)("span",{className:"font-medium",children:N(e.metri_residui)})]}),(0,t.jsxs)("div",{className:"text-slate-500",children:["Totali: ",N(e.metri_totali)]})]})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"text-sm font-medium",children:[Math.round(i),"%"]})}),(0,t.jsx)(c.nA,{children:e_(e.stato_bobina,e.metri_residui,e.metri_totali)}),(0,t.jsx)(c.nA,{children:(0,t.jsx)(r.E,{variant:"outline",children:e.ubicazione_bobina||"Non specificata"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>eg(e),disabled:!j(a),title:a===g.OVER?"Bobina OVER - Non pu\xf2 accettare nuovi cavi":a===g.TERMINATA?"Bobina terminata - Non pu\xf2 accettare nuovi cavi":"Aggiungi cavo a bobina",className:j(a)?"":"opacity-50 cursor-not-allowed",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>eh(e),title:a===g.OVER?"Modifica bobina (limitata per bobine OVER)":"Modifica bobina",children:(0,t.jsx)(H.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>ep(e),disabled:a===g.OVER||a!==g.DISPONIBILE,title:a===g.OVER?"Bobina OVER - Non pu\xf2 essere eliminata":a!==g.DISPONIBILE?"Solo bobine disponibili possono essere eliminate":"Elimina bobina",className:a===g.OVER||a!==g.DISPONIBILE?"opacity-50 cursor-not-allowed":"",children:(0,t.jsx)(O.A,{className:"h-4 w-4"})})]})})]},e.id_bobina)})})]})})})]})}),ed&&(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,t.jsxs)(d.Fc,{className:"bg-green-50 border-green-200",children:[(0,t.jsx)(J.A,{className:"h-4 w-4 text-green-600"}),(0,t.jsx)(d.TN,{className:"text-green-800",children:ed})]})}),eu&&(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50",children:(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(S.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:eu})]})}),(0,t.jsx)(E,{open:ee,onClose:()=>ei(!1),cantiereId:U,onSuccess:ev,onError:ej}),(0,t.jsx)(F,{open:ea,onClose:()=>et(!1),bobina:eo,cantiereId:U,onSuccess:e=>{em(e),eb()},onError:e=>{ex(e)}}),(0,t.jsx)(L,{open:es,onClose:()=>en(!1),bobina:eo,cantiereId:U,onSuccess:e=>{em(e),eb()},onError:e=>{ex(e)}}),(0,t.jsx)(V,{open:el,onClose:()=>er(!1),bobina:eo,cantiereId:U,onSuccess:ev,onError:ej})]})})})}}},e=>{var i=i=>e(e.s=i);e.O(0,[3455,3464,4295,1587,1807,6830,283,1642,3587,8441,1684,7358],()=>i(30557)),_N_E=e.O()}]);