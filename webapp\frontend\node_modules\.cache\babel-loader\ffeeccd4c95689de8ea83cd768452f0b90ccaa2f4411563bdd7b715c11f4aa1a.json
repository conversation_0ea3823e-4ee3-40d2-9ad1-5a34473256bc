{"ast": null, "code": "/**\n * Utility per la navigazione nell'applicazione\n */\n\n/**\n * Reindirizza alla pagina di visualizzazione cavi con un ritardo opzionale\n * @param {function} navigate - Funzione di navigazione di React Router\n * @param {number} delay - <PERSON><PERSON> in millisecondi prima del reindirizzamento (default: 0)\n */\nexport const redirectToVisualizzaCavi = (navigate, delay = 0) => {\n  if (delay > 0) {\n    setTimeout(() => {\n      navigate('/dashboard/cavi/visualizza');\n    }, delay);\n  } else {\n    navigate('/dashboard/cavi/visualizza');\n  }\n};\n\n/**\n * Ricarica la pagina corrente con un ritardo opzionale\n * @param {number} delay - Ritardo in millisecondi prima del ricaricamento (default: 0)\n */\nexport const reloadPage = (delay = 0) => {\n  if (delay > 0) {\n    setTimeout(() => {\n      window.location.reload();\n    }, delay);\n  } else {\n    window.location.reload();\n  }\n};", "map": {"version": 3, "names": ["redirectToVisualizzaCavi", "navigate", "delay", "setTimeout", "reloadPage", "window", "location", "reload"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/utils/navigationUtils.js"], "sourcesContent": ["/**\n * Utility per la navigazione nell'applicazione\n */\n\n/**\n * Reindirizza alla pagina di visualizzazione cavi con un ritardo opzionale\n * @param {function} navigate - Funzione di navigazione di React Router\n * @param {number} delay - <PERSON><PERSON> in millisecondi prima del reindirizzamento (default: 0)\n */\nexport const redirectToVisualizzaCavi = (navigate, delay = 0) => {\n  if (delay > 0) {\n    setTimeout(() => {\n      navigate('/dashboard/cavi/visualizza');\n    }, delay);\n  } else {\n    navigate('/dashboard/cavi/visualizza');\n  }\n};\n\n/**\n * Ricarica la pagina corrente con un ritardo opzionale\n * @param {number} delay - Ritardo in millisecondi prima del ricaricamento (default: 0)\n */\nexport const reloadPage = (delay = 0) => {\n  if (delay > 0) {\n    setTimeout(() => {\n      window.location.reload();\n    }, delay);\n  } else {\n    window.location.reload();\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,wBAAwB,GAAGA,CAACC,QAAQ,EAAEC,KAAK,GAAG,CAAC,KAAK;EAC/D,IAAIA,KAAK,GAAG,CAAC,EAAE;IACbC,UAAU,CAAC,MAAM;MACfF,QAAQ,CAAC,4BAA4B,CAAC;IACxC,CAAC,EAAEC,KAAK,CAAC;EACX,CAAC,MAAM;IACLD,QAAQ,CAAC,4BAA4B,CAAC;EACxC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMG,UAAU,GAAGA,CAACF,KAAK,GAAG,CAAC,KAAK;EACvC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACbC,UAAU,CAAC,MAAM;MACfE,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC1B,CAAC,EAAEL,KAAK,CAAC;EACX,CAAC,MAAM;IACLG,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAC;EAC1B;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}