from pydantic import BaseModel, Field
from typing import Optional
from datetime import date

class NonConformitaBase(BaseModel):
    """Schema base per le non conformità."""
    codice_nc: str = Field(..., description="Codice univoco della non conformità")
    data_rilevazione: date = Field(..., description="Data di rilevazione")
    tipo_nc: Optional[str] = Field(None, description="Tipo di non conformità")
    descrizione: str = Field(..., description="Descrizione della non conformità")
    riferimento_cavo: Optional[str] = Field(None, description="Riferimento al cavo")
    riferimento_prova: Optional[str] = Field(None, description="Riferimento alla prova")
    azione_correttiva: Optional[str] = Field(None, description="Azione correttiva")
    responsabile_azione: Optional[str] = Field(None, description="Responsabile dell'azione")
    data_scadenza_azione: Optional[date] = Field(None, description="Data scadenza azione")
    data_completamento_azione: Optional[date] = Field(None, description="Data completamento azione")
    esito_riverifica: Optional[str] = Field(None, description="Esito della ri-verifica")
    note_riverifica: Optional[str] = Field(None, description="Note della ri-verifica")
    stato_nc: Optional[str] = Field('APERTA', description="Stato della non conformità")

class NonConformitaCreate(NonConformitaBase):
    """Schema per la creazione di una nuova non conformità."""
    id_certificazione: Optional[int] = Field(None, description="ID della certificazione")
    id_rapporto: Optional[int] = Field(None, description="ID del rapporto")

class NonConformitaUpdate(BaseModel):
    """Schema per l'aggiornamento di una non conformità."""
    codice_nc: Optional[str] = None
    data_rilevazione: Optional[date] = None
    tipo_nc: Optional[str] = None
    descrizione: Optional[str] = None
    riferimento_cavo: Optional[str] = None
    riferimento_prova: Optional[str] = None
    azione_correttiva: Optional[str] = None
    responsabile_azione: Optional[str] = None
    data_scadenza_azione: Optional[date] = None
    data_completamento_azione: Optional[date] = None
    esito_riverifica: Optional[str] = None
    note_riverifica: Optional[str] = None
    stato_nc: Optional[str] = None

class NonConformitaInDB(NonConformitaBase):
    """Schema per la non conformità nel database."""
    id_nc: int
    id_certificazione: Optional[int] = None
    id_rapporto: Optional[int] = None

    class Config:
        from_attributes = True

class NonConformitaResponse(NonConformitaInDB):
    """Schema per la risposta API della non conformità."""
    pass

class NonConformitaListResponse(BaseModel):
    """Schema per la lista delle non conformità."""
    id_nc: int
    codice_nc: str
    data_rilevazione: date
    tipo_nc: Optional[str] = None
    descrizione: str
    riferimento_cavo: Optional[str] = None
    stato_nc: str
    responsabile_azione: Optional[str] = None
    data_scadenza_azione: Optional[date] = None

    class Config:
        from_attributes = True
