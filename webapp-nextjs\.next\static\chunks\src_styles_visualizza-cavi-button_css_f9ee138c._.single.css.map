{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/visualizza-cavi-button.css"], "sourcesContent": ["/* Stile personalizzato per i pulsanti \"Visualizza Cavi\" */\n.visualizza-cavi-button {\n  position: relative;\n  height: 50px;\n  padding: 0 30px;\n  border: 2px solid #000;\n  background: #e8e8e8;\n  user-select: none;\n  white-space: nowrap;\n  transition: all .05s linear;\n  font-family: inherit;\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  cursor: pointer;\n  text-decoration: none;\n  color: inherit;\n}\n\n.visualizza-cavi-button:before,\n.visualizza-cavi-button:after {\n  content: \"\";\n  position: absolute;\n  background: #e8e8e8;\n  transition: all .2s linear;\n}\n\n.visualizza-cavi-button:before {\n  width: calc(100% + 6px);\n  height: calc(100% - 16px);\n  top: 8px;\n  left: -3px;\n}\n\n.visualizza-cavi-button:after {\n  width: calc(100% - 16px);\n  height: calc(100% + 6px);\n  top: -3px;\n  left: 8px;\n}\n\n.visualizza-cavi-button:hover {\n  cursor: crosshair;\n}\n\n.visualizza-cavi-button:active {\n  transform: scale(0.95);\n}\n\n.visualizza-cavi-button:hover:before {\n  height: calc(100% - 32px);\n  top: 16px;\n}\n\n.visualizza-cavi-button:hover:after {\n  width: calc(100% - 32px);\n  left: 16px;\n}\n\n.visualizza-cavi-button span {\n  font-size: 15px;\n  z-index: 3;\n  position: relative;\n  font-weight: 600;\n}\n\n/* Variante per stato attivo */\n.visualizza-cavi-button.active {\n  background: #d4edda;\n  border-color: #28a745;\n}\n\n.visualizza-cavi-button.active:before,\n.visualizza-cavi-button.active:after {\n  background: #d4edda;\n}\n\n/* Variante per icone */\n.visualizza-cavi-button .icon {\n  z-index: 3;\n  position: relative;\n  margin-right: 8px;\n}\n\n/* Responsive adjustments */\n@media (max-width: 768px) {\n  .visualizza-cavi-button {\n    height: 40px;\n    padding: 0 20px;\n  }\n  \n  .visualizza-cavi-button span {\n    font-size: 14px;\n  }\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;;AAkBA;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;;;AAQA;;;;;AAKA;;;;AAMA;;;;;;AAOA;EACE;;;;;EAKA"}}]}