"""
Servizio per la generazione di PDF conformi CEI 64-8
Genera rapporti generali di collaudo e certificati singoli professionali
"""

import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4, letter
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch, cm
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY
from reportlab.pdfgen import canvas
from reportlab.lib.utils import ImageReader

from sqlalchemy.orm import Session
from backend.models.rapporto_generale_collaudo import RapportoGeneraleCollaudo
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.models.prova_dettagliata import ProvaDettagliata
from backend.models.non_conformita import NonConformita
from backend.models.strumento_certificato import StrumentoCertificato
from backend.models.cantiere import Cantiere
from backend.services.pdf_certificato_singolo_helpers import PDFCertificatoSingoloHelpers


class PDFHeaderFooter:
    """Classe per gestire header e footer dei PDF"""
    
    def __init__(self, title: str, subtitle: str = ""):
        self.title = title
        self.subtitle = subtitle
    
    def draw_header_footer(self, canvas, doc):
        """Disegna header e footer su ogni pagina"""
        canvas.saveState()
        
        # Header
        canvas.setFont('Helvetica-Bold', 16)
        canvas.drawString(2*cm, A4[1] - 2*cm, self.title)
        
        if self.subtitle:
            canvas.setFont('Helvetica', 12)
            canvas.drawString(2*cm, A4[1] - 2.5*cm, self.subtitle)
        
        # Linea separatrice header
        canvas.setStrokeColor(colors.grey)
        canvas.line(2*cm, A4[1] - 3*cm, A4[0] - 2*cm, A4[1] - 3*cm)
        
        # Footer
        canvas.setFont('Helvetica', 9)
        canvas.drawString(2*cm, 1.5*cm, f"Generato il: {datetime.now().strftime('%d/%m/%Y %H:%M')}")
        canvas.drawRightString(A4[0] - 2*cm, 1.5*cm, f"Pagina {doc.page}")
        
        # Linea separatrice footer
        canvas.line(2*cm, 2*cm, A4[0] - 2*cm, 2*cm)
        
        canvas.restoreState()


class PDFCEI64_8Service:
    """Servizio principale per la generazione di PDF CEI 64-8"""
    
    def __init__(self, db: Session):
        self.db = db
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        self.certificato_helpers = PDFCertificatoSingoloHelpers(self)
    
    def _setup_custom_styles(self):
        """Configura stili personalizzati per i PDF"""
        # Stile per titoli principali
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        ))
        
        # Stile per sottotitoli
        self.styles.add(ParagraphStyle(
            name='CustomSubtitle',
            parent=self.styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            textColor=colors.darkblue
        ))
        
        # Stile per sezioni
        self.styles.add(ParagraphStyle(
            name='SectionHeader',
            parent=self.styles['Heading3'],
            fontSize=12,
            spaceAfter=12,
            textColor=colors.darkgreen,
            borderWidth=1,
            borderColor=colors.darkgreen,
            borderPadding=5,
            backColor=colors.lightgrey
        ))
        
        # Stile per testo normale con giustificazione
        self.styles.add(ParagraphStyle(
            name='JustifiedText',
            parent=self.styles['Normal'],
            alignment=TA_JUSTIFY,
            spaceAfter=12
        ))
    
    def generate_rapporto_generale_pdf(self, rapporto_id: int) -> str:
        """
        Genera il PDF del Rapporto Generale di Collaudo
        
        Args:
            rapporto_id: ID del rapporto generale
            
        Returns:
            str: Percorso del file PDF generato
        """
        # Recupera il rapporto dal database
        rapporto = self.db.query(RapportoGeneraleCollaudo).filter(
            RapportoGeneraleCollaudo.id_rapporto == rapporto_id
        ).first()
        
        if not rapporto:
            raise ValueError(f"Rapporto con ID {rapporto_id} non trovato")
        
        # Recupera il cantiere
        cantiere = self.db.query(Cantiere).filter(
            Cantiere.id_cantiere == rapporto.id_cantiere
        ).first()
        
        # Recupera le certificazioni associate
        certificazioni = self.db.query(CertificazioneCavo).filter(
            CertificazioneCavo.id_rapporto == rapporto_id
        ).all()
        
        # Recupera le non conformità
        non_conformita = self.db.query(NonConformita).filter(
            NonConformita.id_rapporto == rapporto_id
        ).all()
        
        # Crea il file PDF
        filename = f"rapporto_generale_{rapporto.numero_rapporto}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join("static", "pdf", filename)
        
        # Assicurati che la directory esista
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # Crea il documento PDF
        doc = SimpleDocTemplate(
            filepath,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=4*cm,
            bottomMargin=3*cm
        )
        
        # Configura header e footer
        header_footer = PDFHeaderFooter(
            title="RAPPORTO GENERALE DI MESSA IN SERVIZIO E COLLAUDO CAVI",
            subtitle=f"Conforme CEI 64-8 - Rapporto N. {rapporto.numero_rapporto}"
        )
        
        # Costruisci il contenuto
        story = []
        
        # Titolo principale
        story.append(Paragraph(
            f"RAPPORTO GENERALE DI COLLAUDO<br/>N. {rapporto.numero_rapporto}",
            self.styles['CustomTitle']
        ))
        story.append(Spacer(1, 20))
        
        # Sezione 1: Dati Generali del Progetto
        story.extend(self._build_dati_generali_section(rapporto, cantiere))
        
        # Sezione 2: Riferimenti Normativi
        story.extend(self._build_riferimenti_normativi_section(rapporto))
        
        # Sezione 3: Scopo e Ambito
        story.extend(self._build_scopo_ambito_section(rapporto))
        
        # Sezione 4: Strumentazione
        story.extend(self._build_strumentazione_section(rapporto))
        
        # Sezione 5: Riepilogo Prove
        story.extend(self._build_riepilogo_prove_section(rapporto, certificazioni))
        
        # Sezione 6: Non Conformità
        if non_conformita:
            story.extend(self._build_non_conformita_section(non_conformita))
        
        # Sezione 7: Conclusioni
        story.extend(self._build_conclusioni_section(rapporto))
        
        # Sezione 8: Firme
        story.extend(self._build_firme_section(rapporto))
        
        # Genera il PDF
        doc.build(story, onFirstPage=header_footer.draw_header_footer, 
                 onLaterPages=header_footer.draw_header_footer)
        
        return filepath
    
    def _build_dati_generali_section(self, rapporto: RapportoGeneraleCollaudo, cantiere: Cantiere) -> List:
        """Costruisce la sezione Dati Generali del Progetto"""
        content = []
        
        content.append(Paragraph("1. DATI GENERALI DEL PROGETTO/COMMESSA", self.styles['SectionHeader']))
        
        # Tabella con i dati generali
        data = [
            ['Nome Progetto:', rapporto.nome_progetto or '-'],
            ['Codice Progetto:', rapporto.codice_progetto or '-'],
            ['Cliente Finale:', rapporto.cliente_finale or '-'],
            ['Località Impianto:', rapporto.localita_impianto or cantiere.nome],
            ['Società Installatrice:', rapporto.societa_installatrice or '-'],
            ['Società Responsabile Prove:', rapporto.societa_responsabile_prove or '-'],
            ['Data Inizio Collaudo:', rapporto.data_inizio_collaudo.strftime('%d/%m/%Y') if rapporto.data_inizio_collaudo else '-'],
            ['Data Fine Collaudo:', rapporto.data_fine_collaudo.strftime('%d/%m/%Y') if rapporto.data_fine_collaudo else '-'],
            ['Data Rapporto:', rapporto.data_rapporto.strftime('%d/%m/%Y')],
        ]
        
        table = Table(data, colWidths=[6*cm, 10*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        content.append(table)
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_riferimenti_normativi_section(self, rapporto: RapportoGeneraleCollaudo) -> List:
        """Costruisce la sezione Riferimenti Normativi"""
        content = []
        
        content.append(Paragraph("2. RIFERIMENTI NORMATIVI E DOCUMENTALI", self.styles['SectionHeader']))
        
        # Normative standard
        normative_standard = [
            "CEI 64-8 \"Impianti elettrici utilizzatori a tensione nominale non superiore a 1000 V in corrente alternata e a 1500 V in corrente continua\"",
            "IEC 60364 \"Low-voltage electrical installations\"",
            "CEI EN 61936-1 \"Impianti elettrici con tensione superiore a 1 kV in c.a.\"",
            "CEI EN 50522 \"Messa a terra degli impianti elettrici a tensione superiore a 1 kV in c.a.\""
        ]
        
        content.append(Paragraph("<b>Normative Applicate:</b>", self.styles['Normal']))
        for norma in normative_standard:
            content.append(Paragraph(f"• {norma}", self.styles['Normal']))
        
        # Normative aggiuntive se specificate
        if rapporto.normative_applicate:
            try:
                normative_aggiuntive = json.loads(rapporto.normative_applicate)
                if normative_aggiuntive:
                    content.append(Paragraph("<b>Normative Aggiuntive:</b>", self.styles['Normal']))
                    for norma in normative_aggiuntive:
                        content.append(Paragraph(f"• {norma}", self.styles['Normal']))
            except:
                pass
        
        content.append(Spacer(1, 15))
        
        # Documentazione di progetto
        if rapporto.documentazione_progetto:
            try:
                documenti = json.loads(rapporto.documentazione_progetto)
                if documenti:
                    content.append(Paragraph("<b>Documentazione di Progetto:</b>", self.styles['Normal']))
                    for doc in documenti:
                        content.append(Paragraph(f"• {doc}", self.styles['Normal']))
            except:
                pass
        
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_scopo_ambito_section(self, rapporto: RapportoGeneraleCollaudo) -> List:
        """Costruisce la sezione Scopo e Ambito"""
        content = []
        
        content.append(Paragraph("3. SCOPO E AMBITO DEL COLLAUDO", self.styles['SectionHeader']))
        
        if rapporto.scopo_rapporto:
            content.append(Paragraph("<b>Scopo:</b>", self.styles['Normal']))
            content.append(Paragraph(rapporto.scopo_rapporto, self.styles['JustifiedText']))
        
        if rapporto.ambito_collaudo:
            content.append(Paragraph("<b>Ambito:</b>", self.styles['Normal']))
            content.append(Paragraph(rapporto.ambito_collaudo, self.styles['JustifiedText']))
        
        # Condizioni ambientali
        if rapporto.temperatura_ambiente or rapporto.umidita_ambiente:
            content.append(Paragraph("<b>Condizioni Ambientali durante le Prove:</b>", self.styles['Normal']))
            condizioni = []
            if rapporto.temperatura_ambiente:
                condizioni.append(f"Temperatura: {rapporto.temperatura_ambiente}°C")
            if rapporto.umidita_ambiente:
                condizioni.append(f"Umidità relativa: {rapporto.umidita_ambiente}%")
            content.append(Paragraph(" - ".join(condizioni), self.styles['Normal']))
        
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_strumentazione_section(self, rapporto: RapportoGeneraleCollaudo) -> List:
        """Costruisce la sezione Strumentazione"""
        content = []
        
        content.append(Paragraph("4. STRUMENTAZIONE DI MISURA UTILIZZATA", self.styles['SectionHeader']))
        
        # Recupera gli strumenti utilizzati nelle certificazioni di questo rapporto
        strumenti_utilizzati = self.db.query(StrumentoCertificato).join(
            CertificazioneCavo, StrumentoCertificato.id_strumento == CertificazioneCavo.id_strumento
        ).filter(
            CertificazioneCavo.id_rapporto == rapporto.id_rapporto
        ).distinct().all()
        
        if strumenti_utilizzati:
            # Tabella strumenti
            headers = ['Strumento', 'Marca/Modello', 'N. Serie', 'Data Taratura', 'Scadenza', 'Ente Certificatore']
            data = [headers]
            
            for strumento in strumenti_utilizzati:
                data.append([
                    strumento.nome or '-',
                    f"{strumento.marca or ''} {strumento.modello or ''}".strip() or '-',
                    strumento.numero_serie or '-',
                    strumento.data_calibrazione.strftime('%d/%m/%Y') if strumento.data_calibrazione else '-',
                    strumento.data_scadenza_calibrazione.strftime('%d/%m/%Y') if strumento.data_scadenza_calibrazione else '-',
                    strumento.ente_certificatore or '-'
                ])
            
            table = Table(data, colWidths=[3*cm, 3*cm, 2.5*cm, 2.5*cm, 2.5*cm, 3*cm])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            content.append(table)
        else:
            content.append(Paragraph("Nessuno strumento registrato per questo rapporto.", self.styles['Normal']))
        
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_riepilogo_prove_section(self, rapporto: RapportoGeneraleCollaudo, certificazioni: List[CertificazioneCavo]) -> List:
        """Costruisce la sezione Riepilogo delle Prove"""
        content = []
        
        content.append(Paragraph("5. RIEPILOGO ESITI DELLE PROVE", self.styles['SectionHeader']))
        
        # Statistiche generali
        data_stats = [
            ['Numero Totale Cavi Testati:', str(rapporto.numero_cavi_totali)],
            ['Cavi Conformi:', str(rapporto.numero_cavi_conformi)],
            ['Cavi Non Conformi:', str(rapporto.numero_cavi_non_conformi)],
            ['Percentuale Conformità:', f"{(rapporto.numero_cavi_conformi / max(rapporto.numero_cavi_totali, 1) * 100):.1f}%"]
        ]
        
        table_stats = Table(data_stats, colWidths=[8*cm, 4*cm])
        table_stats.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
            ('FONTNAME', (1, 0), (1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))
        
        content.append(table_stats)
        content.append(Spacer(1, 15))
        
        # Elenco certificazioni (limitato per spazio)
        if certificazioni:
            content.append(Paragraph("<b>Elenco Certificazioni (prime 20):</b>", self.styles['Normal']))
            
            headers = ['ID Cavo', 'Data Cert.', 'Lunghezza (m)', 'Esito']
            data_cert = [headers]
            
            for cert in certificazioni[:20]:  # Limita a 20 per non appesantire il PDF
                data_cert.append([
                    cert.id_cavo,
                    cert.data_certificazione.strftime('%d/%m/%Y'),
                    str(cert.lunghezza_misurata or '-'),
                    cert.esito_complessivo or 'N/D'
                ])
            
            table_cert = Table(data_cert, colWidths=[4*cm, 3*cm, 3*cm, 3*cm])
            table_cert.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))
            
            content.append(table_cert)
            
            if len(certificazioni) > 20:
                content.append(Paragraph(f"... e altre {len(certificazioni) - 20} certificazioni (vedi allegati)", self.styles['Normal']))
        
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_non_conformita_section(self, non_conformita: List[NonConformita]) -> List:
        """Costruisce la sezione Non Conformità"""
        content = []
        
        content.append(Paragraph("6. NON CONFORMITÀ RILEVATE E AZIONI CORRETTIVE", self.styles['SectionHeader']))
        
        if not non_conformita:
            content.append(Paragraph("Nessuna non conformità rilevata durante le prove.", self.styles['Normal']))
        else:
            for i, nc in enumerate(non_conformita, 1):
                content.append(Paragraph(f"<b>NC {i:02d} - {nc.codice_nc}</b>", self.styles['Normal']))
                
                nc_data = [
                    ['Tipo:', nc.tipo_nc or '-'],
                    ['Data Rilevazione:', nc.data_rilevazione.strftime('%d/%m/%Y')],
                    ['Descrizione:', nc.descrizione],
                    ['Riferimento Cavo:', nc.riferimento_cavo or '-'],
                    ['Azione Correttiva:', nc.azione_correttiva or 'Da definire'],
                    ['Responsabile:', nc.responsabile_azione or '-'],
                    ['Stato:', nc.stato_nc],
                ]
                
                table_nc = Table(nc_data, colWidths=[4*cm, 12*cm])
                table_nc.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (0, -1), colors.lightyellow),
                    ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 0), (0, -1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, -1), 9),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    ('VALIGN', (0, 0), (-1, -1), 'TOP'),
                ]))
                
                content.append(table_nc)
                content.append(Spacer(1, 10))
        
        content.append(Spacer(1, 20))
        
        return content
    
    def _build_conclusioni_section(self, rapporto: RapportoGeneraleCollaudo) -> List:
        """Costruisce la sezione Conclusioni"""
        content = []
        
        content.append(Paragraph("7. CONCLUSIONI E DICHIARAZIONE DI CONFORMITÀ", self.styles['SectionHeader']))
        
        if rapporto.conclusioni:
            content.append(Paragraph(rapporto.conclusioni, self.styles['JustifiedText']))
        
        # Dichiarazione di conformità
        if rapporto.dichiarazione_conformita:
            dichiarazione = """
            Si dichiara che i cavi oggetto del presente rapporto sono stati installati e verificati 
            in conformità alle normative tecniche vigenti (CEI 64-8, IEC 60364) e alle specifiche 
            di progetto, e risultano idonei alla messa in servizio.
            """
            content.append(Paragraph("<b>DICHIARAZIONE DI CONFORMITÀ:</b>", self.styles['Normal']))
            content.append(Paragraph(dichiarazione, self.styles['JustifiedText']))
        else:
            content.append(Paragraph(
                "<b>ATTENZIONE:</b> Il presente rapporto evidenzia non conformità che devono essere risolte prima della messa in servizio.",
                self.styles['Normal']
            ))
        
        content.append(Spacer(1, 30))
        
        return content
    
    def _build_firme_section(self, rapporto: RapportoGeneraleCollaudo) -> List:
        """Costruisce la sezione Firme"""
        content = []
        
        content.append(Paragraph("8. FIRME E RESPONSABILITÀ", self.styles['SectionHeader']))
        
        # Tabella firme
        data_firme = [
            ['RESPONSABILE TECNICO DELLE VERIFICHE', 'RAPPRESENTANTE DEL CLIENTE'],
            ['', ''],
            [f"Nome: {rapporto.responsabile_tecnico or '___________________'}", f"Nome: {rapporto.rappresentante_cliente or '___________________'}"],
            ['', ''],
            ['Firma: ___________________', 'Firma: ___________________'],
            ['', ''],
            [f"Data: {datetime.now().strftime('%d/%m/%Y')}", f"Data: {datetime.now().strftime('%d/%m/%Y')}"],
        ]
        
        table_firme = Table(data_firme, colWidths=[8*cm, 8*cm])
        table_firme.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.lightblue),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))
        
        content.append(table_firme)

        return content

    def generate_certificato_singolo_pdf(self, certificazione_id: int) -> str:
        """
        Genera il PDF del Certificato di Prova Singolo Cavo

        Args:
            certificazione_id: ID della certificazione

        Returns:
            str: Percorso del file PDF generato
        """
        # Recupera la certificazione dal database
        certificazione = self.db.query(CertificazioneCavo).filter(
            CertificazioneCavo.id_certificazione == certificazione_id
        ).first()

        if not certificazione:
            raise ValueError(f"Certificazione con ID {certificazione_id} non trovata")

        # Recupera il cantiere
        cantiere = self.db.query(Cantiere).filter(
            Cantiere.id_cantiere == certificazione.id_cantiere
        ).first()

        # Recupera le prove dettagliate
        prove = self.db.query(ProvaDettagliata).filter(
            ProvaDettagliata.id_certificazione == certificazione_id
        ).all()

        # Recupera lo strumento utilizzato
        strumento = None
        if certificazione.id_strumento:
            strumento = self.db.query(StrumentoCertificato).filter(
                StrumentoCertificato.id_strumento == certificazione.id_strumento
            ).first()

        # Crea il file PDF
        filename = f"certificato_singolo_{certificazione.numero_certificato}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"

        # Usa un percorso assoluto basato sulla directory corrente
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))  # Vai alla root del progetto
        static_dir = os.path.join(base_dir, "static", "temp")
        filepath = os.path.join(static_dir, filename)

        # Assicurati che la directory esista
        os.makedirs(static_dir, exist_ok=True)

        print(f"🔍 DEBUG PDF - Generando PDF per certificazione {certificazione_id}")
        print(f"🔍 DEBUG PDF - Percorso file: {filepath}")
        print(f"🔍 DEBUG PDF - Directory esiste: {os.path.exists(static_dir)}")

        # Crea il documento PDF
        doc = SimpleDocTemplate(
            filepath,
            pagesize=A4,
            rightMargin=2*cm,
            leftMargin=2*cm,
            topMargin=4*cm,
            bottomMargin=3*cm
        )

        # Configura header e footer
        header_footer = PDFHeaderFooter(
            title="CERTIFICATO DI PROVA SINGOLO CAVO",
            subtitle=f"Conforme CEI 64-8 - Certificato N. {certificazione.numero_certificato}"
        )

        # Costruisci il contenuto
        story = []

        # Il titolo è già nell'header, non serve ripeterlo
        # story.append(Paragraph(
        #     f"CERTIFICATO DI PROVA SINGOLO CAVO<br/>N. {certificazione.numero_certificato}",
        #     self.styles['CustomTitle']
        # ))
        # story.append(Spacer(1, 20))

        # Sezione 1: Identificazione del Certificato
        story.extend(self.certificato_helpers.build_identificazione_certificato_section(certificazione, cantiere))

        # Sezione 2: Informazioni Generali
        story.extend(self.certificato_helpers.build_informazioni_generali_section(cantiere))

        # Sezione 3: Dati Identificativi del Cavo
        story.extend(self.certificato_helpers.build_dati_cavo_section(certificazione))

        # Sezione 4: Prove Eseguite e Risultati
        story.extend(self.certificato_helpers.build_prove_risultati_section(certificazione, prove))

        # Sezione 5: Strumentazione Utilizzata
        if strumento:
            story.extend(self.certificato_helpers.build_strumentazione_certificato_section(strumento))

        # Sezione 6: Firme e Responsabilità
        story.extend(self.certificato_helpers.build_firme_certificato_section(certificazione))

        # Genera il PDF
        try:
            print(f"🔍 DEBUG PDF - Iniziando generazione PDF...")
            doc.build(story, onFirstPage=header_footer.draw_header_footer,
                     onLaterPages=header_footer.draw_header_footer)

            # Verifica che il file sia stato creato
            if os.path.exists(filepath):
                file_size = os.path.getsize(filepath)
                print(f"✅ DEBUG PDF - PDF generato con successo!")
                print(f"   📁 Percorso: {filepath}")
                print(f"   📊 Dimensione: {file_size} bytes")

                # Verifica che il file non sia vuoto
                if file_size > 1000:
                    print("✅ DEBUG PDF - File PDF sembra valido (dimensione > 1KB)")
                else:
                    print(f"⚠️ DEBUG PDF - File PDF potrebbe essere corrotto (dimensione: {file_size} bytes)")
            else:
                print(f"❌ DEBUG PDF - File PDF non trovato dopo la generazione: {filepath}")

        except Exception as e:
            print(f"❌ DEBUG PDF - Errore durante la generazione: {e}")
            import traceback
            traceback.print_exc()
            raise

        return filepath
