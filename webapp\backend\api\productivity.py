"""
API endpoints per il sistema di produttività dei cavi elettrici.
Implementa le specifiche del progetto di produttività secondo Task 2.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from datetime import datetime, timedelta

from ..core.security import get_current_active_user
from ..database import get_db
from ..models.user import User
from ..models.work_log import WorkLog
from ..models.responsabile import Responsabile
from ..models.tipologie_cavi import TipologiaCavo
from ..schemas.work_log import (
    WorkLogCreate, WorkLogUpdate, WorkLogResponse, WorkLogListResponse,
    ProductivityHistoricalRequest, ProductivityHistoricalResponse,
    PredictionEstimationRequest, PredictionEstimationResponse,
    ActivityTypeEnum, EnvironmentalConditionsEnum, ToolsUsedEnum
)

router = APIRouter()

# Coefficienti di correzione per le condizioni ambientali e strumenti
CORRECTION_COEFFICIENTS = {
    "environmental_conditions": {
        "Normale": 1.0,
        "Spazi Ristretti": 0.80,
        "In Altezza": 0.85,
        "Esterno": 0.90
    },
    "tools_used": {
        "Manuale": 1.0,
        "Automatico": 1.25
    },
    "experience_level": {
        "Apprentice": 0.70,
        "Junior": 0.85,
        "Senior": 1.0
    }
}


@router.post("/work-logs", response_model=WorkLogResponse, status_code=status.HTTP_201_CREATED)
async def create_work_log(
    work_log: WorkLogCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Crea un nuovo work log entry.
    Implementa: POST /api/v1/work-logs
    """
    try:
        # Verifica che l'operatore esista
        operator = db.query(Responsabile).filter(
            Responsabile.id_responsabile == work_log.operator_id,
            Responsabile.attivo == True
        ).first()
        
        if not operator:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Operatore non trovato o non attivo"
            )
        
        # Verifica che il tipo di cavo esista (se specificato)
        if work_log.cable_type_id:
            cable_type = db.query(TipologiaCavo).filter(
                TipologiaCavo.id_tipologia == work_log.cable_type_id,
                TipologiaCavo.disponibile == True
            ).first()
            
            if not cable_type:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Tipo di cavo non trovato o non disponibile"
                )
        
        # Calcola automaticamente la durata se non fornita
        if not work_log.duration_minutes:
            delta = work_log.end_timestamp - work_log.start_timestamp
            work_log.duration_minutes = int(delta.total_seconds() / 60)
        
        # Crea il work log
        db_work_log = WorkLog(**work_log.dict())
        db.add(db_work_log)
        db.commit()
        db.refresh(db_work_log)
        
        # Prepara la risposta con dati calcolati
        response_data = WorkLogResponse.from_orm(db_work_log)
        response_data.productivity_per_hour = db_work_log.productivity_per_hour
        response_data.productivity_per_person_per_hour = db_work_log.productivity_per_person_per_hour
        response_data.total_man_hours = db_work_log.total_man_hours
        
        return response_data
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella creazione del work log: {str(e)}"
        )


@router.get("/work-logs", response_model=WorkLogListResponse)
async def get_work_logs(
    page: int = Query(1, ge=1, description="Numero di pagina"),
    per_page: int = Query(50, ge=1, le=100, description="Elementi per pagina"),
    operator_id: Optional[int] = Query(None, description="Filtra per operatore"),
    cable_type_id: Optional[int] = Query(None, description="Filtra per tipo di cavo"),
    activity_type: Optional[ActivityTypeEnum] = Query(None, description="Filtra per tipo di attività"),
    id_cantiere: Optional[int] = Query(None, description="Filtra per cantiere"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene la lista dei work logs con filtri opzionali.
    """
    try:
        # Costruisci la query base
        query = db.query(WorkLog)
        
        # Applica filtri
        if operator_id:
            query = query.filter(WorkLog.operator_id == operator_id)
        if cable_type_id:
            query = query.filter(WorkLog.cable_type_id == cable_type_id)
        if activity_type:
            query = query.filter(WorkLog.activity_type == activity_type.value)
        if id_cantiere:
            query = query.filter(WorkLog.id_cantiere == id_cantiere)
        
        # Conta il totale
        total_count = query.count()
        
        # Applica paginazione
        offset = (page - 1) * per_page
        work_logs = query.order_by(WorkLog.created_at.desc()).offset(offset).limit(per_page).all()
        
        # Prepara la risposta con dati calcolati
        work_logs_response = []
        for wl in work_logs:
            wl_response = WorkLogResponse.from_orm(wl)
            wl_response.productivity_per_hour = wl.productivity_per_hour
            wl_response.productivity_per_person_per_hour = wl.productivity_per_person_per_hour
            wl_response.total_man_hours = wl.total_man_hours
            work_logs_response.append(wl_response)
        
        return WorkLogListResponse(
            work_logs=work_logs_response,
            total_count=total_count,
            page=page,
            per_page=per_page
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero dei work logs: {str(e)}"
        )


@router.get("/productivity/historical", response_model=ProductivityHistoricalResponse)
async def get_historical_productivity(
    cable_type_id: Optional[int] = Query(None, description="Filtra per tipo di cavo"),
    activity_type: Optional[ActivityTypeEnum] = Query(None, description="Filtra per tipo di attività"),
    experience_level: Optional[str] = Query(None, description="Filtra per livello di esperienza"),
    environmental_conditions: Optional[EnvironmentalConditionsEnum] = Query(None, description="Filtra per condizioni ambientali"),
    tools_used: Optional[ToolsUsedEnum] = Query(None, description="Filtra per strumenti utilizzati"),
    start_date: Optional[datetime] = Query(None, description="Data di inizio"),
    end_date: Optional[datetime] = Query(None, description="Data di fine"),
    id_cantiere: Optional[int] = Query(None, description="Filtra per cantiere"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Calcola la produttività storica media basata sui log passati.
    Implementa: GET /api/v1/productivity/historical
    """
    try:
        # Costruisci la query base con JOIN per ottenere experience_level
        query = db.query(
            func.sum(WorkLog.quantity).label('total_quantity'),
            func.sum(WorkLog.duration_minutes * WorkLog.number_of_operators_on_task / 60.0).label('total_man_hours'),
            func.count(WorkLog.id).label('number_of_logs')
        ).join(Responsabile, WorkLog.operator_id == Responsabile.id_responsabile)
        
        # Applica filtri
        filters_applied = {}
        
        if cable_type_id:
            query = query.filter(WorkLog.cable_type_id == cable_type_id)
            filters_applied['cable_type_id'] = cable_type_id
            
        if activity_type:
            query = query.filter(WorkLog.activity_type == activity_type.value)
            filters_applied['activity_type'] = activity_type.value
            
        if experience_level:
            query = query.filter(Responsabile.experience_level == experience_level)
            filters_applied['experience_level'] = experience_level
            
        if environmental_conditions:
            query = query.filter(WorkLog.environmental_conditions == environmental_conditions.value)
            filters_applied['environmental_conditions'] = environmental_conditions.value
            
        if tools_used:
            query = query.filter(WorkLog.tools_used == tools_used.value)
            filters_applied['tools_used'] = tools_used.value
            
        if start_date:
            query = query.filter(WorkLog.start_timestamp >= start_date)
            filters_applied['start_date'] = start_date.isoformat()
            
        if end_date:
            query = query.filter(WorkLog.end_timestamp <= end_date)
            filters_applied['end_date'] = end_date.isoformat()
            
        if id_cantiere:
            query = query.filter(WorkLog.id_cantiere == id_cantiere)
            filters_applied['id_cantiere'] = id_cantiere
        
        # Esegui la query
        result = query.first()
        
        if not result or not result.total_quantity or not result.total_man_hours:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Nessun dato trovato per i filtri specificati"
            )
        
        # Calcola la produttività
        calculated_productivity = result.total_quantity / result.total_man_hours
        
        # Determina l'unità di misura basata sul tipo di attività
        if activity_type == ActivityTypeEnum.POSA:
            unit = "m/hour/operator"
        else:
            unit = "units/hour/operator"
        
        return ProductivityHistoricalResponse(
            calculated_productivity=calculated_productivity,
            unit=unit,
            total_quantity=result.total_quantity,
            total_man_hours=result.total_man_hours,
            number_of_logs=result.number_of_logs,
            filters_applied=filters_applied
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel calcolo della produttività storica: {str(e)}"
        )


@router.post("/predict/estimation", response_model=PredictionEstimationResponse)
async def predict_estimation(
    request: PredictionEstimationRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Fornisce una stima predittiva per un nuovo lavoro.
    Implementa: POST /api/v1/predict/estimation
    """
    try:
        # Step 1: Ottieni la produttività base dal database o dai valori predefiniti
        base_productivity = None

        # Prova a ottenere la produttività base dal tipo di cavo
        if request.cable_type_id:
            cable_type = db.query(TipologiaCavo).filter(
                TipologiaCavo.id_tipologia == request.cable_type_id
            ).first()

            if cable_type:
                if request.activity_type == ActivityTypeEnum.POSA:
                    base_productivity = cable_type.base_productivity_meters_per_hour
                elif request.activity_type in [ActivityTypeEnum.COLLEGAMENTO, ActivityTypeEnum.CERTIFICAZIONE]:
                    base_productivity = cable_type.base_productivity_connections_per_hour

        # Se non disponibile, calcola dalla produttività storica
        if not base_productivity:
            try:
                # Costruisci query per produttività storica simile
                query = db.query(
                    func.sum(WorkLog.quantity).label('total_quantity'),
                    func.sum(WorkLog.duration_minutes * WorkLog.number_of_operators_on_task / 60.0).label('total_man_hours')
                ).join(Responsabile, WorkLog.operator_id == Responsabile.id_responsabile)

                # Filtra per attività simile
                query = query.filter(WorkLog.activity_type == request.activity_type.value)

                # Filtra per tipo di cavo se specificato
                if request.cable_type_id:
                    query = query.filter(WorkLog.cable_type_id == request.cable_type_id)

                # Filtra per livello di esperienza se specificato
                if request.experience_level:
                    query = query.filter(Responsabile.experience_level == request.experience_level)

                result = query.first()

                if result and result.total_quantity and result.total_man_hours:
                    base_productivity = result.total_quantity / result.total_man_hours
                else:
                    # Valori di fallback basati sul tipo di attività
                    if request.activity_type == ActivityTypeEnum.POSA:
                        base_productivity = 25.0  # metri/ora default
                    else:
                        base_productivity = 8.0   # collegamenti/ora default

            except Exception:
                # Valori di fallback in caso di errore
                if request.activity_type == ActivityTypeEnum.POSA:
                    base_productivity = 25.0
                else:
                    base_productivity = 8.0

        # Step 2: Applica i coefficienti di correzione
        correction_factors = {}

        # Fattore per condizioni ambientali
        env_factor = CORRECTION_COEFFICIENTS["environmental_conditions"].get(
            request.environmental_conditions.value, 1.0
        )
        correction_factors["environmental_conditions"] = env_factor

        # Fattore per strumenti utilizzati
        tools_factor = CORRECTION_COEFFICIENTS["tools_used"].get(
            request.tools_used.value, 1.0
        )
        correction_factors["tools_used"] = tools_factor

        # Fattore per livello di esperienza
        exp_factor = 1.0
        if request.experience_level:
            exp_factor = CORRECTION_COEFFICIENTS["experience_level"].get(
                request.experience_level, 1.0
            )
        correction_factors["experience_level"] = exp_factor

        # Step 3: Calcola la produttività attesa
        expected_productivity_per_operator = base_productivity * env_factor * tools_factor * exp_factor

        # Step 4: Calcola le stime temporali
        estimated_total_man_hours = request.quantity_required / expected_productivity_per_operator
        estimated_time_for_team_hours = estimated_total_man_hours / request.number_of_operators

        return PredictionEstimationResponse(
            inputs=request,
            base_productivity=base_productivity,
            expected_productivity_per_operator=expected_productivity_per_operator,
            estimated_total_man_hours=estimated_total_man_hours,
            estimated_time_for_team_hours=estimated_time_for_team_hours,
            correction_factors=correction_factors
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella stima predittiva: {str(e)}"
        )


@router.get("/work-logs/{work_log_id}", response_model=WorkLogResponse)
async def get_work_log(
    work_log_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene un work log specifico per ID.
    """
    work_log = db.query(WorkLog).filter(WorkLog.id == work_log_id).first()

    if not work_log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Work log non trovato"
        )

    # Prepara la risposta con dati calcolati
    response_data = WorkLogResponse.from_orm(work_log)
    response_data.productivity_per_hour = work_log.productivity_per_hour
    response_data.productivity_per_person_per_hour = work_log.productivity_per_person_per_hour
    response_data.total_man_hours = work_log.total_man_hours

    return response_data


@router.put("/work-logs/{work_log_id}", response_model=WorkLogResponse)
async def update_work_log(
    work_log_id: int,
    work_log_update: WorkLogUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Aggiorna un work log esistente.
    """
    work_log = db.query(WorkLog).filter(WorkLog.id == work_log_id).first()

    if not work_log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Work log non trovato"
        )

    # Aggiorna solo i campi forniti
    update_data = work_log_update.dict(exclude_unset=True)

    # Ricalcola la durata se vengono aggiornati i timestamp
    if 'start_timestamp' in update_data or 'end_timestamp' in update_data:
        start = update_data.get('start_timestamp', work_log.start_timestamp)
        end = update_data.get('end_timestamp', work_log.end_timestamp)
        if start and end and end > start:
            delta = end - start
            update_data['duration_minutes'] = int(delta.total_seconds() / 60)

    for field, value in update_data.items():
        setattr(work_log, field, value)

    try:
        db.commit()
        db.refresh(work_log)

        # Prepara la risposta con dati calcolati
        response_data = WorkLogResponse.from_orm(work_log)
        response_data.productivity_per_hour = work_log.productivity_per_hour
        response_data.productivity_per_person_per_hour = work_log.productivity_per_person_per_hour
        response_data.total_man_hours = work_log.total_man_hours

        return response_data

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento del work log: {str(e)}"
        )


@router.delete("/work-logs/{work_log_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_work_log(
    work_log_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Elimina un work log.
    """
    work_log = db.query(WorkLog).filter(WorkLog.id == work_log_id).first()

    if not work_log:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Work log non trovato"
        )

    try:
        db.delete(work_log)
        db.commit()

    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'eliminazione del work log: {str(e)}"
        )
