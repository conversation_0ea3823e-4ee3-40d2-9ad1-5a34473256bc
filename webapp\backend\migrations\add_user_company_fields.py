"""
Migrazione per aggiungere i campi aziendali alla tabella utenti.
"""

import logging
from sqlalchemy import text
from backend.database import get_db

def migrate():
    """
    Aggiunge i campi aziendali alla tabella utenti:
    - ragione_sociale
    - indirizzo
    - nazione
    - email
    - vat (Partita IVA)
    - referente_aziendale
    """
    try:
        # Ottieni una connessione al database
        db = next(get_db())

        # Lista dei nuovi campi da aggiungere
        new_fields = [
            ('ragione_sociale', 'TEXT'),
            ('indirizzo', 'TEXT'),
            ('nazione', 'TEXT'),
            ('email', 'TEXT'),
            ('vat', 'TEXT'),
            ('referente_aziendale', 'TEXT')
        ]

        for field_name, field_type in new_fields:
            try:
                # Verifica se la colonna esiste già
                result = db.execute(text(
                    "SELECT column_name FROM information_schema.columns "
                    "WHERE table_name = 'utenti' AND column_name = :field_name"
                ), {"field_name": field_name})
                
                if result.fetchone():
                    print(f"La colonna {field_name} esiste già nella tabella utenti.")
                    continue

                # Aggiungi la colonna
                db.execute(text(f"ALTER TABLE utenti ADD COLUMN {field_name} {field_type}"))
                print(f"Colonna {field_name} aggiunta con successo alla tabella utenti.")
                
            except Exception as e:
                print(f"Errore durante l'aggiunta della colonna {field_name}: {str(e)}")
                continue

        # Commit delle modifiche
        db.commit()
        print("Migrazione completata con successo.")
        
    except Exception as e:
        print(f"Errore durante la migrazione: {str(e)}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    migrate()
