"""
Modulo per l'invio di SMS tramite diversi provider.
Supporta Twilio, TextMagic, e altri provider SMS.
"""

import logging
import os
import requests
from typing import Optional, Dict, Any
from datetime import datetime

# Configurazione SMS (da variabili d'ambiente)
SMS_PROVIDER = os.getenv('SMS_PROVIDER', 'twilio')  # 'twilio', 'textmagic', 'custom'

# Configurazione Twilio
TWILIO_ACCOUNT_SID = os.getenv('TWILIO_ACCOUNT_SID', '')
TWILIO_AUTH_TOKEN = os.getenv('TWILIO_AUTH_TOKEN', '')
TWILIO_FROM_NUMBER = os.getenv('TWILIO_FROM_NUMBER', '')

# Configurazione TextMagic
TEXTMAGIC_USERNAME = os.getenv('TEXTMAGIC_USERNAME', '')
TEXTMAGIC_API_KEY = os.getenv('TEXTMAGIC_API_KEY', '')

# Configurazione Custom API
CUSTOM_SMS_API_URL = os.getenv('CUSTOM_SMS_API_URL', '')
CUSTOM_SMS_API_KEY = os.getenv('CUSTOM_SMS_API_KEY', '')

def crea_messaggio_sms_comanda(codice_comanda: str, tipo_comanda: str, responsabile_nome: str) -> str:
    """
    Crea il messaggio SMS per la notifica di una nuova comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        tipo_comanda: Tipo di comanda
        responsabile_nome: Nome del responsabile
        
    Returns:
        Messaggio SMS formattato
    """
    
    # Mappa dei tipi comanda per descrizioni brevi
    tipo_brevi = {
        'POSA': 'Posa',
        'COLLEGAMENTO_PARTENZA': 'Coll.Partenza',
        'COLLEGAMENTO_ARRIVO': 'Coll.Arrivo',
        'CERTIFICAZIONE': 'Certificazione'
    }
    
    tipo_breve = tipo_brevi.get(tipo_comanda, tipo_comanda)
    
    # Messaggio SMS conciso (limite caratteri)
    messaggio = f"""CMS: Nuova comanda {tipo_breve}
Codice: {codice_comanda}
Per: {responsabile_nome}
Usa il codice nell'app mobile."""
    
    return messaggio

def invia_sms_twilio(numero_destinatario: str, messaggio: str) -> bool:
    """
    Invia SMS tramite Twilio.
    
    Args:
        numero_destinatario: Numero di telefono del destinatario
        messaggio: Testo del messaggio
        
    Returns:
        True se l'invio è riuscito, False altrimenti
    """
    
    if not all([TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, TWILIO_FROM_NUMBER]):
        logging.warning("⚠️ Configurazione Twilio incompleta")
        return False
    
    try:
        # Importa Twilio solo se necessario
        from twilio.rest import Client
        
        client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)
        
        message = client.messages.create(
            body=messaggio,
            from_=TWILIO_FROM_NUMBER,
            to=numero_destinatario
        )
        
        logging.info(f"✅ SMS Twilio inviato con successo. SID: {message.sid}")
        return True
        
    except ImportError:
        logging.error("❌ Libreria Twilio non installata. Installa con: pip install twilio")
        return False
    except Exception as e:
        logging.error(f"❌ Errore invio SMS Twilio: {str(e)}")
        return False

def invia_sms_textmagic(numero_destinatario: str, messaggio: str) -> bool:
    """
    Invia SMS tramite TextMagic API.
    
    Args:
        numero_destinatario: Numero di telefono del destinatario
        messaggio: Testo del messaggio
        
    Returns:
        True se l'invio è riuscito, False altrimenti
    """
    
    if not all([TEXTMAGIC_USERNAME, TEXTMAGIC_API_KEY]):
        logging.warning("⚠️ Configurazione TextMagic incompleta")
        return False
    
    try:
        url = "https://rest.textmagic.com/api/v2/messages"
        
        headers = {
            'X-TM-Username': TEXTMAGIC_USERNAME,
            'X-TM-Key': TEXTMAGIC_API_KEY,
            'Content-Type': 'application/json'
        }
        
        data = {
            'text': messaggio,
            'phones': numero_destinatario
        }
        
        response = requests.post(url, json=data, headers=headers, timeout=30)
        
        if response.status_code == 201:
            logging.info(f"✅ SMS TextMagic inviato con successo")
            return True
        else:
            logging.error(f"❌ Errore TextMagic: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logging.error(f"❌ Errore invio SMS TextMagic: {str(e)}")
        return False

def invia_sms_custom_api(numero_destinatario: str, messaggio: str) -> bool:
    """
    Invia SMS tramite API personalizzata.
    
    Args:
        numero_destinatario: Numero di telefono del destinatario
        messaggio: Testo del messaggio
        
    Returns:
        True se l'invio è riuscito, False altrimenti
    """
    
    if not all([CUSTOM_SMS_API_URL, CUSTOM_SMS_API_KEY]):
        logging.warning("⚠️ Configurazione API SMS personalizzata incompleta")
        return False
    
    try:
        headers = {
            'Authorization': f'Bearer {CUSTOM_SMS_API_KEY}',
            'Content-Type': 'application/json'
        }
        
        data = {
            'to': numero_destinatario,
            'message': messaggio,
            'from': 'CMS'
        }
        
        response = requests.post(CUSTOM_SMS_API_URL, json=data, headers=headers, timeout=30)
        
        if response.status_code in [200, 201]:
            logging.info(f"✅ SMS API personalizzata inviato con successo")
            return True
        else:
            logging.error(f"❌ Errore API SMS: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logging.error(f"❌ Errore invio SMS API personalizzata: {str(e)}")
        return False

def invia_sms(numero_destinatario: str, messaggio: str) -> bool:
    """
    Invia SMS utilizzando il provider configurato.
    
    Args:
        numero_destinatario: Numero di telefono del destinatario
        messaggio: Testo del messaggio
        
    Returns:
        True se l'invio è riuscito, False altrimenti
    """
    
    # Normalizza il numero di telefono
    numero_normalizzato = normalizza_numero_telefono(numero_destinatario)
    
    if not numero_normalizzato:
        logging.error(f"❌ Numero di telefono non valido: {numero_destinatario}")
        return False
    
    # Invia tramite il provider configurato
    if SMS_PROVIDER.lower() == 'twilio':
        return invia_sms_twilio(numero_normalizzato, messaggio)
    elif SMS_PROVIDER.lower() == 'textmagic':
        return invia_sms_textmagic(numero_normalizzato, messaggio)
    elif SMS_PROVIDER.lower() == 'custom':
        return invia_sms_custom_api(numero_normalizzato, messaggio)
    else:
        logging.error(f"❌ Provider SMS non supportato: {SMS_PROVIDER}")
        return False

def normalizza_numero_telefono(numero: str) -> Optional[str]:
    """
    Normalizza un numero di telefono per l'invio SMS.
    
    Args:
        numero: Numero di telefono da normalizzare
        
    Returns:
        Numero normalizzato o None se non valido
    """
    
    if not numero:
        return None
    
    # Rimuovi spazi, trattini e parentesi
    numero_pulito = ''.join(c for c in numero if c.isdigit() or c == '+')
    
    # Se non inizia con +, aggiungi +39 per l'Italia
    if not numero_pulito.startswith('+'):
        if numero_pulito.startswith('39'):
            numero_pulito = '+' + numero_pulito
        elif numero_pulito.startswith('3'):  # Cellulare italiano
            numero_pulito = '+39' + numero_pulito
        else:
            numero_pulito = '+39' + numero_pulito
    
    # Verifica lunghezza minima
    if len(numero_pulito) < 10:
        return None
    
    return numero_pulito

def invia_sms_comanda(codice_comanda: str, tipo_comanda: str, responsabile_nome: str, numero_telefono: str) -> bool:
    """
    Invia SMS di notifica per una nuova comanda.
    
    Args:
        codice_comanda: Codice univoco della comanda
        tipo_comanda: Tipo di comanda
        responsabile_nome: Nome del responsabile
        numero_telefono: Numero di telefono del responsabile
        
    Returns:
        True se l'invio è riuscito, False altrimenti
    """
    
    messaggio = crea_messaggio_sms_comanda(codice_comanda, tipo_comanda, responsabile_nome)
    return invia_sms(numero_telefono, messaggio)

def test_configurazione_sms() -> Dict[str, bool]:
    """
    Testa la configurazione SMS per tutti i provider.
    
    Returns:
        Dict con risultati test per ogni provider
    """
    
    risultati = {}
    
    # Test Twilio
    risultati['twilio'] = bool(TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN and TWILIO_FROM_NUMBER)
    
    # Test TextMagic
    risultati['textmagic'] = bool(TEXTMAGIC_USERNAME and TEXTMAGIC_API_KEY)
    
    # Test Custom API
    risultati['custom'] = bool(CUSTOM_SMS_API_URL and CUSTOM_SMS_API_KEY)
    
    # Test provider attivo
    risultati['provider_attivo'] = SMS_PROVIDER.lower()
    risultati['configurato'] = risultati.get(SMS_PROVIDER.lower(), False)
    
    return risultati

def invia_sms_test(numero_destinatario: str) -> bool:
    """
    Invia un SMS di test per verificare il funzionamento.
    
    Args:
        numero_destinatario: Numero di telefono di test
        
    Returns:
        True se l'invio è riuscito, False altrimenti
    """
    
    messaggio = f"""Test SMS Sistema CMS

Questo è un messaggio di test.

Se ricevi questo SMS, la configurazione è corretta.

Data: {datetime.now().strftime('%d/%m/%Y %H:%M')}"""
    
    return invia_sms(numero_destinatario, messaggio)

def ottieni_configurazione_sms() -> Dict[str, Any]:
    """
    Restituisce la configurazione SMS attuale.
    
    Returns:
        Dict con la configurazione (senza credenziali sensibili)
    """
    
    return {
        'provider': SMS_PROVIDER,
        'twilio_configurato': bool(TWILIO_ACCOUNT_SID and TWILIO_AUTH_TOKEN),
        'textmagic_configurato': bool(TEXTMAGIC_USERNAME and TEXTMAGIC_API_KEY),
        'custom_configurato': bool(CUSTOM_SMS_API_URL and CUSTOM_SMS_API_KEY),
        'provider_attivo_configurato': test_configurazione_sms()['configurato']
    }
