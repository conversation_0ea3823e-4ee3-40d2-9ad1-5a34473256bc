/* [project]/src/components/cavi/modals/enhanced-modals.css [app-client] (css) */
.modal-overlay {
  backdrop-filter: blur(2px);
  background-color: #00000080;
  transition: all .2s ease-in-out;
}

.enhanced-modal-content {
  border: 1px solid #e5e7ebcc;
  border-radius: 12px;
  box-shadow: 0 25px 50px -12px #00000040;
}

.cable-id-badge {
  color: #1e40af;
  letter-spacing: .025em;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border: 1px solid #93c5fd;
  border-radius: 6px;
  padding: 4px 8px;
  font-family: Monaco, Menlo, Ubuntu Mono, monospace;
  font-size: .875rem;
  font-weight: 600;
}

.enhanced-button {
  transition: all .15s ease-in-out;
  position: relative;
  overflow: hidden;
}

.enhanced-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px #00000026;
}

.enhanced-button:active {
  transform: translateY(0);
}

.enhanced-button:disabled {
  box-shadow: none;
  opacity: .6;
  cursor: not-allowed;
  transform: none;
}

.enhanced-button.loading:before {
  content: "";
  background: linear-gradient(90deg, #0000, #fff3, #0000);
  width: 100%;
  height: 100%;
  animation: 1.5s infinite loading-shimmer;
  position: absolute;
  top: 0;
  left: -100%;
}

@keyframes loading-shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

.status-indicator {
  border-radius: 8px;
  align-items: center;
  gap: .5rem;
  padding: .5rem;
  font-size: .875rem;
  font-weight: 500;
  transition: all .2s ease-in-out;
  display: inline-flex;
}

.status-indicator.installed {
  color: #166534;
  background-color: #dcfce7;
  border: 1px solid #bbf7d0;
}

.status-indicator.connected {
  color: #1e40af;
  background-color: #dbeafe;
  border: 1px solid #93c5fd;
}

.status-indicator.not-certified {
  color: #92400e;
  background-color: #fef3c7;
  border: 1px solid #fcd34d;
}

.status-indicator.error {
  color: #991b1b;
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
}

.status-dot {
  border-radius: 50%;
  width: 12px;
  height: 12px;
  display: inline-block;
  position: relative;
}

.status-dot.green {
  background-color: #10b981;
  box-shadow: 0 0 0 2px #10b98133;
}

.status-dot.red {
  background-color: #ef4444;
  box-shadow: 0 0 0 2px #ef444433;
}

.status-dot.orange {
  background-color: #f59e0b;
  box-shadow: 0 0 0 2px #f59e0b33;
}

.status-dot.pulsing {
  animation: 2s infinite pulse-dot;
}

@keyframes pulse-dot {
  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: .5;
  }
}

.enhanced-input {
  border: 2px solid #e5e7eb;
  transition: all .2s ease-in-out;
}

.enhanced-input:focus {
  border-color: #3b82f6;
  outline: none;
  box-shadow: 0 0 0 3px #3b82f61a;
}

.enhanced-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px #ef44441a;
}

.enhanced-input.success {
  border-color: #10b981;
  box-shadow: 0 0 0 3px #10b9811a;
}

.enhanced-radio {
  cursor: pointer;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px;
  transition: all .2s ease-in-out;
  position: relative;
}

.enhanced-radio:hover {
  background-color: #f9fafb;
  border-color: #d1d5db;
}

.enhanced-radio.selected {
  background-color: #eff6ff;
  border-color: #3b82f6;
}

.enhanced-radio input[type="radio"] {
  accent-color: #3b82f6;
}

.enhanced-tabs {
  border-bottom: 2px solid #e5e7eb;
}

.enhanced-tab {
  color: #6b7280;
  cursor: pointer;
  border-bottom: 2px solid #0000;
  padding: 12px 16px;
  font-weight: 500;
  transition: all .2s ease-in-out;
  position: relative;
}

.enhanced-tab:hover {
  color: #374151;
  background-color: #f9fafb;
}

.enhanced-tab.active {
  color: #3b82f6;
  background-color: #eff6ff;
  border-bottom-color: #3b82f6;
}

.enhanced-tab.active:after {
  content: "";
  background-color: #3b82f6;
  height: 2px;
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
}

.enhanced-alert {
  border-radius: 8px;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  font-size: .875rem;
  display: flex;
}

.enhanced-alert.info {
  color: #1e40af;
  background-color: #eff6ff;
  border: 1px solid #bfdbfe;
}

.enhanced-alert.warning {
  color: #92400e;
  background-color: #fffbeb;
  border: 1px solid #fcd34d;
}

.enhanced-alert.error {
  color: #991b1b;
  background-color: #fef2f2;
  border: 1px solid #fca5a5;
}

.enhanced-alert.success {
  color: #166534;
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.toast-notification {
  z-index: 9999;
  max-width: 400px;
  animation: .3s ease-out toast-slide-in;
  position: fixed;
  top: 1rem;
  right: 1rem;
}

.toast-notification.exiting {
  animation: .3s ease-in toast-slide-out;
}

@keyframes toast-slide-in {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes toast-slide-out {
  from {
    opacity: 1;
    transform: translateX(0);
  }

  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@media (width <= 640px) {
  .enhanced-modal-content {
    width: calc(100% - 2rem);
    margin: 1rem;
  }

  .cable-id-badge {
    padding: 2px 6px;
    font-size: .75rem;
  }

  .enhanced-button {
    padding: 8px 12px;
    font-size: .875rem;
  }

  .toast-notification {
    max-width: none;
    top: .5rem;
    left: .5rem;
    right: .5rem;
  }
}

.enhanced-modal-content:focus, .enhanced-button:focus-visible, .enhanced-input:focus-visible {
  outline-offset: 2px;
  outline: 2px solid #3b82f6;
}

@media (prefers-contrast: high) {
  .cable-id-badge, .status-indicator {
    border-width: 2px;
  }

  .enhanced-input {
    border-width: 3px;
  }
}


/*# sourceMappingURL=src_components_cavi_modals_enhanced-modals_77c01ce3.css.map*/