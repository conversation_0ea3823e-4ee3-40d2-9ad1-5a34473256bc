from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Any, Optional

from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.non_conformita import NonConformita
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.models.rapporto_generale_collaudo import RapportoGeneraleCollaudo
from backend.schemas.non_conformita import (
    NonConformitaCreate,
    NonConformitaUpdate,
    NonConformitaResponse,
    NonConformitaListResponse
)
from backend.api.auth import get_current_active_user

router = APIRouter()

@router.get("/{cantiere_id}/non-conformita", response_model=List[NonConformitaListResponse])
def get_non_conformita_cantiere(
    cantiere_id: int,
    stato_nc: Optional[str] = Query(None, description="Filtro per stato NC"),
    tipo_nc: Optional[str] = Query(None, description="Filtro per tipo NC"),
    skip: int = Query(0, ge=0, description="Numero di record da saltare"),
    limit: int = Query(100, ge=1, le=1000, description="Numero massimo di record da restituire"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutte le non conformità di un cantiere.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Query base per le non conformità del cantiere
    query = db.query(NonConformita).join(
        CertificazioneCavo, 
        NonConformita.id_certificazione == CertificazioneCavo.id_certificazione,
        isouter=True
    ).join(
        RapportoGeneraleCollaudo,
        NonConformita.id_rapporto == RapportoGeneraleCollaudo.id_rapporto,
        isouter=True
    ).filter(
        (CertificazioneCavo.id_cantiere == cantiere_id) | 
        (RapportoGeneraleCollaudo.id_cantiere == cantiere_id)
    )
    
    # Applica filtri
    if stato_nc:
        query = query.filter(NonConformita.stato_nc == stato_nc)
    
    if tipo_nc:
        query = query.filter(NonConformita.tipo_nc == tipo_nc)
    
    non_conformita = query.offset(skip).limit(limit).all()
    return non_conformita

@router.post("/{cantiere_id}/non-conformita", response_model=NonConformitaResponse)
def create_non_conformita(
    cantiere_id: int,
    nc_in: NonConformitaCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea una nuova non conformità.
    """
    # Verifica che il cantiere esista
    cantiere = db.query(Cantiere).filter(Cantiere.id_cantiere == cantiere_id).first()
    if not cantiere:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Cantiere con ID {cantiere_id} non trovato"
        )

    # Verifica che il codice NC sia univoco
    existing_nc = db.query(NonConformita).filter(
        NonConformita.codice_nc == nc_in.codice_nc
    ).first()
    if existing_nc:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Non conformità con codice {nc_in.codice_nc} già esistente"
        )

    # Se specificata, verifica che la certificazione appartenga al cantiere
    if nc_in.id_certificazione:
        certificazione = db.query(CertificazioneCavo).filter(
            CertificazioneCavo.id_certificazione == nc_in.id_certificazione,
            CertificazioneCavo.id_cantiere == cantiere_id
        ).first()
        if not certificazione:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Certificazione con ID {nc_in.id_certificazione} non trovata nel cantiere {cantiere_id}"
            )

    # Se specificato, verifica che il rapporto appartenga al cantiere
    if nc_in.id_rapporto:
        rapporto = db.query(RapportoGeneraleCollaudo).filter(
            RapportoGeneraleCollaudo.id_rapporto == nc_in.id_rapporto,
            RapportoGeneraleCollaudo.id_cantiere == cantiere_id
        ).first()
        if not rapporto:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Rapporto con ID {nc_in.id_rapporto} non trovato nel cantiere {cantiere_id}"
            )

    # Crea la nuova non conformità
    nc_data = nc_in.dict()
    nc = NonConformita(**nc_data)
    
    db.add(nc)
    db.commit()
    db.refresh(nc)

    return nc

@router.get("/{cantiere_id}/non-conformita/{nc_id}", response_model=NonConformitaResponse)
def get_non_conformita(
    cantiere_id: int,
    nc_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera i dettagli di una non conformità specifica.
    """
    # Query per ottenere la non conformità verificando che appartenga al cantiere
    nc = db.query(NonConformita).join(
        CertificazioneCavo, 
        NonConformita.id_certificazione == CertificazioneCavo.id_certificazione,
        isouter=True
    ).join(
        RapportoGeneraleCollaudo,
        NonConformita.id_rapporto == RapportoGeneraleCollaudo.id_rapporto,
        isouter=True
    ).filter(
        NonConformita.id_nc == nc_id,
        (CertificazioneCavo.id_cantiere == cantiere_id) | 
        (RapportoGeneraleCollaudo.id_cantiere == cantiere_id)
    ).first()

    if not nc:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Non conformità con ID {nc_id} non trovata nel cantiere {cantiere_id}"
        )

    return nc

@router.put("/{cantiere_id}/non-conformita/{nc_id}", response_model=NonConformitaResponse)
def update_non_conformita(
    cantiere_id: int,
    nc_id: int,
    nc_update: NonConformitaUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna una non conformità esistente.
    """
    # Query per ottenere la non conformità verificando che appartenga al cantiere
    nc = db.query(NonConformita).join(
        CertificazioneCavo, 
        NonConformita.id_certificazione == CertificazioneCavo.id_certificazione,
        isouter=True
    ).join(
        RapportoGeneraleCollaudo,
        NonConformita.id_rapporto == RapportoGeneraleCollaudo.id_rapporto,
        isouter=True
    ).filter(
        NonConformita.id_nc == nc_id,
        (CertificazioneCavo.id_cantiere == cantiere_id) | 
        (RapportoGeneraleCollaudo.id_cantiere == cantiere_id)
    ).first()

    if not nc:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Non conformità con ID {nc_id} non trovata nel cantiere {cantiere_id}"
        )

    # Aggiorna i campi forniti
    update_data = nc_update.dict(exclude_unset=True)
    if update_data:
        for field, value in update_data.items():
            setattr(nc, field, value)
        
        db.commit()
        db.refresh(nc)

    return nc

@router.delete("/{cantiere_id}/non-conformita/{nc_id}")
def delete_non_conformita(
    cantiere_id: int,
    nc_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina una non conformità.
    """
    # Query per ottenere la non conformità verificando che appartenga al cantiere
    nc = db.query(NonConformita).join(
        CertificazioneCavo, 
        NonConformita.id_certificazione == CertificazioneCavo.id_certificazione,
        isouter=True
    ).join(
        RapportoGeneraleCollaudo,
        NonConformita.id_rapporto == RapportoGeneraleCollaudo.id_rapporto,
        isouter=True
    ).filter(
        NonConformita.id_nc == nc_id,
        (CertificazioneCavo.id_cantiere == cantiere_id) | 
        (RapportoGeneraleCollaudo.id_cantiere == cantiere_id)
    ).first()

    if not nc:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Non conformità con ID {nc_id} non trovata nel cantiere {cantiere_id}"
        )

    db.delete(nc)
    db.commit()

    return {"message": "Non conformità eliminata con successo"}

@router.get("/tipi-nc")
def get_tipi_nc() -> Any:
    """
    Restituisce i tipi di non conformità disponibili.
    """
    return {
        "tipi_nc": [
            {
                "codice": "CRITICA",
                "nome": "Critica",
                "descrizione": "Non conformità che compromette la sicurezza"
            },
            {
                "codice": "MAGGIORE",
                "nome": "Maggiore",
                "descrizione": "Non conformità che compromette la funzionalità"
            },
            {
                "codice": "MINORE",
                "nome": "Minore",
                "descrizione": "Non conformità che non compromette la funzionalità"
            }
        ],
        "stati_nc": [
            {
                "codice": "APERTA",
                "nome": "Aperta",
                "descrizione": "Non conformità rilevata ma non ancora risolta"
            },
            {
                "codice": "IN_CORSO",
                "nome": "In Corso",
                "descrizione": "Azione correttiva in corso"
            },
            {
                "codice": "CHIUSA",
                "nome": "Chiusa",
                "descrizione": "Non conformità risolta e verificata"
            }
        ]
    }
