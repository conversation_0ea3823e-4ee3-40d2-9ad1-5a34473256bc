#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API endpoints per la gestione del database delle tipologie di cavi.
Gestisce categorie, produttori, standard e tipologie di cavi.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func
from typing import List, Optional, Any
import logging

from backend.database import get_db
from backend.models.tipologie_cavi import (
    CategoriaCavo, ProduttoreCavo, StandardCavo, TipologiaCavo,
    SpecificaTecnicaCavo, TipologiaStandard
)
from backend.schemas.tipologie_cavi import (
    CategoriaCavoCreate, CategoriaCavoUpdate, CategoriaCavoInDB, CategoriaCavoCompleta,
    ProduttoreCavoCreate, ProduttoreCavoUpdate, ProduttoreCavoInDB,
    StandardCavoCreate, StandardCavoUpdate, StandardCavoInDB,
    TipologiaCavoCreate, TipologiaCavoUpdate, TipologiaCavoInDB, TipologiaCavoCompleta,
    SpecificaTecnicaCavoCreate, SpecificaTecnicaCavoUpdate, SpecificaTecnicaCavoInDB,
    FiltriTipologieCavi, RisultatiRicercaTipologie
)
from backend.core.security import get_current_active_user
from backend.models.user import User

router = APIRouter(prefix="/tipologie-cavi", tags=["tipologie-cavi"])
logger = logging.getLogger(__name__)


# ==================== CATEGORIE CAVI ====================

@router.get("/categorie", response_model=List[CategoriaCavoCompleta])
def get_categorie(
    include_inactive: bool = Query(False, description="Includi categorie non attive"),
    livello: Optional[int] = Query(None, description="Filtra per livello gerarchico"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Ottiene tutte le categorie di cavi con struttura gerarchica."""
    try:
        query = db.query(CategoriaCavo).options(
            joinedload(CategoriaCavo.categoria_padre),
            joinedload(CategoriaCavo.sottocategorie)
        )
        
        if not include_inactive:
            query = query.filter(CategoriaCavo.attiva == True)
        
        if livello is not None:
            query = query.filter(CategoriaCavo.livello == livello)
        
        query = query.order_by(CategoriaCavo.livello, CategoriaCavo.ordine_visualizzazione, CategoriaCavo.nome_categoria)
        
        categorie = query.all()
        return categorie
        
    except Exception as e:
        logger.error(f"Errore nel recupero categorie: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.post("/categorie", response_model=CategoriaCavoInDB)
def create_categoria(
    categoria: CategoriaCavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Crea una nuova categoria di cavi."""
    try:
        # Verifica che il nome non esista già
        existing = db.query(CategoriaCavo).filter(CategoriaCavo.nome_categoria == categoria.nome_categoria).first()
        if existing:
            raise HTTPException(status_code=400, detail="Nome categoria già esistente")
        
        # Verifica che la categoria padre esista (se specificata)
        if categoria.id_categoria_padre:
            padre = db.query(CategoriaCavo).filter(CategoriaCavo.id_categoria == categoria.id_categoria_padre).first()
            if not padre:
                raise HTTPException(status_code=400, detail="Categoria padre non trovata")
            # Imposta automaticamente il livello
            categoria.livello = padre.livello + 1
        
        db_categoria = CategoriaCavo(**categoria.dict())
        db.add(db_categoria)
        db.commit()
        db.refresh(db_categoria)
        
        logger.info(f"Categoria creata: {db_categoria.nome_categoria} (ID: {db_categoria.id_categoria})")
        return db_categoria
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nella creazione categoria: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.put("/categorie/{categoria_id}", response_model=CategoriaCavoInDB)
def update_categoria(
    categoria_id: int,
    categoria: CategoriaCavoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Aggiorna una categoria di cavi."""
    try:
        db_categoria = db.query(CategoriaCavo).filter(CategoriaCavo.id_categoria == categoria_id).first()
        if not db_categoria:
            raise HTTPException(status_code=404, detail="Categoria non trovata")
        
        # Aggiorna solo i campi forniti
        update_data = categoria.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_categoria, field, value)
        
        db.commit()
        db.refresh(db_categoria)
        
        logger.info(f"Categoria aggiornata: {db_categoria.nome_categoria} (ID: {categoria_id})")
        return db_categoria
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nell'aggiornamento categoria: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.delete("/categorie/{categoria_id}")
def delete_categoria(
    categoria_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Elimina una categoria di cavi (solo se non ha tipologie associate)."""
    try:
        db_categoria = db.query(CategoriaCavo).filter(CategoriaCavo.id_categoria == categoria_id).first()
        if not db_categoria:
            raise HTTPException(status_code=404, detail="Categoria non trovata")
        
        # Verifica che non ci siano tipologie associate
        tipologie_count = db.query(TipologiaCavo).filter(TipologiaCavo.id_categoria == categoria_id).count()
        if tipologie_count > 0:
            raise HTTPException(status_code=400, detail="Impossibile eliminare: categoria ha tipologie associate")
        
        # Verifica che non ci siano sottocategorie
        sottocategorie_count = db.query(CategoriaCavo).filter(CategoriaCavo.id_categoria_padre == categoria_id).count()
        if sottocategorie_count > 0:
            raise HTTPException(status_code=400, detail="Impossibile eliminare: categoria ha sottocategorie")
        
        db.delete(db_categoria)
        db.commit()
        
        logger.info(f"Categoria eliminata: {db_categoria.nome_categoria} (ID: {categoria_id})")
        return {"message": "Categoria eliminata con successo"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nell'eliminazione categoria: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


# ==================== PRODUTTORI CAVI ====================

@router.get("/produttori", response_model=List[ProduttoreCavoInDB])
def get_produttori(
    include_inactive: bool = Query(False, description="Includi produttori non attivi"),
    paese: Optional[str] = Query(None, description="Filtra per paese"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Ottiene tutti i produttori di cavi."""
    try:
        query = db.query(ProduttoreCavo)
        
        if not include_inactive:
            query = query.filter(ProduttoreCavo.attivo == True)
        
        if paese:
            query = query.filter(ProduttoreCavo.paese.ilike(f"%{paese}%"))
        
        query = query.order_by(ProduttoreCavo.nome_produttore)
        
        produttori = query.all()
        return produttori
        
    except Exception as e:
        logger.error(f"Errore nel recupero produttori: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.post("/produttori", response_model=ProduttoreCavoInDB)
def create_produttore(
    produttore: ProduttoreCavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Crea un nuovo produttore di cavi."""
    try:
        # Verifica che il nome non esista già
        existing = db.query(ProduttoreCavo).filter(ProduttoreCavo.nome_produttore == produttore.nome_produttore).first()
        if existing:
            raise HTTPException(status_code=400, detail="Nome produttore già esistente")
        
        db_produttore = ProduttoreCavo(**produttore.dict())
        db.add(db_produttore)
        db.commit()
        db.refresh(db_produttore)
        
        logger.info(f"Produttore creato: {db_produttore.nome_produttore} (ID: {db_produttore.id_produttore})")
        return db_produttore
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nella creazione produttore: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.put("/produttori/{produttore_id}", response_model=ProduttoreCavoInDB)
def update_produttore(
    produttore_id: int,
    produttore: ProduttoreCavoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Aggiorna un produttore di cavi."""
    try:
        db_produttore = db.query(ProduttoreCavo).filter(ProduttoreCavo.id_produttore == produttore_id).first()
        if not db_produttore:
            raise HTTPException(status_code=404, detail="Produttore non trovato")
        
        # Aggiorna solo i campi forniti
        update_data = produttore.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_produttore, field, value)
        
        db.commit()
        db.refresh(db_produttore)
        
        logger.info(f"Produttore aggiornato: {db_produttore.nome_produttore} (ID: {produttore_id})")
        return db_produttore
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nell'aggiornamento produttore: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.delete("/produttori/{produttore_id}")
def delete_produttore(
    produttore_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Elimina un produttore di cavi (solo se non ha tipologie associate)."""
    try:
        db_produttore = db.query(ProduttoreCavo).filter(ProduttoreCavo.id_produttore == produttore_id).first()
        if not db_produttore:
            raise HTTPException(status_code=404, detail="Produttore non trovato")
        
        # Verifica che non ci siano tipologie associate
        tipologie_count = db.query(TipologiaCavo).filter(TipologiaCavo.id_produttore == produttore_id).count()
        if tipologie_count > 0:
            raise HTTPException(status_code=400, detail="Impossibile eliminare: produttore ha tipologie associate")
        
        db.delete(db_produttore)
        db.commit()
        
        logger.info(f"Produttore eliminato: {db_produttore.nome_produttore} (ID: {produttore_id})")
        return {"message": "Produttore eliminato con successo"}
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nell'eliminazione produttore: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


# ==================== STANDARD CAVI ====================

@router.get("/standard", response_model=List[StandardCavoInDB])
def get_standard(
    include_inactive: bool = Query(False, description="Includi standard non attivi"),
    ente_normativo: Optional[str] = Query(None, description="Filtra per ente normativo"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Ottiene tutti gli standard per cavi."""
    try:
        query = db.query(StandardCavo)

        if not include_inactive:
            query = query.filter(StandardCavo.attivo == True)

        if ente_normativo:
            query = query.filter(StandardCavo.ente_normativo.ilike(f"%{ente_normativo}%"))

        query = query.order_by(StandardCavo.ente_normativo, StandardCavo.nome_standard)

        standard = query.all()
        return standard

    except Exception as e:
        logger.error(f"Errore nel recupero standard: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.post("/standard", response_model=StandardCavoInDB)
def create_standard(
    standard: StandardCavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Crea un nuovo standard per cavi."""
    try:
        # Verifica che il nome non esista già
        existing = db.query(StandardCavo).filter(StandardCavo.nome_standard == standard.nome_standard).first()
        if existing:
            raise HTTPException(status_code=400, detail="Nome standard già esistente")

        db_standard = StandardCavo(**standard.dict())
        db.add(db_standard)
        db.commit()
        db.refresh(db_standard)

        logger.info(f"Standard creato: {db_standard.nome_standard} (ID: {db_standard.id_standard})")
        return db_standard

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nella creazione standard: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.put("/standard/{standard_id}", response_model=StandardCavoInDB)
def update_standard(
    standard_id: int,
    standard: StandardCavoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Aggiorna uno standard per cavi."""
    try:
        db_standard = db.query(StandardCavo).filter(StandardCavo.id_standard == standard_id).first()
        if not db_standard:
            raise HTTPException(status_code=404, detail="Standard non trovato")

        # Aggiorna solo i campi forniti
        update_data = standard.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_standard, field, value)

        db.commit()
        db.refresh(db_standard)

        logger.info(f"Standard aggiornato: {db_standard.nome_standard} (ID: {standard_id})")
        return db_standard

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nell'aggiornamento standard: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.delete("/standard/{standard_id}")
def delete_standard(
    standard_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Elimina uno standard per cavi (solo se non ha tipologie associate)."""
    try:
        db_standard = db.query(StandardCavo).filter(StandardCavo.id_standard == standard_id).first()
        if not db_standard:
            raise HTTPException(status_code=404, detail="Standard non trovato")

        # Verifica che non ci siano tipologie associate
        tipologie_count = db.query(TipologiaCavo).filter(TipologiaCavo.id_standard_principale == standard_id).count()
        if tipologie_count > 0:
            raise HTTPException(status_code=400, detail="Impossibile eliminare: standard ha tipologie associate")

        db.delete(db_standard)
        db.commit()

        logger.info(f"Standard eliminato: {db_standard.nome_standard} (ID: {standard_id})")
        return {"message": "Standard eliminato con successo"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nell'eliminazione standard: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


# ==================== TIPOLOGIE CAVI ====================

@router.get("/tipologie", response_model=RisultatiRicercaTipologie)
def get_tipologie(
    page: int = Query(1, ge=1, description="Numero pagina"),
    page_size: int = Query(50, ge=1, le=200, description="Elementi per pagina"),
    categoria_id: Optional[int] = Query(None, description="Filtra per categoria"),
    produttore_id: Optional[int] = Query(None, description="Filtra per produttore"),
    disponibile: Optional[bool] = Query(None, description="Filtra per disponibilità"),
    search_text: Optional[str] = Query(None, description="Ricerca nel nome o codice prodotto"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Ottiene le tipologie di cavi con filtri e paginazione."""
    try:
        query = db.query(TipologiaCavo).options(
            joinedload(TipologiaCavo.produttore),
            joinedload(TipologiaCavo.categoria),
            joinedload(TipologiaCavo.standard_principale)
        )

        # Applica filtri
        if categoria_id:
            query = query.filter(TipologiaCavo.id_categoria == categoria_id)

        if produttore_id:
            query = query.filter(TipologiaCavo.id_produttore == produttore_id)

        if disponibile is not None:
            query = query.filter(TipologiaCavo.disponibile == disponibile)

        if search_text:
            search_filter = or_(
                TipologiaCavo.nome_commerciale.ilike(f"%{search_text}%"),
                TipologiaCavo.codice_prodotto.ilike(f"%{search_text}%"),
                TipologiaCavo.descrizione_breve.ilike(f"%{search_text}%")
            )
            query = query.filter(search_filter)

        # Conta il totale
        total_count = query.count()

        # Applica paginazione
        offset = (page - 1) * page_size
        tipologie = query.order_by(TipologiaCavo.nome_commerciale).offset(offset).limit(page_size).all()

        # Calcola il numero totale di pagine
        total_pages = (total_count + page_size - 1) // page_size

        return RisultatiRicercaTipologie(
            tipologie=tipologie,
            total_count=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )

    except Exception as e:
        logger.error(f"Errore nel recupero tipologie: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.get("/tipologie/{tipologia_id}", response_model=TipologiaCavoCompleta)
def get_tipologia(
    tipologia_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Ottiene una tipologia di cavo specifica con tutte le relazioni."""
    try:
        tipologia = db.query(TipologiaCavo).options(
            joinedload(TipologiaCavo.produttore),
            joinedload(TipologiaCavo.categoria),
            joinedload(TipologiaCavo.standard_principale),
            joinedload(TipologiaCavo.specifiche_tecniche)
        ).filter(TipologiaCavo.id_tipologia == tipologia_id).first()

        if not tipologia:
            raise HTTPException(status_code=404, detail="Tipologia non trovata")

        return tipologia

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Errore nel recupero tipologia: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.post("/tipologie", response_model=TipologiaCavoInDB)
def create_tipologia(
    tipologia: TipologiaCavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Crea una nuova tipologia di cavo."""
    try:
        # Verifica che la categoria esista
        categoria = db.query(CategoriaCavo).filter(CategoriaCavo.id_categoria == tipologia.id_categoria).first()
        if not categoria:
            raise HTTPException(status_code=400, detail="Categoria non trovata")

        # Verifica che il produttore esista (se specificato)
        if tipologia.id_produttore:
            produttore = db.query(ProduttoreCavo).filter(ProduttoreCavo.id_produttore == tipologia.id_produttore).first()
            if not produttore:
                raise HTTPException(status_code=400, detail="Produttore non trovato")

        # Verifica unicità codice prodotto per produttore
        if tipologia.id_produttore:
            existing = db.query(TipologiaCavo).filter(
                and_(
                    TipologiaCavo.codice_prodotto == tipologia.codice_prodotto,
                    TipologiaCavo.id_produttore == tipologia.id_produttore
                )
            ).first()
            if existing:
                raise HTTPException(status_code=400, detail="Codice prodotto già esistente per questo produttore")

        db_tipologia = TipologiaCavo(**tipologia.dict())
        db.add(db_tipologia)
        db.commit()
        db.refresh(db_tipologia)

        logger.info(f"Tipologia creata: {db_tipologia.codice_prodotto} (ID: {db_tipologia.id_tipologia})")
        return db_tipologia

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nella creazione tipologia: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.put("/tipologie/{tipologia_id}", response_model=TipologiaCavoInDB)
def update_tipologia(
    tipologia_id: int,
    tipologia: TipologiaCavoUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Aggiorna una tipologia di cavo."""
    try:
        db_tipologia = db.query(TipologiaCavo).filter(TipologiaCavo.id_tipologia == tipologia_id).first()
        if not db_tipologia:
            raise HTTPException(status_code=404, detail="Tipologia non trovata")

        # Aggiorna solo i campi forniti
        update_data = tipologia.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_tipologia, field, value)

        db.commit()
        db.refresh(db_tipologia)

        logger.info(f"Tipologia aggiornata: {db_tipologia.codice_prodotto} (ID: {tipologia_id})")
        return db_tipologia

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nell'aggiornamento tipologia: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.delete("/tipologie/{tipologia_id}")
def delete_tipologia(
    tipologia_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Elimina una tipologia di cavo (solo se non è utilizzata)."""
    try:
        db_tipologia = db.query(TipologiaCavo).filter(TipologiaCavo.id_tipologia == tipologia_id).first()
        if not db_tipologia:
            raise HTTPException(status_code=404, detail="Tipologia non trovata")

        # Verifica che non sia utilizzata nei cavi o bobine
        # Nota: Questi controlli verranno implementati quando i modelli saranno aggiornati

        db.delete(db_tipologia)
        db.commit()

        logger.info(f"Tipologia eliminata: {db_tipologia.codice_prodotto} (ID: {tipologia_id})")
        return {"message": "Tipologia eliminata con successo"}

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nell'eliminazione tipologia: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


# ==================== SPECIFICHE TECNICHE ====================

@router.get("/tipologie/{tipologia_id}/specifiche", response_model=List[SpecificaTecnicaCavoInDB])
def get_specifiche_tipologia(
    tipologia_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Ottiene tutte le specifiche tecniche di una tipologia."""
    try:
        specifiche = db.query(SpecificaTecnicaCavo).filter(
            SpecificaTecnicaCavo.id_tipologia == tipologia_id
        ).order_by(
            SpecificaTecnicaCavo.gruppo_attributo,
            SpecificaTecnicaCavo.ordine_visualizzazione,
            SpecificaTecnicaCavo.nome_attributo
        ).all()

        return specifiche

    except Exception as e:
        logger.error(f"Errore nel recupero specifiche: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")


@router.post("/tipologie/{tipologia_id}/specifiche", response_model=SpecificaTecnicaCavoInDB)
def create_specifica(
    tipologia_id: int,
    specifica: SpecificaTecnicaCavoCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """Crea una nuova specifica tecnica per una tipologia."""
    try:
        # Verifica che la tipologia esista
        tipologia = db.query(TipologiaCavo).filter(TipologiaCavo.id_tipologia == tipologia_id).first()
        if not tipologia:
            raise HTTPException(status_code=404, detail="Tipologia non trovata")

        # Forza l'ID tipologia dal path
        specifica.id_tipologia = tipologia_id

        # Verifica unicità nome attributo per tipologia
        existing = db.query(SpecificaTecnicaCavo).filter(
            and_(
                SpecificaTecnicaCavo.id_tipologia == tipologia_id,
                SpecificaTecnicaCavo.nome_attributo == specifica.nome_attributo
            )
        ).first()
        if existing:
            raise HTTPException(status_code=400, detail="Attributo già esistente per questa tipologia")

        db_specifica = SpecificaTecnicaCavo(**specifica.dict())
        db.add(db_specifica)
        db.commit()
        db.refresh(db_specifica)

        logger.info(f"Specifica creata: {db_specifica.nome_attributo} per tipologia {tipologia_id}")
        return db_specifica

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Errore nella creazione specifica: {e}")
        raise HTTPException(status_code=500, detail="Errore interno del server")
