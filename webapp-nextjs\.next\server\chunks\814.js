"use strict";exports.id=814,exports.ids=[814],exports.modules={11273:(e,s,r)=>{r.d(s,{A:()=>n,q:()=>i});var a=r(43210),t=r(60687);function i(e,s){let r=a.createContext(s),i=e=>{let{children:s,...i}=e,n=a.useMemo(()=>i,Object.values(i));return(0,t.jsx)(r.Provider,{value:n,children:s})};return i.displayName=e+"Provider",[i,function(t){let i=a.useContext(r);if(i)return i;if(void 0!==s)return s;throw Error(`\`${t}\` must be used within \`${e}\``)}]}function n(e,s=[]){let r=[],i=()=>{let s=r.map(e=>a.createContext(e));return function(r){let t=r?.[e]||s;return a.useMemo(()=>({[`__scope${e}`]:{...r,[e]:t}}),[r,t])}};return i.scopeName=e,[function(s,i){let n=a.createContext(i),l=r.length;r=[...r,i];let o=s=>{let{scope:r,children:i,...o}=s,d=r?.[e]?.[l]||n,c=a.useMemo(()=>o,Object.values(o));return(0,t.jsx)(d.Provider,{value:c,children:i})};return o.displayName=s+"Provider",[o,function(r,t){let o=t?.[e]?.[l]||n,d=a.useContext(o);if(d)return d;if(void 0!==i)return i;throw Error(`\`${r}\` must be used within \`${s}\``)}]},function(...e){let s=e[0];if(1===e.length)return s;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let t=r.reduce((s,{useScope:r,scopeName:a})=>{let t=r(e)[`__scope${a}`];return{...s,...t}},{});return a.useMemo(()=>({[`__scope${s.scopeName}`]:t}),[t])}};return r.scopeName=s.scopeName,r}(i,...s)]}},12413:(e,s,r)=>{r.d(s,{G:()=>y,r:()=>j});var a=r(60687),t=r(43210),i=r(29523),n=r(89667),l=r(80013),o=r(44493),d=r(91821),c=r(46657),u=r(64021),m=r(12597),p=r(13861),x=r(5336),h=r(35071),f=r(43649),w=r(99891),v=r(41550),g=r(4780);function y(){var e;let[s,r]=(0,t.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[v,y]=(0,t.useState)({current:!1,new:!1,confirm:!1}),[j,b]=(0,t.useState)({score:0,feedback:[],isValid:!1}),[N,P]=(0,t.useState)(!1),[k,A]=(0,t.useState)(null),z=async e=>{if(!e)return void b({score:0,feedback:[],isValid:!1});try{let s=await fetch("/api/password/validate-password",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({password:e})});if(s.ok){let e=await s.json();b({score:e.strength_score,feedback:e.suggestions||[],isValid:e.is_valid})}}catch(e){}},S=(e,s)=>{r(r=>({...r,[e]:s})),"newPassword"===e&&z(s)},C=async e=>{e.preventDefault(),P(!0),A(null);try{if(!s.currentPassword||!s.newPassword||!s.confirmPassword)throw Error("Tutti i campi sono obbligatori");if(s.newPassword!==s.confirmPassword)throw Error("Le nuove password non corrispondono");if(!j.isValid)throw Error("La nuova password non rispetta i requisiti di sicurezza");let e=await fetch("/api/password/change-password",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("access_token")}`},body:JSON.stringify({current_password:s.currentPassword,new_password:s.newPassword,confirm_password:s.confirmPassword})}),a=await e.json();if(e.ok&&a.success)A({type:"success",text:a.message}),r({currentPassword:"",newPassword:"",confirmPassword:""}),b({score:0,feedback:[],isValid:!1});else throw Error(a.detail||a.message||"Errore durante il cambio password")}catch(e){A({type:"error",text:e instanceof Error?e.message:"Errore durante il cambio password"})}finally{P(!1)}},T=e=>{y(s=>({...s,[e]:!s[e]}))};return(0,a.jsxs)(o.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsxs)(o.aR,{className:"space-y-1",children:[(0,a.jsxs)(o.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-mariner-600"}),"Cambia Password"]}),(0,a.jsx)(o.BT,{children:"Aggiorna la tua password per mantenere l'account sicuro"})]}),(0,a.jsx)(o.Wu,{children:(0,a.jsxs)("form",{onSubmit:C,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"currentPassword",children:"Password Attuale"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.p,{id:"currentPassword",type:v.current?"text":"password",value:s.currentPassword,onChange:e=>S("currentPassword",e.target.value),className:"pr-10",required:!0}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>T("current"),children:v.current?(0,a.jsx)(m.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"newPassword",children:"Nuova Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.p,{id:"newPassword",type:v.new?"text":"password",value:s.newPassword,onChange:e=>S("newPassword",e.target.value),className:"pr-10",required:!0}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>T("new"),children:v.new?(0,a.jsx)(m.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"})})]}),s.newPassword&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,a.jsx)("span",{children:"Forza password:"}),(0,a.jsx)("span",{className:(0,g.cn)("font-medium",j.score<2?"text-red-600":j.score<4?"text-yellow-600":"text-green-600"),children:(e=j.score)<2?"Debole":e<4?"Media":"Forte"})]}),(0,a.jsx)(c.k,{value:j.score/5*100,className:"h-2"}),j.feedback.length>0&&(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Suggerimenti:"}),(0,a.jsx)("ul",{className:"list-disc list-inside space-y-1",children:j.feedback.map((e,s)=>(0,a.jsx)("li",{children:e},s))})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"confirmPassword",children:"Conferma Nuova Password"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.p,{id:"confirmPassword",type:v.confirm?"text":"password",value:s.confirmPassword,onChange:e=>S("confirmPassword",e.target.value),className:"pr-10",required:!0}),(0,a.jsx)(i.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>T("confirm"),children:v.confirm?(0,a.jsx)(m.A,{className:"h-4 w-4 text-gray-400"}):(0,a.jsx)(p.A,{className:"h-4 w-4 text-gray-400"})})]}),s.confirmPassword&&(0,a.jsx)("div",{className:"flex items-center gap-2 text-sm",children:s.newPassword===s.confirmPassword?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-600"}),(0,a.jsx)("span",{className:"text-green-600",children:"Le password corrispondono"})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)("span",{className:"text-red-600",children:"Le password non corrispondono"})]})})]}),k&&(0,a.jsxs)(d.Fc,{className:(0,g.cn)("success"===k.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===k.type?(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(f.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)(d.TN,{className:(0,g.cn)("success"===k.type?"text-green-800":"text-red-800"),children:k.text})]}),(0,a.jsx)(i.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:N||!j.isValid||s.newPassword!==s.confirmPassword,children:N?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Aggiornamento..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Cambia Password"]})})]})})]})}function j(){let[e,s]=(0,t.useState)({email:"",userType:"user"}),[r,c]=(0,t.useState)(!1),[u,m]=(0,t.useState)(null),p=async r=>{r.preventDefault(),c(!0),m(null);try{if(!e.email)throw Error("L'indirizzo email \xe8 obbligatorio");let r=await fetch("/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:e.email,user_type:e.userType})}),a=await r.json();if(r.ok&&a.success)m({type:"success",text:"Se l'email \xe8 registrata, riceverai le istruzioni per il reset della password."}),s({email:"",userType:"user"});else throw Error(a.detail||a.message||"Errore durante la richiesta di reset")}catch(e){m({type:"error",text:e instanceof Error?e.message:"Errore durante la richiesta di reset"})}finally{c(!1)}};return(0,a.jsxs)(o.Zp,{className:"w-full max-w-md mx-auto",children:[(0,a.jsxs)(o.aR,{className:"space-y-1",children:[(0,a.jsxs)(o.ZB,{className:"text-2xl flex items-center gap-2",children:[(0,a.jsx)(v.A,{className:"h-5 w-5 text-mariner-600"}),"Recupera Password"]}),(0,a.jsx)(o.BT,{children:"Inserisci la tua email per ricevere le istruzioni di reset"})]}),(0,a.jsxs)(o.Wu,{children:[(0,a.jsxs)("form",{onSubmit:p,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{children:"Tipo di Account"}),(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",value:"user",checked:"user"===e.userType,onChange:e=>s(s=>({...s,userType:e.target.value})),className:"text-mariner-600"}),(0,a.jsx)("span",{children:"Utente"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2 cursor-pointer",children:[(0,a.jsx)("input",{type:"radio",value:"cantiere",checked:"cantiere"===e.userType,onChange:e=>s(s=>({...s,userType:e.target.value})),className:"text-mariner-600"}),(0,a.jsx)("span",{children:"Cantiere"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"email",children:"Indirizzo Email"}),(0,a.jsx)(n.p,{id:"email",type:"email",value:e.email,onChange:e=>s(s=>({...s,email:e.target.value})),placeholder:"<EMAIL>",required:!0})]}),u&&(0,a.jsxs)(d.Fc,{className:(0,g.cn)("success"===u.type?"border-green-200 bg-green-50":"border-red-200 bg-red-50"),children:["success"===u.type?(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-600"}):(0,a.jsx)(f.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsx)(d.TN,{className:(0,g.cn)("success"===u.type?"text-green-800":"text-red-800"),children:u.text})]}),(0,a.jsx)(i.$,{type:"submit",className:"w-full bg-mariner-600 hover:bg-mariner-700",disabled:r,children:r?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Invio in corso..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Invia Link di Reset"]})})]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start gap-2",children:[(0,a.jsx)(w.A,{className:"h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-blue-800",children:[(0,a.jsx)("p",{className:"font-medium mb-1",children:"Informazioni sulla Sicurezza"}),(0,a.jsxs)("ul",{className:"space-y-1 text-xs",children:[(0,a.jsx)("li",{children:"• Il link di reset \xe8 valido per 30 minuti"}),(0,a.jsx)("li",{children:"• Pu\xf2 essere utilizzato una sola volta"}),(0,a.jsx)("li",{children:"• Se non ricevi l'email, controlla la cartella spam"}),(0,a.jsx)("li",{children:"• Per motivi di sicurezza, non riveleremo se l'email \xe8 registrata"})]})]})]})})]})]})}},12597:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},14163:(e,s,r)=>{r.d(s,{hO:()=>o,sG:()=>l});var a=r(43210),t=r(51215),i=r(8730),n=r(60687),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,s)=>{let r=(0,i.TL)(`Primitive.${s}`),t=a.forwardRef((e,a)=>{let{asChild:t,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(t?r:s,{...i,ref:a})});return t.displayName=`Primitive.${s}`,{...e,[s]:t}},{});function o(e,s){e&&t.flushSync(()=>e.dispatchEvent(s))}},28559:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},35071:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(62688).A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},41550:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},44493:(e,s,r)=>{r.d(s,{BT:()=>o,Wu:()=>d,ZB:()=>l,Zp:()=>i,aR:()=>n});var a=r(60687);r(43210);var t=r(4780);function i({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...s})}function n({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...s})}function l({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",e),...s})}function o({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",e),...s})}function d({className:e,...s}){return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",e),...s})}},46657:(e,s,r)=>{r.d(s,{k:()=>j});var a=r(60687),t=r(43210),i=r(11273),n=r(14163),l="Progress",[o,d]=(0,i.A)(l),[c,u]=o(l),m=t.forwardRef((e,s)=>{var r,t;let{__scopeProgress:i,value:l=null,max:o,getValueLabel:d=h,...u}=e;(o||0===o)&&!v(o)&&console.error((r=`${o}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let m=v(o)?o:100;null===l||g(l,m)||console.error((t=`${l}`,`Invalid prop \`value\` of value \`${t}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let p=g(l,m)?l:null,x=w(p)?d(p,m):void 0;return(0,a.jsx)(c,{scope:i,value:p,max:m,children:(0,a.jsx)(n.sG.div,{"aria-valuemax":m,"aria-valuemin":0,"aria-valuenow":w(p)?p:void 0,"aria-valuetext":x,role:"progressbar","data-state":f(p,m),"data-value":p??void 0,"data-max":m,...u,ref:s})})});m.displayName=l;var p="ProgressIndicator",x=t.forwardRef((e,s)=>{let{__scopeProgress:r,...t}=e,i=u(p,r);return(0,a.jsx)(n.sG.div,{"data-state":f(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...t,ref:s})});function h(e,s){return`${Math.round(e/s*100)}%`}function f(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function w(e){return"number"==typeof e}function v(e){return w(e)&&!isNaN(e)&&e>0}function g(e,s){return w(e)&&!isNaN(e)&&e<=s&&e>=0}x.displayName=p;var y=r(4780);function j({className:e,value:s,...r}){return(0,a.jsx)(m,{"data-slot":"progress",className:(0,y.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",e),...r,children:(0,a.jsx)(x,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})})}},64021:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(62688).A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},70440:(e,s,r)=>{r.r(s),r.d(s,{default:()=>t});var a=r(31658);let t=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},78148:(e,s,r)=>{r.d(s,{b:()=>l});var a=r(43210),t=r(14163),i=r(60687),n=a.forwardRef((e,s)=>(0,i.jsx)(t.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var l=n},80013:(e,s,r)=>{r.d(s,{J:()=>n});var a=r(60687);r(43210);var t=r(78148),i=r(4780);function n({className:e,...s}){return(0,a.jsx)(t.b,{"data-slot":"label",className:(0,i.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",e),...s})}},89667:(e,s,r)=>{r.d(s,{p:()=>n});var a=r(60687),t=r(43210),i=r(4780);let n=t.forwardRef(({className:e,type:s,...r},t)=>(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",e),ref:t,...r}));n.displayName="Input"},99891:(e,s,r)=>{r.d(s,{A:()=>a});let a=(0,r(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};