"""
Test di Penetrazione per il Sistema CMS
Simula attacchi reali per verificare la sicurezza del sistema.
"""

import requests
import time
import threading
import random
import string
from concurrent.futures import ThreadPoolExecutor
import json

class SecurityPenetrationTester:
    """Tester per penetration testing del sistema CMS."""
    
    def __init__(self, base_url: str = "http://localhost:8001"):
        self.base_url = base_url
        self.session = requests.Session()
        self.results = {
            "vulnerabilities": [],
            "warnings": [],
            "passed_tests": [],
            "failed_tests": []
        }
    
    def log_vulnerability(self, test_name: str, description: str, severity: str = "HIGH"):
        """Logga una vulnerabilità trovata."""
        self.results["vulnerabilities"].append({
            "test": test_name,
            "description": description,
            "severity": severity,
            "timestamp": time.time()
        })
        print(f"🚨 VULNERABILITÀ {severity}: {test_name} - {description}")
    
    def log_warning(self, test_name: str, description: str):
        """Logga un warning."""
        self.results["warnings"].append({
            "test": test_name,
            "description": description,
            "timestamp": time.time()
        })
        print(f"⚠️  WARNING: {test_name} - {description}")
    
    def log_passed(self, test_name: str):
        """Logga un test passato."""
        self.results["passed_tests"].append(test_name)
        print(f"✅ PASSED: {test_name}")
    
    def log_failed(self, test_name: str, error: str):
        """Logga un test fallito."""
        self.results["failed_tests"].append({
            "test": test_name,
            "error": error,
            "timestamp": time.time()
        })
        print(f"❌ FAILED: {test_name} - {error}")

    def test_brute_force_protection(self):
        """Test protezione brute force su login."""
        print("\n🔍 Testing Brute Force Protection...")
        
        test_credentials = [
            {"username": "admin", "password": "admin"},
            {"username": "admin", "password": "password"},
            {"username": "admin", "password": "123456"},
            {"username": "test", "password": "test"},
            {"username": "user", "password": "user"}
        ]
        
        attempt_count = 0
        blocked = False
        
        for i in range(20):  # Tenta 20 volte
            creds = random.choice(test_credentials)
            
            try:
                response = self.session.post(
                    f"{self.base_url}/api/auth/login",
                    data=creds,
                    timeout=10
                )
                
                attempt_count += 1
                
                if response.status_code == 429:  # Too Many Requests
                    blocked = True
                    self.log_passed("Brute Force Protection - Rate limiting attivo")
                    break
                elif response.status_code == 423:  # Locked
                    blocked = True
                    self.log_passed("Brute Force Protection - Account locking attivo")
                    break
                elif i > 10 and response.status_code != 401:
                    self.log_warning(
                        "Brute Force Protection", 
                        f"Risposta inaspettata dopo {i} tentativi: {response.status_code}"
                    )
                
                time.sleep(0.1)  # Piccola pausa tra i tentativi
                
            except requests.exceptions.RequestException as e:
                self.log_failed("Brute Force Protection", f"Errore di connessione: {e}")
                return
        
        if not blocked and attempt_count >= 15:
            self.log_vulnerability(
                "Brute Force Protection",
                f"Nessuna protezione brute force rilevata dopo {attempt_count} tentativi",
                "HIGH"
            )
    
    def test_sql_injection_attacks(self):
        """Test attacchi SQL injection."""
        print("\n🔍 Testing SQL Injection Protection...")
        
        sql_payloads = [
            "' OR '1'='1",
            "'; DROP TABLE users; --",
            "' UNION SELECT * FROM users --",
            "admin'--",
            "' OR 1=1 --",
            "'; INSERT INTO users VALUES ('hacker', 'password'); --",
            "' OR 'x'='x",
            "1' AND 1=1 --",
            "' OR 'a'='a",
            "'; EXEC xp_cmdshell('dir'); --"
        ]
        
        endpoints_to_test = [
            ("/api/auth/login", {"username": "{payload}", "password": "test"}),
            ("/api/password/request-password-reset", {"email": "{payload}@test.com", "user_type": "user"}),
            ("/api/auth/login/cantiere", {"codice_univoco": "{payload}", "password": "test"})
        ]
        
        vulnerabilities_found = 0
        
        for endpoint, data_template in endpoints_to_test:
            for payload in sql_payloads:
                try:
                    # Sostituisce il payload nel template
                    test_data = {}
                    for key, value in data_template.items():
                        test_data[key] = value.replace("{payload}", payload)
                    
                    response = self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=test_data,
                        timeout=10
                    )
                    
                    # Controlla segni di SQL injection
                    response_text = response.text.lower()
                    
                    if any(error in response_text for error in [
                        "sql syntax", "mysql", "postgresql", "sqlite", 
                        "ora-", "syntax error", "quoted string not properly terminated"
                    ]):
                        self.log_vulnerability(
                            "SQL Injection",
                            f"Possibile SQL injection in {endpoint} con payload: {payload[:50]}...",
                            "CRITICAL"
                        )
                        vulnerabilities_found += 1
                    
                    elif response.status_code == 500:
                        self.log_warning(
                            "SQL Injection",
                            f"Errore server 500 in {endpoint} con payload SQL - possibile vulnerabilità"
                        )
                    
                    time.sleep(0.05)  # Pausa per non sovraccaricare
                    
                except requests.exceptions.RequestException as e:
                    self.log_failed("SQL Injection Test", f"Errore di connessione: {e}")
        
        if vulnerabilities_found == 0:
            self.log_passed("SQL Injection Protection - Nessuna vulnerabilità rilevata")
    
    def test_xss_protection(self):
        """Test protezione XSS."""
        print("\n🔍 Testing XSS Protection...")
        
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "<svg onload=alert('XSS')>",
            "';alert('XSS');//",
            "<iframe src=javascript:alert('XSS')></iframe>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>",
            "<textarea onfocus=alert('XSS') autofocus>"
        ]
        
        endpoints_to_test = [
            "/api/password/request-password-reset",
            "/api/auth/login",
            "/api/auth/login/cantiere"
        ]
        
        vulnerabilities_found = 0
        
        for endpoint in endpoints_to_test:
            for payload in xss_payloads:
                try:
                    test_data = {"email": payload, "user_type": "user"}
                    
                    response = self.session.post(
                        f"{self.base_url}{endpoint}",
                        json=test_data,
                        timeout=10
                    )
                    
                    # Controlla se il payload è riflesso nella risposta
                    if payload in response.text:
                        self.log_vulnerability(
                            "XSS Reflection",
                            f"Payload XSS riflesso in {endpoint}: {payload[:50]}...",
                            "HIGH"
                        )
                        vulnerabilities_found += 1
                    
                    time.sleep(0.05)
                    
                except requests.exceptions.RequestException as e:
                    self.log_failed("XSS Test", f"Errore di connessione: {e}")
        
        if vulnerabilities_found == 0:
            self.log_passed("XSS Protection - Nessuna vulnerabilità rilevata")
    
    def test_rate_limiting_effectiveness(self):
        """Test efficacia del rate limiting."""
        print("\n🔍 Testing Rate Limiting Effectiveness...")
        
        def make_request():
            try:
                response = self.session.post(
                    f"{self.base_url}/api/password/request-password-reset",
                    json={"email": "<EMAIL>", "user_type": "user"},
                    timeout=5
                )
                return response.status_code
            except:
                return None
        
        # Test con richieste concorrenti
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(50)]
            results = [f.result() for f in futures]
        
        # Conta le risposte
        success_count = results.count(200)
        rate_limited_count = results.count(429)
        
        if rate_limited_count == 0:
            self.log_vulnerability(
                "Rate Limiting",
                f"Nessun rate limiting rilevato su 50 richieste concorrenti",
                "MEDIUM"
            )
        elif rate_limited_count < 10:
            self.log_warning(
                "Rate Limiting",
                f"Rate limiting debole: solo {rate_limited_count}/50 richieste bloccate"
            )
        else:
            self.log_passed(f"Rate Limiting - {rate_limited_count}/50 richieste bloccate")
    
    def test_password_reset_token_security(self):
        """Test sicurezza dei token di reset password."""
        print("\n🔍 Testing Password Reset Token Security...")
        
        # Test 1: Token predictability
        tokens = []
        for i in range(5):
            try:
                response = self.session.post(
                    f"{self.base_url}/api/password/request-password-reset",
                    json={"email": f"test{i}@example.com", "user_type": "user"},
                    timeout=10
                )
                
                # In un sistema reale, dovresti intercettare i token dalle email
                # Per questo test, assumiamo che i token non siano prevedibili
                time.sleep(1)
                
            except requests.exceptions.RequestException as e:
                self.log_failed("Token Security Test", f"Errore di connessione: {e}")
        
        # Test 2: Token brute force
        common_tokens = [
            "123456", "password", "reset", "token", "admin",
            "12345678", "qwerty", "abc123", "test", "user"
        ]
        
        for token in common_tokens:
            try:
                response = self.session.post(
                    f"{self.base_url}/api/password/confirm-password-reset",
                    json={
                        "token": token,
                        "new_password": "NewPass123!",
                        "confirm_password": "NewPass123!"
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.log_vulnerability(
                        "Token Security",
                        f"Token prevedibile accettato: {token}",
                        "CRITICAL"
                    )
                
                time.sleep(0.1)
                
            except requests.exceptions.RequestException as e:
                self.log_failed("Token Brute Force Test", f"Errore di connessione: {e}")
        
        self.log_passed("Password Reset Token Security - Test completati")
    
    def test_information_disclosure(self):
        """Test per information disclosure."""
        print("\n🔍 Testing Information Disclosure...")
        
        # Test 1: Error messages troppo dettagliati
        test_cases = [
            ("/api/auth/login", {"username": "nonexistent", "password": "test"}),
            ("/api/password/request-password-reset", {"email": "<EMAIL>", "user_type": "user"}),
            ("/api/auth/login/cantiere", {"codice_univoco": "nonexistent", "password": "test"})
        ]
        
        for endpoint, data in test_cases:
            try:
                response = self.session.post(
                    f"{self.base_url}{endpoint}",
                    json=data,
                    timeout=10
                )
                
                response_text = response.text.lower()
                
                # Controlla messaggi che rivelano troppo
                if any(phrase in response_text for phrase in [
                    "user not found", "email not found", "invalid user",
                    "user does not exist", "no such user"
                ]):
                    self.log_warning(
                        "Information Disclosure",
                        f"Messaggio troppo dettagliato in {endpoint}"
                    )
                
            except requests.exceptions.RequestException as e:
                self.log_failed("Information Disclosure Test", f"Errore di connessione: {e}")
        
        self.log_passed("Information Disclosure - Test completati")
    
    def run_all_tests(self):
        """Esegue tutti i test di penetrazione."""
        print("🚀 Avvio Security Penetration Testing...")
        print(f"Target: {self.base_url}")
        print("=" * 60)
        
        try:
            self.test_brute_force_protection()
            self.test_sql_injection_attacks()
            self.test_xss_protection()
            self.test_rate_limiting_effectiveness()
            self.test_password_reset_token_security()
            self.test_information_disclosure()
            
        except KeyboardInterrupt:
            print("\n⏹️  Test interrotti dall'utente")
        
        self.print_summary()
    
    def print_summary(self):
        """Stampa il riassunto dei risultati."""
        print("\n" + "=" * 60)
        print("📊 RIASSUNTO SECURITY PENETRATION TEST")
        print("=" * 60)
        
        print(f"✅ Test Passati: {len(self.results['passed_tests'])}")
        print(f"⚠️  Warning: {len(self.results['warnings'])}")
        print(f"🚨 Vulnerabilità: {len(self.results['vulnerabilities'])}")
        print(f"❌ Test Falliti: {len(self.results['failed_tests'])}")
        
        if self.results['vulnerabilities']:
            print("\n🚨 VULNERABILITÀ CRITICHE:")
            for vuln in self.results['vulnerabilities']:
                print(f"  - {vuln['test']}: {vuln['description']} [{vuln['severity']}]")
        
        if self.results['warnings']:
            print("\n⚠️  WARNING:")
            for warning in self.results['warnings']:
                print(f"  - {warning['test']}: {warning['description']}")
        
        if self.results['failed_tests']:
            print("\n❌ TEST FALLITI:")
            for failed in self.results['failed_tests']:
                print(f"  - {failed['test']}: {failed['error']}")
        
        # Calcola score di sicurezza
        total_tests = (len(self.results['passed_tests']) + 
                      len(self.results['warnings']) + 
                      len(self.results['vulnerabilities']))
        
        if total_tests > 0:
            security_score = (len(self.results['passed_tests']) / total_tests) * 100
            print(f"\n🎯 SECURITY SCORE: {security_score:.1f}%")
            
            if security_score >= 90:
                print("🟢 Sicurezza ECCELLENTE")
            elif security_score >= 75:
                print("🟡 Sicurezza BUONA")
            elif security_score >= 50:
                print("🟠 Sicurezza MEDIA - Miglioramenti necessari")
            else:
                print("🔴 Sicurezza SCARSA - Intervento urgente richiesto")

if __name__ == "__main__":
    import sys
    
    base_url = sys.argv[1] if len(sys.argv) > 1 else "http://localhost:8001"
    
    tester = SecurityPenetrationTester(base_url)
    tester.run_all_tests()
