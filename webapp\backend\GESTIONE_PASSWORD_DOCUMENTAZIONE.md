# 🔐 Sistema di Gestione Password Professionale - CMS

## 📋 Panoramica

Il sistema CMS ora include una gestione password professionale completa per tutti e 3 i livelli di utente:
- **Admin/Owner**: Accesso completo al sistema
- **Utenti Standard**: Accesso ai cantieri di competenza
- **Utenti Cantiere**: Accesso diretto al cantiere specifico

## 🏗️ Architettura del Sistema

### Backend (FastAPI)
- **API Endpoints**: `/api/password/*` per tutte le operazioni
- **Sicurezza**: Rate limiting, validazione password, token sicuri
- **Email Service**: Invio notifiche con template HTML professionali
- **Database**: Gestione token reset e eventi di sicurezza

### Frontend (Next.js)
- **Pagine**: `/forgot-password`, `/reset-password`, `/change-password`
- **Componenti**: Form reattivi con validazione real-time
- **API Routes**: Proxy sicuro verso il backend FastAPI

## 🔧 Funzionalità Implementate

### 1. Cambio Password (Utenti Autenticati)
- **Endpoint**: `POST /api/password/change-password`
- **Frontend**: `/change-password`
- **Sicurezza**: Verifica password attuale, validazione nuova password
- **Notifica**: Email di conferma cambio password

### 2. Reset Password (Password Dimenticata)
- **Richiesta**: `POST /api/password/request-password-reset`
- **Conferma**: `POST /api/password/confirm-password-reset`
- **Frontend**: `/forgot-password` → `/reset-password?token=...`
- **Sicurezza**: Token HMAC con scadenza, rate limiting

### 3. Validazione Password
- **Endpoint**: `POST /api/password/validate-password`
- **Criteri**: Lunghezza, maiuscole, minuscole, numeri, caratteri speciali
- **Score**: Da 0 a 5 con suggerimenti miglioramento
- **Real-time**: Validazione durante la digitazione

### 4. Sicurezza Avanzata
- **Rate Limiting**: Max 10 richieste per 5 minuti per IP
- **Token Sicuri**: HMAC-SHA256 con scadenza 30 minuti
- **Brute Force Protection**: Blocco account dopo 5 tentativi
- **Audit Trail**: Log di tutti gli eventi di sicurezza

## 📧 Sistema Email

### Configurazione
```env
# Modalità testing (stampa invece di inviare)
EMAIL_TESTING_MODE=true

# Configurazione Gmail (produzione)
EMAIL_PROVIDER=gmail
GMAIL_USERNAME=<EMAIL>
GMAIL_APP_PASSWORD=your-16-char-app-password
GMAIL_FROM_EMAIL=<EMAIL>
EMAIL_FROM_NAME=CMS Sistema
```

### Template Email
- **Reset Password**: Template HTML responsive con branding
- **Password Cambiata**: Notifica di sicurezza con dettagli
- **Multilingua**: Supporto italiano con possibilità di estensione

## 🚀 Come Utilizzare

### Per Utenti Finali

1. **Password Dimenticata**:
   - Vai su `/forgot-password`
   - Inserisci email e tipo utente
   - Controlla email per link reset
   - Clicca link e imposta nuova password

2. **Cambio Password**:
   - Accedi al sistema
   - Vai su `/change-password`
   - Inserisci password attuale e nuova
   - Conferma cambio

### Per Sviluppatori

1. **Test Sistema**:
   ```bash
   cd webapp/backend
   python test_password_system.py
   ```

2. **Test Email**:
   ```bash
   cd webapp/backend
   python test_email_service.py
   ```

3. **Avvio Servizi**:
   ```bash
   # Backend
   cd webapp/backend
   python -m uvicorn main:app --reload --port 8001
   
   # Frontend
   cd webapp-nextjs
   npm run dev
   ```

## 🔒 Configurazione Sicurezza

### Variabili d'Ambiente Critiche
```env
# Chiave segreta per token (CAMBIARE IN PRODUZIONE!)
SECRET_KEY=your-super-secret-key-change-this-in-production

# Configurazione rate limiting
RATE_LIMIT_WINDOW=300  # 5 minuti
RATE_LIMIT_MAX_REQUESTS=10

# Configurazione password
PASSWORD_RESET_TOKEN_EXPIRY=30  # minuti
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=15  # minuti
```

### Raccomandazioni Produzione

1. **Email Service**:
   - Configura SMTP reale (Gmail, Outlook, SendGrid)
   - Usa App Password per Gmail
   - Imposta `EMAIL_TESTING_MODE=false`

2. **Database**:
   - Backup regolari della tabella `password_reset_tokens`
   - Pulizia periodica token scaduti

3. **Monitoraggio**:
   - Log eventi di sicurezza
   - Alert per tentativi di brute force
   - Monitoraggio rate limiting

## 📊 Endpoint API Completi

### Password Management
- `POST /api/password/change-password` - Cambio password autenticato
- `POST /api/password/request-password-reset` - Richiesta reset
- `POST /api/password/confirm-password-reset` - Conferma reset
- `POST /api/password/validate-password` - Validazione password
- `GET /api/password/verify-reset-token` - Verifica token reset

### Frontend API Routes
- `POST /api/password/change-password` - Proxy cambio password
- `POST /api/password/request-password-reset` - Proxy richiesta reset
- `POST /api/password/confirm-password-reset` - Proxy conferma reset
- `POST /api/password/validate-password` - Proxy validazione
- `GET /api/password/verify-reset-token` - Proxy verifica token

## 🧪 Test e Validazione

### Test Automatici
- ✅ Validazione password con diversi livelli di sicurezza
- ✅ Richiesta reset per tutti i tipi di utente
- ✅ API routes frontend funzionanti
- ✅ Rate limiting configurato
- ✅ Sistema email in modalità testing

### Test Manuali
- ✅ Interfacce utente responsive
- ✅ Validazione real-time password
- ✅ Flusso completo reset password
- ✅ Notifiche email con template professionali

## 🎯 Prossimi Passi

1. **Configurazione Produzione**:
   - Configurare servizio email reale
   - Impostare chiavi segrete sicure
   - Configurare monitoraggio

2. **Miglioramenti Futuri**:
   - Autenticazione a due fattori (2FA)
   - Password policy personalizzabili
   - Dashboard amministrativa per gestione utenti
   - Integrazione con Active Directory/LDAP

## 📞 Supporto

Per problemi o domande:
1. Controlla i log del backend: `webapp/backend/logs/`
2. Esegui test diagnostici: `python test_password_system.py`
3. Verifica configurazione email: `python test_email_service.py`

---

**Sistema implementato con successo! 🎉**
Gestione password professionale attiva per tutti e 3 i livelli di utente.
