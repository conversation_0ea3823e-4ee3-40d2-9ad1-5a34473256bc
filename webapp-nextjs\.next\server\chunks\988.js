"use strict";exports.id=988,exports.ids=[988],exports.modules={26134:(e,t,r)=>{r.d(t,{UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>z,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(43210),o=r(70569),a=r(98599),s=r(11273),i=r(96963),l=r(65551),d=r(31355),u=r(32547),c=r(25028),p=r(46059),f=r(14163),g=r(1359),m=r(42247),h=r(63376),x=r(8730),v=r(60687),y="Dialog",[b,j]=(0,s.A)(y),[C,D]=b(y),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:s,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p,f]=(0,l.i)({prop:o,defaultProp:a??!1,onChange:s,caller:y});return(0,v.jsx)(C,{scope:t,triggerRef:u,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=y;var w="DialogTrigger",k=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=D(w,r),i=(0,a.s)(t,s.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":K(s.open),...n,ref:i,onClick:(0,o.m)(e.onClick,s.onOpenToggle)})});k.displayName=w;var E="DialogPortal",[I,N]=b(E,{forceMount:void 0}),O=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,s=D(E,t);return(0,v.jsx)(I,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(p.C,{present:r||s.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};O.displayName=E;var _="DialogOverlay",F=n.forwardRef((e,t)=>{let r=N(_,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(_,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:n||a.open,children:(0,v.jsx)(A,{...o,ref:t})}):null});F.displayName=_;var P=(0,x.TL)("DialogOverlay.RemoveScroll"),A=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(_,r);return(0,v.jsx)(m.A,{as:P,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":K(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),G="DialogContent",T=n.forwardRef((e,t)=>{let r=N(G,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=D(G,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||a.open,children:a.modal?(0,v.jsx)(L,{...o,ref:t}):(0,v.jsx)(B,{...o,ref:t})})});T.displayName=G;var L=n.forwardRef((e,t)=>{let r=D(G,e.__scopeDialog),s=n.useRef(null),i=(0,a.s)(t,r.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,h.Eq)(e)},[]),(0,v.jsx)(M,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),B=n.forwardRef((e,t)=>{let r=D(G,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,v.jsx)(M,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),M=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:i,...l}=e,c=D(G,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:s,onUnmountAutoFocus:i,children:(0,v.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":K(c.open),...l,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:c.titleId}),(0,v.jsx)(Y,{contentRef:p,descriptionId:c.descriptionId})]})]})}),S="DialogTitle",q=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D(S,r);return(0,v.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});q.displayName=S;var $="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=D($,r);return(0,v.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=$;var Z="DialogClose",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=D(Z,r);return(0,v.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function K(e){return e?"open":"closed"}H.displayName=Z;var U="DialogTitleWarning",[V,X]=(0,s.q)(U,{contentName:G,titleName:S,docsSlug:"dialog"}),J=({titleId:e})=>{let t=X(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},Y=({contentRef:e,descriptionId:t})=>{let r=X("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},z=R,Q=k,ee=O,et=F,er=T,en=q,eo=W,ea=H},40211:(e,t,r)=>{r.d(t,{C1:()=>D,bL:()=>j});var n=r(43210),o=r(98599),a=r(11273),s=r(70569),i=r(65551),l=r(83721),d=r(18853),u=r(46059),c=r(14163),p=r(60687),f="Checkbox",[g,m]=(0,a.A)(f),[h,x]=g(f);function v(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:s,form:l,name:d,onCheckedChange:u,required:c,value:g="on",internal_do_not_use_render:m}=e,[x,v]=(0,i.i)({prop:r,defaultProp:a??!1,onChange:u,caller:f}),[y,b]=n.useState(null),[j,C]=n.useState(null),D=n.useRef(!1),R=!y||!!l||!!y.closest("form"),w={checked:x,disabled:s,setChecked:v,control:y,setControl:b,name:d,form:l,value:g,hasConsumerStoppedPropagationRef:D,required:c,defaultChecked:!k(a)&&a,isFormControl:R,bubbleInput:j,setBubbleInput:C};return(0,p.jsx)(h,{scope:t,...w,children:"function"==typeof m?m(w):o})}var y="CheckboxTrigger",b=n.forwardRef(({__scopeCheckbox:e,onKeyDown:t,onClick:r,...a},i)=>{let{control:l,value:d,disabled:u,checked:f,required:g,setControl:m,setChecked:h,hasConsumerStoppedPropagationRef:v,isFormControl:b,bubbleInput:j}=x(y,e),C=(0,o.s)(i,m),D=n.useRef(f);return n.useEffect(()=>{let e=l?.form;if(e){let t=()=>h(D.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[l,h]),(0,p.jsx)(c.sG.button,{type:"button",role:"checkbox","aria-checked":k(f)?"mixed":f,"aria-required":g,"data-state":E(f),"data-disabled":u?"":void 0,disabled:u,value:d,...a,ref:C,onKeyDown:(0,s.m)(t,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,s.m)(r,e=>{h(e=>!!k(e)||!e),j&&b&&(v.current=e.isPropagationStopped(),v.current||e.stopPropagation())})})});b.displayName=y;var j=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:s,disabled:i,value:l,onCheckedChange:d,form:u,...c}=e;return(0,p.jsx)(v,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:i,required:s,onCheckedChange:d,name:n,form:u,value:l,internal_do_not_use_render:({isFormControl:e})=>(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(b,{...c,ref:t,__scopeCheckbox:r}),e&&(0,p.jsx)(w,{__scopeCheckbox:r})]})})});j.displayName=f;var C="CheckboxIndicator",D=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=x(C,r);return(0,p.jsx)(u.C,{present:n||k(a.checked)||!0===a.checked,children:(0,p.jsx)(c.sG.span,{"data-state":E(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});D.displayName=C;var R="CheckboxBubbleInput",w=n.forwardRef(({__scopeCheckbox:e,...t},r)=>{let{control:a,hasConsumerStoppedPropagationRef:s,checked:i,defaultChecked:u,required:f,disabled:g,name:m,value:h,form:v,bubbleInput:y,setBubbleInput:b}=x(R,e),j=(0,o.s)(r,b),C=(0,l.Z)(i),D=(0,d.X)(a);n.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!s.current;if(C!==i&&e){let r=new Event("click",{bubbles:t});y.indeterminate=k(i),e.call(y,!k(i)&&i),y.dispatchEvent(r)}},[y,C,i,s]);let w=n.useRef(!k(i)&&i);return(0,p.jsx)(c.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:u??w.current,required:f,disabled:g,name:m,value:h,form:v,...t,tabIndex:-1,ref:j,style:{...t.style,...D,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function k(e){return"indeterminate"===e}function E(e){return k(e)?"indeterminate":e?"checked":"unchecked"}w.displayName=R}};