"""
Sistema di Gestione Password Sicuro - Core Security Module
Implementa funzionalità di sicurezza avanzate per la gestione delle password.
"""

import secrets
import string
import hashlib
import hmac
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, <PERSON>ple
import bcrypt
import re
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import smtplib
import os
from dataclasses import dataclass
import logging

# Configurazione logging sicuro
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PasswordPolicy:
    """Politiche di sicurezza per le password."""
    min_length: int = 8
    max_length: int = 128
    require_uppercase: bool = True
    require_lowercase: bool = True
    require_numbers: bool = True
    require_special: bool = True
    min_strength_score: int = 3
    forbidden_patterns: list = None
    
    def __post_init__(self):
        if self.forbidden_patterns is None:
            self.forbidden_patterns = [
                'password', '123456', 'admin', 'qwerty', 'letmein',
                'welcome', 'monkey', 'dragon', 'master', 'login'
            ]

@dataclass
class SecurityConfig:
    """Configurazione di sicurezza del sistema."""
    max_login_attempts: int = 5
    lockout_duration_minutes: int = 15
    token_expiry_minutes: int = 30
    rate_limit_window_seconds: int = 300  # 5 minuti
    max_requests_per_window: int = 10
    
class PasswordValidator:
    """Validatore avanzato per password."""
    
    def __init__(self, policy: PasswordPolicy = None):
        self.policy = policy or PasswordPolicy()
    
    def validate_password(self, password: str) -> Tuple[bool, str, int]:
        """
        Valida una password secondo le politiche di sicurezza.
        
        Args:
            password: Password da validare
            
        Returns:
            Tuple[bool, str, int]: (is_valid, error_message, strength_score)
        """
        if not password:
            return False, "Password non può essere vuota", 0
            
        # Controllo lunghezza
        if len(password) < self.policy.min_length:
            return False, f"Password deve essere di almeno {self.policy.min_length} caratteri", 0
            
        if len(password) > self.policy.max_length:
            return False, f"Password non può superare {self.policy.max_length} caratteri", 0
        
        strength_score = 0
        missing_requirements = []
        
        # Controllo caratteri maiuscoli
        if self.policy.require_uppercase:
            if re.search(r'[A-Z]', password):
                strength_score += 1
            else:
                missing_requirements.append("almeno una lettera maiuscola")
        
        # Controllo caratteri minuscoli
        if self.policy.require_lowercase:
            if re.search(r'[a-z]', password):
                strength_score += 1
            else:
                missing_requirements.append("almeno una lettera minuscola")
        
        # Controllo numeri
        if self.policy.require_numbers:
            if re.search(r'[0-9]', password):
                strength_score += 1
            else:
                missing_requirements.append("almeno un numero")
        
        # Controllo caratteri speciali
        if self.policy.require_special:
            if re.search(r'[^a-zA-Z0-9]', password):
                strength_score += 1
            else:
                missing_requirements.append("almeno un carattere speciale")
        
        # Bonus per lunghezza
        if len(password) >= 12:
            strength_score += 1
        
        # Controllo pattern vietati
        password_lower = password.lower()
        for pattern in self.policy.forbidden_patterns:
            if pattern in password_lower:
                return False, f"Password contiene pattern vietato: {pattern}", strength_score
        
        # Controllo forza minima
        if strength_score < self.policy.min_strength_score:
            error_msg = "Password troppo debole. Mancano: " + ", ".join(missing_requirements)
            return False, error_msg, strength_score
        
        return True, "Password valida", strength_score

class SecureTokenManager:
    """Gestore sicuro per token di reset password."""
    
    def __init__(self, secret_key: str = None):
        self.secret_key = secret_key or os.getenv('SECRET_KEY', 'default-secret-key-change-in-production')
        self.config = SecurityConfig()
    
    def generate_reset_token(self, user_id: int, email: str) -> str:
        """
        Genera un token sicuro per il reset della password.
        
        Args:
            user_id: ID dell'utente
            email: Email dell'utente
            
        Returns:
            str: Token sicuro
        """
        # Timestamp di scadenza
        expiry = int(time.time()) + (self.config.token_expiry_minutes * 60)
        
        # Dati del token
        token_data = f"{user_id}:{email}:{expiry}"
        
        # Genera signature HMAC
        signature = hmac.new(
            self.secret_key.encode(),
            token_data.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Token finale: base64(token_data:signature)
        import base64
        full_token = f"{token_data}:{signature}"
        return base64.urlsafe_b64encode(full_token.encode()).decode()
    
    def validate_reset_token(self, token: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        Valida un token di reset password.
        
        Args:
            token: Token da validare
            
        Returns:
            Tuple[bool, Optional[Dict]]: (is_valid, token_data)
        """
        try:
            import base64
            
            # Decodifica il token
            decoded = base64.urlsafe_b64decode(token.encode()).decode()
            parts = decoded.split(':')
            
            if len(parts) != 4:
                return False, None
            
            user_id, email, expiry_str, signature = parts
            
            # Ricostruisce i dati originali
            token_data = f"{user_id}:{email}:{expiry_str}"
            
            # Verifica signature
            expected_signature = hmac.new(
                self.secret_key.encode(),
                token_data.encode(),
                hashlib.sha256
            ).hexdigest()
            
            if not hmac.compare_digest(signature, expected_signature):
                logger.warning(f"Token signature mismatch for user {user_id}")
                return False, None
            
            # Verifica scadenza
            expiry = int(expiry_str)
            if time.time() > expiry:
                logger.info(f"Token expired for user {user_id}")
                return False, None
            
            return True, {
                'user_id': int(user_id),
                'email': email,
                'expiry': expiry
            }
            
        except Exception as e:
            logger.error(f"Error validating token: {str(e)}")
            return False, None

class RateLimiter:
    """Rate limiter per prevenire attacchi brute force."""
    
    def __init__(self):
        self.attempts = {}  # In produzione usare Redis
        self.config = SecurityConfig()
    
    def is_rate_limited(self, identifier: str) -> Tuple[bool, int]:
        """
        Controlla se un identificatore è rate limited.
        
        Args:
            identifier: Identificatore (IP, user_id, email)
            
        Returns:
            Tuple[bool, int]: (is_limited, seconds_until_reset)
        """
        current_time = time.time()
        window_start = current_time - self.config.rate_limit_window_seconds
        
        # Pulisce tentativi vecchi
        if identifier in self.attempts:
            self.attempts[identifier] = [
                attempt_time for attempt_time in self.attempts[identifier]
                if attempt_time > window_start
            ]
        
        # Conta tentativi nella finestra corrente
        attempt_count = len(self.attempts.get(identifier, []))
        
        if attempt_count >= self.config.max_requests_per_window:
            # Calcola tempo rimanente
            oldest_attempt = min(self.attempts[identifier])
            reset_time = oldest_attempt + self.config.rate_limit_window_seconds
            seconds_until_reset = int(reset_time - current_time)
            return True, max(0, seconds_until_reset)
        
        return False, 0
    
    def record_attempt(self, identifier: str):
        """Registra un tentativo per l'identificatore."""
        if identifier not in self.attempts:
            self.attempts[identifier] = []
        
        self.attempts[identifier].append(time.time())

class SecurePasswordHasher:
    """Hasher sicuro per password con salt automatico."""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """
        Genera hash sicuro della password.
        
        Args:
            password: Password in chiaro
            
        Returns:
            str: Hash bcrypt della password
        """
        # Genera salt casuale e hash
        salt = bcrypt.gensalt(rounds=12)  # 12 rounds = buon compromesso sicurezza/performance
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """
        Verifica una password contro il suo hash.
        
        Args:
            password: Password in chiaro
            hashed: Hash della password
            
        Returns:
            bool: True se la password è corretta
        """
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception as e:
            logger.error(f"Error verifying password: {str(e)}")
            return False

# Istanze globali
password_validator = PasswordValidator()
token_manager = SecureTokenManager()
rate_limiter = RateLimiter()
password_hasher = SecurePasswordHasher()
