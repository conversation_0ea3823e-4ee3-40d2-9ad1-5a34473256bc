"""
Modulo per la criptazione e decriptazione delle password dei cantieri.
Utilizzato per permettere il recupero delle password dimenticate.
"""

import os
import base64
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import secrets
import random
import asyncio

# Chiave master per la criptazione (in produzione dovrebbe essere in variabile d'ambiente)
MASTER_PASSWORD = os.getenv("CANTIERE_MASTER_PASSWORD", "CMS_CANTIERE_MASTER_KEY_2024_SECURE")

def _get_encryption_key() -> bytes:
    """
    Genera una chiave di criptazione derivata dalla master password.
    
    Returns:
        bytes: Chiave di criptazione
    """
    # Usa un salt fisso per garantire che la stessa password generi sempre la stessa chiave
    salt = b'cms_cantiere_salt_2024'
    
    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    
    key = base64.urlsafe_b64encode(kdf.derive(MASTER_PASSWORD.encode()))
    return key

def encrypt_password(password: str) -> str:
    """
    Cripta una password per permetterne il recupero futuro.
    
    Args:
        password (str): Password in chiaro da criptare
        
    Returns:
        str: Password criptata (base64 encoded)
    """
    try:
        key = _get_encryption_key()
        fernet = Fernet(key)
        
        # Cripta la password
        encrypted_password = fernet.encrypt(password.encode())
        
        # Restituisce la password criptata come stringa base64
        return base64.urlsafe_b64encode(encrypted_password).decode()
        
    except Exception as e:
        print(f"Errore nella criptazione della password: {e}")
        return None

def decrypt_password(encrypted_password: str) -> str:
    """
    Decripta una password precedentemente criptata.
    
    Args:
        encrypted_password (str): Password criptata (base64 encoded)
        
    Returns:
        str: Password in chiaro, None se errore
    """
    try:
        key = _get_encryption_key()
        fernet = Fernet(key)
        
        # Decodifica da base64
        encrypted_data = base64.urlsafe_b64decode(encrypted_password.encode())
        
        # Decripta la password
        decrypted_password = fernet.decrypt(encrypted_data)
        
        return decrypted_password.decode()
        
    except Exception as e:
        print(f"Errore nella decriptazione della password: {e}")
        return None

def generate_random_delay() -> float:
    """
    Genera un delay casuale tra 2 e 5 secondi per la sicurezza anti-bot.
    
    Returns:
        float: Delay in secondi
    """
    return random.uniform(2.0, 5.0)

async def secure_password_recovery(encrypted_password: str) -> tuple[str, float]:
    """
    Recupera una password con delay casuale per sicurezza anti-bot.
    
    Args:
        encrypted_password (str): Password criptata da decriptare
        
    Returns:
        tuple[str, float]: (password_decriptata, delay_utilizzato)
    """
    # Genera delay casuale
    delay = generate_random_delay()
    
    # Attende il delay
    await asyncio.sleep(delay)
    
    # Decripta la password
    password = decrypt_password(encrypted_password)
    
    return password, delay

def test_encryption():
    """
    Funzione di test per verificare che la criptazione/decriptazione funzioni.
    """
    test_password = "TestPassword123!"
    
    print(f"Password originale: {test_password}")
    
    # Cripta
    encrypted = encrypt_password(test_password)
    print(f"Password criptata: {encrypted}")
    
    # Decripta
    decrypted = decrypt_password(encrypted)
    print(f"Password decriptata: {decrypted}")
    
    # Verifica
    if test_password == decrypted:
        print("✅ Test criptazione/decriptazione SUCCESSO")
        return True
    else:
        print("❌ Test criptazione/decriptazione FALLITO")
        return False

if __name__ == "__main__":
    test_encryption()
