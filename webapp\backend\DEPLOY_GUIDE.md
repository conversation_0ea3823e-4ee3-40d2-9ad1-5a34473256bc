# 🚀 Guida Deploy - Sistema Gestione Password CMS

## 📋 Panoramica

Questa guida fornisce istruzioni complete per il deploy del sistema di gestione password professionale per il CMS con supporto ai 3 livelli di utente.

## 🔧 Prerequisiti

### Sistema
- **Python**: 3.8+ con pip
- **Node.js**: 18+ con npm/yarn
- **Database**: PostgreSQL 12+
- **Server Web**: Nginx (raccomandato)
- **SSL**: Certificato valido per HTTPS

### Servizi Email
- **Gmail**: App Password configurata
- **Outlook**: Credenziali SMTP
- **SendGrid**: API Key (alternativa)
- **Custom SMTP**: Server configurato

## 🏗️ Preparazione Ambiente

### 1. Clonazione e Setup
```bash
# Clone del repository
git clone <repository-url>
cd CMS

# Setup backend
cd webapp/backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# oppure
venv\Scripts\activate     # Windows

pip install -r requirements.txt

# Setup frontend
cd ../webapp-nextjs
npm install
```

### 2. Configurazione Database
```sql
-- Creazione database (PostgreSQL)
CREATE DATABASE cms_production;
CREATE USER cms_user WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE cms_production TO cms_user;
```

### 3. Variabili d'Ambiente Produzione

#### Backend (.env)
```env
# Database
DATABASE_URL=postgresql://cms_user:secure_password_here@localhost/cms_production

# Sicurezza (CAMBIARE ASSOLUTAMENTE!)
SECRET_KEY=your-super-secure-secret-key-minimum-32-characters-long
JWT_SECRET_KEY=another-super-secure-jwt-key-minimum-32-characters

# Email Service (Gmail esempio)
EMAIL_TESTING_MODE=false
EMAIL_PROVIDER=gmail
GMAIL_USERNAME=<EMAIL>
GMAIL_APP_PASSWORD=your-16-char-app-password
GMAIL_FROM_EMAIL=<EMAIL>
EMAIL_FROM_NAME=CMS Sistema

# Rate Limiting
RATE_LIMIT_WINDOW=300
RATE_LIMIT_MAX_REQUESTS=10

# Password Policy
PASSWORD_RESET_TOKEN_EXPIRY=30
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCKOUT_DURATION=15

# Ambiente
ENVIRONMENT=production
DEBUG=false
```

#### Frontend (.env.local)
```env
# API Backend
NEXT_PUBLIC_API_URL=https://yourdomain.com/api
NEXTAUTH_URL=https://yourdomain.com
NEXTAUTH_SECRET=your-nextauth-secret-key

# Ambiente
NODE_ENV=production
```

## 📧 Configurazione Email

### Gmail (Raccomandato)
1. Abilita autenticazione a 2 fattori
2. Genera App Password:
   - Google Account → Sicurezza → App Password
   - Seleziona "Mail" e "Computer"
   - Copia la password a 16 caratteri
3. Configura nel .env:
   ```env
   EMAIL_PROVIDER=gmail
   GMAIL_USERNAME=<EMAIL>
   GMAIL_APP_PASSWORD=abcd-efgh-ijkl-mnop
   ```

### Outlook/Hotmail
```env
EMAIL_PROVIDER=outlook
OUTLOOK_USERNAME=<EMAIL>
OUTLOOK_PASSWORD=your-password
```

### SendGrid
```env
EMAIL_PROVIDER=sendgrid
SENDGRID_API_KEY=your-sendgrid-api-key
```

### SMTP Personalizzato
```env
EMAIL_PROVIDER=custom
SMTP_HOST=mail.yourdomain.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-smtp-password
SMTP_USE_TLS=true
```

## 🗄️ Setup Database

### 1. Migrazione Schema
```bash
cd webapp/backend

# Creazione tabelle
python -c "
from database import engine
from models.models import Base
from models.security_models import Base as SecurityBase
Base.metadata.create_all(bind=engine)
SecurityBase.metadata.create_all(bind=engine)
print('Database schema creato con successo!')
"
```

### 2. Dati Iniziali (Opzionale)
```bash
# Creazione utente admin iniziale
python -c "
from database import get_db
from models.models import User
from passlib.context import CryptContext
from sqlalchemy.orm import Session

pwd_context = CryptContext(schemes=['bcrypt'], deprecated='auto')
db = next(get_db())

admin = User(
    username='admin',
    email='<EMAIL>',
    password_hash=pwd_context.hash('ChangeThisPassword123!'),
    is_active=True,
    role='admin'
)

db.add(admin)
db.commit()
print('Utente admin creato!')
"
```

## 🚀 Deploy Applicazione

### 1. Build Frontend
```bash
cd webapp-nextjs
npm run build
npm run export  # Se serve static export
```

### 2. Configurazione Nginx

#### /etc/nginx/sites-available/cms
```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;

    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    # SSL Security
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;

    # Frontend Next.js
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Backend API
    location /api/ {
        proxy_pass http://localhost:8001/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Rate limiting
        limit_req zone=api burst=20 nodelay;
    }

    # Rate limiting zone
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/m;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
```

### 3. Servizi Systemd

#### /etc/systemd/system/cms-backend.service
```ini
[Unit]
Description=CMS Backend FastAPI
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/CMS/webapp/backend
Environment=PATH=/path/to/CMS/webapp/backend/venv/bin
ExecStart=/path/to/CMS/webapp/backend/venv/bin/uvicorn main:app --host 0.0.0.0 --port 8001
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

#### /etc/systemd/system/cms-frontend.service
```ini
[Unit]
Description=CMS Frontend Next.js
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/CMS/webapp-nextjs
Environment=NODE_ENV=production
ExecStart=/usr/bin/npm start
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
```

### 4. Avvio Servizi
```bash
# Abilita e avvia servizi
sudo systemctl enable cms-backend cms-frontend nginx
sudo systemctl start cms-backend cms-frontend nginx

# Verifica stato
sudo systemctl status cms-backend cms-frontend nginx
```

## 🔒 Sicurezza Produzione

### 1. Firewall
```bash
# UFW (Ubuntu)
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable

# Blocca accesso diretto alle porte applicazione
sudo ufw deny 3000
sudo ufw deny 8001
```

### 2. Backup Database
```bash
# Script backup giornaliero
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump cms_production > /backup/cms_backup_$DATE.sql
find /backup -name "cms_backup_*.sql" -mtime +7 -delete
```

### 3. Monitoraggio Log
```bash
# Log applicazione
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log
journalctl -u cms-backend -f
journalctl -u cms-frontend -f
```

## ✅ Test Post-Deploy

### 1. Verifica Servizi
```bash
# Test endpoint API
curl -X POST https://yourdomain.com/api/password/validate-password \
  -H "Content-Type: application/json" \
  -d '{"password":"Test123!"}'

# Test pagine frontend
curl -I https://yourdomain.com/forgot-password
curl -I https://yourdomain.com/change-password
```

### 2. Test Email
```bash
cd webapp/backend
python test_email_service.py
```

### 3. Test Sicurezza
- Verifica SSL: https://www.ssllabs.com/ssltest/
- Test rate limiting con richieste multiple
- Verifica headers sicurezza

## 🔧 Manutenzione

### Aggiornamenti
```bash
# Backend
cd webapp/backend
git pull
pip install -r requirements.txt
sudo systemctl restart cms-backend

# Frontend
cd webapp-nextjs
git pull
npm install
npm run build
sudo systemctl restart cms-frontend
```

### Pulizia Database
```sql
-- Rimuovi token scaduti (esegui settimanalmente)
DELETE FROM password_reset_tokens 
WHERE expires_at < NOW() - INTERVAL '1 day';

-- Archivia eventi vecchi (esegui mensilmente)
DELETE FROM security_events 
WHERE created_at < NOW() - INTERVAL '90 days';
```

## 📞 Troubleshooting

### Problemi Comuni
1. **Email non inviate**: Verifica credenziali SMTP e firewall
2. **Rate limiting troppo aggressivo**: Modifica configurazione Nginx
3. **Errori database**: Controlla connessione e permessi
4. **SSL errors**: Verifica certificato e configurazione Nginx

### Log Utili
```bash
# Errori applicazione
journalctl -u cms-backend --since "1 hour ago"
journalctl -u cms-frontend --since "1 hour ago"

# Errori Nginx
tail -f /var/log/nginx/error.log

# Database
sudo -u postgres tail -f /var/log/postgresql/postgresql-*.log
```

---

**Deploy completato! 🎉**
Il sistema di gestione password è ora attivo in produzione con sicurezza professionale.
