{"ast": null, "code": "import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\nconst API_URL = config.API_URL;\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', {\n        cantiereId,\n        tipoCavo,\n        filters\n      });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Verifica che cantiereId sia definito\n      if (cantiereId === undefined || cantiereId === null) {\n        console.error('cantiereId è undefined o null');\n        throw new Error('ID cantiere mancante');\n      }\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          });\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, {\n          timeout: 60000\n        });\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n        return response.data;\n      } catch (apiError) {\n        var _apiError$response, _apiError$response2, _apiError$response3, _apiError$response4;\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: (_apiError$response = apiError.response) === null || _apiError$response === void 0 ? void 0 : _apiError$response.status,\n          statusText: (_apiError$response2 = apiError.response) === null || _apiError$response2 === void 0 ? void 0 : _apiError$response2.statusText,\n          data: (_apiError$response3 = apiError.response) === null || _apiError$response3 === void 0 ? void 0 : _apiError$response3.data,\n          headers: (_apiError$response4 = apiError.response) === null || _apiError$response4 === void 0 ? void 0 : _apiError$response4.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n        throw apiError;\n      }\n    } catch (error) {\n      var _error$response, _error$response2, _error$response3, _error$response4, _error$response4$data, _error$response5, _error$response6;\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status,\n        statusText: (_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.statusText,\n        data: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.detail) || error.message || 'Errore sconosciuto');\n      enhancedError.status = (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.status;\n      enhancedError.data = (_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : _error$response6.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n      throw enhancedError;\n    }\n  },\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);\n      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/health`, {\n          method: 'GET'\n        });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n        if (!pingResponse.ok) {\n          console.error('Il server non risponde correttamente:', pingResponse.status);\n          throw new Error('Il server non risponde correttamente. Riprova più tardi.');\n        }\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');\n      }\n\n      // Usa axiosInstance con la configurazione migliorata\n      console.log('Invio richiesta con axiosInstance...');\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData, {\n        timeout: 90000 // 90 secondi (timeout esteso)\n      });\n      console.log('Risposta del server:', response.status, response.statusText);\n      console.log('Dati ricevuti:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n\n      // Gestione dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta alcuna risposta\n        console.error('Nessuna risposta dal server:', error.request);\n\n        // Prova un approccio alternativo con XMLHttpRequest\n        console.log('Tentativo con XMLHttpRequest...');\n        return new Promise((resolve, reject) => {\n          const xhr = new XMLHttpRequest();\n          xhr.open('POST', `${API_URL}/cavi/${cantiereId}`, true);\n          xhr.setRequestHeader('Content-Type', 'application/json');\n\n          // Ottieni il token di autenticazione\n          const authToken = localStorage.getItem('token');\n          if (authToken) {\n            xhr.setRequestHeader('Authorization', `Bearer ${authToken}`);\n          }\n          xhr.timeout = 60000; // 60 secondi\n\n          xhr.onload = function () {\n            if (xhr.status >= 200 && xhr.status < 300) {\n              console.log('XMLHttpRequest successo:', xhr.responseText);\n              try {\n                const data = JSON.parse(xhr.responseText);\n                resolve(data);\n              } catch (e) {\n                console.warn('Risposta non contiene JSON valido:', e);\n                resolve({\n                  message: 'Operazione completata con successo'\n                });\n              }\n            } else {\n              console.error('XMLHttpRequest errore:', xhr.status, xhr.statusText);\n              reject(new Error(`Errore ${xhr.status}: ${xhr.statusText}`));\n            }\n          };\n          xhr.onerror = function () {\n            console.error('XMLHttpRequest errore di rete');\n            reject(new Error('Errore di rete. Verifica la connessione e riprova.'));\n          };\n          xhr.ontimeout = function () {\n            console.error('XMLHttpRequest timeout');\n            reject(new Error('Timeout della richiesta. Il server non risponde.'));\n          };\n          xhr.send(JSON.stringify(cavoData));\n        });\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw new Error(`Errore di connessione: ${error.message}`);\n      }\n    }\n  },\n  // Ottiene un cavo specifico per ID\n  getCavoById: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavo by ID error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Dati inviati:', cavoData);\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/health`, {\n          method: 'GET'\n        });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n        if (!pingResponse.ok) {\n          console.error('Il server non risponde correttamente:', pingResponse.status);\n          throw new Error('Il server non risponde correttamente. Riprova più tardi.');\n        }\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');\n      }\n\n      // Imposta un timeout più lungo per la richiesta\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {\n        timeout: 90000 // 90 secondi (timeout esteso)\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n\n      // Gestione più dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta alcuna risposta\n        console.error('Nessuna risposta dal server:', error.request);\n        throw {\n          detail: 'Nessuna risposta dal server. La modifica potrebbe essere stata salvata comunque. Verifica prima di riprovare.',\n          status: 0,\n          isNetworkError: true\n        };\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw {\n          detail: error.message,\n          status: 500\n        };\n      }\n    }\n  },\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        force\n      });\n\n      // Prova prima con l'endpoint POST specifico\n      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);\n      try {\n        const postResponse = await axios.post(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`, {\n          force: force\n        }, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n        return cavoResponse.data;\n      } catch (postError) {\n        // Se fallisce il POST, prova con DELETE mode=spare\n        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);\n        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n        const deleteResponse = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: {\n            mode: 'spare'\n          }\n        });\n        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');\n        const cavoResponse = await axios.get(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n        return cavoResponse.data;\n      }\n    } catch (error) {\n      var _error$response7, _error$response8, _error$response9;\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: (_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.status,\n        statusText: (_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : _error$response8.statusText,\n        data: (_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', {\n        cantiereId: cantiereIdNum,\n        cavoId,\n        mode\n      });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000,\n        // Timeout aumentato a 30 secondi\n        params: mode ? {\n          mode\n        } : {}\n      };\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response1, _error$response10;\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: (_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : _error$response0.status,\n        statusText: (_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : _error$response1.statusText,\n        data: (_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : _error$response10.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/metri-posati`);\n      console.log('Metri posati:', metriPosati);\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/bobina`);\n      console.log('ID Bobina:', idBobina);\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Ottiene direttamente i cavi SPARE\n  getCaviSpare: async cantiereId => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log('Caricamento cavi SPARE...');\n\n      // Prova prima con l'endpoint standard con tipo_cavo=3\n      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      try {\n        const response = await axios.get(`${API_URL}/cavi/${cantiereIdNum}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000,\n          params: {\n            tipo_cavo: 3\n          }\n        });\n        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo SPARE:', response.data[0]);\n        }\n        return response.data;\n      } catch (standardError) {\n        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);\n\n        // Se fallisce, prova con l'endpoint dedicato\n        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n        const dedicatedResponse = await axios.get(`${API_URL}/cavi/spare/${cantiereIdNum}`, {\n          headers: {\n            'Content-Type': 'application/json',\n            'Authorization': `Bearer ${localStorage.getItem('token')}`\n          },\n          timeout: 30000\n        });\n        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');\n        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {\n          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);\n        }\n        return dedicatedResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/collegamento`);\n      console.log('Dati:', {\n        lato,\n        responsabile\n      });\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      }, {\n        timeout: 60000 // 60 secondi\n      });\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw {\n          detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.',\n          status: 0,\n          isNetworkError: true\n        };\n      }\n      throw error.response ? error.response.data : {\n        detail: error.message,\n        status: 500\n      };\n    }\n  },\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`);\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\nexport default caviService;", "map": {"version": 3, "names": ["axios", "config", "axiosInstance", "API_URL", "caviService", "get<PERSON><PERSON>", "cantiereId", "tipoCavo", "filters", "console", "log", "undefined", "error", "Error", "cantiereIdNum", "parseInt", "isNaN", "response", "get", "headers", "localStorage", "getItem", "timeout", "data", "spareError", "url", "queryParams", "push", "stato_installazione", "encodeURIComponent", "tipologia", "sort_by", "sort_order", "length", "join", "status", "Array", "isArray", "warn", "apiError", "_apiError$response", "_apiError$response2", "_apiError$response3", "_apiError$response4", "message", "statusText", "code", "isAxiosError", "method", "testResponse", "fetch", "testError", "_error$response", "_error$response2", "_error$response3", "_error$response4", "_error$response4$data", "_error$response5", "_error$response6", "stack", "includes", "enhancedError", "detail", "originalError", "createCavo", "cavoData", "JSON", "stringify", "pingResponse", "ok", "pingError", "post", "request", "Promise", "resolve", "reject", "xhr", "XMLHttpRequest", "open", "setRequestHeader", "authToken", "onload", "responseText", "parse", "e", "onerror", "ontimeout", "send", "getCavoById", "cavoId", "updateCavo", "put", "isNetworkError", "isTimeoutError", "customMessage", "getRevisioneCorrente", "revisione_corrente", "markCavoAsSpare", "force", "postResponse", "setTimeout", "cavoResponse", "modificato_manualmente", "postError", "deleteResponse", "delete", "params", "mode", "_error$response7", "_error$response8", "_error$response9", "deleteCavo", "requestConfig", "_error$response0", "_error$response1", "_error$response10", "updateMetri<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "metri_posati", "updateBobina", "idBobina", "id_bobina", "getCaviInstallati", "getCaviStats", "getCaviSpare", "tipo_cavo", "standardError", "dedicatedResponse", "collegaCavo", "lato", "responsabile", "scollegaCavo", "debugCavo", "attivi", "cavoAttivo", "find", "c", "id_cavo", "spare", "cavoSpare", "trovato_tra_attivi", "trovato_tra_spare", "cavo_attivo", "cavo_spare"], "sources": ["C:/Users/<USER>/PycharmProjects/CMS/webapp/frontend/src/services/caviService.js"], "sourcesContent": ["import axios from 'axios';\nimport config from '../config';\nimport axiosInstance from './axiosConfig';\n\nconst API_URL = config.API_URL;\n\nconst caviService = {\n  // Ottiene la lista dei cavi di un cantiere\n  getCavi: async (cantiereId, tipoCavo = null, filters = {}) => {\n    try {\n      console.log('getCavi chiamato con:', { cantiereId, tipoCavo, filters });\n      console.log('Tipo di cantiereId:', typeof cantiereId);\n\n      // Verifica che cantiereId sia definito\n      if (cantiereId === undefined || cantiereId === null) {\n        console.error('cantiereId è undefined o null');\n        throw new Error('ID cantiere mancante');\n      }\n\n      // Assicurati che cantiereId sia un numero\n      let cantiereIdNum = cantiereId;\n      if (typeof cantiereId === 'string') {\n        cantiereIdNum = parseInt(cantiereId, 10);\n        console.log('cantiereId convertito da stringa a numero:', cantiereIdNum);\n      }\n\n      if (isNaN(cantiereIdNum)) {\n        console.error('ID cantiere non è un numero valido:', cantiereId);\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log(`Caricamento cavi per cantiere ${cantiereIdNum} con tipo_cavo=${tipoCavo}`);\n\n      // Soluzione alternativa per i cavi SPARE\n      if (tipoCavo === 3) {\n        console.log('Caricamento cavi SPARE con query diretta...');\n        try {\n          // Usa una query SQL diretta per ottenere i cavi SPARE\n          const response = await axios.get(\n            `${API_URL}/cavi/spare/${cantiereIdNum}`,\n            {\n              headers: {\n                'Content-Type': 'application/json',\n                'Authorization': `Bearer ${localStorage.getItem('token')}`\n              },\n              timeout: 30000\n            }\n          );\n\n          console.log('Risposta cavi SPARE:', response.data);\n          return response.data;\n        } catch (spareError) {\n          console.error('Errore nel caricamento dei cavi SPARE:', spareError);\n          // Se fallisce, continua con il metodo standard\n        }\n      }\n\n      // Costruisci l'URL con i parametri di query\n      let url = `/cavi/${cantiereIdNum}`;\n      const queryParams = [];\n\n      if (tipoCavo !== null) {\n        queryParams.push(`tipo_cavo=${tipoCavo}`);\n      }\n\n      // Aggiungi filtri aggiuntivi se presenti\n      if (filters.stato_installazione) {\n        queryParams.push(`stato_installazione=${encodeURIComponent(filters.stato_installazione)}`);\n      }\n\n      if (filters.tipologia) {\n        queryParams.push(`tipologia=${encodeURIComponent(filters.tipologia)}`);\n      }\n\n      if (filters.sort_by) {\n        queryParams.push(`sort_by=${encodeURIComponent(filters.sort_by)}`);\n        if (filters.sort_order) {\n          queryParams.push(`sort_order=${encodeURIComponent(filters.sort_order)}`);\n        }\n      }\n\n      // Aggiungi i parametri di query all'URL\n      if (queryParams.length > 0) {\n        url += `?${queryParams.join('&')}`;\n      }\n\n      // Log dettagliato dell'URL e dei parametri\n      console.log('URL API completo:', url);\n      console.log('Parametri di query:', queryParams);\n\n      console.log(`Chiamata API: GET ${url}`);\n      console.log('Token:', localStorage.getItem('token') ? 'Presente' : 'Mancante');\n      console.log('URL completo:', `${API_URL}${url}`);\n\n      try {\n        console.log(`Tentativo di chiamata API: GET ${url} con token: ${localStorage.getItem('token') ? 'presente' : 'mancante'}`);\n        console.log('Headers della richiesta:', {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        });\n\n        // Aggiungi un timeout più lungo per la richiesta\n        const response = await axiosInstance.get(url, { timeout: 60000 });\n\n        console.log(`Risposta API: ${url}`, response.data);\n        console.log('Status della risposta:', response.status);\n        console.log('Headers della risposta:', response.headers);\n\n        if (Array.isArray(response.data)) {\n          console.log(`Numero di cavi ricevuti: ${response.data.length}`);\n          if (response.data.length > 0) {\n            console.log('Primo cavo ricevuto:', response.data[0]);\n          } else {\n            console.warn(`Nessun cavo trovato per il cantiere ${cantiereIdNum} con tipo ${tipoCavo}`);\n          }\n        } else {\n          console.warn(`Risposta non è un array: ${typeof response.data}`, response.data);\n        }\n\n        return response.data;\n      } catch (apiError) {\n        console.error(`Errore nella chiamata API GET ${url}:`, apiError);\n        console.error('Dettagli errore API:', {\n          message: apiError.message,\n          status: apiError.response?.status,\n          statusText: apiError.response?.statusText,\n          data: apiError.response?.data,\n          headers: apiError.response?.headers,\n          code: apiError.code,\n          isAxiosError: apiError.isAxiosError,\n          config: apiError.config ? {\n            url: apiError.config.url,\n            method: apiError.config.method,\n            timeout: apiError.config.timeout,\n            headers: apiError.config.headers\n          } : 'No config'\n        });\n\n        // Gestione specifica per errori di rete\n        if (apiError.code === 'ERR_NETWORK') {\n          console.error('Errore di rete. Verifica che il backend sia in esecuzione e accessibile.');\n          // Prova a fare una richiesta di base per verificare se il backend è raggiungibile\n          try {\n            console.log('Tentativo di test di connessione al backend...');\n            const testResponse = await fetch(API_URL);\n            console.log('Test di connessione al backend:', testResponse.status);\n          } catch (testError) {\n            console.error('Test di connessione al backend fallito:', testError);\n          }\n        }\n\n        throw apiError;\n      }\n    } catch (error) {\n      console.error('Get cavi error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `/cavi/${cantiereId}${tipoCavo !== null ? `?tipo_cavo=${tipoCavo}` : ''}`,\n        stack: error.stack\n      });\n\n      // Verifica se l'errore è dovuto a un problema di connessione\n      if (error.code === 'ECONNABORTED' || error.message.includes('timeout') || error.message.includes('Network Error')) {\n        console.error('Errore di connessione o timeout');\n        // Ritorna un array vuoto invece di lanciare un errore\n        console.log('Ritorno array vuoto come fallback');\n        return [];\n      }\n\n      // Crea un errore più informativo\n      const enhancedError = new Error(error.response?.data?.detail || error.message || 'Errore sconosciuto');\n      enhancedError.status = error.response?.status;\n      enhancedError.data = error.response?.data;\n      enhancedError.response = error.response;\n      enhancedError.originalError = error;\n      enhancedError.code = error.code;\n      enhancedError.isAxiosError = error.isAxiosError;\n\n      throw enhancedError;\n    }\n  },\n\n  // Crea un nuovo cavo\n  createCavo: async (cantiereId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Tentativo di creazione cavo per cantiere ${cantiereIdNum}`);\n      console.log('Dati inviati:', JSON.stringify(cavoData, null, 2));\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/health`, { method: 'GET' });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n        if (!pingResponse.ok) {\n          console.error('Il server non risponde correttamente:', pingResponse.status);\n          throw new Error('Il server non risponde correttamente. Riprova più tardi.');\n        }\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');\n      }\n\n      // Usa axiosInstance con la configurazione migliorata\n      console.log('Invio richiesta con axiosInstance...');\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}`, cavoData, {\n        timeout: 90000, // 90 secondi (timeout esteso)\n      });\n\n      console.log('Risposta del server:', response.status, response.statusText);\n      console.log('Dati ricevuti:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Create cavo error:', error);\n\n      // Gestione dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta alcuna risposta\n        console.error('Nessuna risposta dal server:', error.request);\n\n        // Prova un approccio alternativo con XMLHttpRequest\n        console.log('Tentativo con XMLHttpRequest...');\n        return new Promise((resolve, reject) => {\n          const xhr = new XMLHttpRequest();\n          xhr.open('POST', `${API_URL}/cavi/${cantiereId}`, true);\n          xhr.setRequestHeader('Content-Type', 'application/json');\n\n          // Ottieni il token di autenticazione\n          const authToken = localStorage.getItem('token');\n          if (authToken) {\n            xhr.setRequestHeader('Authorization', `Bearer ${authToken}`);\n          }\n          xhr.timeout = 60000; // 60 secondi\n\n          xhr.onload = function() {\n            if (xhr.status >= 200 && xhr.status < 300) {\n              console.log('XMLHttpRequest successo:', xhr.responseText);\n              try {\n                const data = JSON.parse(xhr.responseText);\n                resolve(data);\n              } catch (e) {\n                console.warn('Risposta non contiene JSON valido:', e);\n                resolve({ message: 'Operazione completata con successo' });\n              }\n            } else {\n              console.error('XMLHttpRequest errore:', xhr.status, xhr.statusText);\n              reject(new Error(`Errore ${xhr.status}: ${xhr.statusText}`));\n            }\n          };\n\n          xhr.onerror = function() {\n            console.error('XMLHttpRequest errore di rete');\n            reject(new Error('Errore di rete. Verifica la connessione e riprova.'));\n          };\n\n          xhr.ontimeout = function() {\n            console.error('XMLHttpRequest timeout');\n            reject(new Error('Timeout della richiesta. Il server non risponde.'));\n          };\n\n          xhr.send(JSON.stringify(cavoData));\n        });\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw new Error(`Errore di connessione: ${error.message}`);\n      }\n    }\n  },\n\n  // Ottiene un cavo specifico per ID\n  getCavoById: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/${cavoId}`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavo by ID error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Aggiorna un cavo esistente\n  updateCavo: async (cantiereId, cavoId, cavoData) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta PUT a /cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Dati inviati:', cavoData);\n\n      // Verifica che il backend sia raggiungibile\n      try {\n        console.log('Verifica connessione al backend...');\n        const pingResponse = await fetch(`${API_URL}/health`, { method: 'GET' });\n        console.log('Ping al backend:', pingResponse.status, pingResponse.statusText);\n        if (!pingResponse.ok) {\n          console.error('Il server non risponde correttamente:', pingResponse.status);\n          throw new Error('Il server non risponde correttamente. Riprova più tardi.');\n        }\n      } catch (pingError) {\n        console.error('Errore durante il ping al backend:', pingError);\n        throw new Error('Impossibile connettersi al server. Verifica la connessione di rete e riprova.');\n      }\n\n      // Imposta un timeout più lungo per la richiesta\n      const response = await axiosInstance.put(`/cavi/${cantiereIdNum}/${cavoId}`, cavoData, {\n        timeout: 90000, // 90 secondi (timeout esteso)\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      // Gestione più dettagliata dell'errore\n      if (error.response) {\n        // Il server ha risposto con un codice di stato diverso da 2xx\n        console.error('Errore dal server:', error.response.status, error.response.statusText);\n        console.error('Dati errore:', error.response.data);\n        throw error.response.data;\n      } else if (error.request) {\n        // La richiesta è stata inviata ma non è stata ricevuta alcuna risposta\n        console.error('Nessuna risposta dal server:', error.request);\n        throw { detail: 'Nessuna risposta dal server. La modifica potrebbe essere stata salvata comunque. Verifica prima di riprovare.', status: 0, isNetworkError: true };\n      } else {\n        // Si è verificato un errore durante l'impostazione della richiesta\n        console.error('Errore durante l\\'impostazione della richiesta:', error.message);\n        throw { detail: error.message, status: 500 };\n      }\n    }\n  },\n\n  // Ottiene la revisione corrente del cantiere\n  getRevisioneCorrente: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/revisione-corrente`);\n      return response.data.revisione_corrente;\n    } catch (error) {\n      console.error('Get revisione corrente error:', error);\n      return '00'; // Valore di default in caso di errore\n    }\n  },\n\n  // Marca un cavo come SPARE\n  markCavoAsSpare: async (cantiereId, cavoId, force = false) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di marcare cavo come SPARE:', { cantiereId: cantiereIdNum, cavoId, force });\n\n      // Prova prima con l'endpoint POST specifico\n      console.log('URL API (POST):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`);\n\n      try {\n        const postResponse = await axios.post(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}/mark-as-spare`,\n          { force: force },\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Risposta markCavoAsSpare (POST):', postResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n\n        return cavoResponse.data;\n      } catch (postError) {\n        // Se fallisce il POST, prova con DELETE mode=spare\n        console.error('Errore con endpoint POST, tentativo con DELETE mode=spare:', postError);\n        console.log('URL API (DELETE):', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}?mode=spare`);\n\n        const deleteResponse = await axios.delete(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000,\n            params: { mode: 'spare' }\n          }\n        );\n\n        console.log('Risposta markCavoAsSpare (DELETE mode=spare):', deleteResponse.data);\n\n        // Verifica che il cavo sia stato effettivamente marcato come SPARE\n        await new Promise(resolve => setTimeout(resolve, 1000));\n\n        // Verifica lo stato del cavo\n        console.log('Verifica dello stato del cavo dopo marcatura SPARE con DELETE...');\n        const cavoResponse = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Stato del cavo dopo marcatura con DELETE:', cavoResponse.data);\n\n        // Verifica che modificato_manualmente sia 3\n        if (cavoResponse.data.modificato_manualmente !== 3) {\n          console.error('ERRORE: Il cavo non risulta marcato come SPARE (modificato_manualmente != 3)');\n          throw new Error('Il cavo non risulta marcato come SPARE');\n        }\n\n        return cavoResponse.data;\n      }\n    } catch (error) {\n      console.error('Mark cavo as SPARE error:', error);\n      console.error('Dettagli errore:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Elimina un cavo o lo marca come SPARE\n  deleteCavo: async (cantiereId, cavoId, mode = null) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Log dettagliati per debug\n      console.log('Tentativo di eliminare/marcare cavo:', { cantiereId: cantiereIdNum, cavoId, mode });\n\n      // Se è specificata la modalità, aggiungi il parametro alla richiesta\n      const requestConfig = {\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${localStorage.getItem('token')}`\n        },\n        timeout: 30000, // Timeout aumentato a 30 secondi\n        params: mode ? { mode } : {}\n      };\n\n      console.log('URL API:', `${API_URL}/cavi/${cantiereIdNum}/${cavoId}`);\n      console.log('Config:', requestConfig);\n\n      // Usa axios direttamente invece di axiosInstance per avere più controllo\n      const response = await axios.delete(`${API_URL}/cavi/${cantiereIdNum}/${cavoId}`, requestConfig);\n      console.log('Risposta deleteCavo:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Delete cavo error:', error);\n      console.error('Error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data,\n        url: `${API_URL}/cavi/${cantiereId}/${cavoId}`,\n        config: error.config\n      });\n\n      // Crea un errore più informativo\n      if (error.response && error.response.data) {\n        throw error.response.data;\n      } else if (error.message) {\n        throw new Error(error.message);\n      } else {\n        throw new Error('Errore durante l\\'eliminazione del cavo');\n      }\n    }\n  },\n\n  // Aggiorna i metri posati di un cavo\n  updateMetriPosati: async (cantiereId, cavoId, metriPosati) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/metri-posati`);\n      console.log('Metri posati:', metriPosati);\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/metri-posati`, {\n        metri_posati: metriPosati\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update metri posati error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Modifica la bobina di un cavo posato\n  updateBobina: async (cantiereId, cavoId, idBobina) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/bobina`);\n      console.log('ID Bobina:', idBobina);\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/bobina`, {\n        id_bobina: idBobina\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Update bobina error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Ottiene la lista dei cavi installati di un cantiere\n  getCaviInstallati: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/installati`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi installati error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene le statistiche dei cavi di un cantiere\n  getCaviStats: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.get(`/cavi/${cantiereIdNum}/stats`);\n      return response.data;\n    } catch (error) {\n      console.error('Get cavi stats error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Ottiene direttamente i cavi SPARE\n  getCaviSpare: async (cantiereId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log('Caricamento cavi SPARE...');\n\n      // Prova prima con l'endpoint standard con tipo_cavo=3\n      console.log('URL API (standard):', `${API_URL}/cavi/${cantiereIdNum}?tipo_cavo=3`);\n\n      try {\n        const response = await axios.get(\n          `${API_URL}/cavi/${cantiereIdNum}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000,\n            params: { tipo_cavo: 3 }\n          }\n        );\n\n        console.log('Risposta getCaviSpare (standard):', response.data ? response.data.length : 0, 'cavi SPARE trovati');\n        if (response.data && response.data.length > 0) {\n          console.log('Primo cavo SPARE:', response.data[0]);\n        }\n\n        return response.data;\n      } catch (standardError) {\n        console.error('Errore con endpoint standard, tentativo con endpoint dedicato:', standardError);\n\n        // Se fallisce, prova con l'endpoint dedicato\n        console.log('URL API (dedicato):', `${API_URL}/cavi/spare/${cantiereIdNum}`);\n\n        const dedicatedResponse = await axios.get(\n          `${API_URL}/cavi/spare/${cantiereIdNum}`,\n          {\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${localStorage.getItem('token')}`\n            },\n            timeout: 30000\n          }\n        );\n\n        console.log('Risposta getCaviSpare (dedicato):', dedicatedResponse.data ? dedicatedResponse.data.length : 0, 'cavi SPARE trovati');\n        if (dedicatedResponse.data && dedicatedResponse.data.length > 0) {\n          console.log('Primo cavo SPARE (dedicato):', dedicatedResponse.data[0]);\n        }\n\n        return dedicatedResponse.data;\n      }\n    } catch (error) {\n      console.error('Get cavi SPARE error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Collega un lato di un cavo\n  collegaCavo: async (cantiereId, cavoId, lato, responsabile) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      console.log(`Inviando richiesta POST a /cavi/${cantiereIdNum}/${cavoId}/collegamento`);\n      console.log('Dati:', { lato, responsabile });\n\n      const response = await axiosInstance.post(`/cavi/${cantiereIdNum}/${cavoId}/collegamento`, {\n        lato: lato,\n        responsabile: responsabile || 'cantiere'\n      }, {\n        timeout: 60000, // 60 secondi\n      });\n\n      console.log('Risposta ricevuta:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Collega cavo error:', error);\n\n      // Gestione specifica per errori di rete\n      if (error.isNetworkError || error.isTimeoutError) {\n        console.error('Errore di rete o timeout:', error.customMessage || error.message);\n        throw { detail: error.customMessage || 'Impossibile connettersi al server. Verifica la connessione di rete e riprova.', status: 0, isNetworkError: true };\n      }\n\n      throw error.response ? error.response.data : { detail: error.message, status: 500 };\n    }\n  },\n\n  // Scollega un lato di un cavo\n  scollegaCavo: async (cantiereId, cavoId, lato) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      const response = await axiosInstance.delete(`/cavi/${cantiereIdNum}/${cavoId}/collegamento/${lato}`);\n      return response.data;\n    } catch (error) {\n      console.error('Scollega cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  },\n\n  // Verifica lo stato di un cavo specifico (debug)\n  debugCavo: async (cantiereId, cavoId) => {\n    try {\n      // Assicurati che cantiereId sia un numero\n      const cantiereIdNum = parseInt(cantiereId, 10);\n      if (isNaN(cantiereIdNum)) {\n        throw new Error(`ID cantiere non valido: ${cantiereId}`);\n      }\n\n      // Ottieni i cavi attivi\n      console.log('Verificando cavo tra i cavi attivi...');\n      const attivi = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=0`);\n      const cavoAttivo = attivi.data.find(c => c.id_cavo === cavoId);\n\n      // Ottieni i cavi SPARE\n      console.log('Verificando cavo tra i cavi SPARE...');\n      const spare = await axiosInstance.get(`/cavi/${cantiereIdNum}?tipo_cavo=3`);\n      const cavoSpare = spare.data.find(c => c.id_cavo === cavoId);\n\n      return {\n        trovato_tra_attivi: !!cavoAttivo,\n        trovato_tra_spare: !!cavoSpare,\n        cavo_attivo: cavoAttivo,\n        cavo_spare: cavoSpare\n      };\n    } catch (error) {\n      console.error('Debug cavo error:', error);\n      throw error.response ? error.response.data : error;\n    }\n  }\n};\n\nexport default caviService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,aAAa,MAAM,eAAe;AAEzC,MAAMC,OAAO,GAAGF,MAAM,CAACE,OAAO;AAE9B,MAAMC,WAAW,GAAG;EAClB;EACAC,OAAO,EAAE,MAAAA,CAAOC,UAAU,EAAEC,QAAQ,GAAG,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;IAC5D,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE;QAAEJ,UAAU;QAAEC,QAAQ;QAAEC;MAAQ,CAAC,CAAC;MACvEC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,OAAOJ,UAAU,CAAC;;MAErD;MACA,IAAIA,UAAU,KAAKK,SAAS,IAAIL,UAAU,KAAK,IAAI,EAAE;QACnDG,OAAO,CAACG,KAAK,CAAC,+BAA+B,CAAC;QAC9C,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;MACzC;;MAEA;MACA,IAAIC,aAAa,GAAGR,UAAU;MAC9B,IAAI,OAAOA,UAAU,KAAK,QAAQ,EAAE;QAClCQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;QACxCG,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEI,aAAa,CAAC;MAC1E;MAEA,IAAIE,KAAK,CAACF,aAAa,CAAC,EAAE;QACxBL,OAAO,CAACG,KAAK,CAAC,qCAAqC,EAAEN,UAAU,CAAC;QAChE,MAAM,IAAIO,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,iCAAiCI,aAAa,kBAAkBP,QAAQ,EAAE,CAAC;;MAEvF;MACA,IAAIA,QAAQ,KAAK,CAAC,EAAE;QAClBE,OAAO,CAACC,GAAG,CAAC,6CAA6C,CAAC;QAC1D,IAAI;UACF;UACA,MAAMO,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAC9B,GAAGf,OAAO,eAAeW,aAAa,EAAE,EACxC;YACEK,OAAO,EAAE;cACP,cAAc,EAAE,kBAAkB;cAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;YAC1D,CAAC;YACDC,OAAO,EAAE;UACX,CACF,CAAC;UAEDb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC;UAClD,OAAON,QAAQ,CAACM,IAAI;QACtB,CAAC,CAAC,OAAOC,UAAU,EAAE;UACnBf,OAAO,CAACG,KAAK,CAAC,wCAAwC,EAAEY,UAAU,CAAC;UACnE;QACF;MACF;;MAEA;MACA,IAAIC,GAAG,GAAG,SAASX,aAAa,EAAE;MAClC,MAAMY,WAAW,GAAG,EAAE;MAEtB,IAAInB,QAAQ,KAAK,IAAI,EAAE;QACrBmB,WAAW,CAACC,IAAI,CAAC,aAAapB,QAAQ,EAAE,CAAC;MAC3C;;MAEA;MACA,IAAIC,OAAO,CAACoB,mBAAmB,EAAE;QAC/BF,WAAW,CAACC,IAAI,CAAC,uBAAuBE,kBAAkB,CAACrB,OAAO,CAACoB,mBAAmB,CAAC,EAAE,CAAC;MAC5F;MAEA,IAAIpB,OAAO,CAACsB,SAAS,EAAE;QACrBJ,WAAW,CAACC,IAAI,CAAC,aAAaE,kBAAkB,CAACrB,OAAO,CAACsB,SAAS,CAAC,EAAE,CAAC;MACxE;MAEA,IAAItB,OAAO,CAACuB,OAAO,EAAE;QACnBL,WAAW,CAACC,IAAI,CAAC,WAAWE,kBAAkB,CAACrB,OAAO,CAACuB,OAAO,CAAC,EAAE,CAAC;QAClE,IAAIvB,OAAO,CAACwB,UAAU,EAAE;UACtBN,WAAW,CAACC,IAAI,CAAC,cAAcE,kBAAkB,CAACrB,OAAO,CAACwB,UAAU,CAAC,EAAE,CAAC;QAC1E;MACF;;MAEA;MACA,IAAIN,WAAW,CAACO,MAAM,GAAG,CAAC,EAAE;QAC1BR,GAAG,IAAI,IAAIC,WAAW,CAACQ,IAAI,CAAC,GAAG,CAAC,EAAE;MACpC;;MAEA;MACAzB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEe,GAAG,CAAC;MACrChB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEgB,WAAW,CAAC;MAE/CjB,OAAO,CAACC,GAAG,CAAC,qBAAqBe,GAAG,EAAE,CAAC;MACvChB,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,CAAC;MAC9EZ,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,GAAGP,OAAO,GAAGsB,GAAG,EAAE,CAAC;MAEhD,IAAI;QACFhB,OAAO,CAACC,GAAG,CAAC,kCAAkCe,GAAG,eAAeL,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,GAAG,UAAU,GAAG,UAAU,EAAE,CAAC;QAC1HZ,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE;UACtC,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC,CAAC;;QAEF;QACA,MAAMJ,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAACO,GAAG,EAAE;UAAEH,OAAO,EAAE;QAAM,CAAC,CAAC;QAEjEb,OAAO,CAACC,GAAG,CAAC,iBAAiBe,GAAG,EAAE,EAAER,QAAQ,CAACM,IAAI,CAAC;QAClDd,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,QAAQ,CAACkB,MAAM,CAAC;QACtD1B,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,QAAQ,CAACE,OAAO,CAAC;QAExD,IAAIiB,KAAK,CAACC,OAAO,CAACpB,QAAQ,CAACM,IAAI,CAAC,EAAE;UAChCd,OAAO,CAACC,GAAG,CAAC,4BAA4BO,QAAQ,CAACM,IAAI,CAACU,MAAM,EAAE,CAAC;UAC/D,IAAIhB,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;YAC5BxB,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;UACvD,CAAC,MAAM;YACLd,OAAO,CAAC6B,IAAI,CAAC,uCAAuCxB,aAAa,aAAaP,QAAQ,EAAE,CAAC;UAC3F;QACF,CAAC,MAAM;UACLE,OAAO,CAAC6B,IAAI,CAAC,4BAA4B,OAAOrB,QAAQ,CAACM,IAAI,EAAE,EAAEN,QAAQ,CAACM,IAAI,CAAC;QACjF;QAEA,OAAON,QAAQ,CAACM,IAAI;MACtB,CAAC,CAAC,OAAOgB,QAAQ,EAAE;QAAA,IAAAC,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;QACjBlC,OAAO,CAACG,KAAK,CAAC,iCAAiCa,GAAG,GAAG,EAAEc,QAAQ,CAAC;QAChE9B,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAE;UACpCgC,OAAO,EAAEL,QAAQ,CAACK,OAAO;UACzBT,MAAM,GAAAK,kBAAA,GAAED,QAAQ,CAACtB,QAAQ,cAAAuB,kBAAA,uBAAjBA,kBAAA,CAAmBL,MAAM;UACjCU,UAAU,GAAAJ,mBAAA,GAAEF,QAAQ,CAACtB,QAAQ,cAAAwB,mBAAA,uBAAjBA,mBAAA,CAAmBI,UAAU;UACzCtB,IAAI,GAAAmB,mBAAA,GAAEH,QAAQ,CAACtB,QAAQ,cAAAyB,mBAAA,uBAAjBA,mBAAA,CAAmBnB,IAAI;UAC7BJ,OAAO,GAAAwB,mBAAA,GAAEJ,QAAQ,CAACtB,QAAQ,cAAA0B,mBAAA,uBAAjBA,mBAAA,CAAmBxB,OAAO;UACnC2B,IAAI,EAAEP,QAAQ,CAACO,IAAI;UACnBC,YAAY,EAAER,QAAQ,CAACQ,YAAY;UACnC9C,MAAM,EAAEsC,QAAQ,CAACtC,MAAM,GAAG;YACxBwB,GAAG,EAAEc,QAAQ,CAACtC,MAAM,CAACwB,GAAG;YACxBuB,MAAM,EAAET,QAAQ,CAACtC,MAAM,CAAC+C,MAAM;YAC9B1B,OAAO,EAAEiB,QAAQ,CAACtC,MAAM,CAACqB,OAAO;YAChCH,OAAO,EAAEoB,QAAQ,CAACtC,MAAM,CAACkB;UAC3B,CAAC,GAAG;QACN,CAAC,CAAC;;QAEF;QACA,IAAIoB,QAAQ,CAACO,IAAI,KAAK,aAAa,EAAE;UACnCrC,OAAO,CAACG,KAAK,CAAC,0EAA0E,CAAC;UACzF;UACA,IAAI;YACFH,OAAO,CAACC,GAAG,CAAC,gDAAgD,CAAC;YAC7D,MAAMuC,YAAY,GAAG,MAAMC,KAAK,CAAC/C,OAAO,CAAC;YACzCM,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAEuC,YAAY,CAACd,MAAM,CAAC;UACrE,CAAC,CAAC,OAAOgB,SAAS,EAAE;YAClB1C,OAAO,CAACG,KAAK,CAAC,yCAAyC,EAAEuC,SAAS,CAAC;UACrE;QACF;QAEA,MAAMZ,QAAQ;MAChB;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MAAA,IAAAwC,eAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdjD,OAAO,CAACG,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;MACvCH,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAE;QAC9BgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAAiB,eAAA,GAAExC,KAAK,CAACK,QAAQ,cAAAmC,eAAA,uBAAdA,eAAA,CAAgBjB,MAAM;QAC9BU,UAAU,GAAAQ,gBAAA,GAAEzC,KAAK,CAACK,QAAQ,cAAAoC,gBAAA,uBAAdA,gBAAA,CAAgBR,UAAU;QACtCtB,IAAI,GAAA+B,gBAAA,GAAE1C,KAAK,CAACK,QAAQ,cAAAqC,gBAAA,uBAAdA,gBAAA,CAAgB/B,IAAI;QAC1BE,GAAG,EAAE,SAASnB,UAAU,GAAGC,QAAQ,KAAK,IAAI,GAAG,cAAcA,QAAQ,EAAE,GAAG,EAAE,EAAE;QAC9EoD,KAAK,EAAE/C,KAAK,CAAC+C;MACf,CAAC,CAAC;;MAEF;MACA,IAAI/C,KAAK,CAACkC,IAAI,KAAK,cAAc,IAAIlC,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,SAAS,CAAC,IAAIhD,KAAK,CAACgC,OAAO,CAACgB,QAAQ,CAAC,eAAe,CAAC,EAAE;QACjHnD,OAAO,CAACG,KAAK,CAAC,iCAAiC,CAAC;QAChD;QACAH,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;QAChD,OAAO,EAAE;MACX;;MAEA;MACA,MAAMmD,aAAa,GAAG,IAAIhD,KAAK,CAAC,EAAA0C,gBAAA,GAAA3C,KAAK,CAACK,QAAQ,cAAAsC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsBM,MAAM,KAAIlD,KAAK,CAACgC,OAAO,IAAI,oBAAoB,CAAC;MACtGiB,aAAa,CAAC1B,MAAM,IAAAsB,gBAAA,GAAG7C,KAAK,CAACK,QAAQ,cAAAwC,gBAAA,uBAAdA,gBAAA,CAAgBtB,MAAM;MAC7C0B,aAAa,CAACtC,IAAI,IAAAmC,gBAAA,GAAG9C,KAAK,CAACK,QAAQ,cAAAyC,gBAAA,uBAAdA,gBAAA,CAAgBnC,IAAI;MACzCsC,aAAa,CAAC5C,QAAQ,GAAGL,KAAK,CAACK,QAAQ;MACvC4C,aAAa,CAACE,aAAa,GAAGnD,KAAK;MACnCiD,aAAa,CAACf,IAAI,GAAGlC,KAAK,CAACkC,IAAI;MAC/Be,aAAa,CAACd,YAAY,GAAGnC,KAAK,CAACmC,YAAY;MAE/C,MAAMc,aAAa;IACrB;EACF,CAAC;EAED;EACAG,UAAU,EAAE,MAAAA,CAAO1D,UAAU,EAAE2D,QAAQ,KAAK;IAC1C,IAAI;MACF;MACA,MAAMnD,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,4CAA4CI,aAAa,EAAE,CAAC;MACxEL,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEwD,IAAI,CAACC,SAAS,CAACF,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;;MAE/D;MACA,IAAI;QACFxD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAM0D,YAAY,GAAG,MAAMlB,KAAK,CAAC,GAAG/C,OAAO,SAAS,EAAE;UAAE6C,MAAM,EAAE;QAAM,CAAC,CAAC;QACxEvC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0D,YAAY,CAACjC,MAAM,EAAEiC,YAAY,CAACvB,UAAU,CAAC;QAC7E,IAAI,CAACuB,YAAY,CAACC,EAAE,EAAE;UACpB5D,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEwD,YAAY,CAACjC,MAAM,CAAC;UAC3E,MAAM,IAAItB,KAAK,CAAC,0DAA0D,CAAC;QAC7E;MACF,CAAC,CAAC,OAAOyD,SAAS,EAAE;QAClB7D,OAAO,CAACG,KAAK,CAAC,oCAAoC,EAAE0D,SAAS,CAAC;QAC9D,MAAM,IAAIzD,KAAK,CAAC,+EAA+E,CAAC;MAClG;;MAEA;MACAJ,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMO,QAAQ,GAAG,MAAMf,aAAa,CAACqE,IAAI,CAAC,SAASzD,aAAa,EAAE,EAAEmD,QAAQ,EAAE;QAC5E3C,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACkB,MAAM,EAAElB,QAAQ,CAAC4B,UAAU,CAAC;MACzEpC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAC5C,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACA,IAAIA,KAAK,CAACK,QAAQ,EAAE;QAClB;QACAR,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACK,QAAQ,CAACkB,MAAM,EAAEvB,KAAK,CAACK,QAAQ,CAAC4B,UAAU,CAAC;QACrFpC,OAAO,CAACG,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACK,QAAQ,CAACM,IAAI,CAAC;QAClD,MAAMX,KAAK,CAACK,QAAQ,CAACM,IAAI;MAC3B,CAAC,MAAM,IAAIX,KAAK,CAAC4D,OAAO,EAAE;QACxB;QACA/D,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC4D,OAAO,CAAC;;QAE5D;QACA/D,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;QAC9C,OAAO,IAAI+D,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;UACtC,MAAMC,GAAG,GAAG,IAAIC,cAAc,CAAC,CAAC;UAChCD,GAAG,CAACE,IAAI,CAAC,MAAM,EAAE,GAAG3E,OAAO,SAASG,UAAU,EAAE,EAAE,IAAI,CAAC;UACvDsE,GAAG,CAACG,gBAAgB,CAAC,cAAc,EAAE,kBAAkB,CAAC;;UAExD;UACA,MAAMC,SAAS,GAAG5D,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC/C,IAAI2D,SAAS,EAAE;YACbJ,GAAG,CAACG,gBAAgB,CAAC,eAAe,EAAE,UAAUC,SAAS,EAAE,CAAC;UAC9D;UACAJ,GAAG,CAACtD,OAAO,GAAG,KAAK,CAAC,CAAC;;UAErBsD,GAAG,CAACK,MAAM,GAAG,YAAW;YACtB,IAAIL,GAAG,CAACzC,MAAM,IAAI,GAAG,IAAIyC,GAAG,CAACzC,MAAM,GAAG,GAAG,EAAE;cACzC1B,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEkE,GAAG,CAACM,YAAY,CAAC;cACzD,IAAI;gBACF,MAAM3D,IAAI,GAAG2C,IAAI,CAACiB,KAAK,CAACP,GAAG,CAACM,YAAY,CAAC;gBACzCR,OAAO,CAACnD,IAAI,CAAC;cACf,CAAC,CAAC,OAAO6D,CAAC,EAAE;gBACV3E,OAAO,CAAC6B,IAAI,CAAC,oCAAoC,EAAE8C,CAAC,CAAC;gBACrDV,OAAO,CAAC;kBAAE9B,OAAO,EAAE;gBAAqC,CAAC,CAAC;cAC5D;YACF,CAAC,MAAM;cACLnC,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEgE,GAAG,CAACzC,MAAM,EAAEyC,GAAG,CAAC/B,UAAU,CAAC;cACnE8B,MAAM,CAAC,IAAI9D,KAAK,CAAC,UAAU+D,GAAG,CAACzC,MAAM,KAAKyC,GAAG,CAAC/B,UAAU,EAAE,CAAC,CAAC;YAC9D;UACF,CAAC;UAED+B,GAAG,CAACS,OAAO,GAAG,YAAW;YACvB5E,OAAO,CAACG,KAAK,CAAC,+BAA+B,CAAC;YAC9C+D,MAAM,CAAC,IAAI9D,KAAK,CAAC,oDAAoD,CAAC,CAAC;UACzE,CAAC;UAED+D,GAAG,CAACU,SAAS,GAAG,YAAW;YACzB7E,OAAO,CAACG,KAAK,CAAC,wBAAwB,CAAC;YACvC+D,MAAM,CAAC,IAAI9D,KAAK,CAAC,kDAAkD,CAAC,CAAC;UACvE,CAAC;UAED+D,GAAG,CAACW,IAAI,CAACrB,IAAI,CAACC,SAAS,CAACF,QAAQ,CAAC,CAAC;QACpC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL;QACAxD,OAAO,CAACG,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAACgC,OAAO,CAAC;QAC/E,MAAM,IAAI/B,KAAK,CAAC,0BAA0BD,KAAK,CAACgC,OAAO,EAAE,CAAC;MAC5D;IACF;EACF,CAAC;EAED;EACA4C,WAAW,EAAE,MAAAA,CAAOlF,UAAU,EAAEmF,MAAM,KAAK;IACzC,IAAI;MACF;MACA,MAAM3E,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,IAAI2E,MAAM,EAAE,CAAC;MAC5E,OAAOxE,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA8E,UAAU,EAAE,MAAAA,CAAOpF,UAAU,EAAEmF,MAAM,EAAExB,QAAQ,KAAK;IAClD,IAAI;MACF;MACA,MAAMnD,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,kCAAkCI,aAAa,IAAI2E,MAAM,EAAE,CAAC;MACxEhF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEuD,QAAQ,CAAC;;MAEtC;MACA,IAAI;QACFxD,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;QACjD,MAAM0D,YAAY,GAAG,MAAMlB,KAAK,CAAC,GAAG/C,OAAO,SAAS,EAAE;UAAE6C,MAAM,EAAE;QAAM,CAAC,CAAC;QACxEvC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0D,YAAY,CAACjC,MAAM,EAAEiC,YAAY,CAACvB,UAAU,CAAC;QAC7E,IAAI,CAACuB,YAAY,CAACC,EAAE,EAAE;UACpB5D,OAAO,CAACG,KAAK,CAAC,uCAAuC,EAAEwD,YAAY,CAACjC,MAAM,CAAC;UAC3E,MAAM,IAAItB,KAAK,CAAC,0DAA0D,CAAC;QAC7E;MACF,CAAC,CAAC,OAAOyD,SAAS,EAAE;QAClB7D,OAAO,CAACG,KAAK,CAAC,oCAAoC,EAAE0D,SAAS,CAAC;QAC9D,MAAM,IAAIzD,KAAK,CAAC,+EAA+E,CAAC;MAClG;;MAEA;MACA,MAAMI,QAAQ,GAAG,MAAMf,aAAa,CAACyF,GAAG,CAAC,SAAS7E,aAAa,IAAI2E,MAAM,EAAE,EAAExB,QAAQ,EAAE;QACrF3C,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;;MAE1C;MACA,IAAIA,KAAK,CAACgF,cAAc,IAAIhF,KAAK,CAACiF,cAAc,EAAE;QAChDpF,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACkF,aAAa,IAAIlF,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACkF,aAAa,IAAI,+EAA+E;UAAE3D,MAAM,EAAE,CAAC;UAAEyD,cAAc,EAAE;QAAK,CAAC;MAC3J;;MAEA;MACA,IAAIhF,KAAK,CAACK,QAAQ,EAAE;QAClB;QACAR,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAACK,QAAQ,CAACkB,MAAM,EAAEvB,KAAK,CAACK,QAAQ,CAAC4B,UAAU,CAAC;QACrFpC,OAAO,CAACG,KAAK,CAAC,cAAc,EAAEA,KAAK,CAACK,QAAQ,CAACM,IAAI,CAAC;QAClD,MAAMX,KAAK,CAACK,QAAQ,CAACM,IAAI;MAC3B,CAAC,MAAM,IAAIX,KAAK,CAAC4D,OAAO,EAAE;QACxB;QACA/D,OAAO,CAACG,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC4D,OAAO,CAAC;QAC5D,MAAM;UAAEV,MAAM,EAAE,+GAA+G;UAAE3B,MAAM,EAAE,CAAC;UAAEyD,cAAc,EAAE;QAAK,CAAC;MACpK,CAAC,MAAM;QACL;QACAnF,OAAO,CAACG,KAAK,CAAC,iDAAiD,EAAEA,KAAK,CAACgC,OAAO,CAAC;QAC/E,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACgC,OAAO;UAAET,MAAM,EAAE;QAAI,CAAC;MAC9C;IACF;EACF,CAAC;EAED;EACA4D,oBAAoB,EAAE,MAAOzF,UAAU,IAAK;IAC1C,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,qBAAqB,CAAC;MACrF,OAAOG,QAAQ,CAACM,IAAI,CAACyE,kBAAkB;IACzC,CAAC,CAAC,OAAOpF,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,IAAI,CAAC,CAAC;IACf;EACF,CAAC;EAED;EACAqF,eAAe,EAAE,MAAAA,CAAO3F,UAAU,EAAEmF,MAAM,EAAES,KAAK,GAAG,KAAK,KAAK;IAC5D,IAAI;MACF;MACA,MAAMpF,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,EAAE;QAAEJ,UAAU,EAAEQ,aAAa;QAAE2E,MAAM;QAAES;MAAM,CAAC,CAAC;;MAElG;MACAzF,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAI2E,MAAM,gBAAgB,CAAC;MAE1F,IAAI;QACF,MAAMU,YAAY,GAAG,MAAMnG,KAAK,CAACuE,IAAI,CACnC,GAAGpE,OAAO,SAASW,aAAa,IAAI2E,MAAM,gBAAgB,EAC1D;UAAES,KAAK,EAAEA;QAAM,CAAC,EAChB;UACE/E,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAEyF,YAAY,CAAC5E,IAAI,CAAC;;QAElE;QACA,MAAM,IAAIkD,OAAO,CAACC,OAAO,IAAI0B,UAAU,CAAC1B,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACAjE,OAAO,CAACC,GAAG,CAAC,uDAAuD,CAAC;QACpE,MAAM2F,YAAY,GAAG,MAAMrG,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAI2E,MAAM,EAAE,EAC5C;UACEtE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE2F,YAAY,CAAC9E,IAAI,CAAC;;QAEhE;QACA,IAAI8E,YAAY,CAAC9E,IAAI,CAAC+E,sBAAsB,KAAK,CAAC,EAAE;UAClD7F,OAAO,CAACG,KAAK,CAAC,8EAA8E,CAAC;UAC7F,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,OAAOwF,YAAY,CAAC9E,IAAI;MAC1B,CAAC,CAAC,OAAOgF,SAAS,EAAE;QAClB;QACA9F,OAAO,CAACG,KAAK,CAAC,4DAA4D,EAAE2F,SAAS,CAAC;QACtF9F,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAI2E,MAAM,aAAa,CAAC;QAEzF,MAAMe,cAAc,GAAG,MAAMxG,KAAK,CAACyG,MAAM,CACvC,GAAGtG,OAAO,SAASW,aAAa,IAAI2E,MAAM,EAAE,EAC5C;UACEtE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE,KAAK;UACdoF,MAAM,EAAE;YAAEC,IAAI,EAAE;UAAQ;QAC1B,CACF,CAAC;QAEDlG,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE8F,cAAc,CAACjF,IAAI,CAAC;;QAEjF;QACA,MAAM,IAAIkD,OAAO,CAACC,OAAO,IAAI0B,UAAU,CAAC1B,OAAO,EAAE,IAAI,CAAC,CAAC;;QAEvD;QACAjE,OAAO,CAACC,GAAG,CAAC,kEAAkE,CAAC;QAC/E,MAAM2F,YAAY,GAAG,MAAMrG,KAAK,CAACkB,GAAG,CAClC,GAAGf,OAAO,SAASW,aAAa,IAAI2E,MAAM,EAAE,EAC5C;UACEtE,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,2CAA2C,EAAE2F,YAAY,CAAC9E,IAAI,CAAC;;QAE3E;QACA,IAAI8E,YAAY,CAAC9E,IAAI,CAAC+E,sBAAsB,KAAK,CAAC,EAAE;UAClD7F,OAAO,CAACG,KAAK,CAAC,8EAA8E,CAAC;UAC7F,MAAM,IAAIC,KAAK,CAAC,wCAAwC,CAAC;QAC3D;QAEA,OAAOwF,YAAY,CAAC9E,IAAI;MAC1B;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAgG,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;MACdrG,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDH,OAAO,CAACG,KAAK,CAAC,kBAAkB,EAAE;QAChCgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAAyE,gBAAA,GAAEhG,KAAK,CAACK,QAAQ,cAAA2F,gBAAA,uBAAdA,gBAAA,CAAgBzE,MAAM;QAC9BU,UAAU,GAAAgE,gBAAA,GAAEjG,KAAK,CAACK,QAAQ,cAAA4F,gBAAA,uBAAdA,gBAAA,CAAgBhE,UAAU;QACtCtB,IAAI,GAAAuF,gBAAA,GAAElG,KAAK,CAACK,QAAQ,cAAA6F,gBAAA,uBAAdA,gBAAA,CAAgBvF,IAAI;QAC1BE,GAAG,EAAE,GAAGtB,OAAO,SAASG,UAAU,IAAImF,MAAM,EAAE;QAC9CxF,MAAM,EAAEW,KAAK,CAACX;MAChB,CAAC,CAAC;MACF,MAAMW,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAmG,UAAU,EAAE,MAAAA,CAAOzG,UAAU,EAAEmF,MAAM,EAAEkB,IAAI,GAAG,IAAI,KAAK;IACrD,IAAI;MACF;MACA,MAAM7F,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAE;QAAEJ,UAAU,EAAEQ,aAAa;QAAE2E,MAAM;QAAEkB;MAAK,CAAC,CAAC;;MAEhG;MACA,MAAMK,aAAa,GAAG;QACpB7F,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;QAC1D,CAAC;QACDC,OAAO,EAAE,KAAK;QAAE;QAChBoF,MAAM,EAAEC,IAAI,GAAG;UAAEA;QAAK,CAAC,GAAG,CAAC;MAC7B,CAAC;MAEDlG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,GAAGP,OAAO,SAASW,aAAa,IAAI2E,MAAM,EAAE,CAAC;MACrEhF,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEsG,aAAa,CAAC;;MAErC;MACA,MAAM/F,QAAQ,GAAG,MAAMjB,KAAK,CAACyG,MAAM,CAAC,GAAGtG,OAAO,SAASW,aAAa,IAAI2E,MAAM,EAAE,EAAEuB,aAAa,CAAC;MAChGvG,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAClD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MAAA,IAAAqG,gBAAA,EAAAC,gBAAA,EAAAC,iBAAA;MACd1G,OAAO,CAACG,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1CH,OAAO,CAACG,KAAK,CAAC,gBAAgB,EAAE;QAC9BgC,OAAO,EAAEhC,KAAK,CAACgC,OAAO;QACtBT,MAAM,GAAA8E,gBAAA,GAAErG,KAAK,CAACK,QAAQ,cAAAgG,gBAAA,uBAAdA,gBAAA,CAAgB9E,MAAM;QAC9BU,UAAU,GAAAqE,gBAAA,GAAEtG,KAAK,CAACK,QAAQ,cAAAiG,gBAAA,uBAAdA,gBAAA,CAAgBrE,UAAU;QACtCtB,IAAI,GAAA4F,iBAAA,GAAEvG,KAAK,CAACK,QAAQ,cAAAkG,iBAAA,uBAAdA,iBAAA,CAAgB5F,IAAI;QAC1BE,GAAG,EAAE,GAAGtB,OAAO,SAASG,UAAU,IAAImF,MAAM,EAAE;QAC9CxF,MAAM,EAAEW,KAAK,CAACX;MAChB,CAAC,CAAC;;MAEF;MACA,IAAIW,KAAK,CAACK,QAAQ,IAAIL,KAAK,CAACK,QAAQ,CAACM,IAAI,EAAE;QACzC,MAAMX,KAAK,CAACK,QAAQ,CAACM,IAAI;MAC3B,CAAC,MAAM,IAAIX,KAAK,CAACgC,OAAO,EAAE;QACxB,MAAM,IAAI/B,KAAK,CAACD,KAAK,CAACgC,OAAO,CAAC;MAChC,CAAC,MAAM;QACL,MAAM,IAAI/B,KAAK,CAAC,yCAAyC,CAAC;MAC5D;IACF;EACF,CAAC;EAED;EACAuG,iBAAiB,EAAE,MAAAA,CAAO9G,UAAU,EAAEmF,MAAM,EAAE4B,WAAW,KAAK;IAC5D,IAAI;MACF;MACA,MAAMvG,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAI2E,MAAM,eAAe,CAAC;MACtFhF,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE2G,WAAW,CAAC;MAEzC,MAAMpG,QAAQ,GAAG,MAAMf,aAAa,CAACqE,IAAI,CAAC,SAASzD,aAAa,IAAI2E,MAAM,eAAe,EAAE;QACzF6B,YAAY,EAAED;MAChB,CAAC,EAAE;QACD/F,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;;MAElD;MACA,IAAIA,KAAK,CAACgF,cAAc,IAAIhF,KAAK,CAACiF,cAAc,EAAE;QAChDpF,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACkF,aAAa,IAAIlF,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACkF,aAAa,IAAI,+EAA+E;UAAE3D,MAAM,EAAE,CAAC;UAAEyD,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMhF,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACAoF,YAAY,EAAE,MAAAA,CAAOjH,UAAU,EAAEmF,MAAM,EAAE+B,QAAQ,KAAK;IACpD,IAAI;MACF;MACA,MAAM1G,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAI2E,MAAM,SAAS,CAAC;MAChFhF,OAAO,CAACC,GAAG,CAAC,YAAY,EAAE8G,QAAQ,CAAC;MAEnC,MAAMvG,QAAQ,GAAG,MAAMf,aAAa,CAACqE,IAAI,CAAC,SAASzD,aAAa,IAAI2E,MAAM,SAAS,EAAE;QACnFgC,SAAS,EAAED;MACb,CAAC,EAAE;QACDlG,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;;MAE5C;MACA,IAAIA,KAAK,CAACgF,cAAc,IAAIhF,KAAK,CAACiF,cAAc,EAAE;QAChDpF,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACkF,aAAa,IAAIlF,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACkF,aAAa,IAAI,+EAA+E;UAAE3D,MAAM,EAAE,CAAC;UAAEyD,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMhF,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACAuF,iBAAiB,EAAE,MAAOpH,UAAU,IAAK;IACvC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,aAAa,CAAC;MAC7E,OAAOG,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACA+G,YAAY,EAAE,MAAOrH,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,QAAQ,CAAC;MACxE,OAAOG,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAgH,YAAY,EAAE,MAAOtH,UAAU,IAAK;IAClC,IAAI;MACF;MACA,MAAMQ,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;;MAExC;MACAD,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGP,OAAO,SAASW,aAAa,cAAc,CAAC;MAElF,IAAI;QACF,MAAMG,QAAQ,GAAG,MAAMjB,KAAK,CAACkB,GAAG,CAC9B,GAAGf,OAAO,SAASW,aAAa,EAAE,EAClC;UACEK,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE,KAAK;UACdoF,MAAM,EAAE;YAAEmB,SAAS,EAAE;UAAE;QACzB,CACF,CAAC;QAEDpH,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEO,QAAQ,CAACM,IAAI,GAAGN,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAChH,IAAIhB,QAAQ,CAACM,IAAI,IAAIN,QAAQ,CAACM,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC7CxB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEO,QAAQ,CAACM,IAAI,CAAC,CAAC,CAAC,CAAC;QACpD;QAEA,OAAON,QAAQ,CAACM,IAAI;MACtB,CAAC,CAAC,OAAOuG,aAAa,EAAE;QACtBrH,OAAO,CAACG,KAAK,CAAC,gEAAgE,EAAEkH,aAAa,CAAC;;QAE9F;QACArH,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE,GAAGP,OAAO,eAAeW,aAAa,EAAE,CAAC;QAE5E,MAAMiH,iBAAiB,GAAG,MAAM/H,KAAK,CAACkB,GAAG,CACvC,GAAGf,OAAO,eAAeW,aAAa,EAAE,EACxC;UACEK,OAAO,EAAE;YACP,cAAc,EAAE,kBAAkB;YAClC,eAAe,EAAE,UAAUC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;UAC1D,CAAC;UACDC,OAAO,EAAE;QACX,CACF,CAAC;QAEDb,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEqH,iBAAiB,CAACxG,IAAI,GAAGwG,iBAAiB,CAACxG,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE,oBAAoB,CAAC;QAClI,IAAI8F,iBAAiB,CAACxG,IAAI,IAAIwG,iBAAiB,CAACxG,IAAI,CAACU,MAAM,GAAG,CAAC,EAAE;UAC/DxB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEqH,iBAAiB,CAACxG,IAAI,CAAC,CAAC,CAAC,CAAC;QACxE;QAEA,OAAOwG,iBAAiB,CAACxG,IAAI;MAC/B;IACF,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAoH,WAAW,EAAE,MAAAA,CAAO1H,UAAU,EAAEmF,MAAM,EAAEwC,IAAI,EAAEC,YAAY,KAAK;IAC7D,IAAI;MACF;MACA,MAAMpH,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEAG,OAAO,CAACC,GAAG,CAAC,mCAAmCI,aAAa,IAAI2E,MAAM,eAAe,CAAC;MACtFhF,OAAO,CAACC,GAAG,CAAC,OAAO,EAAE;QAAEuH,IAAI;QAAEC;MAAa,CAAC,CAAC;MAE5C,MAAMjH,QAAQ,GAAG,MAAMf,aAAa,CAACqE,IAAI,CAAC,SAASzD,aAAa,IAAI2E,MAAM,eAAe,EAAE;QACzFwC,IAAI,EAAEA,IAAI;QACVC,YAAY,EAAEA,YAAY,IAAI;MAChC,CAAC,EAAE;QACD5G,OAAO,EAAE,KAAK,CAAE;MAClB,CAAC,CAAC;MAEFb,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEO,QAAQ,CAACM,IAAI,CAAC;MAChD,OAAON,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACA,IAAIA,KAAK,CAACgF,cAAc,IAAIhF,KAAK,CAACiF,cAAc,EAAE;QAChDpF,OAAO,CAACG,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACkF,aAAa,IAAIlF,KAAK,CAACgC,OAAO,CAAC;QAChF,MAAM;UAAEkB,MAAM,EAAElD,KAAK,CAACkF,aAAa,IAAI,+EAA+E;UAAE3D,MAAM,EAAE,CAAC;UAAEyD,cAAc,EAAE;QAAK,CAAC;MAC3J;MAEA,MAAMhF,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAG;QAAEuC,MAAM,EAAElD,KAAK,CAACgC,OAAO;QAAET,MAAM,EAAE;MAAI,CAAC;IACrF;EACF,CAAC;EAED;EACAgG,YAAY,EAAE,MAAAA,CAAO7H,UAAU,EAAEmF,MAAM,EAAEwC,IAAI,KAAK;IAChD,IAAI;MACF;MACA,MAAMnH,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;MAEA,MAAMW,QAAQ,GAAG,MAAMf,aAAa,CAACuG,MAAM,CAAC,SAAS3F,aAAa,IAAI2E,MAAM,iBAAiBwC,IAAI,EAAE,CAAC;MACpG,OAAOhH,QAAQ,CAACM,IAAI;IACtB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF,CAAC;EAED;EACAwH,SAAS,EAAE,MAAAA,CAAO9H,UAAU,EAAEmF,MAAM,KAAK;IACvC,IAAI;MACF;MACA,MAAM3E,aAAa,GAAGC,QAAQ,CAACT,UAAU,EAAE,EAAE,CAAC;MAC9C,IAAIU,KAAK,CAACF,aAAa,CAAC,EAAE;QACxB,MAAM,IAAID,KAAK,CAAC,2BAA2BP,UAAU,EAAE,CAAC;MAC1D;;MAEA;MACAG,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;MACpD,MAAM2H,MAAM,GAAG,MAAMnI,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,cAAc,CAAC;MAC5E,MAAMwH,UAAU,GAAGD,MAAM,CAAC9G,IAAI,CAACgH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKhD,MAAM,CAAC;;MAE9D;MACAhF,OAAO,CAACC,GAAG,CAAC,sCAAsC,CAAC;MACnD,MAAMgI,KAAK,GAAG,MAAMxI,aAAa,CAACgB,GAAG,CAAC,SAASJ,aAAa,cAAc,CAAC;MAC3E,MAAM6H,SAAS,GAAGD,KAAK,CAACnH,IAAI,CAACgH,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,OAAO,KAAKhD,MAAM,CAAC;MAE5D,OAAO;QACLmD,kBAAkB,EAAE,CAAC,CAACN,UAAU;QAChCO,iBAAiB,EAAE,CAAC,CAACF,SAAS;QAC9BG,WAAW,EAAER,UAAU;QACvBS,UAAU,EAAEJ;MACd,CAAC;IACH,CAAC,CAAC,OAAO/H,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzC,MAAMA,KAAK,CAACK,QAAQ,GAAGL,KAAK,CAACK,QAAQ,CAACM,IAAI,GAAGX,KAAK;IACpD;EACF;AACF,CAAC;AAED,eAAeR,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}