from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, Any, <PERSON><PERSON>

import bcrypt
from jose import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from sqlalchemy.orm import Session

from backend.config import settings
from backend.database import get_db
from backend.models.user import User
from backend.schemas.auth import TokenData

# OAuth2 scheme per l'autenticazione
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=f"{settings.API_PREFIX}/auth/login")

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verifica se la password in chiaro corrisponde all'hash memorizzato.

    Args:
        plain_password: Password in chiaro
        hashed_password: Hash della password memorizzato

    Returns:
        bool: True se la password è corretta, False altrimenti
    """
    try:
        # Stampa informazioni di debug
        print(f"DEBUG - Verifica password - Password inserita: '{plain_password}'")
        if isinstance(hashed_password, str):
            print(f"DEBUG - Hash nel DB (troncato): '{hashed_password[:30]}...'")
        elif isinstance(hashed_password, bytes):
            print(f"DEBUG - Hash nel DB (bytes, troncato): '{hashed_password[:30]}...'")
        else:
            print(f"DEBUG - Hash nel DB (tipo sconosciuto): {type(hashed_password)}")

        # Gestione del caso in cui la password hash sia None o vuota
        if hashed_password is None or hashed_password == '':
            print("\u274c Password hash è None o vuota")
            return False

        # Gestione del formato binario (\x...)
        if isinstance(hashed_password, str) and hashed_password.startswith('\\x'):
            print("DEBUG - Hash in formato binario esadecimale")
            try:
                # Rimuovi il prefisso \x
                hex_str = hashed_password[2:]
                # Converti da esadecimale a bytes
                hashed_password = bytes.fromhex(hex_str)
                print(f"DEBUG - Hash convertito da hex a bytes: {hashed_password[:30]}...")
            except Exception as hex_error:
                print(f"Errore nella conversione da hex a bytes: {str(hex_error)}")
                # Continua con la stringa originale se la conversione fallisce

        # Converti la password in bytes se è una stringa
        if isinstance(hashed_password, str):
            hashed_password = hashed_password.encode('utf-8')

        # Verifica la password
        try:
            result = bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password)
            if result:
                print("\u2705 Password verificata correttamente")
            else:
                print("\u274c Password non corretta")
            return result
        except ValueError as bcrypt_error:
            print(f"Errore bcrypt: {str(bcrypt_error)}")
            # Prova a fare un confronto diretto come fallback
            if plain_password == hashed_password.decode('utf-8', errors='ignore'):
                print("\u2705 Password verificata con confronto diretto (fallback)")
                return True
            return False
    except ValueError as e:
        # Nessun fallback speciale, se il formato della password non è valido, l'autenticazione fallisce
        print(f"Errore di formato password: {str(e)}")
        return False
    except Exception as e:
        print(f"Errore generico nella verifica della password: {str(e)}")
        return False

def verify_cantiere_password(plain_password: str, cantiere_password: str) -> bool:
    """
    Verifica la password del cantiere senza casi speciali.
    Utilizza solo le credenziali definite nel database.
    Supporta sia password hash che password in chiaro nel database.

    Args:
        plain_password: Password in chiaro inserita dall'utente
        cantiere_password: Password hash o in chiaro del cantiere dal database

    Returns:
        bool: True se la password è corretta, False altrimenti
    """
    try:
        # Stampa informazioni di debug
        print(f"DEBUG - Password inserita: '{plain_password}'")
        print(f"DEBUG - Password nel DB (troncata): '{str(cantiere_password)[:30]}...'")
        print(f"DEBUG - Tipo password nel DB: {type(cantiere_password)}")

        # Gestione del caso in cui la password nel DB sia None o vuota
        if cantiere_password is None or cantiere_password == '':
            print("❌ Password nel DB è None o vuota")
            return False

        # Gestione del formato binario (\x...)
        if isinstance(cantiere_password, str) and cantiere_password.startswith('\\x'):
            print("DEBUG - Password in formato binario esadecimale")
            # Rimuovi il prefisso \x e converti da esadecimale a bytes
            try:
                # Rimuovi il prefisso \x
                hex_str = cantiere_password[2:]
                # Converti da esadecimale a bytes
                cantiere_password = bytes.fromhex(hex_str)
                print(f"DEBUG - Password convertita da hex a bytes: {cantiere_password[:30]}...")
            except Exception as hex_error:
                print(f"Errore nella conversione da hex a bytes: {str(hex_error)}")
                # Continua con la stringa originale se la conversione fallisce

        # Verifica se la password nel DB è in formato bcrypt valido
        is_bcrypt_format = False
        if isinstance(cantiere_password, str):
            is_bcrypt_format = cantiere_password.startswith('$2')
        elif isinstance(cantiere_password, bytes):
            is_bcrypt_format = cantiere_password.startswith(b'$2')
        print(f"DEBUG - Password in formato bcrypt: {is_bcrypt_format}")

        # Caso 1: Password in formato bcrypt - usa bcrypt per verificare
        if is_bcrypt_format:
            result = verify_password(plain_password, cantiere_password)
            if result:
                print("✅ Accesso consentito con password corretta (bcrypt)")
            else:
                print("❌ Password errata (bcrypt)")
            return result

        # Caso 2: Password in chiaro nel database - confronto diretto
        if plain_password == cantiere_password:
            print("✅ Accesso consentito con password corretta (plaintext)")
            return True

        # Caso 3: Prova a decodificare la password se è in bytes
        if isinstance(cantiere_password, bytes):
            try:
                decoded_password = cantiere_password.decode('utf-8')
                print(f"DEBUG - Password decodificata: {decoded_password[:30]}...")
                if plain_password == decoded_password:
                    print("✅ Accesso consentito con password decodificata")
                    return True
            except Exception as decode_error:
                print(f"Errore nella decodifica della password: {str(decode_error)}")

        # Caso 4: Nessuna corrispondenza
        print("❌ Password errata (nessuna corrispondenza)")
        return False
    except Exception as e:
        print(f"Errore nella verifica della password: {str(e)}")
        return False

def get_password_hash(password: str) -> str:
    """
    Genera l'hash di una password.

    Args:
        password: Password in chiaro

    Returns:
        str: Hash della password
    """
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt())

    # Converti l'hash in stringa se è in formato bytes
    if isinstance(hashed_password, bytes):
        return hashed_password.decode('utf-8')
    return hashed_password

def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    Crea un token JWT di accesso.

    Args:
        data: Dati da includere nel token
        expires_delta: Durata di validità del token

    Returns:
        str: Token JWT
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

    return encoded_jwt

def get_current_user(
    token: str = Depends(oauth2_scheme),
    db: Session = Depends(get_db)
) -> User:
    """
    Ottiene l'utente corrente dal token JWT.

    Args:
        token: Token JWT
        db: Sessione del database

    Returns:
        User: Utente corrente

    Raises:
        HTTPException: Se il token non è valido o l'utente non esiste
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Credenziali non valide",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # Decodifica il token
        print(f"Tentativo di decodifica token: {token[:20]}...")
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        print(f"Token decodificato con successo: {payload}")

        username: str = payload.get("sub")
        user_id: int = payload.get("user_id")
        role: str = payload.get("role")
        is_impersonated: bool = payload.get("is_impersonated", False)
        impersonated_id: int = payload.get("impersonated_id")
        impersonated_username: str = payload.get("impersonated_username")
        impersonated_role: str = payload.get("impersonated_role")

        print(f"Dati estratti dal token - username: {username}, user_id: {user_id}, role: {role}, is_impersonated: {is_impersonated}")
        if is_impersonated:
            print(f"Dati utente impersonato - id: {impersonated_id}, username: {impersonated_username}, ruolo: {impersonated_role}")

        if username is None or user_id is None:
            print("Username o user_id mancanti nel token")
            raise credentials_exception

        token_data = TokenData(
            username=username,
            user_id=user_id,
            role=role,
            is_impersonated=is_impersonated,
            impersonated_id=impersonated_id,
            impersonated_username=impersonated_username,
            impersonated_role=impersonated_role
        )
    except Exception as e:
        print(f"Errore durante la decodifica del token: {str(e)}")
        raise credentials_exception

    # Ottieni l'utente dal database
    print(f"Ricerca utente nel database con id: {token_data.user_id}")
    user = db.query(User).filter(User.id_utente == token_data.user_id).first()

    if user is None:
        print(f"Utente con id {token_data.user_id} non trovato nel database")
        raise credentials_exception

    if not user.abilitato:
        print(f"Utente {user.username} non abilitato")
        raise credentials_exception

    print(f"Utente {user.username} autenticato con successo")

    # Aggiungi i dati del token all'oggetto utente per poterli usare nei controlli di accesso
    setattr(user, "token_data", token_data)

    return user

def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    Verifica che l'utente corrente sia attivo.

    Args:
        current_user: Utente corrente

    Returns:
        User: Utente corrente

    Raises:
        HTTPException: Se l'utente non è attivo
    """
    if not current_user.abilitato:
        raise HTTPException(status_code=400, detail="Utente disabilitato")
    return current_user
