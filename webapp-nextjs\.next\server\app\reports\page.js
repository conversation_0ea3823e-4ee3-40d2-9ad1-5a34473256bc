(()=>{var t={};t.id=22,t.ids=[22],t.modules={22:(t,e,r)=>{var n=r(75254),i=r(20623),o=r(48169),a=r(40542),c=r(45058);t.exports=function(t){return"function"==typeof t?t:null==t?o:"object"==typeof t?a(t)?i(t[0],t[1]):n(t):c(t)}},658:(t,e,r)=>{t.exports=r(41547)(r(85718),"Map")},1566:(t,e,r)=>{var n=r(89167),i=r(658),o=r(30401),a=r(34772),c=r(17830),u=r(29395),l=r(12290),s="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=l(n),v=l(i),m=l(o),b=l(a),g=l(c),x=u;(n&&x(new n(new ArrayBuffer(1)))!=d||i&&x(new i)!=s||o&&x(o.resolve())!=f||a&&x(new a)!=p||c&&x(new c)!=h)&&(x=function(t){var e=u(t),r="[object Object]"==e?t.constructor:void 0,n=r?l(r):"";if(n)switch(n){case y:return d;case v:return s;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},1707:(t,e,r)=>{var n=r(35142),i=r(46436);t.exports=function(t,e){e=n(e,t);for(var r=0,o=e.length;null!=t&&r<o;)t=t[i(e[r++])];return r&&r==o?t:void 0}},1944:t=>{t.exports=function(){return!1}},2408:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},2896:(t,e,r)=>{var n=r(81488),i=r(59467);t.exports=function(t,e){return null!=t&&i(t,e,n)}},2984:(t,e,r)=>{var n=r(49227);t.exports=function(t,e,r){for(var i=-1,o=t.length;++i<o;){var a=t[i],c=e(a);if(null!=c&&(void 0===u?c==c&&!n(c):r(c,u)))var u=c,l=a}return l}},3105:t=>{t.exports=function(t){return t.split("")}},3295:t=>{"use strict";t.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4999:(t,e,r)=>{t.exports=r(85718).Uint8Array},5231:(t,e,r)=>{var n=r(29395),i=r(55048);t.exports=function(t){if(!i(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},5359:t=>{t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},5566:(t,e,r)=>{var n=r(41011),i=r(34117),o=r(66713),a=r(42403);t.exports=function(t){return function(e){var r=i(e=a(e))?o(e):void 0,c=r?r[0]:e.charAt(0),u=r?n(r,1).join(""):e.slice(1);return c[t]()+u}}},6053:t=>{var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},6330:t=>{t.exports=function(){return[]}},6335:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>dB});var n={};r.r(n),r.d(n,{scaleBand:()=>nz,scaleDiverging:()=>function t(){var e=oP(c$()(oc));return e.copy=function(){return cz(e,t())},nN.apply(e,arguments)},scaleDivergingLog:()=>function t(){var e=oI(c$()).domain([.1,1,10]);return e.copy=function(){return cz(e,t()).base(e.base())},nN.apply(e,arguments)},scaleDivergingPow:()=>cF,scaleDivergingSqrt:()=>cq,scaleDivergingSymlog:()=>function t(){var e=oR(c$());return e.copy=function(){return cz(e,t()).constant(e.constant())},nN.apply(e,arguments)},scaleIdentity:()=>function t(e){var r;function n(t){return null==t||isNaN(t*=1)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,oo),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,oo):[0,1],oP(n)},scaleImplicit:()=>nB,scaleLinear:()=>oA,scaleLog:()=>function t(){let e=oI(op()).domain([1,10]);return e.copy=()=>of(e,t()).base(e.base()),nT.apply(e,arguments),e},scaleOrdinal:()=>nR,scalePoint:()=>nL,scalePow:()=>oF,scaleQuantile:()=>function t(){var e,r=[],n=[],i=[];function o(){var t=0,e=Math.max(1,n.length);for(i=Array(e-1);++t<e;)i[t-1]=function(t,e,r=iS){if(!(!(n=t.length)||isNaN(e*=1))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,i=(n-1)*e,o=Math.floor(i),a=+r(t[o],o,t);return a+(r(t[o+1],o+1,t)-a)*(i-o)}}(r,t/e);return a}function a(t){return null==t||isNaN(t*=1)?e:n[iA(i,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?i[e-1]:r[0],e<i.length?i[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e*=1)||r.push(e);return r.sort(ix),o()},a.range=function(t){return arguments.length?(n=Array.from(t),o()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return i.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},nT.apply(a,arguments)},scaleQuantize:()=>function t(){var e,r=0,n=1,i=1,o=[.5],a=[0,1];function c(t){return null!=t&&t<=t?a[iA(o,t,0,i)]:e}function u(){var t=-1;for(o=Array(i);++t<i;)o[t]=((t+1)*n-(t-i)*r)/(i+1);return c}return c.domain=function(t){return arguments.length?([r,n]=t,r*=1,n*=1,u()):[r,n]},c.range=function(t){return arguments.length?(i=(a=Array.from(t)).length-1,u()):a.slice()},c.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,o[0]]:e>=i?[o[i-1],n]:[o[e-1],o[e]]},c.unknown=function(t){return arguments.length&&(e=t),c},c.thresholds=function(){return o.slice()},c.copy=function(){return t().domain([r,n]).range(a).unknown(e)},nT.apply(oP(c),arguments)},scaleRadial:()=>function t(){var e,r=oh(),n=[0,1],i=!1;function o(t){var n,o=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(o)?e:i?Math.round(o):o}return o.invert=function(t){return r.invert(oW(t))},o.domain=function(t){return arguments.length?(r.domain(t),o):r.domain()},o.range=function(t){return arguments.length?(r.range((n=Array.from(t,oo)).map(oW)),o):n.slice()},o.rangeRound=function(t){return o.range(t).round(!0)},o.round=function(t){return arguments.length?(i=!!t,o):i},o.clamp=function(t){return arguments.length?(r.clamp(t),o):r.clamp()},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t(r.domain(),n).round(i).clamp(r.clamp()).unknown(e)},nT.apply(o,arguments),oP(o)},scaleSequential:()=>function t(){var e=oP(cR()(oc));return e.copy=function(){return cz(e,t())},nN.apply(e,arguments)},scaleSequentialLog:()=>function t(){var e=oI(cR()).domain([1,10]);return e.copy=function(){return cz(e,t()).base(e.base())},nN.apply(e,arguments)},scaleSequentialPow:()=>cL,scaleSequentialQuantile:()=>function t(){var e=[],r=oc;function n(t){if(null!=t&&!isNaN(t*=1))return r((iA(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r*=1)||e.push(r);return e.sort(ix),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e*=1)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n*=1)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e*=1))){if(e<=0||n<2)return oG(t);if(e>=1)return oX(t);var n,i=(n-1)*e,o=Math.floor(i),a=oX((function t(e,r,n=0,i=1/0,o){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),i=Math.floor(Math.min(e.length-1,i)),!(n<=r&&r<=i))return e;for(o=void 0===o?oH:function(t=ix){if(t===ix)return oH;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(o);i>n;){if(i-n>600){let a=i-n+1,c=r-n+1,u=Math.log(a),l=.5*Math.exp(2*u/3),s=.5*Math.sqrt(u*l*(a-l)/a)*(c-a/2<0?-1:1),f=Math.max(n,Math.floor(r-c*l/a+s)),p=Math.min(i,Math.floor(r+(a-c)*l/a+s));t(e,r,f,p,o)}let a=e[r],c=n,u=i;for(oV(e,n,r),o(e[i],a)>0&&oV(e,n,i);c<u;){for(oV(e,c,u),++c,--u;0>o(e[c],a);)++c;for(;o(e[u],a)>0;)--u}0===o(e[n],a)?oV(e,n,u):oV(e,++u,i),u<=r&&(n=u+1),r<=u&&(i=u-1)}return e})(t,o).subarray(0,o+1));return a+(oG(t.subarray(o+1))-a)*(i-o)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},nN.apply(n,arguments)},scaleSequentialSqrt:()=>cU,scaleSequentialSymlog:()=>function t(){var e=oR(cR());return e.copy=function(){return cz(e,t()).constant(e.constant())},nN.apply(e,arguments)},scaleSqrt:()=>oq,scaleSymlog:()=>function t(){var e=oR(op());return e.copy=function(){return of(e,t()).constant(e.constant())},nT.apply(e,arguments)},scaleThreshold:()=>function t(){var e,r=[.5],n=[0,1],i=1;function o(t){return null!=t&&t<=t?n[iA(r,t,0,i)]:e}return o.domain=function(t){return arguments.length?(i=Math.min((r=Array.from(t)).length,n.length-1),o):r.slice()},o.range=function(t){return arguments.length?(n=Array.from(t),i=Math.min(r.length,n.length-1),o):n.slice()},o.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},o.unknown=function(t){return arguments.length?(e=t,o):e},o.copy=function(){return t().domain(r).range(n).unknown(e)},nT.apply(o,arguments)},scaleTime:()=>cD,scaleUtc:()=>cB,tickFormat:()=>oS});var i=r(60687),o=r(43210),a=r.n(o),c=r(44493),u=r(29523),l=r(96834),s=r(46657),f=r(56770),p=r(63213),h=r(76628),d=r(25653),y=r(62185),v=r(78122),m=r(41862),b=r(93613),g=r(28947),x=r(10022),w=r(19080),O=r(45583),j=r(5336),S=r(25541),P=r(58559),A=r(48730),E=r(40228),_=r(31158),M=r(43649),k=r(49384),T=r(45603),N=r.n(T),C=r(63866),I=r.n(C),D=r(77822),B=r.n(D),R=r(40491),z=r.n(R),L=r(93490),U=r.n(L),$=function(t){return 0===t?0:t>0?1:-1},F=function(t){return I()(t)&&t.indexOf("%")===t.length-1},q=function(t){return U()(t)&&!B()(t)},W=function(t){return q(t)||I()(t)},X=0,G=function(t){var e=++X;return"".concat(t||"").concat(e)},H=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!q(t)&&!I()(t))return n;if(F(t)){var o=t.indexOf("%");r=e*parseFloat(t.slice(0,o))/100}else r=+t;return B()(r)&&(r=n),i&&r>e&&(r=e),r},V=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},Y=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++)if(r[t[n]])return!0;else r[t[n]]=!0;return!1},Z=function(t,e){return q(t)&&q(e)?function(r){return t+r*(e-t)}:function(){return e}};function K(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):z()(t,e))===r}):null}var Q=function(t,e){return q(t)&&q(e)?t-e:I()(t)&&I()(e)?t.localeCompare(e):t instanceof Date&&e instanceof Date?t.getTime()-e.getTime():String(t).localeCompare(String(e))},J=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),i=2;i<r;i++)n[i-2]=arguments[i]},tt=r(37456),te=r.n(tt),tr=r(5231),tn=r.n(tr),ti=r(55048),to=r.n(ti),ta=r(93780);function tc(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}function tu(t){return(tu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var tl=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],ts=["points","pathLength"],tf={svg:["viewBox","children"],polygon:ts,polyline:ts},tp=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],th=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,o.isValidElement)(t)&&(r=t.props),!to()(r))return null;var n={};return Object.keys(r).forEach(function(t){tp.includes(t)&&(n[t]=e||function(e){return r[t](r,e)})}),n},td=function(t,e,r){if(!to()(t)||"object"!==tu(t))return null;var n=null;return Object.keys(t).forEach(function(i){var o=t[i];tp.includes(i)&&"function"==typeof o&&(n||(n={}),n[i]=function(t){return o(e,r,t),null})}),n},ty=["children"],tv=["children"];function tm(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var tb={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},tg=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},tx=null,tw=null,tO=function t(e){if(e===tx&&Array.isArray(tw))return tw;var r=[];return o.Children.forEach(e,function(e){te()(e)||((0,ta.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),tw=r,tx=e,r};function tj(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return tg(t)}):[tg(e)],tO(t).forEach(function(t){var e=z()(t,"type.displayName")||z()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function tS(t,e){var r=tj(t,e);return r&&r[0]}var tP=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!q(r)&&!(r<=0)&&!!q(n)&&!(n<=0)},tA=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],tE=function(t,e,r,n){var i,o=null!=(i=null==tf?void 0:tf[n])?i:[];return e.startsWith("data-")||!tn()(t)&&(n&&o.includes(e)||tl.includes(e))||r&&tp.includes(e)},t_=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,o.isValidElement)(t)&&(n=t.props),!to()(n))return null;var i={};return Object.keys(n).forEach(function(t){var o;tE(null==(o=n)?void 0:o[t],t,e,r)&&(i[t]=n[t])}),i},tM=function t(e,r){if(e===r)return!0;var n=o.Children.count(e);if(n!==o.Children.count(r))return!1;if(0===n)return!0;if(1===n)return tk(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var i=0;i<n;i++){var a=e[i],c=r[i];if(Array.isArray(a)||Array.isArray(c)){if(!t(a,c))return!1}else if(!tk(a,c))return!1}return!0},tk=function(t,e){if(te()(t)&&te()(e))return!0;if(!te()(t)&&!te()(e)){var r=t.props||{},n=r.children,i=tm(r,ty),o=e.props||{},a=o.children,c=tm(o,tv);if(n&&a)return tc(i,c)&&tM(n,a);if(!n&&!a)return tc(i,c)}return!1},tT=function(t,e){var r=[],n={};return tO(t).forEach(function(t,i){var o;if((o=t)&&o.type&&I()(o.type)&&tA.indexOf(o.type)>=0)r.push(t);else if(t){var a=tg(t.type),c=e[a]||{},u=c.handler,l=c.once;if(u&&(!l||!n[a])){var s=u(t,a,i);r.push(s),n[a]=!0}}}),r},tN=function(t){var e=t&&t.type;return e&&tb[e]?tb[e]:null};function tC(t){return(tC="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tI(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tD(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tI(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tC(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tC(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tC(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tI(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tB(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var tR=(0,o.forwardRef)(function(t,e){var r,n=t.aspect,i=t.initialDimension,c=void 0===i?{width:-1,height:-1}:i,u=t.width,l=void 0===u?"100%":u,s=t.height,f=void 0===s?"100%":s,p=t.minWidth,h=void 0===p?0:p,d=t.minHeight,y=t.maxHeight,v=t.children,m=t.debounce,b=void 0===m?0:m,g=t.id,x=t.className,w=t.onResize,O=t.style,j=(0,o.useRef)(null),S=(0,o.useRef)();S.current=w,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(j.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),j.current},configurable:!0})});var P=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:c.width,containerHeight:c.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(r,2)||function(t,e){if(t){if("string"==typeof t)return tB(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tB(t,e)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),A=P[0],E=P[1],_=(0,o.useCallback)(function(t,e){E(function(r){var n=Math.round(t),i=Math.round(e);return r.containerWidth===n&&r.containerHeight===i?r:{containerWidth:n,containerHeight:i}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,i=r.height;_(n,i),null==(e=S.current)||e.call(S,n,i)};b>0&&(t=N()(t,b,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=j.current.getBoundingClientRect();return _(r.width,r.height),e.observe(j.current),function(){e.disconnect()}},[_,b]);var M=(0,o.useMemo)(function(){var t=A.containerWidth,e=A.containerHeight;if(t<0||e<0)return null;J(F(l)||F(f),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",l,f),J(!n||n>0,"The aspect(%s) must be greater than zero.",n);var r=F(l)?t:l,i=F(f)?e:f;n&&n>0&&(r?i=r/n:i&&(r=i*n),y&&i>y&&(i=y)),J(r>0||i>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,i,l,f,h,d,n);var c=!Array.isArray(v)&&tg(v.type).endsWith("Chart");return a().Children.map(v,function(t){return a().isValidElement(t)?(0,o.cloneElement)(t,tD({width:r,height:i},c?{style:tD({height:"100%",width:"100%",maxHeight:i,maxWidth:r},t.props.style)}:{})):t})},[n,v,f,y,d,h,A,l]);return a().createElement("div",{id:g?"".concat(g):void 0,className:(0,k.A)("recharts-responsive-container",x),style:tD(tD({},void 0===O?{}:O),{},{width:l,height:f,minWidth:h,minHeight:d,maxHeight:y}),ref:j},M)}),tz=r(34990),tL=r.n(tz),tU=r(85938),t$=r.n(tU);function tF(t,e){if(!t)throw Error("Invariant failed")}var tq=["children","width","height","viewBox","className","style","title","desc"];function tW(){return(tW=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tX(t){var e=t.children,r=t.width,n=t.height,i=t.viewBox,o=t.className,c=t.style,u=t.title,l=t.desc,s=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tq),f=i||{width:r,height:n,x:0,y:0},p=(0,k.A)("recharts-surface",o);return a().createElement("svg",tW({},t_(s,!0,"svg"),{className:p,width:r,height:n,style:c,viewBox:"".concat(f.x," ").concat(f.y," ").concat(f.width," ").concat(f.height)}),a().createElement("title",null,u),a().createElement("desc",null,l),e)}var tG=["children","className"];function tH(){return(tH=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var tV=a().forwardRef(function(t,e){var r=t.children,n=t.className,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,tG),o=(0,k.A)("recharts-layer",n);return a().createElement("g",tH({className:o},t_(i,!0),{ref:e}),r)});function tY(t){return(tY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tZ(){return(tZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tK(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tQ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tJ(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tQ(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=tY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tY(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tQ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function t0(t){return Array.isArray(t)&&W(t[0])&&W(t[1])?t.join(" ~ "):t}var t1=function(t){var e=t.separator,r=void 0===e?" : ":e,n=t.contentStyle,i=t.itemStyle,o=void 0===i?{}:i,c=t.labelStyle,u=t.payload,l=t.formatter,s=t.itemSorter,f=t.wrapperClassName,p=t.labelClassName,h=t.label,d=t.labelFormatter,y=t.accessibilityLayer,v=tJ({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===n?{}:n),m=tJ({margin:0},void 0===c?{}:c),b=!te()(h),g=b?h:"",x=(0,k.A)("recharts-default-tooltip",f),w=(0,k.A)("recharts-tooltip-label",p);return b&&d&&null!=u&&(g=d(h,u)),a().createElement("div",tZ({className:x,style:v},void 0!==y&&y?{role:"status","aria-live":"assertive"}:{}),a().createElement("p",{className:w,style:m},a().isValidElement(g)?g:"".concat(g)),function(){if(u&&u.length){var t=(s?t$()(u,s):u).map(function(t,e){if("none"===t.type)return null;var n=tJ({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},o),i=t.formatter||l||t0,c=t.value,s=t.name,f=c,p=s;if(i&&null!=f&&null!=p){var h=i(c,s,t,e,u);if(Array.isArray(h)){var d=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(h,2)||function(t,e){if(t){if("string"==typeof t)return tK(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tK(t,e)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();f=d[0],p=d[1]}else f=h}return a().createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:n},W(p)?a().createElement("span",{className:"recharts-tooltip-item-name"},p):null,W(p)?a().createElement("span",{className:"recharts-tooltip-item-separator"},r):null,a().createElement("span",{className:"recharts-tooltip-item-value"},f),a().createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return a().createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function t2(t){return(t2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t5(t,e,r){var n;return(n=function(t,e){if("object"!=t2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==t2(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var t4="recharts-tooltip-wrapper",t3={visibility:"hidden"};function t6(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,i=t.offsetTopLeft,o=t.position,a=t.reverseDirection,c=t.tooltipDimension,u=t.viewBox,l=t.viewBoxDimension;if(o&&q(o[n]))return o[n];var s=r[n]-c-i,f=r[n]+i;return e[n]?a[n]?s:f:a[n]?s<u[n]?Math.max(f,u[n]):Math.max(s,u[n]):f+c>u[n]+l?Math.max(s,u[n]):Math.max(f,u[n])}function t8(t){return(t8="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t7(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function t9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?t7(Object(r),!0).forEach(function(e){en(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):t7(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function et(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(et=function(){return!!t})()}function ee(t){return(ee=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function er(t,e){return(er=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function en(t,e,r){return(e=ei(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ei(t){var e=function(t,e){if("object"!=t8(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t8(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t8(e)?e:e+""}var eo=function(t){var e;function r(){var t,e,n;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=r,n=[].concat(o),e=ee(e),en(t=function(t,e){if(e&&("object"===t8(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,et()?Reflect.construct(e,n||[],ee(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),en(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,i,o;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!=(r=null==(n=t.props.coordinate)?void 0:n.x)?r:0,y:null!=(i=null==(o=t.props.coordinate)?void 0:o.y)?i:0}})}}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&er(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null==(t=this.props.coordinate)?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null==(e=this.props.coordinate)?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,n,i,o,c,u,l,s,f,p,h,d,y,v,m,b,g,x=this,w=this.props,O=w.active,j=w.allowEscapeViewBox,S=w.animationDuration,P=w.animationEasing,A=w.children,E=w.coordinate,_=w.hasPayload,M=w.isAnimationActive,T=w.offset,N=w.position,C=w.reverseDirection,I=w.useTranslate3d,D=w.viewBox,B=w.wrapperStyle,R=(p=(t={allowEscapeViewBox:j,coordinate:E,offsetTopLeft:T,position:N,reverseDirection:C,tooltipBox:this.state.lastBoundingBox,useTranslate3d:I,viewBox:D}).allowEscapeViewBox,h=t.coordinate,d=t.offsetTopLeft,y=t.position,v=t.reverseDirection,m=t.tooltipBox,b=t.useTranslate3d,g=t.viewBox,m.height>0&&m.width>0&&h?(r=(e={translateX:s=t6({allowEscapeViewBox:p,coordinate:h,key:"x",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.width,viewBox:g,viewBoxDimension:g.width}),translateY:f=t6({allowEscapeViewBox:p,coordinate:h,key:"y",offsetTopLeft:d,position:y,reverseDirection:v,tooltipDimension:m.height,viewBox:g,viewBoxDimension:g.height}),useTranslate3d:b}).translateX,n=e.translateY,l={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(n,"px, 0)"):"translate(".concat(r,"px, ").concat(n,"px)")}):l=t3,{cssProperties:l,cssClasses:(o=(i={translateX:s,translateY:f,coordinate:h}).coordinate,c=i.translateX,u=i.translateY,(0,k.A)(t4,t5(t5(t5(t5({},"".concat(t4,"-right"),q(c)&&o&&q(o.x)&&c>=o.x),"".concat(t4,"-left"),q(c)&&o&&q(o.x)&&c<o.x),"".concat(t4,"-bottom"),q(u)&&o&&q(o.y)&&u>=o.y),"".concat(t4,"-top"),q(u)&&o&&q(o.y)&&u<o.y)))}),z=R.cssClasses,L=R.cssProperties,U=t9(t9({transition:M&&O?"transform ".concat(S,"ms ").concat(P):void 0},L),{},{pointerEvents:"none",visibility:!this.state.dismissed&&O&&_?"visible":"hidden",position:"absolute",top:0,left:0},B);return a().createElement("div",{tabIndex:-1,className:z,style:U,ref:function(t){x.wrapperNode=t}},A)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ei(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent),ea={isSsr:!0,get:function(t){return ea[t]},set:function(t,e){if("string"==typeof t)ea[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){ea[e]=t[e]})}}},ec=r(36315),eu=r.n(ec);function el(t,e,r){return!0===e?eu()(t,r):tn()(e)?eu()(t,e):t}function es(t){return(es="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ef(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ep(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ef(Object(r),!0).forEach(function(e){ev(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ef(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eh(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eh=function(){return!!t})()}function ed(t){return(ed=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ey(t,e){return(ey=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ev(t,e,r){return(e=em(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function em(t){var e=function(t,e){if("object"!=es(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=es(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==es(e)?e:e+""}function eb(t){return t.dataKey}var eg=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=ed(t),function(t,e){if(e&&("object"===es(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,eh()?Reflect.construct(t,e||[],ed(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&ey(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,n=r.active,i=r.allowEscapeViewBox,o=r.animationDuration,c=r.animationEasing,u=r.content,l=r.coordinate,s=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,v=r.reverseDirection,m=r.useTranslate3d,b=r.viewBox,g=r.wrapperStyle,x=null!=h?h:[];s&&x.length&&(x=el(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,eb));var w=x.length>0;return a().createElement(eo,{allowEscapeViewBox:i,animationDuration:o,animationEasing:c,isAnimationActive:f,active:n,coordinate:l,hasPayload:w,offset:p,position:y,reverseDirection:v,useTranslate3d:m,viewBox:b,wrapperStyle:g},(t=ep(ep({},this.props),{},{payload:x}),a().isValidElement(u)?a().cloneElement(u,t):"function"==typeof u?a().createElement(u,t):a().createElement(t1,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,em(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);ev(eg,"displayName","Tooltip"),ev(eg,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!ea.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}});var ex=r(69433),ew=r.n(ex);let eO=Math.cos,ej=Math.sin,eS=Math.sqrt,eP=Math.PI,eA=2*eP,eE={draw(t,e){let r=eS(e/eP);t.moveTo(r,0),t.arc(0,0,r,0,eA)}},e_=eS(1/3),eM=2*e_,ek=ej(eP/10)/ej(7*eP/10),eT=ej(eA/10)*ek,eN=-eO(eA/10)*ek,eC=eS(3),eI=eS(3)/2,eD=1/eS(12),eB=(eD/2+1)*3;function eR(t){return function(){return t}}let ez=Math.PI,eL=2*ez,eU=eL-1e-6;function e$(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class eF{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?e$:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return e$;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,i,o){this._append`C${+t},${+e},${+r},${+n},${this._x1=+i},${this._y1=+o}`}arcTo(t,e,r,n,i){if(t*=1,e*=1,r*=1,n*=1,(i*=1)<0)throw Error(`negative radius: ${i}`);let o=this._x1,a=this._y1,c=r-t,u=n-e,l=o-t,s=a-e,f=l*l+s*s;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(f>1e-6)if(Math.abs(s*c-u*l)>1e-6&&i){let p=r-o,h=n-a,d=c*c+u*u,y=Math.sqrt(d),v=Math.sqrt(f),m=i*Math.tan((ez-Math.acos((d+f-(p*p+h*h))/(2*y*v)))/2),b=m/v,g=m/y;Math.abs(b-1)>1e-6&&this._append`L${t+b*l},${e+b*s}`,this._append`A${i},${i},0,0,${+(s*p>l*h)},${this._x1=t+g*c},${this._y1=e+g*u}`}else this._append`L${this._x1=t},${this._y1=e}`}arc(t,e,r,n,i,o){if(t*=1,e*=1,r*=1,o=!!o,r<0)throw Error(`negative radius: ${r}`);let a=r*Math.cos(n),c=r*Math.sin(n),u=t+a,l=e+c,s=1^o,f=o?n-i:i-n;null===this._x1?this._append`M${u},${l}`:(Math.abs(this._x1-u)>1e-6||Math.abs(this._y1-l)>1e-6)&&this._append`L${u},${l}`,r&&(f<0&&(f=f%eL+eL),f>eU?this._append`A${r},${r},0,1,${s},${t-a},${e-c}A${r},${r},0,1,${s},${this._x1=u},${this._y1=l}`:f>1e-6&&this._append`A${r},${r},0,${+(f>=ez)},${s},${this._x1=t+r*Math.cos(i)},${this._y1=e+r*Math.sin(i)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r*=1}v${+n}h${-r}Z`}toString(){return this._}}function eq(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new eF(e)}function eW(t){return(eW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}eF.prototype,eS(3),eS(3);var eX=["type","size","sizeType"];function eG(){return(eG=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function eH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function eV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?eH(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=eW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eW(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):eH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var eY={symbolCircle:eE,symbolCross:{draw(t,e){let r=eS(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=eS(e/eM),n=r*e_;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=eS(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=eS(.8908130915292852*e),n=eT*r,i=eN*r;t.moveTo(0,-r),t.lineTo(n,i);for(let e=1;e<5;++e){let o=eA*e/5,a=eO(o),c=ej(o);t.lineTo(c*r,-a*r),t.lineTo(a*n-c*i,c*n+a*i)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-eS(e/(3*eC));t.moveTo(0,2*r),t.lineTo(-eC*r,-r),t.lineTo(eC*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=eS(e/eB),n=r/2,i=r*eD,o=r*eD+r,a=-n;t.moveTo(n,i),t.lineTo(n,o),t.lineTo(a,o),t.lineTo(-.5*n-eI*i,eI*n+-.5*i),t.lineTo(-.5*n-eI*o,eI*n+-.5*o),t.lineTo(-.5*a-eI*o,eI*a+-.5*o),t.lineTo(-.5*n+eI*i,-.5*i-eI*n),t.lineTo(-.5*n+eI*o,-.5*o-eI*n),t.lineTo(-.5*a+eI*o,-.5*o-eI*a),t.closePath()}}},eZ=Math.PI/180,eK=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*eZ;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},eQ=function(t){var e,r=t.type,n=void 0===r?"circle":r,i=t.size,o=void 0===i?64:i,c=t.sizeType,u=void 0===c?"area":c,l=eV(eV({},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,eX)),{},{type:n,size:o,sizeType:u}),s=l.className,f=l.cx,p=l.cy,h=t_(l,!0);return f===+f&&p===+p&&o===+o?a().createElement("path",eG({},h,{className:(0,k.A)("recharts-symbols",s),transform:"translate(".concat(f,", ").concat(p,")"),d:(e=eY["symbol".concat(ew()(n))]||eE,(function(t,e){let r=null,n=eq(i);function i(){let i;if(r||(r=i=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),i)return r=null,i+""||null}return t="function"==typeof t?t:eR(t||eE),e="function"==typeof e?e:eR(void 0===e?64:+e),i.type=function(e){return arguments.length?(t="function"==typeof e?e:eR(e),i):t},i.size=function(t){return arguments.length?(e="function"==typeof t?t:eR(+t),i):e},i.context=function(t){return arguments.length?(r=null==t?null:t,i):r},i})().type(e).size(eK(o,u,n))())})):null};function eJ(t){return(eJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function e0(){return(e0=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function e1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}eQ.registerSymbol=function(t,e){eY["symbol".concat(ew()(t))]=e};function e2(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(e2=function(){return!!t})()}function e5(t){return(e5=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function e4(t,e){return(e4=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function e3(t,e,r){return(e=e6(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function e6(t){var e=function(t,e){if("object"!=eJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=eJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==eJ(e)?e:e+""}var e8=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=e5(t),function(t,e){if(e&&("object"===eJ(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,e2()?Reflect.construct(t,e||[],e5(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&e4(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,n=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return a().createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return a().createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(n,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*n,",").concat(16,"\n            H").concat(32,"M").concat(2*n,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(n,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return a().createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(a().isValidElement(t.legendIcon)){var o=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e1(Object(r),!0).forEach(function(e){e3(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete o.legendIcon,a().cloneElement(t.legendIcon,o)}return a().createElement(eQ,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,n=e.iconSize,i=e.layout,o=e.formatter,c=e.inactiveColor,u={x:0,y:0,width:32,height:32},l={display:"horizontal"===i?"inline-block":"block",marginRight:10},s={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var i=e.formatter||o,f=(0,k.A)(e3(e3({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var p=tn()(e.value)?null:e.value;J(!tn()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var h=e.inactive?c:e.color;return a().createElement("li",e0({className:f,style:l,key:"legend-item-".concat(r)},td(t.props,e,r)),a().createElement(tX,{width:n,height:n,viewBox:u,style:s},t.renderIcon(e)),a().createElement("span",{className:"recharts-legend-item-text",style:{color:h}},i?i(p,e,r):p))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,n=t.align;return e&&e.length?a().createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?n:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,e6(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(o.PureComponent);function e7(t){return(e7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}e3(e8,"displayName","Legend"),e3(e8,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var e9=["ref"];function rt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function re(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rt(Object(r),!0).forEach(function(e){ra(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rt(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rr(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,rc(n.key),n)}}function rn(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(rn=function(){return!!t})()}function ri(t){return(ri=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ro(t,e){return(ro=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ra(t,e,r){return(e=rc(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rc(t){var e=function(t,e){if("object"!=e7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=e7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e7(e)?e:e+""}function ru(t){return t.value}var rl=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=ri(e),ra(t=function(t,e){if(e&&("object"===e7(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,rn()?Reflect.construct(e,r||[],ri(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&ro(n,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?re({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,i=n.layout,o=n.align,a=n.verticalAlign,c=n.margin,u=n.chartWidth,l=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===o&&"vertical"===i?{left:((u||0)-this.getBBoxSnapshot().width)/2}:"right"===o?{right:c&&c.right||0}:{left:c&&c.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((l||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:c&&c.bottom||0}:{top:c&&c.top||0}),re(re({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,n=e.width,i=e.height,o=e.wrapperStyle,c=e.payloadUniqBy,u=e.payload,l=re(re({position:"absolute",width:n||"auto",height:i||"auto"},this.getDefaultPosition(o)),o);return a().createElement("div",{className:"recharts-legend-wrapper",style:l,ref:function(e){t.wrapperNode=e}},function(t,e){if(a().isValidElement(t))return a().cloneElement(t,e);if("function"==typeof t)return a().createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,e9);return a().createElement(e8,r)}(r,re(re({},this.props),{},{payload:el(u,c,ru)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=re(re({},this.defaultProps),t.props).layout;return"vertical"===r&&q(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&rr(n.prototype,e),r&&rr(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function rs(){return(rs=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}ra(rl,"displayName","Legend"),ra(rl,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"});var rf=function(t){var e=t.cx,r=t.cy,n=t.r,i=t.className,o=(0,k.A)("recharts-dot",i);return e===+e&&r===+r&&n===+n?a().createElement("circle",rs({},t_(t,!1),th(t),{className:o,cx:e,cy:r,r:n})):null},rp=r(87955),rh=r.n(rp),rd=Object.getOwnPropertyNames,ry=Object.getOwnPropertySymbols,rv=Object.prototype.hasOwnProperty;function rm(t,e){return function(r,n,i){return t(r,n,i)&&e(r,n,i)}}function rb(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var i=n.cache,o=i.get(e),a=i.get(r);if(o&&a)return o===r&&a===e;i.set(e,r),i.set(r,e);var c=t(e,r,n);return i.delete(e),i.delete(r),c}}function rg(t){return rd(t).concat(ry(t))}var rx=Object.hasOwn||function(t,e){return rv.call(t,e)};function rw(t,e){return t===e||!t&&!e&&t!=t&&e!=e}var rO=Object.getOwnPropertyDescriptor,rj=Object.keys;function rS(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function rP(t,e){return rw(t.getTime(),e.getTime())}function rA(t,e){return t.name===e.name&&t.message===e.message&&t.cause===e.cause&&t.stack===e.stack}function rE(t,e){return t===e}function r_(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.entries(),u=0;(n=c.next())&&!n.done;){for(var l=e.entries(),s=!1,f=0;(i=l.next())&&!i.done;){if(a[f]){f++;continue}var p=n.value,h=i.value;if(r.equals(p[0],h[0],u,f,t,e,r)&&r.equals(p[1],h[1],p[0],h[0],t,e,r)){s=a[f]=!0;break}f++}if(!s)return!1;u++}return!0}function rM(t,e,r){var n=rj(t),i=n.length;if(rj(e).length!==i)return!1;for(;i-- >0;)if(!rB(t,e,r,n[i]))return!1;return!0}function rk(t,e,r){var n,i,o,a=rg(t),c=a.length;if(rg(e).length!==c)return!1;for(;c-- >0;)if(!rB(t,e,r,n=a[c])||(i=rO(t,n),o=rO(e,n),(i||o)&&(!i||!o||i.configurable!==o.configurable||i.enumerable!==o.enumerable||i.writable!==o.writable)))return!1;return!0}function rT(t,e){return rw(t.valueOf(),e.valueOf())}function rN(t,e){return t.source===e.source&&t.flags===e.flags}function rC(t,e,r){var n,i,o=t.size;if(o!==e.size)return!1;if(!o)return!0;for(var a=Array(o),c=t.values();(n=c.next())&&!n.done;){for(var u=e.values(),l=!1,s=0;(i=u.next())&&!i.done;){if(!a[s]&&r.equals(n.value,i.value,n.value,i.value,t,e,r)){l=a[s]=!0;break}s++}if(!l)return!1}return!0}function rI(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}function rD(t,e){return t.hostname===e.hostname&&t.pathname===e.pathname&&t.protocol===e.protocol&&t.port===e.port&&t.hash===e.hash&&t.username===e.username&&t.password===e.password}function rB(t,e,r,n){return("_owner"===n||"__o"===n||"__v"===n)&&(!!t.$$typeof||!!e.$$typeof)||rx(e,n)&&r.equals(t[n],e[n],n,n,t,e,r)}var rR=Array.isArray,rz="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,rL=Object.assign,rU=Object.prototype.toString.call.bind(Object.prototype.toString),r$=rF();function rF(t){void 0===t&&(t={});var e,r,n,i,o,a,c,u,l,s,f,p,h,d=t.circular,y=t.createInternalComparator,v=t.createState,m=t.strict,b=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,i={areArraysEqual:n?rk:rS,areDatesEqual:rP,areErrorsEqual:rA,areFunctionsEqual:rE,areMapsEqual:n?rm(r_,rk):r_,areNumbersEqual:rw,areObjectsEqual:n?rk:rM,arePrimitiveWrappersEqual:rT,areRegExpsEqual:rN,areSetsEqual:n?rm(rC,rk):rC,areTypedArraysEqual:n?rk:rI,areUrlsEqual:rD};if(r&&(i=rL({},i,r(i))),e){var o=rb(i.areArraysEqual),a=rb(i.areMapsEqual),c=rb(i.areObjectsEqual),u=rb(i.areSetsEqual);i=rL({},i,{areArraysEqual:o,areMapsEqual:a,areObjectsEqual:c,areSetsEqual:u})}return i}(t)).areArraysEqual,n=e.areDatesEqual,i=e.areErrorsEqual,o=e.areFunctionsEqual,a=e.areMapsEqual,c=e.areNumbersEqual,u=e.areObjectsEqual,l=e.arePrimitiveWrappersEqual,s=e.areRegExpsEqual,f=e.areSetsEqual,p=e.areTypedArraysEqual,h=e.areUrlsEqual,function(t,e,d){if(t===e)return!0;if(null==t||null==e)return!1;var y=typeof t;if(y!==typeof e)return!1;if("object"!==y)return"number"===y?c(t,e,d):"function"===y&&o(t,e,d);var v=t.constructor;if(v!==e.constructor)return!1;if(v===Object)return u(t,e,d);if(rR(t))return r(t,e,d);if(null!=rz&&rz(t))return p(t,e,d);if(v===Date)return n(t,e,d);if(v===RegExp)return s(t,e,d);if(v===Map)return a(t,e,d);if(v===Set)return f(t,e,d);var m=rU(t);return"[object Date]"===m?n(t,e,d):"[object RegExp]"===m?s(t,e,d):"[object Map]"===m?a(t,e,d):"[object Set]"===m?f(t,e,d):"[object Object]"===m?"function"!=typeof t.then&&"function"!=typeof e.then&&u(t,e,d):"[object URL]"===m?h(t,e,d):"[object Error]"===m?i(t,e,d):"[object Arguments]"===m?u(t,e,d):("[object Boolean]"===m||"[object Number]"===m||"[object String]"===m)&&l(t,e,d)}),g=y?y(b):function(t,e,r,n,i,o,a){return b(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,i=t.equals,o=t.strict;if(n)return function(t,a){var c=n(),u=c.cache;return r(t,a,{cache:void 0===u?e?new WeakMap:void 0:u,equals:i,meta:c.meta,strict:o})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:i,meta:void 0,strict:o})};var a={cache:void 0,equals:i,meta:void 0,strict:o};return function(t,e){return r(t,e,a)}}({circular:void 0!==d&&d,comparator:b,createState:v,equals:g,strict:void 0!==m&&m})}function rq(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(i){if(r<0&&(r=i),i-r>e)t(i),r=-1;else{var o;o=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(o)}})}function rW(t){return(rW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rX(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rG(t){return(rG="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rV(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rH(Object(r),!0).forEach(function(e){rY(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rY(t,e,r){var n;return(n=function(t,e){if("object"!==rG(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rG(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===rG(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}rF({strict:!0}),rF({circular:!0}),rF({circular:!0,strict:!0}),rF({createInternalComparator:function(){return rw}}),rF({strict:!0,createInternalComparator:function(){return rw}}),rF({circular:!0,createInternalComparator:function(){return rw}}),rF({circular:!0,createInternalComparator:function(){return rw},strict:!0});var rZ=function(t){return t},rK=function(t,e){return Object.keys(e).reduce(function(r,n){return rV(rV({},r),{},rY({},n,t(n,e[n])))},{})},rQ=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},rJ=function(t,e,r,n,i,o,a,c){};function r0(t,e){if(t){if("string"==typeof t)return r1(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return r1(t,e)}}function r1(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var r2=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},r5=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},r4=function(t,e){return function(r){return r5(r2(t,e),r)}},r3=function(){for(var t,e,r=arguments.length,n=Array(r),i=0;i<r;i++)n[i]=arguments[i];var o=n[0],a=n[1],c=n[2],u=n[3];if(1===n.length)switch(n[0]){case"linear":o=0,a=0,c=1,u=1;break;case"ease":o=.25,a=.1,c=.25,u=1;break;case"ease-in":o=.42,a=0,c=1,u=1;break;case"ease-out":o=.42,a=0,c=.58,u=1;break;case"ease-in-out":o=0,a=0,c=.58,u=1;break;default:var l=n[0].split("(");if("cubic-bezier"===l[0]&&4===l[1].split(")")[0].split(",").length){var s,f=function(t){if(Array.isArray(t))return t}(s=l[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(s,4)||r0(s,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=f[0],a=f[1],c=f[2],u=f[3]}else rJ(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}rJ([o,c,a,u].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=r4(o,c),h=r4(a,u),d=(t=o,e=c,function(r){var n;return r5([].concat(function(t){if(Array.isArray(t))return r1(t)}(n=r2(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||r0(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var i,o=p(r)-e,a=d(r);if(1e-4>Math.abs(o-e)||a<1e-4)break;r=(i=r-o/a)>1?1:i<0?0:i}return h(r)};return y.isStepper=!1,y},r6=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,i=void 0===n?8:n,o=t.dt,a=void 0===o?17:o,c=function(t,e,n){var o=n+(-(t-e)*r-n*i)*a/1e3,c=n*a/1e3+t;return 1e-4>Math.abs(c-e)&&1e-4>Math.abs(o)?[e,0]:[c,o]};return c.isStepper=!0,c.dt=a,c},r8=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return r3(n);case"spring":return r6();default:if("cubic-bezier"===n.split("(")[0])return r3(n);rJ(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(rJ(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function r7(t){return(r7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r9(t){return function(t){if(Array.isArray(t))return ni(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||nn(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ne(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nt(Object(r),!0).forEach(function(e){nr(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nt(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nr(t,e,r){var n;return(n=function(t,e){if("object"!==r7(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==r7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===r7(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nn(t,e){if(t){if("string"==typeof t)return ni(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ni(t,e)}}function ni(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var no=function(t,e,r){return t+(e-t)*r},na=function(t){return t.from!==t.to},nc=function t(e,r,n){var i=rK(function(t,r){if(na(r)){var n,i=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(n,2)||nn(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i[1];return ne(ne({},r),{},{from:o,velocity:a})}return r},r);return n<1?rK(function(t,e){return na(e)?ne(ne({},e),{},{velocity:no(e.velocity,i[t].velocity,n),from:no(e.from,i[t].from,n)}):e},r):t(e,i,n-1)};let nu=function(t,e,r,n,i){var o,a,c=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),u=c.reduce(function(r,n){return ne(ne({},r),{},nr({},n,[t[n],e[n]]))},{}),l=c.reduce(function(r,n){return ne(ne({},r),{},nr({},n,{from:t[n],velocity:0,to:e[n]}))},{}),s=-1,f=function(){return null};return f=r.isStepper?function(n){o||(o=n);var a=(n-o)/r.dt;l=nc(r,l,a),i(ne(ne(ne({},t),e),rK(function(t,e){return e.from},l))),o=n,Object.values(l).filter(na).length&&(s=requestAnimationFrame(f))}:function(o){a||(a=o);var c=(o-a)/n,l=rK(function(t,e){return no.apply(void 0,r9(e).concat([r(c)]))},u);if(i(ne(ne(ne({},t),e),l)),c<1)s=requestAnimationFrame(f);else{var p=rK(function(t,e){return no.apply(void 0,r9(e).concat([r(1)]))},u);i(ne(ne(ne({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(s)}}};function nl(t){return(nl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ns=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function nf(t){return function(t){if(Array.isArray(t))return np(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return np(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return np(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function np(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nh(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nd(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nh(Object(r),!0).forEach(function(e){ny(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nh(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ny(t,e,r){return(e=nv(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nv(t){var e=function(t,e){if("object"!==nl(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==nl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===nl(e)?e:String(e)}function nm(t,e){return(nm=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nb(t,e){if(e&&("object"===nl(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return ng(t)}function ng(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function nx(t){return(nx=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var nw=function(t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");i.prototype=Object.create(t&&t.prototype,{constructor:{value:i,writable:!0,configurable:!0}}),Object.defineProperty(i,"prototype",{writable:!1}),t&&nm(i,t);var e,r,n=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=nx(i);return t=e?Reflect.construct(r,arguments,nx(this).constructor):r.apply(this,arguments),nb(this,t)});function i(t,e){if(!(this instanceof i))throw TypeError("Cannot call a class as a function");var r=n.call(this,t,e),o=r.props,a=o.isActive,c=o.attributeName,u=o.from,l=o.to,s=o.steps,f=o.children,p=o.duration;if(r.handleStyleChange=r.handleStyleChange.bind(ng(r)),r.changeStyle=r.changeStyle.bind(ng(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:l}),nb(r);if(s&&s.length)r.state={style:s[0].style};else if(u){if("function"==typeof f)return r.state={style:u},nb(r);r.state={style:c?ny({},c,u):u}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,i=e.attributeName,o=e.shouldReAnimate,a=e.to,c=e.from,u=this.state.style;if(n){if(!r){var l={style:i?ny({},i,a):a};this.state&&u&&(i&&u[i]!==a||!i&&u!==a)&&this.setState(l);return}if(!r$(t.to,a)||!t.canBegin||!t.isActive){var s=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=s||o?c:t.to;if(this.state&&u){var p={style:i?ny({},i,f):f};(i&&u[i]!==f||!i&&u!==f)&&this.setState(p)}this.runAnimation(nd(nd({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,i=t.duration,o=t.easing,a=t.begin,c=t.onAnimationEnd,u=t.onAnimationStart,l=nu(r,n,r8(o),i,this.changeStyle);this.manager.start([u,a,function(){e.stopJSAnimation=l()},i,c])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,i=t.onAnimationStart,o=r[0],a=o.style,c=o.duration;return this.manager.start([i].concat(nf(r.reduce(function(t,n,i){if(0===i)return t;var o=n.duration,a=n.easing,c=void 0===a?"ease":a,u=n.style,l=n.properties,s=n.onAnimationEnd,f=i>0?r[i-1]:n,p=l||Object.keys(u);if("function"==typeof c||"spring"===c)return[].concat(nf(t),[e.runJSAnimation.bind(e,{from:f.style,to:u,duration:o,easing:c}),o]);var h=rQ(p,o,c),d=nd(nd(nd({},f.style),u),{},{transition:h});return[].concat(nf(t),[d,o,s]).filter(rZ)},[a,Math.max(void 0===c?0:c,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){this.manager||(this.manager=(r=function(){return null},n=!1,i=function t(e){if(!n){if(Array.isArray(e)){if(!e.length)return;var i=function(t){if(Array.isArray(t))return t}(e)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(e)||function(t,e){if(t){if("string"==typeof t)return rX(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rX(t,e)}}(e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),o=i[0],a=i.slice(1);return"number"==typeof o?void rq(t.bind(null,a),o):(t(o),void rq(t.bind(null,a)))}"object"===rW(e)&&r(e),"function"==typeof e&&e()}},{stop:function(){n=!0},start:function(t){n=!1,i(t)},subscribe:function(t){return r=t,function(){r=function(){return null}}}}));var e,r,n,i,o=t.begin,a=t.duration,c=t.attributeName,u=t.to,l=t.easing,s=t.onAnimationStart,f=t.onAnimationEnd,p=t.steps,h=t.children,d=this.manager;if(this.unSubscribe=d.subscribe(this.handleStyleChange),"function"==typeof l||"function"==typeof h||"spring"===l)return void this.runJSAnimation(t);if(p.length>1)return void this.runStepAnimation(t);var y=c?ny({},c,u):u,v=rQ(Object.keys(y),a,l);d.start([s,o,nd(nd({},y),{},{transition:v}),a,f])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),n=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r,n,i={},o=Object.keys(t);for(n=0;n<o.length;n++)r=o[n],e.indexOf(r)>=0||(i[r]=t[r]);return i}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,ns)),c=o.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!n||0===c||r<=0)return e;var l=function(t){var e=t.props,r=e.style,n=e.className;return(0,o.cloneElement)(t,nd(nd({},i),{},{style:nd(nd({},void 0===r?{}:r),u),className:n}))};return 1===c?l(o.Children.only(e)):a().createElement("div",null,o.Children.map(e,function(t){return l(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nv(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(o.PureComponent);function nO(t){return(nO="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nj(){return(nj=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nS(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nP(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nA(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nP(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=nO(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nO(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nO(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nP(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}nw.displayName="Animate",nw.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},nw.propTypes={from:rh().oneOfType([rh().object,rh().string]),to:rh().oneOfType([rh().object,rh().string]),attributeName:rh().string,duration:rh().number,begin:rh().number,easing:rh().oneOfType([rh().string,rh().func]),steps:rh().arrayOf(rh().shape({duration:rh().number.isRequired,style:rh().object.isRequired,easing:rh().oneOfType([rh().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),rh().func]),properties:rh().arrayOf("string"),onAnimationEnd:rh().func})),children:rh().oneOfType([rh().node,rh().func]),isActive:rh().bool,canBegin:rh().bool,onAnimationEnd:rh().func,shouldReAnimate:rh().bool,onAnimationStart:rh().func,onAnimationReStart:rh().func};var nE=function(t,e,r,n,i){var o,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),c=n>=0?1:-1,u=r>=0?1:-1,l=+(n>=0&&r>=0||n<0&&r<0);if(a>0&&i instanceof Array){for(var s=[0,0,0,0],f=0;f<4;f++)s[f]=i[f]>a?a:i[f];o="M".concat(t,",").concat(e+c*s[0]),s[0]>0&&(o+="A ".concat(s[0],",").concat(s[0],",0,0,").concat(l,",").concat(t+u*s[0],",").concat(e)),o+="L ".concat(t+r-u*s[1],",").concat(e),s[1]>0&&(o+="A ".concat(s[1],",").concat(s[1],",0,0,").concat(l,",\n        ").concat(t+r,",").concat(e+c*s[1])),o+="L ".concat(t+r,",").concat(e+n-c*s[2]),s[2]>0&&(o+="A ".concat(s[2],",").concat(s[2],",0,0,").concat(l,",\n        ").concat(t+r-u*s[2],",").concat(e+n)),o+="L ".concat(t+u*s[3],",").concat(e+n),s[3]>0&&(o+="A ".concat(s[3],",").concat(s[3],",0,0,").concat(l,",\n        ").concat(t,",").concat(e+n-c*s[3])),o+="Z"}else if(a>0&&i===+i&&i>0){var p=Math.min(a,i);o="M ".concat(t,",").concat(e+c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+u*p,",").concat(e,"\n            L ").concat(t+r-u*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r,",").concat(e+c*p,"\n            L ").concat(t+r,",").concat(e+n-c*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t+r-u*p,",").concat(e+n,"\n            L ").concat(t+u*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(l,",").concat(t,",").concat(e+n-c*p," Z")}else o="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return o},n_=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,i=e.x,o=e.y,a=e.width,c=e.height;if(Math.abs(a)>0&&Math.abs(c)>0){var u=Math.min(i,i+a),l=Math.max(i,i+a),s=Math.min(o,o+c),f=Math.max(o,o+c);return r>=u&&r<=l&&n>=s&&n<=f}return!1},nM={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},nk=function(t){var e,r=nA(nA({},nM),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return nS(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nS(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],u=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.width,p=r.height,h=r.radius,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isAnimationActive,g=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||0===f||0===p)return null;var x=(0,k.A)("recharts-rectangle",d);return g?a().createElement(nw,{canBegin:c>0,from:{width:f,height:p,x:l,y:s},to:{width:f,height:p,x:l,y:s},duration:v,animationEasing:y,isActive:g},function(t){var e=t.width,i=t.height,o=t.x,u=t.y;return a().createElement(nw,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,isActive:b,easing:y},a().createElement("path",nj({},t_(r,!0),{className:x,d:nE(o,u,e,i,h),ref:n})))}):a().createElement("path",nj({},t_(r,!0),{className:x,d:nE(l,s,f,p,h)}))};function nT(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function nN(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}class nC extends Map{constructor(t,e=nD){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(nI(this,t))}has(t){return super.has(nI(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function nI({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function nD(t){return null!==t&&"object"==typeof t?t.valueOf():t}let nB=Symbol("implicit");function nR(){var t=new nC,e=[],r=[],n=nB;function i(i){let o=t.get(i);if(void 0===o){if(n!==nB)return n;t.set(i,o=e.push(i)-1)}return r[o%r.length]}return i.domain=function(r){if(!arguments.length)return e.slice();for(let n of(e=[],t=new nC,r))t.has(n)||t.set(n,e.push(n)-1);return i},i.range=function(t){return arguments.length?(r=Array.from(t),i):r.slice()},i.unknown=function(t){return arguments.length?(n=t,i):n},i.copy=function(){return nR(e,r).unknown(n)},nT.apply(i,arguments),i}function nz(){var t,e,r=nR().unknown(void 0),n=r.domain,i=r.range,o=0,a=1,c=!1,u=0,l=0,s=.5;function f(){var r=n().length,f=a<o,p=f?a:o,h=f?o:a;t=(h-p)/Math.max(1,r-u+2*l),c&&(t=Math.floor(t)),p+=(h-p-t*(r-u))*s,e=t*(1-u),c&&(p=Math.round(p),e=Math.round(e));var d=(function(t,e,r){t*=1,e*=1,r=(i=arguments.length)<2?(e=t,t=0,1):i<3?1:+r;for(var n=-1,i=0|Math.max(0,Math.ceil((e-t)/r)),o=Array(i);++n<i;)o[n]=t+n*r;return o})(r).map(function(e){return p+t*e});return i(f?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(n(t),f()):n()},r.range=function(t){return arguments.length?([o,a]=t,o*=1,a*=1,f()):[o,a]},r.rangeRound=function(t){return[o,a]=t,o*=1,a*=1,c=!0,f()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(c=!!t,f()):c},r.padding=function(t){return arguments.length?(u=Math.min(1,l=+t),f()):u},r.paddingInner=function(t){return arguments.length?(u=Math.min(1,t),f()):u},r.paddingOuter=function(t){return arguments.length?(l=+t,f()):l},r.align=function(t){return arguments.length?(s=Math.max(0,Math.min(1,t)),f()):s},r.copy=function(){return nz(n(),[o,a]).round(c).paddingInner(u).paddingOuter(l).align(s)},nT.apply(f(),arguments)}function nL(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(nz.apply(null,arguments).paddingInner(1))}function nU(t){return(nU="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n$(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nF(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n$(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=nU(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nU(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nU(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n$(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nq(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nW={widthCache:{},cacheCount:0},nX={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},nG="recharts_measurement_span",nH=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||ea.isSsr)return{width:0,height:0};var n=(Object.keys(e=nF({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:n});if(nW.widthCache[i])return nW.widthCache[i];try{var o=document.getElementById(nG);o||((o=document.createElement("span")).setAttribute("id",nG),o.setAttribute("aria-hidden","true"),document.body.appendChild(o));var a=nF(nF({},nX),n);Object.assign(o.style,a),o.textContent="".concat(t);var c=o.getBoundingClientRect(),u={width:c.width,height:c.height};return nW.widthCache[i]=u,++nW.cacheCount>2e3&&(nW.cacheCount=0,nW.widthCache={}),u}catch(t){return{width:0,height:0}}};function nV(t){return(nV="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nY(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return nZ(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nZ(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nZ(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nK(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=nV(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nV(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nV(e)?e:e+""}(n.key),n)}}var nQ=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,nJ=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,n0=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,n1=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,n2={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},n5=Object.keys(n2),n4=function(){var t,e;function r(t,e){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||n0.test(e)||(this.num=NaN,this.unit=""),n5.includes(e)&&(this.num=t*n2[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=nY(null!=(e=n1.exec(t))?e:[],3),i=n[1],o=n[2];return new r(parseFloat(i),null!=o?o:"")}}],t&&nK(r.prototype,t),e&&nK(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function n3(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=nY(null!=(r=nQ.exec(e))?r:[],4),i=n[1],o=n[2],a=n[3],c=n4.parse(null!=i?i:""),u=n4.parse(null!=a?a:""),l="*"===o?c.multiply(u):c.divide(u);if(l.isNaN())return"NaN";e=e.replace(nQ,l.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var s,f=nY(null!=(s=nJ.exec(e))?s:[],4),p=f[1],h=f[2],d=f[3],y=n4.parse(null!=p?p:""),v=n4.parse(null!=d?d:""),m="+"===h?y.add(v):y.subtract(v);if(m.isNaN())return"NaN";e=e.replace(nJ,m.toString())}return e}var n6=/\(([^()]*)\)/;function n8(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=nY(n6.exec(e),2)[1];e=e.replace(n6,n3(r))}return e}(e),e=n3(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var n7=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],n9=["dx","dy","angle","className","breakAll"];function it(){return(it=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ie(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function ir(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||function(t,e){if(t){if("string"==typeof t)return ii(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ii(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ii(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var io=/[ \f\n\r\t\v\u2028\u2029]+/,ia=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var i=[];te()(e)||(i=r?e.toString().split(""):e.toString().split(io));var o=i.map(function(t){return{word:t,width:nH(t,n).width}}),a=r?0:nH("\xa0",n).width;return{wordsWithComputedWidth:o,spaceWidth:a}}catch(t){return null}},ic=function(t,e,r,n,i){var o,a=t.maxLines,c=t.children,u=t.style,l=t.breakAll,s=q(a),f=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var o=e.word,a=e.width,c=t[t.length-1];return c&&(null==n||i||c.width+a+r<Number(n))?(c.words.push(o),c.width+=a+r):t.push({words:[o],width:a}),t},[])},p=f(e);if(!s)return p;for(var h=function(t){var e=f(ia({breakAll:l,style:u,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},d=0,y=c.length-1,v=0;d<=y&&v<=c.length-1;){var m=Math.floor((d+y)/2),b=ir(h(m-1),2),g=b[0],x=b[1],w=ir(h(m),1)[0];if(g||w||(d=m+1),g&&w&&(y=m-1),!g&&w){o=x;break}v++}return o||p},iu=function(t){return[{words:te()(t)?[]:t.toString().split(io)}]},il=function(t){var e=t.width,r=t.scaleToFit,n=t.children,i=t.style,o=t.breakAll,a=t.maxLines;if((e||r)&&!ea.isSsr){var c=ia({breakAll:o,children:n,style:i});if(!c)return iu(n);var u=c.wordsWithComputedWidth,l=c.spaceWidth;return ic({breakAll:o,children:n,maxLines:a,style:i},u,l,e,r)}return iu(n)},is="#808080",ip=function(t){var e,r=t.x,n=void 0===r?0:r,i=t.y,c=void 0===i?0:i,u=t.lineHeight,l=void 0===u?"1em":u,s=t.capHeight,f=void 0===s?"0.71em":s,p=t.scaleToFit,h=void 0!==p&&p,d=t.textAnchor,y=t.verticalAnchor,v=t.fill,m=void 0===v?is:v,b=ie(t,n7),g=(0,o.useMemo)(function(){return il({breakAll:b.breakAll,children:b.children,maxLines:b.maxLines,scaleToFit:h,style:b.style,width:b.width})},[b.breakAll,b.children,b.maxLines,h,b.style,b.width]),x=b.dx,w=b.dy,O=b.angle,j=b.className,S=b.breakAll,P=ie(b,n9);if(!W(n)||!W(c))return null;var A=n+(q(x)?x:0),E=c+(q(w)?w:0);switch(void 0===y?"end":y){case"start":e=n8("calc(".concat(f,")"));break;case"middle":e=n8("calc(".concat((g.length-1)/2," * -").concat(l," + (").concat(f," / 2))"));break;default:e=n8("calc(".concat(g.length-1," * -").concat(l,")"))}var _=[];if(h){var M=g[0].width,T=b.width;_.push("scale(".concat((q(T)?T/M:1)/M,")"))}return O&&_.push("rotate(".concat(O,", ").concat(A,", ").concat(E,")")),_.length&&(P.transform=_.join(" ")),a().createElement("text",it({},t_(P,!0),{x:A,y:E,className:(0,k.A)("recharts-text",j),textAnchor:void 0===d?"start":d,fill:m.includes("url")?is:m}),g.map(function(t,r){var n=t.words.join(S?"":" ");return a().createElement("tspan",{x:A,dy:0===r?e:l,key:"".concat(n,"-").concat(r)},n)}))};let ih=Math.sqrt(50),id=Math.sqrt(10),iy=Math.sqrt(2);function iv(t,e,r){let n,i,o,a=(e-t)/Math.max(0,r),c=Math.floor(Math.log10(a)),u=a/Math.pow(10,c),l=u>=ih?10:u>=id?5:u>=iy?2:1;return(c<0?(n=Math.round(t*(o=Math.pow(10,-c)/l)),i=Math.round(e*o),n/o<t&&++n,i/o>e&&--i,o=-o):(n=Math.round(t/(o=Math.pow(10,c)*l)),i=Math.round(e/o),n*o<t&&++n,i*o>e&&--i),i<n&&.5<=r&&r<2)?iv(t,e,2*r):[n,i,o]}function im(t,e,r){if(e*=1,t*=1,!((r*=1)>0))return[];if(t===e)return[t];let n=e<t,[i,o,a]=n?iv(e,t,r):iv(t,e,r);if(!(o>=i))return[];let c=o-i+1,u=Array(c);if(n)if(a<0)for(let t=0;t<c;++t)u[t]=-((o-t)/a);else for(let t=0;t<c;++t)u[t]=(o-t)*a;else if(a<0)for(let t=0;t<c;++t)u[t]=-((i+t)/a);else for(let t=0;t<c;++t)u[t]=(i+t)*a;return u}function ib(t,e,r){return iv(t*=1,e*=1,r*=1)[2]}function ig(t,e,r){e*=1,t*=1,r*=1;let n=e<t,i=n?ib(e,t,r):ib(t,e,r);return(n?-1:1)*(i<0?-(1/i):i)}function ix(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function iw(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function iO(t){let e,r,n;function i(t,n,o=0,a=t.length){if(o<a){if(0!==e(n,n))return a;do{let e=o+a>>>1;0>r(t[e],n)?o=e+1:a=e}while(o<a)}return o}return 2!==t.length?(e=ix,r=(e,r)=>ix(t(e),r),n=(e,r)=>t(e)-r):(e=t===ix||t===iw?t:ij,r=t,n=t),{left:i,center:function(t,e,r=0,o=t.length){let a=i(t,e,r,o-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,i=0,o=t.length){if(i<o){if(0!==e(n,n))return o;do{let e=i+o>>>1;0>=r(t[e],n)?i=e+1:o=e}while(i<o)}return i}}}function ij(){return 0}function iS(t){return null===t?NaN:+t}let iP=iO(ix),iA=iP.right;function iE(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function i_(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function iM(){}iP.left,iO(iS).center;var ik="\\s*([+-]?\\d+)\\s*",iT="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",iN="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",iC=/^#([0-9a-f]{3,8})$/,iI=RegExp(`^rgb\\(${ik},${ik},${ik}\\)$`),iD=RegExp(`^rgb\\(${iN},${iN},${iN}\\)$`),iB=RegExp(`^rgba\\(${ik},${ik},${ik},${iT}\\)$`),iR=RegExp(`^rgba\\(${iN},${iN},${iN},${iT}\\)$`),iz=RegExp(`^hsl\\(${iT},${iN},${iN}\\)$`),iL=RegExp(`^hsla\\(${iT},${iN},${iN},${iT}\\)$`),iU={aliceblue:0xf0f8ff,antiquewhite:0xfaebd7,aqua:65535,aquamarine:8388564,azure:0xf0ffff,beige:0xf5f5dc,bisque:0xffe4c4,black:0,blanchedalmond:0xffebcd,blue:255,blueviolet:9055202,brown:0xa52a2a,burlywood:0xdeb887,cadetblue:6266528,chartreuse:8388352,chocolate:0xd2691e,coral:0xff7f50,cornflowerblue:6591981,cornsilk:0xfff8dc,crimson:0xdc143c,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:0xb8860b,darkgray:0xa9a9a9,darkgreen:25600,darkgrey:0xa9a9a9,darkkhaki:0xbdb76b,darkmagenta:9109643,darkolivegreen:5597999,darkorange:0xff8c00,darkorchid:0x9932cc,darkred:9109504,darksalmon:0xe9967a,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:0xff1493,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:0xb22222,floralwhite:0xfffaf0,forestgreen:2263842,fuchsia:0xff00ff,gainsboro:0xdcdcdc,ghostwhite:0xf8f8ff,gold:0xffd700,goldenrod:0xdaa520,gray:8421504,green:32768,greenyellow:0xadff2f,grey:8421504,honeydew:0xf0fff0,hotpink:0xff69b4,indianred:0xcd5c5c,indigo:4915330,ivory:0xfffff0,khaki:0xf0e68c,lavender:0xe6e6fa,lavenderblush:0xfff0f5,lawngreen:8190976,lemonchiffon:0xfffacd,lightblue:0xadd8e6,lightcoral:0xf08080,lightcyan:0xe0ffff,lightgoldenrodyellow:0xfafad2,lightgray:0xd3d3d3,lightgreen:9498256,lightgrey:0xd3d3d3,lightpink:0xffb6c1,lightsalmon:0xffa07a,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:0xb0c4de,lightyellow:0xffffe0,lime:65280,limegreen:3329330,linen:0xfaf0e6,magenta:0xff00ff,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:0xba55d3,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:0xc71585,midnightblue:1644912,mintcream:0xf5fffa,mistyrose:0xffe4e1,moccasin:0xffe4b5,navajowhite:0xffdead,navy:128,oldlace:0xfdf5e6,olive:8421376,olivedrab:7048739,orange:0xffa500,orangered:0xff4500,orchid:0xda70d6,palegoldenrod:0xeee8aa,palegreen:0x98fb98,paleturquoise:0xafeeee,palevioletred:0xdb7093,papayawhip:0xffefd5,peachpuff:0xffdab9,peru:0xcd853f,pink:0xffc0cb,plum:0xdda0dd,powderblue:0xb0e0e6,purple:8388736,rebeccapurple:6697881,red:0xff0000,rosybrown:0xbc8f8f,royalblue:4286945,saddlebrown:9127187,salmon:0xfa8072,sandybrown:0xf4a460,seagreen:3050327,seashell:0xfff5ee,sienna:0xa0522d,silver:0xc0c0c0,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:0xfffafa,springgreen:65407,steelblue:4620980,tan:0xd2b48c,teal:32896,thistle:0xd8bfd8,tomato:0xff6347,turquoise:4251856,violet:0xee82ee,wheat:0xf5deb3,white:0xffffff,whitesmoke:0xf5f5f5,yellow:0xffff00,yellowgreen:0x9acd32};function i$(){return this.rgb().formatHex()}function iF(){return this.rgb().formatRgb()}function iq(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=iC.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?iW(e):3===r?new iH(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?iX(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?iX(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=iI.exec(t))?new iH(e[1],e[2],e[3],1):(e=iD.exec(t))?new iH(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=iB.exec(t))?iX(e[1],e[2],e[3],e[4]):(e=iR.exec(t))?iX(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=iz.exec(t))?iJ(e[1],e[2]/100,e[3]/100,1):(e=iL.exec(t))?iJ(e[1],e[2]/100,e[3]/100,e[4]):iU.hasOwnProperty(t)?iW(iU[t]):"transparent"===t?new iH(NaN,NaN,NaN,0):null}function iW(t){return new iH(t>>16&255,t>>8&255,255&t,1)}function iX(t,e,r,n){return n<=0&&(t=e=r=NaN),new iH(t,e,r,n)}function iG(t,e,r,n){var i;return 1==arguments.length?((i=t)instanceof iM||(i=iq(i)),i)?new iH((i=i.rgb()).r,i.g,i.b,i.opacity):new iH:new iH(t,e,r,null==n?1:n)}function iH(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function iV(){return`#${iQ(this.r)}${iQ(this.g)}${iQ(this.b)}`}function iY(){let t=iZ(this.opacity);return`${1===t?"rgb(":"rgba("}${iK(this.r)}, ${iK(this.g)}, ${iK(this.b)}${1===t?")":`, ${t})`}`}function iZ(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function iK(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function iQ(t){return((t=iK(t))<16?"0":"")+t.toString(16)}function iJ(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new i1(t,e,r,n)}function i0(t){if(t instanceof i1)return new i1(t.h,t.s,t.l,t.opacity);if(t instanceof iM||(t=iq(t)),!t)return new i1;if(t instanceof i1)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,i=Math.min(e,r,n),o=Math.max(e,r,n),a=NaN,c=o-i,u=(o+i)/2;return c?(a=e===o?(r-n)/c+(r<n)*6:r===o?(n-e)/c+2:(e-r)/c+4,c/=u<.5?o+i:2-o-i,a*=60):c=u>0&&u<1?0:a,new i1(a,c,u,t.opacity)}function i1(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function i2(t){return(t=(t||0)%360)<0?t+360:t}function i5(t){return Math.max(0,Math.min(1,t||0))}function i4(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function i3(t,e,r,n,i){var o=t*t,a=o*t;return((1-3*t+3*o-a)*e+(4-6*o+3*a)*r+(1+3*t+3*o-3*a)*n+a*i)/6}iE(iM,iq,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:i$,formatHex:i$,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return i0(this).formatHsl()},formatRgb:iF,toString:iF}),iE(iH,iG,i_(iM,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new iH(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new iH(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new iH(iK(this.r),iK(this.g),iK(this.b),iZ(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:iV,formatHex:iV,formatHex8:function(){return`#${iQ(this.r)}${iQ(this.g)}${iQ(this.b)}${iQ((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:iY,toString:iY})),iE(i1,function(t,e,r,n){return 1==arguments.length?i0(t):new i1(t,e,r,null==n?1:n)},i_(iM,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new i1(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new i1(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,i=2*r-n;return new iH(i4(t>=240?t-240:t+120,i,n),i4(t,i,n),i4(t<120?t+240:t-120,i,n),this.opacity)},clamp(){return new i1(i2(this.h),i5(this.s),i5(this.l),iZ(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=iZ(this.opacity);return`${1===t?"hsl(":"hsla("}${i2(this.h)}, ${100*i5(this.s)}%, ${100*i5(this.l)}%${1===t?")":`, ${t})`}`}}));let i6=t=>()=>t;function i8(t,e){var r,n,i=e-t;return i?(r=t,n=i,function(t){return r+t*n}):i6(isNaN(t)?e:t)}let i7=function t(e){var r,n=1==(r=+e)?i8:function(t,e){var n,i,o;return e-t?(n=t,i=e,n=Math.pow(n,o=r),i=Math.pow(i,o)-n,o=1/o,function(t){return Math.pow(n+t*i,o)}):i6(isNaN(t)?e:t)};function i(t,e){var r=n((t=iG(t)).r,(e=iG(e)).r),i=n(t.g,e.g),o=n(t.b,e.b),a=i8(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=i(e),t.b=o(e),t.opacity=a(e),t+""}}return i.gamma=t,i}(1);function i9(t){return function(e){var r,n,i=e.length,o=Array(i),a=Array(i),c=Array(i);for(r=0;r<i;++r)n=iG(e[r]),o[r]=n.r||0,a[r]=n.g||0,c[r]=n.b||0;return o=t(o),a=t(a),c=t(c),n.opacity=1,function(t){return n.r=o(t),n.g=a(t),n.b=c(t),n+""}}}i9(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),i=t[n],o=t[n+1],a=n>0?t[n-1]:2*i-o,c=n<e-1?t[n+2]:2*o-i;return i3((r-n/e)*e,a,i,o,c)}}),i9(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),i=t[(n+e-1)%e],o=t[n%e],a=t[(n+1)%e],c=t[(n+2)%e];return i3((r-n/e)*e,i,o,a,c)}});function ot(t,e){return t*=1,e*=1,function(r){return t*(1-r)+e*r}}var oe=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,or=RegExp(oe.source,"g");function on(t,e){var r,n,i=typeof e;return null==e||"boolean"===i?i6(e):("number"===i?ot:"string"===i?(n=iq(e))?(e=n,i7):function(t,e){var r,n,i,o,a,c=oe.lastIndex=or.lastIndex=0,u=-1,l=[],s=[];for(t+="",e+="";(i=oe.exec(t))&&(o=or.exec(e));)(a=o.index)>c&&(a=e.slice(c,a),l[u]?l[u]+=a:l[++u]=a),(i=i[0])===(o=o[0])?l[u]?l[u]+=o:l[++u]=o:(l[++u]=null,s.push({i:u,x:ot(i,o)})),c=or.lastIndex;return c<e.length&&(a=e.slice(c),l[u]?l[u]+=a:l[++u]=a),l.length<2?s[0]?(r=s[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=s.length,function(t){for(var r,n=0;n<e;++n)l[(r=s[n]).i]=r.x(t);return l.join("")})}:e instanceof iq?i7:e instanceof Date?function(t,e){var r=new Date;return t*=1,e*=1,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,i=t?Math.min(n,t.length):0,o=Array(i),a=Array(n);for(r=0;r<i;++r)o[r]=on(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<i;++r)a[r]=o[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},i={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=on(t[r],e[r]):i[r]=e[r];return function(t){for(r in n)i[r]=n[r](t);return i}}:ot:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,i=e.slice();return function(o){for(r=0;r<n;++r)i[r]=t[r]*(1-o)+e[r]*o;return i}})(t,e)}function oi(t,e){return t*=1,e*=1,function(r){return Math.round(t*(1-r)+e*r)}}function oo(t){return+t}var oa=[0,1];function oc(t){return t}function ou(t,e){var r;return(e-=t*=1)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function ol(t,e,r){var n=t[0],i=t[1],o=e[0],a=e[1];return i<n?(n=ou(i,n),o=r(a,o)):(n=ou(n,i),o=r(o,a)),function(t){return o(n(t))}}function os(t,e,r){var n=Math.min(t.length,e.length)-1,i=Array(n),o=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)i[a]=ou(t[a],t[a+1]),o[a]=r(e[a],e[a+1]);return function(e){var r=iA(t,e,1,n)-1;return o[r](i[r](e))}}function of(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function op(){var t,e,r,n,i,o,a=oa,c=oa,u=on,l=oc;function s(){var t,e,r,u=Math.min(a.length,c.length);return l!==oc&&(t=a[0],e=a[u-1],t>e&&(r=t,t=e,e=r),l=function(r){return Math.max(t,Math.min(e,r))}),n=u>2?os:ol,i=o=null,f}function f(e){return null==e||isNaN(e*=1)?r:(i||(i=n(a.map(t),c,u)))(t(l(e)))}return f.invert=function(r){return l(e((o||(o=n(c,a.map(t),ot)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,oo),s()):a.slice()},f.range=function(t){return arguments.length?(c=Array.from(t),s()):c.slice()},f.rangeRound=function(t){return c=Array.from(t),u=oi,s()},f.clamp=function(t){return arguments.length?(l=!!t||oc,s()):l!==oc},f.interpolate=function(t){return arguments.length?(u=t,s()):u},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,s()}}function oh(){return op()(oc,oc)}var od=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function oy(t){var e;if(!(e=od.exec(t)))throw Error("invalid format: "+t);return new ov({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function ov(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function om(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function ob(t){return(t=om(Math.abs(t)))?t[1]:NaN}function og(t,e){var r=om(t,e);if(!r)return t+"";var n=r[0],i=r[1];return i<0?"0."+Array(-i).join("0")+n:n.length>i+1?n.slice(0,i+1)+"."+n.slice(i+1):n+Array(i-n.length+2).join("0")}oy.prototype=ov.prototype,ov.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};let ox={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>og(100*t,e),r:og,s:function(t,e){var r=om(t,e);if(!r)return t+"";var n=r[0],i=r[1],o=i-(cY=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,a=n.length;return o===a?n:o>a?n+Array(o-a+1).join("0"):o>0?n.slice(0,o)+"."+n.slice(o):"0."+Array(1-o).join("0")+om(t,Math.max(0,e+o-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function ow(t){return t}var oO=Array.prototype.map,oj=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function oS(t,e,r,n){var i,o,a,c=ig(t,e,r);switch((n=oy(null==n?",f":n)).type){case"s":var u=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(a=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(ob(u)/3)))-ob(Math.abs(c))))||(n.precision=a),cQ(n,u);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(a=Math.max(0,ob(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(i=Math.abs(i=c)))-ob(i))+1)||(n.precision=a-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(a=Math.max(0,-ob(Math.abs(c))))||(n.precision=a-("%"===n.type)*2)}return cK(n)}function oP(t){var e=t.domain;return t.ticks=function(t){var r=e();return im(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return oS(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,i,o=e(),a=0,c=o.length-1,u=o[a],l=o[c],s=10;for(l<u&&(i=u,u=l,l=i,i=a,a=c,c=i);s-- >0;){if((i=ib(u,l,r))===n)return o[a]=u,o[c]=l,e(o);if(i>0)u=Math.floor(u/i)*i,l=Math.ceil(l/i)*i;else if(i<0)u=Math.ceil(u*i)/i,l=Math.floor(l*i)/i;else break;n=i}return t},t}function oA(){var t=oh();return t.copy=function(){return of(t,oA())},nT.apply(t,arguments),oP(t)}function oE(t,e){t=t.slice();var r,n=0,i=t.length-1,o=t[n],a=t[i];return a<o&&(r=n,n=i,i=r,r=o,o=a,a=r),t[n]=e.floor(o),t[i]=e.ceil(a),t}function o_(t){return Math.log(t)}function oM(t){return Math.exp(t)}function ok(t){return-Math.log(-t)}function oT(t){return-Math.exp(-t)}function oN(t){return isFinite(t)?+("1e"+t):t<0?0:t}function oC(t){return(e,r)=>-t(-e,r)}function oI(t){let e,r,n=t(o_,oM),i=n.domain,o=10;function a(){var a,c;return e=(a=o)===Math.E?Math.log:10===a&&Math.log10||2===a&&Math.log2||(a=Math.log(a),t=>Math.log(t)/a),r=10===(c=o)?oN:c===Math.E?Math.exp:t=>Math.pow(c,t),i()[0]<0?(e=oC(e),r=oC(r),t(ok,oT)):t(o_,oM),n}return n.base=function(t){return arguments.length?(o=+t,a()):o},n.domain=function(t){return arguments.length?(i(t),a()):i()},n.ticks=t=>{let n,a,c=i(),u=c[0],l=c[c.length-1],s=l<u;s&&([u,l]=[l,u]);let f=e(u),p=e(l),h=null==t?10:+t,d=[];if(!(o%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),u>0){for(;f<=p;++f)for(n=1;n<o;++n)if(!((a=f<0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}}else for(;f<=p;++f)for(n=o-1;n>=1;--n)if(!((a=f>0?n/r(-f):n*r(f))<u)){if(a>l)break;d.push(a)}2*d.length<h&&(d=im(u,l,h))}else d=im(f,p,Math.min(p-f,h)).map(r);return s?d.reverse():d},n.tickFormat=(t,i)=>{if(null==t&&(t=10),null==i&&(i=10===o?"s":","),"function"!=typeof i&&(o%1||null!=(i=oy(i)).precision||(i.trim=!0),i=cK(i)),t===1/0)return i;let a=Math.max(1,o*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*o<o-.5&&(n*=o),n<=a?i(t):""}},n.nice=()=>i(oE(i(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function oD(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function oB(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function oR(t){var e=1,r=t(oD(1),oB(e));return r.constant=function(r){return arguments.length?t(oD(e=+r),oB(e)):e},oP(r)}function oz(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function oL(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function oU(t){return t<0?-t*t:t*t}function o$(t){var e=t(oc,oc),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(oc,oc):.5===r?t(oL,oU):t(oz(r),oz(1/r)):r},oP(e)}function oF(){var t=o$(op());return t.copy=function(){return of(t,oF()).exponent(t.exponent())},nT.apply(t,arguments),t}function oq(){return oF.apply(null,arguments).exponent(.5)}function oW(t){return Math.sign(t)*t*t}function oX(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r<i||void 0===r&&i>=i)&&(r=i)}return r}function oG(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let i of t)null!=(i=e(i,++n,t))&&(r>i||void 0===r&&i>=i)&&(r=i)}return r}cK=(cZ=function(t){var e,r,n,i=void 0===t.grouping||void 0===t.thousands?ow:(e=oO.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var i=t.length,o=[],a=0,c=e[0],u=0;i>0&&c>0&&(u+c+1>n&&(c=Math.max(1,n-u)),o.push(t.substring(i-=c,i+c)),!((u+=c+1)>n));)c=e[a=(a+1)%e.length];return o.reverse().join(r)}),o=void 0===t.currency?"":t.currency[0]+"",a=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",u=void 0===t.numerals?ow:(n=oO.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return n[+t]})}),l=void 0===t.percent?"%":t.percent+"",s=void 0===t.minus?"−":t.minus+"",f=void 0===t.nan?"NaN":t.nan+"";function p(t){var e=(t=oy(t)).fill,r=t.align,n=t.sign,p=t.symbol,h=t.zero,d=t.width,y=t.comma,v=t.precision,m=t.trim,b=t.type;"n"===b?(y=!0,b="g"):ox[b]||(void 0===v&&(v=12),m=!0,b="g"),(h||"0"===e&&"="===r)&&(h=!0,e="0",r="=");var g="$"===p?o:"#"===p&&/[boxX]/.test(b)?"0"+b.toLowerCase():"",x="$"===p?a:/[%p]/.test(b)?l:"",w=ox[b],O=/[defgprs%]/.test(b);function j(t){var o,a,l,p=g,j=x;if("c"===b)j=w(t)+j,t="";else{var S=(t*=1)<0||1/t<0;if(t=isNaN(t)?f:w(Math.abs(t),v),m&&(t=function(t){t:for(var e,r=t.length,n=1,i=-1;n<r;++n)switch(t[n]){case".":i=e=n;break;case"0":0===i&&(i=n),e=n;break;default:if(!+t[n])break t;i>0&&(i=0)}return i>0?t.slice(0,i)+t.slice(e+1):t}(t)),S&&0==+t&&"+"!==n&&(S=!1),p=(S?"("===n?n:s:"-"===n||"("===n?"":n)+p,j=("s"===b?oj[8+cY/3]:"")+j+(S&&"("===n?")":""),O){for(o=-1,a=t.length;++o<a;)if(48>(l=t.charCodeAt(o))||l>57){j=(46===l?c+t.slice(o+1):t.slice(o))+j,t=t.slice(0,o);break}}}y&&!h&&(t=i(t,1/0));var P=p.length+t.length+j.length,A=P<d?Array(d-P+1).join(e):"";switch(y&&h&&(t=i(A+t,A.length?d-j.length:1/0),A=""),r){case"<":t=p+t+j+A;break;case"=":t=p+A+t+j;break;case"^":t=A.slice(0,P=A.length>>1)+p+t+j+A.slice(P);break;default:t=A+p+t+j}return u(t)}return v=void 0===v?6:/[gprs]/.test(b)?Math.max(1,Math.min(21,v)):Math.max(0,Math.min(20,v)),j.toString=function(){return t+""},j}return{format:p,formatPrefix:function(t,e){var r=p(((t=oy(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(ob(e)/3))),i=Math.pow(10,-n),o=oj[8+n/3];return function(t){return r(i*t)+o}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,cQ=cZ.formatPrefix;function oH(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:+(t>e))}function oV(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let oY=new Date,oZ=new Date;function oK(t,e,r,n){function i(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return i.floor=e=>(t(e=new Date(+e)),e),i.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),i.round=t=>{let e=i(t),r=i.ceil(t);return t-e<r-t?e:r},i.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),i.range=(r,n,o)=>{let a,c=[];if(r=i.ceil(r),o=null==o?1:Math.floor(o),!(r<n)||!(o>0))return c;do c.push(a=new Date(+r)),e(r,o),t(r);while(a<r&&r<n);return c},i.filter=r=>oK(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t)if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}),r&&(i.count=(e,n)=>(oY.setTime(+e),oZ.setTime(+n),t(oY),t(oZ),Math.floor(r(oY,oZ))),i.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?i.filter(n?e=>n(e)%t==0:e=>i.count(0,e)%t==0):i:null),i}let oQ=oK(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);oQ.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?oK(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):oQ:null,oQ.range;let oJ=oK(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());oJ.range;let o0=oK(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());o0.range;let o1=oK(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());o1.range;let o2=oK(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());o2.range;let o5=oK(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());o5.range;let o4=oK(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);o4.range;let o3=oK(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);o3.range;let o6=oK(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function o8(t){return oK(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}o6.range;let o7=o8(0),o9=o8(1),at=o8(2),ae=o8(3),ar=o8(4),an=o8(5),ai=o8(6);function ao(t){return oK(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}o7.range,o9.range,at.range,ae.range,ar.range,an.range,ai.range;let aa=ao(0),ac=ao(1),au=ao(2),al=ao(3),as=ao(4),af=ao(5),ap=ao(6);aa.range,ac.range,au.range,al.range,as.range,af.range,ap.range;let ah=oK(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());ah.range;let ad=oK(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());ad.range;let ay=oK(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());ay.every=t=>isFinite(t=Math.floor(t))&&t>0?oK(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,ay.range;let av=oK(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function am(t,e,r,n,i,o){let a=[[oJ,1,1e3],[oJ,5,5e3],[oJ,15,15e3],[oJ,30,3e4],[o,1,6e4],[o,5,3e5],[o,15,9e5],[o,30,18e5],[i,1,36e5],[i,3,108e5],[i,6,216e5],[i,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function c(e,r,n){let i=Math.abs(r-e)/n,o=iO(([,,t])=>t).right(a,i);if(o===a.length)return t.every(ig(e/31536e6,r/31536e6,n));if(0===o)return oQ.every(Math.max(ig(e,r,n),1));let[c,u]=a[i/a[o-1][2]<a[o][2]/i?o-1:o];return c.every(u)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let i=r&&"function"==typeof r.range?r:c(t,e,r),o=i?i.range(t,+e+1):[];return n?o.reverse():o},c]}av.every=t=>isFinite(t=Math.floor(t))&&t>0?oK(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,av.range;let[ab,ag]=am(av,ad,aa,o6,o5,o1),[ax,aw]=am(ay,ah,o7,o4,o2,o0);function aO(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function aj(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function aS(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var aP={"-":"",_:" ",0:"0"},aA=/^\s*\d+/,aE=/^%/,a_=/[\\^$*+?|[\]().{}]/g;function aM(t,e,r){var n=t<0?"-":"",i=(n?-t:t)+"",o=i.length;return n+(o<r?Array(r-o+1).join(e)+i:i)}function ak(t){return t.replace(a_,"\\$&")}function aT(t){return RegExp("^(?:"+t.map(ak).join("|")+")","i")}function aN(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function aC(t,e,r){var n=aA.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function aI(t,e,r){var n=aA.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function aD(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function aB(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function aR(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function az(t,e,r){var n=aA.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function aL(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function aU(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function a$(t,e,r){var n=aA.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function aF(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function aq(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function aW(t,e,r){var n=aA.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function aX(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function aG(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function aH(t,e,r){var n=aA.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function aV(t,e,r){var n=aA.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function aY(t,e,r){var n=aA.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function aZ(t,e,r){var n=aE.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function aK(t,e,r){var n=aA.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function aQ(t,e,r){var n=aA.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function aJ(t,e){return aM(t.getDate(),e,2)}function a0(t,e){return aM(t.getHours(),e,2)}function a1(t,e){return aM(t.getHours()%12||12,e,2)}function a2(t,e){return aM(1+o4.count(ay(t),t),e,3)}function a5(t,e){return aM(t.getMilliseconds(),e,3)}function a4(t,e){return a5(t,e)+"000"}function a3(t,e){return aM(t.getMonth()+1,e,2)}function a6(t,e){return aM(t.getMinutes(),e,2)}function a8(t,e){return aM(t.getSeconds(),e,2)}function a7(t){var e=t.getDay();return 0===e?7:e}function a9(t,e){return aM(o7.count(ay(t)-1,t),e,2)}function ct(t){var e=t.getDay();return e>=4||0===e?ar(t):ar.ceil(t)}function ce(t,e){return t=ct(t),aM(ar.count(ay(t),t)+(4===ay(t).getDay()),e,2)}function cr(t){return t.getDay()}function cn(t,e){return aM(o9.count(ay(t)-1,t),e,2)}function ci(t,e){return aM(t.getFullYear()%100,e,2)}function co(t,e){return aM((t=ct(t)).getFullYear()%100,e,2)}function ca(t,e){return aM(t.getFullYear()%1e4,e,4)}function cc(t,e){var r=t.getDay();return aM((t=r>=4||0===r?ar(t):ar.ceil(t)).getFullYear()%1e4,e,4)}function cu(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+aM(e/60|0,"0",2)+aM(e%60,"0",2)}function cl(t,e){return aM(t.getUTCDate(),e,2)}function cs(t,e){return aM(t.getUTCHours(),e,2)}function cf(t,e){return aM(t.getUTCHours()%12||12,e,2)}function cp(t,e){return aM(1+o3.count(av(t),t),e,3)}function ch(t,e){return aM(t.getUTCMilliseconds(),e,3)}function cd(t,e){return ch(t,e)+"000"}function cy(t,e){return aM(t.getUTCMonth()+1,e,2)}function cv(t,e){return aM(t.getUTCMinutes(),e,2)}function cm(t,e){return aM(t.getUTCSeconds(),e,2)}function cb(t){var e=t.getUTCDay();return 0===e?7:e}function cg(t,e){return aM(aa.count(av(t)-1,t),e,2)}function cx(t){var e=t.getUTCDay();return e>=4||0===e?as(t):as.ceil(t)}function cw(t,e){return t=cx(t),aM(as.count(av(t),t)+(4===av(t).getUTCDay()),e,2)}function cO(t){return t.getUTCDay()}function cj(t,e){return aM(ac.count(av(t)-1,t),e,2)}function cS(t,e){return aM(t.getUTCFullYear()%100,e,2)}function cP(t,e){return aM((t=cx(t)).getUTCFullYear()%100,e,2)}function cA(t,e){return aM(t.getUTCFullYear()%1e4,e,4)}function cE(t,e){var r=t.getUTCDay();return aM((t=r>=4||0===r?as(t):as.ceil(t)).getUTCFullYear()%1e4,e,4)}function c_(){return"+0000"}function cM(){return"%"}function ck(t){return+t}function cT(t){return Math.floor(t/1e3)}function cN(t){return new Date(t)}function cC(t){return t instanceof Date?+t:+new Date(+t)}function cI(t,e,r,n,i,o,a,c,u,l){var s=oh(),f=s.invert,p=s.domain,h=l(".%L"),d=l(":%S"),y=l("%I:%M"),v=l("%I %p"),m=l("%a %d"),b=l("%b %d"),g=l("%B"),x=l("%Y");function w(t){return(u(t)<t?h:c(t)<t?d:a(t)<t?y:o(t)<t?v:n(t)<t?i(t)<t?m:b:r(t)<t?g:x)(t)}return s.invert=function(t){return new Date(f(t))},s.domain=function(t){return arguments.length?p(Array.from(t,cC)):p().map(cN)},s.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},s.tickFormat=function(t,e){return null==e?w:l(e)},s.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(oE(r,t)):s},s.copy=function(){return of(s,cI(t,e,r,n,i,o,a,c,u,l))},s}function cD(){return nT.apply(cI(ax,aw,ay,ah,o7,o4,o2,o0,oJ,c0).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function cB(){return nT.apply(cI(ab,ag,av,ad,aa,o3,o5,o1,oJ,c1).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function cR(){var t,e,r,n,i,o=0,a=1,c=oc,u=!1;function l(e){return null==e||isNaN(e*=1)?i:c(0===r?.5:(e=(n(e)-t)*r,u?Math.max(0,Math.min(1,e)):e))}function s(t){return function(e){var r,n;return arguments.length?([r,n]=e,c=t(r,n),l):[c(0),c(1)]}}return l.domain=function(i){return arguments.length?([o,a]=i,t=n(o*=1),e=n(a*=1),r=t===e?0:1/(e-t),l):[o,a]},l.clamp=function(t){return arguments.length?(u=!!t,l):u},l.interpolator=function(t){return arguments.length?(c=t,l):c},l.range=s(on),l.rangeRound=s(oi),l.unknown=function(t){return arguments.length?(i=t,l):i},function(i){return n=i,t=i(o),e=i(a),r=t===e?0:1/(e-t),l}}function cz(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function cL(){var t=o$(cR());return t.copy=function(){return cz(t,cL()).exponent(t.exponent())},nN.apply(t,arguments)}function cU(){return cL.apply(null,arguments).exponent(.5)}function c$(){var t,e,r,n,i,o,a,c=0,u=.5,l=1,s=1,f=oc,p=!1;function h(t){return isNaN(t*=1)?a:(t=.5+((t=+o(t))-e)*(s*t<s*e?n:i),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,i;return arguments.length?([r,n,i]=e,f=function(t,e){void 0===e&&(e=t,t=on);for(var r=0,n=e.length-1,i=e[0],o=Array(n<0?0:n);r<n;)o[r]=t(i,i=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return o[e](t-e)}}(t,[r,n,i]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([c,u,l]=a,t=o(c*=1),e=o(u*=1),r=o(l*=1),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h):[c,u,l]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(on),h.rangeRound=d(oi),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return o=a,t=a(c),e=a(u),r=a(l),n=t===e?0:.5/(e-t),i=e===r?0:.5/(r-e),s=e<t?-1:1,h}}function cF(){var t=o$(c$());return t.copy=function(){return cz(t,cF()).exponent(t.exponent())},nN.apply(t,arguments)}function cq(){return cF.apply(null,arguments).exponent(.5)}function cW(t,e){if((i=t.length)>1)for(var r,n,i,o=1,a=t[e[0]],c=a.length;o<i;++o)for(n=a,a=t[e[o]],r=0;r<c;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}function cX(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}function cG(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function cH(t,e){return t[e]}function cV(t){let e=[];return e.key=t,e}c0=(cJ=function(t){var e=t.dateTime,r=t.date,n=t.time,i=t.periods,o=t.days,a=t.shortDays,c=t.months,u=t.shortMonths,l=aT(i),s=aN(i),f=aT(o),p=aN(o),h=aT(a),d=aN(a),y=aT(c),v=aN(c),m=aT(u),b=aN(u),g={a:function(t){return a[t.getDay()]},A:function(t){return o[t.getDay()]},b:function(t){return u[t.getMonth()]},B:function(t){return c[t.getMonth()]},c:null,d:aJ,e:aJ,f:a4,g:co,G:cc,H:a0,I:a1,j:a2,L:a5,m:a3,M:a6,p:function(t){return i[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:ck,s:cT,S:a8,u:a7,U:a9,V:ce,w:cr,W:cn,x:null,X:null,y:ci,Y:ca,Z:cu,"%":cM},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return o[t.getUTCDay()]},b:function(t){return u[t.getUTCMonth()]},B:function(t){return c[t.getUTCMonth()]},c:null,d:cl,e:cl,f:cd,g:cP,G:cE,H:cs,I:cf,j:cp,L:ch,m:cy,M:cv,p:function(t){return i[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:ck,s:cT,S:cm,u:cb,U:cg,V:cw,w:cO,W:cj,x:null,X:null,y:cS,Y:cA,Z:c_,"%":cM},w={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:aq,e:aq,f:aY,g:aL,G:az,H:aX,I:aX,j:aW,L:aV,m:aF,M:aG,p:function(t,e,r){var n=l.exec(e.slice(r));return n?(t.p=s.get(n[0].toLowerCase()),r+n[0].length):-1},q:a$,Q:aK,s:aQ,S:aH,u:aI,U:aD,V:aB,w:aC,W:aR,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:aL,Y:az,Z:aU,"%":aZ};function O(t,e){return function(r){var n,i,o,a=[],c=-1,u=0,l=t.length;for(r instanceof Date||(r=new Date(+r));++c<l;)37===t.charCodeAt(c)&&(a.push(t.slice(u,c)),null!=(i=aP[n=t.charAt(++c)])?n=t.charAt(++c):i="e"===n?" ":"0",(o=e[n])&&(n=o(r,i)),a.push(n),u=c+1);return a.push(t.slice(u,c)),a.join("")}}function j(t,e){return function(r){var n,i,o=aS(1900,void 0,1);if(S(o,t,r+="",0)!=r.length)return null;if("Q"in o)return new Date(o.Q);if("s"in o)return new Date(1e3*o.s+("L"in o?o.L:0));if(!e||"Z"in o||(o.Z=0),"p"in o&&(o.H=o.H%12+12*o.p),void 0===o.m&&(o.m="q"in o?o.q:0),"V"in o){if(o.V<1||o.V>53)return null;"w"in o||(o.w=1),"Z"in o?(n=(i=(n=aj(aS(o.y,0,1))).getUTCDay())>4||0===i?ac.ceil(n):ac(n),n=o3.offset(n,(o.V-1)*7),o.y=n.getUTCFullYear(),o.m=n.getUTCMonth(),o.d=n.getUTCDate()+(o.w+6)%7):(n=(i=(n=aO(aS(o.y,0,1))).getDay())>4||0===i?o9.ceil(n):o9(n),n=o4.offset(n,(o.V-1)*7),o.y=n.getFullYear(),o.m=n.getMonth(),o.d=n.getDate()+(o.w+6)%7)}else("W"in o||"U"in o)&&("w"in o||(o.w="u"in o?o.u%7:+("W"in o)),i="Z"in o?aj(aS(o.y,0,1)).getUTCDay():aO(aS(o.y,0,1)).getDay(),o.m=0,o.d="W"in o?(o.w+6)%7+7*o.W-(i+5)%7:o.w+7*o.U-(i+6)%7);return"Z"in o?(o.H+=o.Z/100|0,o.M+=o.Z%100,aj(o)):aO(o)}}function S(t,e,r,n){for(var i,o,a=0,c=e.length,u=r.length;a<c;){if(n>=u)return -1;if(37===(i=e.charCodeAt(a++))){if(!(o=w[(i=e.charAt(a++))in aP?e.charAt(a++):i])||(n=o(t,r,n))<0)return -1}else if(i!=r.charCodeAt(n++))return -1}return n}return g.x=O(r,g),g.X=O(n,g),g.c=O(e,g),x.x=O(r,x),x.X=O(n,x),x.c=O(e,x),{format:function(t){var e=O(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=O(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,cJ.parse,c1=cJ.utcFormat,cJ.utcParse,Array.prototype.slice;var cY,cZ,cK,cQ,cJ,c0,c1,c2,c5,c4=r(90453),c3=r.n(c4),c6=r(15883),c8=r.n(c6),c7=r(21592),c9=r.n(c7),ut=r(71967),ue=r.n(ut),ur=!0,un="[DecimalError] ",ui=un+"Invalid argument: ",uo=un+"Exponent out of range: ",ua=Math.floor,uc=Math.pow,uu=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ul=ua(1286742750677284.5),us={};function uf(t,e){var r,n,i,o,a,c,u,l,s=t.constructor,f=s.precision;if(!t.s||!e.s)return e.s||(e=new s(t)),ur?uw(e,f):e;if(u=t.d,l=e.d,a=t.e,i=e.e,u=u.slice(),o=a-i){for(o<0?(n=u,o=-o,c=l.length):(n=l,i=a,c=u.length),o>(c=(a=Math.ceil(f/7))>c?a+1:c+1)&&(o=c,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for((c=u.length)-(o=l.length)<0&&(o=c,n=l,l=u,u=n),r=0;o;)r=(u[--o]=u[o]+l[o]+r)/1e7|0,u[o]%=1e7;for(r&&(u.unshift(r),++i),c=u.length;0==u[--c];)u.pop();return e.d=u,e.e=i,ur?uw(e,f):e}function up(t,e,r){if(t!==~~t||t<e||t>r)throw Error(ui+t)}function uh(t){var e,r,n,i=t.length-1,o="",a=t[0];if(i>0){for(o+=a,e=1;e<i;e++)(r=7-(n=t[e]+"").length)&&(o+=ub(r)),o+=n;(r=7-(n=(a=t[e])+"").length)&&(o+=ub(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return o+a}us.absoluteValue=us.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},us.comparedTo=us.cmp=function(t){var e,r,n,i;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(i=t.d.length)?n:i;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===i?0:n>i^this.s<0?1:-1},us.decimalPlaces=us.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},us.dividedBy=us.div=function(t){return ud(this,new this.constructor(t))},us.dividedToIntegerBy=us.idiv=function(t){var e=this.constructor;return uw(ud(this,new e(t),0,1),e.precision)},us.equals=us.eq=function(t){return!this.cmp(t)},us.exponent=function(){return uv(this)},us.greaterThan=us.gt=function(t){return this.cmp(t)>0},us.greaterThanOrEqualTo=us.gte=function(t){return this.cmp(t)>=0},us.isInteger=us.isint=function(){return this.e>this.d.length-2},us.isNegative=us.isneg=function(){return this.s<0},us.isPositive=us.ispos=function(){return this.s>0},us.isZero=function(){return 0===this.s},us.lessThan=us.lt=function(t){return 0>this.cmp(t)},us.lessThanOrEqualTo=us.lte=function(t){return 1>this.cmp(t)},us.logarithm=us.log=function(t){var e,r=this.constructor,n=r.precision,i=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(c5))throw Error(un+"NaN");if(this.s<1)throw Error(un+(this.s?"NaN":"-Infinity"));return this.eq(c5)?new r(0):(ur=!1,e=ud(ug(this,i),ug(t,i),i),ur=!0,uw(e,n))},us.minus=us.sub=function(t){return t=new this.constructor(t),this.s==t.s?uO(this,t):uf(this,(t.s=-t.s,t))},us.modulo=us.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(un+"NaN");return this.s?(ur=!1,e=ud(this,t,0,1).times(t),ur=!0,this.minus(e)):uw(new r(this),n)},us.naturalExponential=us.exp=function(){return uy(this)},us.naturalLogarithm=us.ln=function(){return ug(this)},us.negated=us.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},us.plus=us.add=function(t){return t=new this.constructor(t),this.s==t.s?uf(this,t):uO(this,(t.s=-t.s,t))},us.precision=us.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(ui+t);if(e=uv(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},us.squareRoot=us.sqrt=function(){var t,e,r,n,i,o,a,c=this.constructor;if(this.s<1){if(!this.s)return new c(0);throw Error(un+"NaN")}for(t=uv(this),ur=!1,0==(i=Math.sqrt(+this))||i==1/0?(((e=uh(this.d)).length+t)%2==0&&(e+="0"),i=Math.sqrt(e),t=ua((t+1)/2)-(t<0||t%2),n=new c(e=i==1/0?"5e"+t:(e=i.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new c(i.toString()),i=a=(r=c.precision)+3;;)if(n=(o=n).plus(ud(this,o,a+2)).times(.5),uh(o.d).slice(0,a)===(e=uh(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),i==a&&"4999"==e){if(uw(o,r+1,0),o.times(o).eq(this)){n=o;break}}else if("9999"!=e)break;a+=4}return ur=!0,uw(n,r)},us.times=us.mul=function(t){var e,r,n,i,o,a,c,u,l,s=this.constructor,f=this.d,p=(t=new s(t)).d;if(!this.s||!t.s)return new s(0);for(t.s*=this.s,r=this.e+t.e,(u=f.length)<(l=p.length)&&(o=f,f=p,p=o,a=u,u=l,l=a),o=[],n=a=u+l;n--;)o.push(0);for(n=l;--n>=0;){for(e=0,i=u+n;i>n;)c=o[i]+p[n]*f[i-n-1]+e,o[i--]=c%1e7|0,e=c/1e7|0;o[i]=(o[i]+e)%1e7|0}for(;!o[--a];)o.pop();return e?++r:o.shift(),t.d=o,t.e=r,ur?uw(t,s.precision):t},us.toDecimalPlaces=us.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(up(t,0,1e9),void 0===e?e=n.rounding:up(e,0,8),uw(r,t+uv(r)+1,e))},us.toExponential=function(t,e){var r,n=this,i=n.constructor;return void 0===t?r=uj(n,!0):(up(t,0,1e9),void 0===e?e=i.rounding:up(e,0,8),r=uj(n=uw(new i(n),t+1,e),!0,t+1)),r},us.toFixed=function(t,e){var r,n,i=this.constructor;return void 0===t?uj(this):(up(t,0,1e9),void 0===e?e=i.rounding:up(e,0,8),r=uj((n=uw(new i(this),t+uv(this)+1,e)).abs(),!1,t+uv(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},us.toInteger=us.toint=function(){var t=this.constructor;return uw(new t(this),uv(this)+1,t.rounding)},us.toNumber=function(){return+this},us.toPower=us.pow=function(t){var e,r,n,i,o,a,c=this,u=c.constructor,l=+(t=new u(t));if(!t.s)return new u(c5);if(!(c=new u(c)).s){if(t.s<1)throw Error(un+"Infinity");return c}if(c.eq(c5))return c;if(n=u.precision,t.eq(c5))return uw(c,n);if(a=(e=t.e)>=(r=t.d.length-1),o=c.s,a){if((r=l<0?-l:l)<=0x1fffffffffffff){for(i=new u(c5),e=Math.ceil(n/7+4),ur=!1;r%2&&uS((i=i.times(c)).d,e),0!==(r=ua(r/2));)uS((c=c.times(c)).d,e);return ur=!0,t.s<0?new u(c5).div(i):uw(i,n)}}else if(o<0)throw Error(un+"NaN");return o=o<0&&1&t.d[Math.max(e,r)]?-1:1,c.s=1,ur=!1,i=t.times(ug(c,n+12)),ur=!0,(i=uy(i)).s=o,i},us.toPrecision=function(t,e){var r,n,i=this,o=i.constructor;return void 0===t?(r=uv(i),n=uj(i,r<=o.toExpNeg||r>=o.toExpPos)):(up(t,1,1e9),void 0===e?e=o.rounding:up(e,0,8),r=uv(i=uw(new o(i),t,e)),n=uj(i,t<=r||r<=o.toExpNeg,t)),n},us.toSignificantDigits=us.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(up(t,1,1e9),void 0===e?e=r.rounding:up(e,0,8)),uw(new r(this),t,e)},us.toString=us.valueOf=us.val=us.toJSON=us[Symbol.for("nodejs.util.inspect.custom")]=function(){var t=uv(this),e=this.constructor;return uj(this,t<=e.toExpNeg||t>=e.toExpPos)};var ud=function(){function t(t,e){var r,n=0,i=t.length;for(t=t.slice();i--;)r=t[i]*e+n,t[i]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=+(t[r]<e[r]),t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,i,o,a){var c,u,l,s,f,p,h,d,y,v,m,b,g,x,w,O,j,S,P=n.constructor,A=n.s==i.s?1:-1,E=n.d,_=i.d;if(!n.s)return new P(n);if(!i.s)throw Error(un+"Division by zero");for(l=0,u=n.e-i.e,j=_.length,w=E.length,d=(h=new P(A)).d=[];_[l]==(E[l]||0);)++l;if(_[l]>(E[l]||0)&&--u,(b=null==o?o=P.precision:a?o+(uv(n)-uv(i))+1:o)<0)return new P(0);if(b=b/7+2|0,l=0,1==j)for(s=0,_=_[0],b++;(l<w||s)&&b--;l++)g=1e7*s+(E[l]||0),d[l]=g/_|0,s=g%_|0;else{for((s=1e7/(_[0]+1)|0)>1&&(_=t(_,s),E=t(E,s),j=_.length,w=E.length),x=j,v=(y=E.slice(0,j)).length;v<j;)y[v++]=0;(S=_.slice()).unshift(0),O=_[0],_[1]>=1e7/2&&++O;do s=0,(c=e(_,y,j,v))<0?(m=y[0],j!=v&&(m=1e7*m+(y[1]||0)),(s=m/O|0)>1?(s>=1e7&&(s=1e7-1),p=(f=t(_,s)).length,v=y.length,1==(c=e(f,y,p,v))&&(s--,r(f,j<p?S:_,p))):(0==s&&(c=s=1),f=_.slice()),(p=f.length)<v&&f.unshift(0),r(y,f,v),-1==c&&(v=y.length,(c=e(_,y,j,v))<1&&(s++,r(y,j<v?S:_,v))),v=y.length):0===c&&(s++,y=[0]),d[l++]=s,c&&y[0]?y[v++]=E[x]||0:(y=[E[x]],v=1);while((x++<w||void 0!==y[0])&&b--)}return d[0]||d.shift(),h.e=u,uw(h,a?o+uv(h)+1:o)}}();function uy(t,e){var r,n,i,o,a,c=0,u=0,l=t.constructor,s=l.precision;if(uv(t)>16)throw Error(uo+uv(t));if(!t.s)return new l(c5);for(null==e?(ur=!1,a=s):a=e,o=new l(.03125);t.abs().gte(.1);)t=t.times(o),u+=5;for(a+=Math.log(uc(2,u))/Math.LN10*2+5|0,r=n=i=new l(c5),l.precision=a;;){if(n=uw(n.times(t),a),r=r.times(++c),uh((o=i.plus(ud(n,r,a))).d).slice(0,a)===uh(i.d).slice(0,a)){for(;u--;)i=uw(i.times(i),a);return l.precision=s,null==e?(ur=!0,uw(i,s)):i}i=o}}function uv(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function um(t,e,r){if(e>t.LN10.sd())throw ur=!0,r&&(t.precision=r),Error(un+"LN10 precision limit exceeded");return uw(new t(t.LN10),e)}function ub(t){for(var e="";t--;)e+="0";return e}function ug(t,e){var r,n,i,o,a,c,u,l,s,f=1,p=t,h=p.d,d=p.constructor,y=d.precision;if(p.s<1)throw Error(un+(p.s?"NaN":"-Infinity"));if(p.eq(c5))return new d(0);if(null==e?(ur=!1,l=y):l=e,p.eq(10))return null==e&&(ur=!0),um(d,l);if(d.precision=l+=10,n=(r=uh(h)).charAt(0),!(15e14>Math.abs(o=uv(p))))return u=um(d,l+2,y).times(o+""),p=ug(new d(n+"."+r.slice(1)),l-10).plus(u),d.precision=y,null==e?(ur=!0,uw(p,y)):p;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=uh((p=p.times(t)).d)).charAt(0),f++;for(o=uv(p),n>1?(p=new d("0."+r),o++):p=new d(n+"."+r.slice(1)),c=a=p=ud(p.minus(c5),p.plus(c5),l),s=uw(p.times(p),l),i=3;;){if(a=uw(a.times(s),l),uh((u=c.plus(ud(a,new d(i),l))).d).slice(0,l)===uh(c.d).slice(0,l))return c=c.times(2),0!==o&&(c=c.plus(um(d,l+2,y).times(o+""))),c=ud(c,new d(f),l),d.precision=y,null==e?(ur=!0,uw(c,y)):c;c=u,i+=2}}function ux(t,e){var r,n,i;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(i=e.length;48===e.charCodeAt(i-1);)--i;if(e=e.slice(n,i)){if(i-=n,t.e=ua((r=r-n-1)/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<i){for(n&&t.d.push(+e.slice(0,n)),i-=7;n<i;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=i;for(;n--;)e+="0";if(t.d.push(+e),ur&&(t.e>ul||t.e<-ul))throw Error(uo+r)}else t.s=0,t.e=0,t.d=[0];return t}function uw(t,e,r){var n,i,o,a,c,u,l,s,f=t.d;for(a=1,o=f[0];o>=10;o/=10)a++;if((n=e-a)<0)n+=7,i=e,l=f[s=0];else{if((s=Math.ceil((n+1)/7))>=(o=f.length))return t;for(a=1,l=o=f[s];o>=10;o/=10)a++;n%=7,i=n-7+a}if(void 0!==r&&(c=l/(o=uc(10,a-i-1))%10|0,u=e<0||void 0!==f[s+1]||l%o,u=r<4?(c||u)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||u||6==r&&(n>0?i>0?l/uc(10,a-i):0:f[s-1])%10&1||r==(t.s<0?8:7))),e<1||!f[0])return u?(o=uv(t),f.length=1,e=e-o-1,f[0]=uc(10,(7-e%7)%7),t.e=ua(-e/7)||0):(f.length=1,f[0]=t.e=t.s=0),t;if(0==n?(f.length=s,o=1,s--):(f.length=s+1,o=uc(10,7-n),f[s]=i>0?(l/uc(10,a-i)%uc(10,i)|0)*o:0),u)for(;;)if(0==s){1e7==(f[0]+=o)&&(f[0]=1,++t.e);break}else{if(f[s]+=o,1e7!=f[s])break;f[s--]=0,o=1}for(n=f.length;0===f[--n];)f.pop();if(ur&&(t.e>ul||t.e<-ul))throw Error(uo+uv(t));return t}function uO(t,e){var r,n,i,o,a,c,u,l,s,f,p=t.constructor,h=p.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new p(t),ur?uw(e,h):e;if(u=t.d,f=e.d,n=e.e,l=t.e,u=u.slice(),a=l-n){for((s=a<0)?(r=u,a=-a,c=f.length):(r=f,n=l,c=u.length),a>(i=Math.max(Math.ceil(h/7),c)+2)&&(a=i,r.length=1),r.reverse(),i=a;i--;)r.push(0);r.reverse()}else{for((s=(i=u.length)<(c=f.length))&&(c=i),i=0;i<c;i++)if(u[i]!=f[i]){s=u[i]<f[i];break}a=0}for(s&&(r=u,u=f,f=r,e.s=-e.s),c=u.length,i=f.length-c;i>0;--i)u[c++]=0;for(i=f.length;i>a;){if(u[--i]<f[i]){for(o=i;o&&0===u[--o];)u[o]=1e7-1;--u[o],u[i]+=1e7}u[i]-=f[i]}for(;0===u[--c];)u.pop();for(;0===u[0];u.shift())--n;return u[0]?(e.d=u,e.e=n,ur?uw(e,h):e):new p(0)}function uj(t,e,r){var n,i=uv(t),o=uh(t.d),a=o.length;return e?(r&&(n=r-a)>0?o=o.charAt(0)+"."+o.slice(1)+ub(n):a>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+ub(-i-1)+o,r&&(n=r-a)>0&&(o+=ub(n))):i>=a?(o+=ub(i+1-a),r&&(n=r-i-1)>0&&(o=o+"."+ub(n))):((n=i+1)<a&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-a)>0&&(i+1===a&&(o+="."),o+=ub(n))),t.s<0?"-"+o:o}function uS(t,e){if(t.length>e)return t.length=e,!0}function uP(t){if(!t||"object"!=typeof t)throw Error(un+"Object expected");var e,r,n,i=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<i.length;e+=3)if(void 0!==(n=t[r=i[e]]))if(ua(n)===n&&n>=i[e+1]&&n<=i[e+2])this[r]=n;else throw Error(ui+r+": "+n);if(void 0!==(n=t[r="LN10"]))if(n==Math.LN10)this[r]=new this(n);else throw Error(ui+r+": "+n);return this}var c2=function t(e){var r,n,i;function o(t){if(!(this instanceof o))return new o(t);if(this.constructor=o,t instanceof o){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(ui+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return ux(this,t.toString())}if("string"!=typeof t)throw Error(ui+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,uu.test(t))ux(this,t);else throw Error(ui+t)}if(o.prototype=us,o.ROUND_UP=0,o.ROUND_DOWN=1,o.ROUND_CEIL=2,o.ROUND_FLOOR=3,o.ROUND_HALF_UP=4,o.ROUND_HALF_DOWN=5,o.ROUND_HALF_EVEN=6,o.ROUND_HALF_CEIL=7,o.ROUND_HALF_FLOOR=8,o.clone=t,o.config=o.set=uP,void 0===e&&(e={}),e)for(r=0,i=["precision","rounding","toExpNeg","toExpPos","LN10"];r<i.length;)e.hasOwnProperty(n=i[r++])||(e[n]=this[n]);return o.config(e),o}({precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"});c5=new c2(1);let uA=c2;function uE(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var u_=function(t){return t},uM={},uk=function(t){return t===uM},uT=function(t){return function e(){return 0==arguments.length||1==arguments.length&&uk(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},uN=function(t){return function t(e,r){return 1===e?r:uT(function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];var a=i.filter(function(t){return t!==uM}).length;return a>=e?r.apply(void 0,i):t(e-a,uT(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var o=i.map(function(t){return uk(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return uE(t)})(o)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(o)||function(t,e){if(t){if("string"==typeof t)return uE(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uE(t,e)}}(o)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},uC=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},uI=uN(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),uD=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return u_;var n=e.reverse(),i=n[0],o=n.slice(1);return function(){return o.reduce(function(t,e){return e(t)},i.apply(void 0,arguments))}},uB=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},uR=function(t){var e=null,r=null;return function(){for(var n=arguments.length,i=Array(n),o=0;o<n;o++)i[o]=arguments[o];return e&&i.every(function(t,r){return t===e[r]})?r:(e=i,r=t.apply(void 0,i))}};uN(function(t,e,r){var n=+t;return n+r*(e-n)}),uN(function(t,e,r){var n=e-t;return(r-t)/(n=n||1/0)}),uN(function(t,e,r){var n=e-t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))});let uz={rangeStep:function(t,e,r){for(var n=new uA(t),i=0,o=[];n.lt(e)&&i<1e5;)o.push(n.toNumber()),n=n.add(r),i++;return o},getDigitCount:function(t){var e;return 0===t?1:Math.floor(new uA(t).abs().log(10).toNumber())+1}};function uL(t){return function(t){if(Array.isArray(t))return uF(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||u$(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function uU(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,i=!1,o=void 0;try{for(var a,c=t[Symbol.iterator]();!(n=(a=c.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){i=!0,o=t}finally{try{n||null==c.return||c.return()}finally{if(i)throw o}}return r}}(t,e)||u$(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u$(t,e){if(t){if("string"==typeof t)return uF(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uF(t,e)}}function uF(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uq(t){var e=uU(t,2),r=e[0],n=e[1],i=r,o=n;return r>n&&(i=n,o=r),[i,o]}function uW(t,e,r){if(t.lte(0))return new uA(0);var n=uz.getDigitCount(t.toNumber()),i=new uA(10).pow(n),o=t.div(i),a=1!==n?.05:.1,c=new uA(Math.ceil(o.div(a).toNumber())).add(r).mul(a).mul(i);return e?c:new uA(Math.ceil(c))}function uX(t,e,r){var n=1,i=new uA(t);if(!i.isint()&&r){var o=Math.abs(t);o<1?(n=new uA(10).pow(uz.getDigitCount(t)-1),i=new uA(Math.floor(i.div(n).toNumber())).mul(n)):o>1&&(i=new uA(Math.floor(t)))}else 0===t?i=new uA(Math.floor((e-1)/2)):r||(i=new uA(Math.floor(t)));var a=Math.floor((e-1)/2);return uD(uI(function(t){return i.add(new uA(t-a).mul(n)).toNumber()}),uC)(0,e)}var uG=uR(function(t){var e=uU(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=uU(uq([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0){var s=l===1/0?[u].concat(uL(uC(0,i-1).map(function(){return 1/0}))):[].concat(uL(uC(0,i-1).map(function(){return-1/0})),[l]);return r>n?uB(s):s}if(u===l)return uX(u,i,o);var f=function t(e,r,n,i){var o,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new uA(0),tickMin:new uA(0),tickMax:new uA(0)};var c=uW(new uA(r).sub(e).div(n-1),i,a),u=Math.ceil((o=e<=0&&r>=0?new uA(0):(o=new uA(e).add(r).div(2)).sub(new uA(o).mod(c))).sub(e).div(c).toNumber()),l=Math.ceil(new uA(r).sub(o).div(c).toNumber()),s=u+l+1;return s>n?t(e,r,n,i,a+1):(s<n&&(l=r>0?l+(n-s):l,u=r>0?u:u+(n-s)),{step:c,tickMin:o.sub(new uA(u).mul(c)),tickMax:o.add(new uA(l).mul(c))})}(u,l,a,o),p=f.step,h=f.tickMin,d=f.tickMax,y=uz.rangeStep(h,d.add(new uA(.1).mul(p)),p);return r>n?uB(y):y});uR(function(t){var e=uU(t,2),r=e[0],n=e[1],i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(i,2),c=uU(uq([r,n]),2),u=c[0],l=c[1];if(u===-1/0||l===1/0)return[r,n];if(u===l)return uX(u,i,o);var s=uW(new uA(l).sub(u).div(a-1),o,0),f=uD(uI(function(t){return new uA(u).add(new uA(t).mul(s)).toNumber()}),uC)(0,a).filter(function(t){return t>=u&&t<=l});return r>n?uB(f):f});var uH=uR(function(t,e){var r=uU(t,2),n=r[0],i=r[1],o=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=uU(uq([n,i]),2),c=a[0],u=a[1];if(c===-1/0||u===1/0)return[n,i];if(c===u)return[c];var l=Math.max(e,2),s=uW(new uA(u).sub(c).div(l-1),o,0),f=[].concat(uL(uz.rangeStep(new uA(c),new uA(u).sub(new uA(.99).mul(s)),s)),[u]);return n>i?uB(f):f}),uV=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function uY(t){return(uY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function uZ(){return(uZ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function uK(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function uQ(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(uQ=function(){return!!t})()}function uJ(t){return(uJ=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u0(t,e){return(u0=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function u1(t,e,r){return(e=u2(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function u2(t){var e=function(t,e){if("object"!=uY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=uY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==uY(e)?e:e+""}var u5=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=uJ(t),function(t,e){if(e&&("object"===uY(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,uQ()?Reflect.construct(t,e||[],uJ(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&u0(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,n=t.width,i=t.dataKey,o=t.data,c=t.dataPointFormatter,u=t.xAxis,l=t.yAxis,s=t_(function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,uV),!1);"x"===this.props.direction&&"number"!==u.type&&tF(!1);var f=o.map(function(t){var o,f,p=c(t,i),h=p.x,d=p.y,y=p.value,v=p.errorVal;if(!v)return null;var m=[];if(Array.isArray(v)){var b=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return uK(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return uK(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=b[0],f=b[1]}else o=f=v;if("vertical"===r){var g=u.scale,x=d+e,w=x+n,O=x-n,j=g(y-o),S=g(y+f);m.push({x1:S,y1:w,x2:S,y2:O}),m.push({x1:j,y1:x,x2:S,y2:x}),m.push({x1:j,y1:w,x2:j,y2:O})}else if("horizontal"===r){var P=l.scale,A=h+e,E=A-n,_=A+n,M=P(y-o),k=P(y+f);m.push({x1:E,y1:k,x2:_,y2:k}),m.push({x1:A,y1:M,x2:A,y2:k}),m.push({x1:E,y1:M,x2:_,y2:M})}return a().createElement(tV,uZ({className:"recharts-errorBar",key:"bar-".concat(m.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},s),m.map(function(t){return a().createElement("line",uZ({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return a().createElement(tV,{className:"recharts-errorBars"},f)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,u2(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function u4(t){return(u4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u3(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function u6(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u3(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=u4(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u4(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u4(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u3(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}u1(u5,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),u1(u5,"displayName","ErrorBar");var u8=function(t){var e,r=t.children,n=t.formattedGraphicalItems,i=t.legendWidth,o=t.legendContent,a=tS(r,rl);if(!a)return null;var c=rl.defaultProps,u=void 0!==c?u6(u6({},c),a.props):{};return e=a.props&&a.props.payload?a.props&&a.props.payload:"children"===o?(n||[]).reduce(function(t,e){var r=e.item,n=e.props,i=n.sectors||n.data||[];return t.concat(i.map(function(t){return{type:a.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(n||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?u6(u6({},r),e.props):{},i=n.dataKey,o=n.name,a=n.legendType;return{inactive:n.hide,dataKey:i,type:u.iconType||a||"square",color:lc(e),value:o||i,payload:n}}),u6(u6(u6({},u),rl.getWithHeight(a,i)),{},{payload:e,item:a})};function u7(t){return(u7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u9(t){return function(t){if(Array.isArray(t))return lt(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return lt(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lt(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lt(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function le(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lr(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?le(Object(r),!0).forEach(function(e){ln(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):le(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function ln(t,e,r){var n;return(n=function(t,e){if("object"!=u7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==u7(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function li(t,e,r){return te()(t)||te()(e)?r:W(e)?z()(t,e,r):tn()(e)?e(t):r}function lo(t,e,r,n){var i=c9()(t,function(t){return li(t,e)});if("number"===r){var o=i.filter(function(t){return q(t)||parseFloat(t)});return o.length?[c8()(o),c3()(o)]:[1/0,-1/0]}return(n?i.filter(function(t){return!te()(t)}):i).map(function(t){return W(t)||t instanceof Date?t:""})}var la=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,o=-1,a=null!=(e=null==r?void 0:r.length)?e:0;if(a<=1)return 0;if(i&&"angleAxis"===i.axisType&&1e-6>=Math.abs(Math.abs(i.range[1]-i.range[0])-360))for(var c=i.range,u=0;u<a;u++){var l=u>0?n[u-1].coordinate:n[a-1].coordinate,s=n[u].coordinate,f=u>=a-1?n[0].coordinate:n[u+1].coordinate,p=void 0;if($(s-l)!==$(f-s)){var h=[];if($(f-s)===$(c[1]-c[0])){p=f;var d=s+c[1]-c[0];h[0]=Math.min(d,(d+l)/2),h[1]=Math.max(d,(d+l)/2)}else{p=l;var y=f+c[1]-c[0];h[0]=Math.min(s,(y+s)/2),h[1]=Math.max(s,(y+s)/2)}var v=[Math.min(s,(p+s)/2),Math.max(s,(p+s)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){o=n[u].index;break}}else{var m=Math.min(l,f),b=Math.max(l,f);if(t>(m+s)/2&&t<=(b+s)/2){o=n[u].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){o=r[g].index;break}return o},lc=function(t){var e,r,n=t.type.displayName,i=null!=(e=t.type)&&e.defaultProps?lr(lr({},t.type.defaultProps),t.props):t.props,o=i.stroke,a=i.fill;switch(n){case"Line":r=o;break;case"Area":case"Radar":r=o&&"none"!==o?o:a;break;default:r=a}return r},lu=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,i=void 0===n?{}:n;if(!i)return{};for(var o={},a=Object.keys(i),c=0,u=a.length;c<u;c++)for(var l=i[a[c]].stackGroups,s=Object.keys(l),f=0,p=s.length;f<p;f++){var h=l[s[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return tg(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?lr(lr({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];o[x]||(o[x]=[]);var w=te()(g)?e:g;o[x].push({item:v[0],stackList:v.slice(1),barSize:te()(w)?void 0:H(w,r,0)})}}return o},ll=function(t){var e,r=t.barGap,n=t.barCategoryGap,i=t.bandSize,o=t.sizeList,a=void 0===o?[]:o,c=t.maxBarSize,u=a.length;if(u<1)return null;var l=H(r,i,0,!0),s=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=i/u,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(u-1)*l)>=i&&(h-=(u-1)*l,l=0),h>=i&&p>0&&(f=!0,p*=.9,h=u*p);var d={offset:((i-h)/2|0)-l,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+l,size:f?p:e.barSize}},n=[].concat(u9(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},s)}else{var y=H(n,i,0,!0);i-2*y-(u-1)*l<=0&&(l=0);var v=(i-2*y-(u-1)*l)/u;v>1&&(v>>=0);var m=c===+c?Math.min(v,c):v;e=a.reduce(function(t,e,r){var n=[].concat(u9(t),[{item:e.item,position:{offset:y+(v+l)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},s)}return e},ls=function(t,e,r,n){var i=r.children,o=r.width,a=r.margin,c=u8({children:i,legendWidth:o-(a.left||0)-(a.right||0)});if(c){var u=n||{},l=u.width,s=u.height,f=c.align,p=c.verticalAlign,h=c.layout;if(("vertical"===h||"horizontal"===h&&"middle"===p)&&"center"!==f&&q(t[f]))return lr(lr({},t),{},ln({},f,t[f]+(l||0)));if(("horizontal"===h||"vertical"===h&&"center"===f)&&"middle"!==p&&q(t[p]))return lr(lr({},t),{},ln({},p,t[p]+(s||0)))}return t},lf=function(t,e,r,n,i){var o=tj(e.props.children,u5).filter(function(t){var e;return e=t.props.direction,!!te()(i)||("horizontal"===n?"yAxis"===i:"vertical"===n||"x"===e?"xAxis"===i:"y"!==e||"yAxis"===i)});if(o&&o.length){var a=o.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=li(e,r);if(te()(n))return t;var i=Array.isArray(n)?[c8()(n),c3()(n)]:[n,n],o=a.reduce(function(t,r){var n=li(e,r,0),o=i[0]-Math.abs(Array.isArray(n)?n[0]:n),a=i[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(o,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(o[0],t[0]),Math.max(o[1],t[1])]},[1/0,-1/0])}return null},lp=function(t,e,r,n,i){var o=e.map(function(e){return lf(t,e,r,i,n)}).filter(function(t){return!te()(t)});return o&&o.length?o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},lh=function(t,e,r,n,i){var o=e.map(function(e){var o=e.props.dataKey;return"number"===r&&o&&lf(t,e,o,n)||lo(t,o,r,i)});if("number"===r)return o.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return o.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},ld=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},ly=function(t,e,r,n){if(n)return t.map(function(t){return t.coordinate});var i,o,a=t.map(function(t){return t.coordinate===e&&(i=!0),t.coordinate===r&&(o=!0),t.coordinate});return i||a.push(e),o||a.push(r),a},lv=function(t,e,r){if(!t)return null;var n=t.scale,i=t.duplicateDomain,o=t.type,a=t.range,c="scaleBand"===t.realScaleType?n.bandwidth()/2:2,u=(e||r)&&"category"===o&&n.bandwidth?n.bandwidth()/c:0;return(u="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*$(a[0]-a[1])*u:u,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(i?i.indexOf(t):t)+u,value:t,offset:u}}).filter(function(t){return!B()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+u,value:t,index:e,offset:u}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+u,value:t,offset:u}}):n.domain().map(function(t,e){return{coordinate:n(t)+u,value:i?i[t]:t,index:e,offset:u}})},lm=new WeakMap,lb=function(t,e){if("function"!=typeof e)return t;lm.has(t)||lm.set(t,new WeakMap);var r=lm.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},lg=function(t,e,r){var i=t.scale,o=t.type,a=t.layout,c=t.axisType;if("auto"===i)return"radial"===a&&"radiusAxis"===c?{scale:nz(),realScaleType:"band"}:"radial"===a&&"angleAxis"===c?{scale:oA(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:nL(),realScaleType:"point"}:"category"===o?{scale:nz(),realScaleType:"band"}:{scale:oA(),realScaleType:"linear"};if(I()(i)){var u="scale".concat(ew()(i));return{scale:(n[u]||nL)(),realScaleType:n[u]?u:"point"}}return tn()(i)?{scale:i}:{scale:nL(),realScaleType:"point"}},lx=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),i=Math.min(n[0],n[1])-1e-4,o=Math.max(n[0],n[1])+1e-4,a=t(e[0]),c=t(e[r-1]);(a<i||a>o||c<i||c>o)&&t.domain([e[0],e[r-1]])}},lw=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},lO=function(t,e){if(!e||2!==e.length||!q(e[0])||!q(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),i=[t[0],t[1]];return(!q(t[0])||t[0]<r)&&(i[0]=r),(!q(t[1])||t[1]>n)&&(i[1]=n),i[0]>n&&(i[0]=n),i[1]<r&&(i[1]=r),i},lj={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0,a=0;a<e;++a){var c=B()(t[a][r][1])?t[a][r][0]:t[a][r][1];c>=0?(t[a][r][0]=i,t[a][r][1]=i+c,i=t[a][r][1]):(t[a][r][0]=o,t[a][r][1]=o+c,o=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,i,o=0,a=t[0].length;o<a;++o){for(i=r=0;r<n;++r)i+=t[r][o][1]||0;if(i)for(r=0;r<n;++r)t[r][o][1]/=i}cW(t,e)}},none:cW,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,i=t[e[0]],o=i.length;n<o;++n){for(var a=0,c=0;a<r;++a)c+=t[a][n][1]||0;i[n][1]+=i[n][0]=-c/2}cW(t,e)}},wiggle:function(t,e){if((i=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,i,o=0,a=1;a<n;++a){for(var c=0,u=0,l=0;c<i;++c){for(var s=t[e[c]],f=s[a][1]||0,p=(f-(s[a-1][1]||0))/2,h=0;h<c;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}u+=f,l+=p*f}r[a-1][1]+=r[a-1][0]=o,u&&(o-=l/u)}r[a-1][1]+=r[a-1][0]=o,cW(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var i=0,o=0;o<e;++o){var a=B()(t[o][r][1])?t[o][r][0]:t[o][r][1];a>=0?(t[o][r][0]=i,t[o][r][1]=i+a,i=t[o][r][1]):(t[o][r][0]=0,t[o][r][1]=0)}}},lS=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),i=lj[r];return(function(){var t=eR([]),e=cG,r=cW,n=cH;function i(i){var o,a,c=Array.from(t.apply(this,arguments),cV),u=c.length,l=-1;for(let t of i)for(o=0,++l;o<u;++o)(c[o][l]=[0,+n(t,c[o].key,l,i)]).data=t;for(o=0,a=cX(e(c));o<u;++o)c[a[o]].index=o;return r(c,a),c}return i.keys=function(e){return arguments.length?(t="function"==typeof e?e:eR(Array.from(e)),i):t},i.value=function(t){return arguments.length?(n="function"==typeof t?t:eR(+t),i):n},i.order=function(t){return arguments.length?(e=null==t?cG:"function"==typeof t?t:eR(Array.from(t)),i):e},i.offset=function(t){return arguments.length?(r=null==t?cW:t,i):r},i})().keys(n).value(function(t,e){return+li(t,e,0)}).order(cG).offset(i)(t)},lP=function(t,e,r,n,i,o){if(!t)return null;var a=(o?e.reverse():e).reduce(function(t,e){var i,o=null!=(i=e.type)&&i.defaultProps?lr(lr({},e.type.defaultProps),e.props):e.props,a=o.stackId;if(o.hide)return t;var c=o[r],u=t[c]||{hasStack:!1,stackGroups:{}};if(W(a)){var l=u.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};l.items.push(e),u.hasStack=!0,u.stackGroups[a]=l}else u.stackGroups[G("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return lr(lr({},t),{},ln({},c,u))},{});return Object.keys(a).reduce(function(e,o){var c=a[o];return c.hasStack&&(c.stackGroups=Object.keys(c.stackGroups).reduce(function(e,o){var a=c.stackGroups[o];return lr(lr({},e),{},ln({},o,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:lS(t,a.items,i)}))},{})),lr(lr({},e),{},ln({},o,c))},{})},lA=function(t,e){var r=e.realScaleType,n=e.type,i=e.tickCount,o=e.originalDomain,a=e.allowDecimals,c=r||e.scale;if("auto"!==c&&"linear"!==c)return null;if(i&&"number"===n&&o&&("auto"===o[0]||"auto"===o[1])){var u=t.domain();if(!u.length)return null;var l=uG(u,i,a);return t.domain([c8()(l),c3()(l)]),{niceTicks:l}}return i&&"number"===n?{niceTicks:uH(t.domain(),i,a)}:null},lE=function(t){var e=t.axis,r=t.ticks,n=t.offset,i=t.bandSize,o=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var c=li(o,e.dataKey,e.domain[a]);return te()(c)?null:e.scale(c)-i/2+n},l_=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),i=Math.max(r[0],r[1]);return n<=0&&i>=0?0:i<0?i:n}return r[0]},lM=function(t,e){var r,n=(null!=(r=t.type)&&r.defaultProps?lr(lr({},t.type.defaultProps),t.props):t.props).stackId;if(W(n)){var i=e[n];if(i){var o=i.items.indexOf(t);return o>=0?i.stackedData[o]:null}}return null},lk=function(t,e,r){return Object.keys(t).reduce(function(n,i){var o=t[i].stackedData.reduce(function(t,n){var i=n.slice(e,r+1).reduce(function(t,e){return[c8()(e.concat([t[0]]).filter(q)),c3()(e.concat([t[1]]).filter(q))]},[1/0,-1/0]);return[Math.min(t[0],i[0]),Math.max(t[1],i[1])]},[1/0,-1/0]);return[Math.min(o[0],n[0]),Math.max(o[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},lT=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lN=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,lC=function(t,e,r){if(tn()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if(q(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(lT.test(t[0])){var i=+lT.exec(t[0])[1];n[0]=e[0]-i}else tn()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if(q(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(lN.test(t[1])){var o=+lN.exec(t[1])[1];n[1]=e[1]+o}else tn()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},lI=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var i=t$()(e,function(t){return t.coordinate}),o=1/0,a=1,c=i.length;a<c;a++){var u=i[a],l=i[a-1];o=Math.min((u.coordinate||0)-(l.coordinate||0),o)}return o===1/0?0:o}return r?void 0:0},lD=function(t,e,r){return!t||!t.length||ue()(t,z()(r,"type.defaultProps.domain"))?e:t},lB=function(t,e){var r=t.type.defaultProps?lr(lr({},t.type.defaultProps),t.props):t.props,n=r.dataKey,i=r.name,o=r.unit,a=r.formatter,c=r.tooltipType,u=r.chartType,l=r.hide;return lr(lr({},t_(t,!1)),{},{dataKey:n,unit:o,formatter:a,name:i||n,color:lc(t),value:li(e,n),type:c,payload:e,chartType:u,hide:l})};function lR(t){return(lR="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lz(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lL(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lz(Object(r),!0).forEach(function(e){lU(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lz(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lU(t,e,r){var n;return(n=function(t,e){if("object"!=lR(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lR(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==lR(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var l$=["Webkit","Moz","O","ms"],lF=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=l$.reduce(function(t,n){return lL(lL({},t),{},lU({},n+r,e))},{});return n[t]=e,n};function lq(t){return(lq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function lW(){return(lW=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function lX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function lG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?lX(Object(r),!0).forEach(function(e){lK(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):lX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function lH(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,lQ(n.key),n)}}function lV(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(lV=function(){return!!t})()}function lY(t){return(lY=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function lZ(t,e){return(lZ=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function lK(t,e,r){return(e=lQ(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function lQ(t){var e=function(t,e){if("object"!=lq(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=lq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==lq(e)?e:e+""}var lJ=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,i=t.x,o=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var c=e.length,u=nL().domain(tL()(0,c)).range([i,i+o-a]),l=u.domain().map(function(t){return u(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:u(r),endX:u(n),scale:u,scaleValues:l}},l0=function(t){return t.changedTouches&&!!t.changedTouches.length},l1=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=lY(r),lK(e=function(t,e){if(e&&("object"===lq(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,lV()?Reflect.construct(r,i||[],lY(this).constructor):r.apply(this,i)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),lK(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),lK(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,i=t.startIndex;null==n||n({endIndex:r,startIndex:i})}),e.detachDragEndListener()}),lK(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),lK(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),lK(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),lK(e,"handleSlideDragStart",function(t){var r=l0(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&lZ(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,i=this.state.scaleValues,o=this.props,a=o.gap,c=o.data.length-1,u=Math.min(e,r),l=Math.max(e,r),s=n.getIndexInRange(i,u),f=n.getIndexInRange(i,l);return{startIndex:s-s%a,endIndex:f===c?c:f-f%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,i=e.dataKey,o=li(r[t],i,t);return tn()(n)?n(o,t):o}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,i=e.endX,o=this.props,a=o.x,c=o.width,u=o.travellerWidth,l=o.startIndex,s=o.endIndex,f=o.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+c-u-i,a+c-u-n):p<0&&(p=Math.max(p,a-n,a-i));var h=this.getIndex({startX:n+p,endX:i+p});(h.startIndex!==l||h.endIndex!==s)&&f&&f(h),this.setState({startX:n+p,endX:i+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=l0(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,i=e.endX,o=e.startX,a=this.state[n],c=this.props,u=c.x,l=c.width,s=c.travellerWidth,f=c.onChange,p=c.gap,h=c.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,u+l-s-a):y<0&&(y=Math.max(y,u-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(i>o?m%p==0:b%p==0)||!!(i<o)&&b===t||"endX"===n&&(i>o?b%p==0:m%p==0)||!!(i>o)&&b===t};this.setState(lK(lK({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,i=n.scaleValues,o=n.startX,a=n.endX,c=this.state[e],u=i.indexOf(c);if(-1!==u){var l=u+t;if(-1!==l&&!(l>=i.length)){var s=i[l];"startX"===e&&s>=a||"endX"===e&&s<=o||this.setState(lK({},e,s),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.fill,c=t.stroke;return a().createElement("rect",{stroke:c,fill:o,x:e,y:r,width:n,height:i})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,c=t.data,u=t.children,l=t.padding,s=o.Children.only(u);return s?a().cloneElement(s,{x:e,y:r,width:n,height:i,margin:l,compact:!0,data:c}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,i,o=this,c=this.props,u=c.y,l=c.travellerWidth,s=c.height,f=c.traveller,p=c.ariaLabel,h=c.data,d=c.startIndex,y=c.endIndex,v=Math.max(t,this.props.x),m=lG(lG({},t_(this.props,!1)),{},{x:v,y:u,width:l,height:s}),b=p||"Min value: ".concat(null==(r=h[d])?void 0:r.name,", Max value: ").concat(null==(i=h[y])?void 0:i.name);return a().createElement(tV,{tabIndex:0,role:"slider","aria-label":b,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),o.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){o.setState({isTravellerFocused:!0})},onBlur:function(){o.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(f,m))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,i=r.height,o=r.stroke,c=r.travellerWidth,u=Math.min(t,e)+c,l=Math.max(Math.abs(e-t)-c,0);return a().createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:o,fillOpacity:.2,x:u,y:n,width:l,height:i})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,i=t.height,o=t.travellerWidth,c=t.stroke,u=this.state,l=u.startX,s=u.endX,f={pointerEvents:"none",fill:c};return a().createElement(tV,{className:"recharts-brush-texts"},a().createElement(ip,lW({textAnchor:"end",verticalAnchor:"middle",x:Math.min(l,s)-5,y:n+i/2},f),this.getTextOfTick(e)),a().createElement(ip,lW({textAnchor:"start",verticalAnchor:"middle",x:Math.max(l,s)+o+5,y:n+i/2},f),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,i=t.x,o=t.y,c=t.width,u=t.height,l=t.alwaysShowText,s=this.state,f=s.startX,p=s.endX,h=s.isTextActive,d=s.isSlideMoving,y=s.isTravellerMoving,v=s.isTravellerFocused;if(!e||!e.length||!q(i)||!q(o)||!q(c)||!q(u)||c<=0||u<=0)return null;var m=(0,k.A)("recharts-brush",r),b=1===a().Children.count(n),g=lF("userSelect","none");return a().createElement(tV,{className:m,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:g},this.renderBackground(),b&&this.renderPanorama(),this.renderSlide(f,p),this.renderTravellerLayer(f,"startX"),this.renderTravellerLayer(p,"endX"),(h||d||y||v||l)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,i=t.height,o=t.stroke,c=Math.floor(r+i/2)-1;return a().createElement(a().Fragment,null,a().createElement("rect",{x:e,y:r,width:n,height:i,fill:o,stroke:"none"}),a().createElement("line",{x1:e+1,y1:c,x2:e+n-1,y2:c,fill:"none",stroke:"#fff"}),a().createElement("line",{x1:e+1,y1:c+2,x2:e+n-1,y2:c+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):tn()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,i=t.x,o=t.travellerWidth,a=t.updateId,c=t.startIndex,u=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return lG({prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n},r&&r.length?lJ({data:r,width:n,x:i,travellerWidth:o,startIndex:c,endIndex:u}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||i!==e.prevX||o!==e.prevTravellerWidth)){e.scale.range([i,i+n-o]);var l=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:o,prevUpdateId:a,prevX:i,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:l}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,i=r-1;i-n>1;){var o=Math.floor((n+i)/2);t[o]>e?i=o:n=o}return e>=t[i]?i:n}}],e&&lH(n.prototype,e),r&&lH(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function l2(t){return(l2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l5(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function l4(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l5(Object(r),!0).forEach(function(e){(function(t,e,r){var n;(n=function(t,e){if("object"!=l2(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l2(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==l2(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r})(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l5(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}lK(l1,"displayName","Brush"),lK(l1,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var l3=Math.PI/180,l6=function(t,e,r,n){return{x:t+Math.cos(-l3*n)*r,y:e+Math.sin(-l3*n)*r}},l8=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},l7=function(t,e){var r=t.x,n=t.y,i=e.cx,o=e.cy,a=l8({x:r,y:n},{x:i,y:o});if(a<=0)return{radius:a};var c=Math.acos((r-i)/a);return n>o&&(c=2*Math.PI-c),{radius:a,angle:180*c/Math.PI,angleInRadian:c}},l9=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},st=function(t,e){var r,n=l7({x:t.x,y:t.y},e),i=n.radius,o=n.angle,a=e.innerRadius,c=e.outerRadius;if(i<a||i>c)return!1;if(0===i)return!0;var u=l9(e),l=u.startAngle,s=u.endAngle,f=o;if(l<=s){for(;f>s;)f-=360;for(;f<l;)f+=360;r=f>=l&&f<=s}else{for(;f>l;)f-=360;for(;f<s;)f+=360;r=f>=s&&f<=l}return r?l4(l4({},e),{},{radius:i,angle:f+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null};function se(t){return(se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sr=["offset"];function sn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function si(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function so(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?si(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=se(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=se(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==se(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):si(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function sa(){return(sa=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var sc=function(t){var e=t.value,r=t.formatter,n=te()(t.children)?e:t.children;return tn()(r)?r(n):n},su=function(t,e,r){var n,i,o=t.position,c=t.viewBox,u=t.offset,l=t.className,s=c.cx,f=c.cy,p=c.innerRadius,h=c.outerRadius,d=c.startAngle,y=c.endAngle,v=c.clockWise,m=(p+h)/2,b=$(y-d)*Math.min(Math.abs(y-d),360),g=b>=0?1:-1;"insideStart"===o?(n=d+g*u,i=v):"insideEnd"===o?(n=y-g*u,i=!v):"end"===o&&(n=y+g*u,i=v),i=b<=0?i:!i;var x=l6(s,f,m,n),w=l6(s,f,m,n+(i?1:-1)*359),O="M".concat(x.x,",").concat(x.y,"\n    A").concat(m,",").concat(m,",0,1,").concat(+!i,",\n    ").concat(w.x,",").concat(w.y),j=te()(t.id)?G("recharts-radial-line-"):t.id;return a().createElement("text",sa({},r,{dominantBaseline:"central",className:(0,k.A)("recharts-radial-bar-label",l)}),a().createElement("defs",null,a().createElement("path",{id:j,d:O})),a().createElement("textPath",{xlinkHref:"#".concat(j)},e))},sl=function(t){var e=t.viewBox,r=t.offset,n=t.position,i=e.cx,o=e.cy,a=e.innerRadius,c=e.outerRadius,u=(e.startAngle+e.endAngle)/2;if("outside"===n){var l=l6(i,o,c+r,u),s=l.x;return{x:s,y:l.y,textAnchor:s>=i?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:i,y:o,textAnchor:"middle",verticalAnchor:"end"};var f=l6(i,o,(a+c)/2,u);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},ss=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,i=t.position,o=e.x,a=e.y,c=e.width,u=e.height,l=u>=0?1:-1,s=l*n,f=l>0?"end":"start",p=l>0?"start":"end",h=c>=0?1:-1,d=h*n,y=h>0?"end":"start",v=h>0?"start":"end";if("top"===i)return so(so({},{x:o+c/2,y:a-l*n,textAnchor:"middle",verticalAnchor:f}),r?{height:Math.max(a-r.y,0),width:c}:{});if("bottom"===i)return so(so({},{x:o+c/2,y:a+u+s,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(r.y+r.height-(a+u),0),width:c}:{});if("left"===i){var m={x:o-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"};return so(so({},m),r?{width:Math.max(m.x-r.x,0),height:u}:{})}if("right"===i){var b={x:o+c+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"};return so(so({},b),r?{width:Math.max(r.x+r.width-b.x,0),height:u}:{})}var g=r?{width:c,height:u}:{};return"insideLeft"===i?so({x:o+d,y:a+u/2,textAnchor:v,verticalAnchor:"middle"},g):"insideRight"===i?so({x:o+c-d,y:a+u/2,textAnchor:y,verticalAnchor:"middle"},g):"insideTop"===i?so({x:o+c/2,y:a+s,textAnchor:"middle",verticalAnchor:p},g):"insideBottom"===i?so({x:o+c/2,y:a+u-s,textAnchor:"middle",verticalAnchor:f},g):"insideTopLeft"===i?so({x:o+d,y:a+s,textAnchor:v,verticalAnchor:p},g):"insideTopRight"===i?so({x:o+c-d,y:a+s,textAnchor:y,verticalAnchor:p},g):"insideBottomLeft"===i?so({x:o+d,y:a+u-s,textAnchor:v,verticalAnchor:f},g):"insideBottomRight"===i?so({x:o+c-d,y:a+u-s,textAnchor:y,verticalAnchor:f},g):to()(i)&&(q(i.x)||F(i.x))&&(q(i.y)||F(i.y))?so({x:o+H(i.x,c),y:a+H(i.y,u),textAnchor:"end",verticalAnchor:"end"},g):so({x:o+c/2,y:a+u/2,textAnchor:"middle",verticalAnchor:"middle"},g)};function sf(t){var e,r=t.offset,n=so({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,sr)),i=n.viewBox,c=n.position,u=n.value,l=n.children,s=n.content,f=n.className,p=n.textBreakAll;if(!i||te()(u)&&te()(l)&&!(0,o.isValidElement)(s)&&!tn()(s))return null;if((0,o.isValidElement)(s))return(0,o.cloneElement)(s,n);if(tn()(s)){if(e=(0,o.createElement)(s,n),(0,o.isValidElement)(e))return e}else e=sc(n);var h="cx"in i&&q(i.cx),d=t_(n,!0);if(h&&("insideStart"===c||"insideEnd"===c||"end"===c))return su(n,e,d);var y=h?sl(n):ss(n);return a().createElement(ip,sa({className:(0,k.A)("recharts-label",void 0===f?"":f)},d,y,{breakAll:p}),e)}sf.displayName="Label";var sp=function(t){var e=t.cx,r=t.cy,n=t.angle,i=t.startAngle,o=t.endAngle,a=t.r,c=t.radius,u=t.innerRadius,l=t.outerRadius,s=t.x,f=t.y,p=t.top,h=t.left,d=t.width,y=t.height,v=t.clockWise,m=t.labelViewBox;if(m)return m;if(q(d)&&q(y)){if(q(s)&&q(f))return{x:s,y:f,width:d,height:y};if(q(p)&&q(h))return{x:p,y:h,width:d,height:y}}return q(s)&&q(f)?{x:s,y:f,width:0,height:0}:q(e)&&q(r)?{cx:e,cy:r,startAngle:i||n||0,endAngle:o||n||0,innerRadius:u||0,outerRadius:l||c||a||0,clockWise:v}:t.viewBox?t.viewBox:{}};sf.parseViewBox=sp,sf.renderCallByParent=function(t,e){var r,n,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var c=t.children,u=sp(t),l=tj(c,sf).map(function(t,r){return(0,o.cloneElement)(t,{viewBox:e||u,key:"label-".concat(r)})});if(!i)return l;return[(r=t.label,n=e||u,!r?null:!0===r?a().createElement(sf,{key:"label-implicit",viewBox:n}):W(r)?a().createElement(sf,{key:"label-implicit",viewBox:n,value:r}):(0,o.isValidElement)(r)?r.type===sf?(0,o.cloneElement)(r,{key:"label-implicit",viewBox:n}):a().createElement(sf,{key:"label-implicit",content:r,viewBox:n}):tn()(r)?a().createElement(sf,{key:"label-implicit",content:r,viewBox:n}):to()(r)?a().createElement(sf,sa({viewBox:n},r,{key:"label-implicit"})):null)].concat(function(t){if(Array.isArray(t))return sn(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return sn(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sn(t,e)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())};var sh=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},sd=r(69691),sy=r.n(sd),sv=r(47212),sm=r.n(sv),sb=function(t){return null};sb.displayName="Cell";var sg=r(5359),sx=r.n(sg);function sw(t){return(sw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var sO=["valueAccessor"],sj=["data","dataKey","clockWise","id","textBreakAll"];function sS(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sP(){return(sP=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sA(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sE(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sA(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sw(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sA(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s_(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var sM=function(t){return Array.isArray(t.value)?sx()(t.value):t.value};function sk(t){var e=t.valueAccessor,r=void 0===e?sM:e,n=s_(t,sO),i=n.data,o=n.dataKey,c=n.clockWise,u=n.id,l=n.textBreakAll,s=s_(n,sj);return i&&i.length?a().createElement(tV,{className:"recharts-label-list"},i.map(function(t,e){var n=te()(o)?r(t,e):li(t&&t.payload,o),i=te()(u)?{}:{id:"".concat(u,"-").concat(e)};return a().createElement(sf,sP({},t_(t,!0),s,i,{parentViewBox:t.parentViewBox,value:n,textBreakAll:l,viewBox:sf.parseViewBox(te()(c)?t:sE(sE({},t),{},{clockWise:c})),key:"label-".concat(e),index:e}))})):null}sk.displayName="LabelList",sk.renderCallByParent=function(t,e){var r,n=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&n&&!t.label)return null;var i=tj(t.children,sk).map(function(t,r){return(0,o.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return n?[(r=t.label,!r?null:!0===r?a().createElement(sk,{key:"labelList-implicit",data:e}):a().isValidElement(r)||tn()(r)?a().createElement(sk,{key:"labelList-implicit",data:e,content:r}):to()(r)?a().createElement(sk,sP({data:e},r,{key:"labelList-implicit"})):null)].concat(function(t){if(Array.isArray(t))return sS(t)}(i)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return sS(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sS(t,e)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):i};var sT=r(38404),sN=r.n(sT),sC=r(98451),sI=r.n(sC);function sD(t){return(sD="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sB(){return(sB=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sR(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function sz(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sL(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sz(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sD(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sD(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sD(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sz(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sU=function(t,e,r,n,i){var o,a=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-a/2,",").concat(e+i)+"L ".concat(t+r-a/2-n,",").concat(e+i)+"L ".concat(t,",").concat(e," Z")},s$={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},sF=function(t){var e,r=sL(sL({},s$),t),n=(0,o.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,o.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(e,2)||function(t,e){if(t){if("string"==typeof t)return sR(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return sR(t,e)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),c=i[0],u=i[1];(0,o.useEffect)(function(){if(n.current&&n.current.getTotalLength)try{var t=n.current.getTotalLength();t&&u(t)}catch(t){}},[]);var l=r.x,s=r.y,f=r.upperWidth,p=r.lowerWidth,h=r.height,d=r.className,y=r.animationEasing,v=r.animationDuration,m=r.animationBegin,b=r.isUpdateAnimationActive;if(l!==+l||s!==+s||f!==+f||p!==+p||h!==+h||0===f&&0===p||0===h)return null;var g=(0,k.A)("recharts-trapezoid",d);return b?a().createElement(nw,{canBegin:c>0,from:{upperWidth:0,lowerWidth:0,height:h,x:l,y:s},to:{upperWidth:f,lowerWidth:p,height:h,x:l,y:s},duration:v,animationEasing:y,isActive:b},function(t){var e=t.upperWidth,i=t.lowerWidth,o=t.height,u=t.x,l=t.y;return a().createElement(nw,{canBegin:c>0,from:"0px ".concat(-1===c?1:c,"px"),to:"".concat(c,"px 0px"),attributeName:"strokeDasharray",begin:m,duration:v,easing:y},a().createElement("path",sB({},t_(r,!0),{className:g,d:sU(u,l,e,i,o),ref:n})))}):a().createElement("g",null,a().createElement("path",sB({},t_(r,!0),{className:g,d:sU(l,s,f,p,h)})))};function sq(t){return(sq="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function sW(){return(sW=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function sX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function sG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sX(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sq(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sq(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sq(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var sH=function(t){var e=t.cx,r=t.cy,n=t.radius,i=t.angle,o=t.sign,a=t.isExternal,c=t.cornerRadius,u=t.cornerIsExternal,l=c*(a?1:-1)+n,s=Math.asin(c/l)/l3,f=u?i:i+o*s;return{center:l6(e,r,l,f),circleTangency:l6(e,r,n,f),lineTangency:l6(e,r,l*Math.cos(s*l3),u?i-o*s:i),theta:s}},sV=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.startAngle,a=t.endAngle,c=$(a-o)*Math.min(Math.abs(a-o),359.999),u=o+c,l=l6(e,r,i,o),s=l6(e,r,i,u),f="M ".concat(l.x,",").concat(l.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(c)>180),",").concat(+(o>u),",\n    ").concat(s.x,",").concat(s.y,"\n  ");if(n>0){var p=l6(e,r,n,o),h=l6(e,r,n,u);f+="L ".concat(h.x,",").concat(h.y,"\n            A ").concat(n,",").concat(n,",0,\n            ").concat(+(Math.abs(c)>180),",").concat(+(o<=u),",\n            ").concat(p.x,",").concat(p.y," Z")}else f+="L ".concat(e,",").concat(r," Z");return f},sY=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,i=t.outerRadius,o=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,u=t.startAngle,l=t.endAngle,s=$(l-u),f=sH({cx:e,cy:r,radius:i,angle:u,sign:s,cornerRadius:o,cornerIsExternal:c}),p=f.circleTangency,h=f.lineTangency,d=f.theta,y=sH({cx:e,cy:r,radius:i,angle:l,sign:-s,cornerRadius:o,cornerIsExternal:c}),v=y.circleTangency,m=y.lineTangency,b=y.theta,g=c?Math.abs(u-l):Math.abs(u-l)-d-b;if(g<0)return a?"M ".concat(h.x,",").concat(h.y,"\n        a").concat(o,",").concat(o,",0,0,1,").concat(2*o,",0\n        a").concat(o,",").concat(o,",0,0,1,").concat(-(2*o),",0\n      "):sV({cx:e,cy:r,innerRadius:n,outerRadius:i,startAngle:u,endAngle:l});var x="M ".concat(h.x,",").concat(h.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(p.x,",").concat(p.y,"\n    A").concat(i,",").concat(i,",0,").concat(+(g>180),",").concat(+(s<0),",").concat(v.x,",").concat(v.y,"\n    A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(m.x,",").concat(m.y,"\n  ");if(n>0){var w=sH({cx:e,cy:r,radius:n,angle:u,sign:s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),O=w.circleTangency,j=w.lineTangency,S=w.theta,P=sH({cx:e,cy:r,radius:n,angle:l,sign:-s,isExternal:!0,cornerRadius:o,cornerIsExternal:c}),A=P.circleTangency,E=P.lineTangency,_=P.theta,M=c?Math.abs(u-l):Math.abs(u-l)-S-_;if(M<0&&0===o)return"".concat(x,"L").concat(e,",").concat(r,"Z");x+="L".concat(E.x,",").concat(E.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(A.x,",").concat(A.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(M>180),",").concat(+(s>0),",").concat(O.x,",").concat(O.y,"\n      A").concat(o,",").concat(o,",0,0,").concat(+(s<0),",").concat(j.x,",").concat(j.y,"Z")}else x+="L".concat(e,",").concat(r,"Z");return x},sZ={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},sK=function(t){var e,r=sG(sG({},sZ),t),n=r.cx,i=r.cy,o=r.innerRadius,c=r.outerRadius,u=r.cornerRadius,l=r.forceCornerRadius,s=r.cornerIsExternal,f=r.startAngle,p=r.endAngle,h=r.className;if(c<o||f===p)return null;var d=(0,k.A)("recharts-sector",h),y=c-o,v=H(u,y,0,!0);return e=v>0&&360>Math.abs(f-p)?sY({cx:n,cy:i,innerRadius:o,outerRadius:c,cornerRadius:Math.min(v,y/2),forceCornerRadius:l,cornerIsExternal:s,startAngle:f,endAngle:p}):sV({cx:n,cy:i,innerRadius:o,outerRadius:c,startAngle:f,endAngle:p}),a().createElement("path",sW({},t_(r,!0),{className:d,d:e,role:"img"}))},sQ=["option","shapeType","propTransformer","activeClassName","isActive"];function sJ(t){return(sJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function s1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?s0(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=sJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=sJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==sJ(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):s0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function s2(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return a().createElement(nk,r);case"trapezoid":return a().createElement(sF,r);case"sector":return a().createElement(sK,r);case"symbols":if("symbols"===e)return a().createElement(eQ,r);break;default:return null}}function s5(t){var e,r=t.option,n=t.shapeType,i=t.propTransformer,c=t.activeClassName,u=t.isActive,l=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,sQ);if((0,o.isValidElement)(r))e=(0,o.cloneElement)(r,s1(s1({},l),(0,o.isValidElement)(r)?r.props:r));else if(tn()(r))e=r(l);else if(sN()(r)&&!sI()(r)){var s=(void 0===i?function(t,e){return s1(s1({},e),t)}:i)(r,l);e=a().createElement(s2,{shapeType:n,elementProps:s})}else e=a().createElement(s2,{shapeType:n,elementProps:l});return u?a().createElement(tV,{className:void 0===c?"recharts-active-shape":c},e):e}function s4(t,e){return null!=e&&"trapezoids"in t.props}function s3(t,e){return null!=e&&"sectors"in t.props}function s6(t,e){return null!=e&&"points"in t.props}function s8(t,e){var r,n,i=t.x===(null==e||null==(r=e.labelViewBox)?void 0:r.x)||t.x===e.x,o=t.y===(null==e||null==(n=e.labelViewBox)?void 0:n.y)||t.y===e.y;return i&&o}function s7(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function s9(t,e){var r=t.x===e.x,n=t.y===e.y,i=t.z===e.z;return r&&n&&i}var ft=["x","y"];function fe(t){return(fe="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fr(){return(fr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fn(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=fe(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fe(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fe(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fo(t,e){var r=t.x,n=t.y,i=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,ft),o=parseInt("".concat(r),10),a=parseInt("".concat(n),10),c=parseInt("".concat(e.height||i.height),10),u=parseInt("".concat(e.width||i.width),10);return fi(fi(fi(fi(fi({},e),i),o?{x:o}:{}),a?{y:a}:{}),{},{height:c,width:u,name:e.name,radius:e.radius})}function fa(t){return a().createElement(s5,fr({shapeType:"rectangle",propTransformer:fo,activeClassName:"recharts-active-bar"},t))}var fc=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var i="number"==typeof r;return i?t(r,n):(i||tF(!1),e)}},fu=["value","background"];function fl(t){return(fl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fs(){return(fs=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function ff(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fp(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ff(Object(r),!0).forEach(function(e){fm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ff(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fh(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fb(n.key),n)}}function fd(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fd=function(){return!!t})()}function fy(t){return(fy=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fv(t,e){return(fv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fm(t,e,r){return(e=fb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fb(t){var e=function(t,e){if("object"!=fl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fl(e)?e:e+""}var fg=function(t){var e,r;function n(){var t,e,r;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");for(var i=arguments.length,o=Array(i),a=0;a<i;a++)o[a]=arguments[a];return e=n,r=[].concat(o),e=fy(e),fm(t=function(t,e){if(e&&("object"===fl(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fd()?Reflect.construct(e,r||[],fy(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),fm(t,"id",G("recharts-bar-")),fm(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),fm(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&fv(n,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,n=r.shape,i=r.dataKey,o=r.activeIndex,c=r.activeBar,u=t_(this.props,!1);return t&&t.map(function(t,r){var l=r===o,s=fp(fp(fp({},u),t),{},{isActive:l,option:l?c:n,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return a().createElement(tV,fs({className:"recharts-bar-rectangle"},td(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value,"-").concat(r)}),a().createElement(fa,s))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,n=e.layout,i=e.isAnimationActive,o=e.animationBegin,c=e.animationDuration,u=e.animationEasing,l=e.animationId,s=this.state.prevData;return a().createElement(nw,{begin:o,duration:c,isActive:i,easing:u,from:{t:0},to:{t:1},key:"bar-".concat(l),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,o=r.map(function(t,e){var r=s&&s[e];if(r){var o=Z(r.x,t.x),a=Z(r.y,t.y),c=Z(r.width,t.width),u=Z(r.height,t.height);return fp(fp({},t),{},{x:o(i),y:a(i),width:c(i),height:u(i)})}if("horizontal"===n){var l=Z(0,t.height)(i);return fp(fp({},t),{},{y:t.y+t.height-l,height:l})}var f=Z(0,t.width)(i);return fp(fp({},t),{},{width:f})});return a().createElement(tV,null,t.renderRectanglesStatically(o))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!ue()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,n=e.dataKey,i=e.activeIndex,o=t_(this.props.background,!1);return r.map(function(e,r){e.value;var c=e.background,u=function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(e,fu);if(!c)return null;var l=fp(fp(fp(fp(fp({},u),{},{fill:"#eee"},c),o),td(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:n,index:r,className:"recharts-bar-background-rectangle"});return a().createElement(fa,fs({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},l))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,n=r.data,i=r.xAxis,o=r.yAxis,c=r.layout,u=tj(r.children,u5);if(!u)return null;var l="vertical"===c?n[0].height/2:n[0].width/2,s=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:li(t,e)}};return a().createElement(tV,{clipPath:t?"url(#clipPath-".concat(e,")"):null},u.map(function(t){return a().cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:n,xAxis:i,yAxis:o,layout:c,offset:l,dataPointFormatter:s})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,n=t.className,i=t.xAxis,o=t.yAxis,c=t.left,u=t.top,l=t.width,s=t.height,f=t.isAnimationActive,p=t.background,h=t.id;if(e||!r||!r.length)return null;var d=this.state.isAnimationFinished,y=(0,k.A)("recharts-bar",n),v=i&&i.allowDataOverflow,m=o&&o.allowDataOverflow,b=v||m,g=te()(h)?this.id:h;return a().createElement(tV,{className:y},v||m?a().createElement("defs",null,a().createElement("clipPath",{id:"clipPath-".concat(g)},a().createElement("rect",{x:v?c:c-l/2,y:m?u:u-s/2,width:v?l:2*l,height:m?s:2*s}))):null,a().createElement(tV,{className:"recharts-bar-rectangles",clipPath:b?"url(#clipPath-".concat(g,")"):null},p?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(b,g),(!f||d)&&sk.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&fh(n.prototype,e),r&&fh(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.PureComponent);function fx(t){return(fx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fw(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fP(n.key),n)}}function fO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fO(Object(r),!0).forEach(function(e){fS(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fS(t,e,r){return(e=fP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fP(t){var e=function(t,e){if("object"!=fx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fx(e)?e:e+""}fm(fg,"displayName","Bar"),fm(fg,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!ea.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),fm(fg,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,i=t.bandSize,o=t.xAxis,a=t.yAxis,c=t.xAxisTicks,u=t.yAxisTicks,l=t.stackedData,s=t.dataStartIndex,f=t.displayedData,p=t.offset,h=lw(n,r);if(!h)return null;var d=e.layout,y=r.type.defaultProps,v=void 0!==y?fp(fp({},y),r.props):r.props,m=v.dataKey,b=v.children,g=v.minPointSize,x="horizontal"===d?a:o,w=l?x.scale.domain():null,O=l_({numericAxis:x}),j=tj(b,sb),S=f.map(function(t,e){l?f=lO(l[s+e],w):Array.isArray(f=li(t,m))||(f=[O,f]);var n=fc(g,fg.defaultProps.minPointSize)(f[1],e);if("horizontal"===d){var f,p,y,v,b,x,S,P=[a.scale(f[0]),a.scale(f[1])],A=P[0],E=P[1];p=lE({axis:o,ticks:c,bandSize:i,offset:h.offset,entry:t,index:e}),y=null!=(S=null!=E?E:A)?S:void 0,v=h.size;var _=A-E;if(b=Number.isNaN(_)?0:_,x={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(b)<Math.abs(n)){var M=$(b||n)*(Math.abs(n)-Math.abs(b));y-=M,b+=M}}else{var k=[o.scale(f[0]),o.scale(f[1])],T=k[0],N=k[1];if(p=T,y=lE({axis:a,ticks:u,bandSize:i,offset:h.offset,entry:t,index:e}),v=N-T,b=h.size,x={x:o.x,y:y,width:o.width,height:b},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var C=$(v||n)*(Math.abs(n)-Math.abs(v));v+=C}}return fp(fp(fp({},t),{},{x:p,y:y,width:v,height:b,value:l?f:f[1],payload:t,background:x},j&&j[e]&&j[e].props),{},{tooltipPayload:[lB(r,t)],tooltipPosition:{x:p+v/2,y:y+b/2}})});return fp({data:S,layout:d},p)});var fA=function(t,e){var r=t.x,n=t.y,i=e.x,o=e.y;return{x:Math.min(r,i),y:Math.min(n,o),width:Math.abs(i-r),height:Math.abs(o-n)}},fE=function(){var t,e;function r(t){if(!(this instanceof r))throw TypeError("Cannot call a class as a function");this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var i=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+i;case"end":var o=this.bandwidth?this.bandwidth():0;return this.scale(t)+o}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&fw(r.prototype,t),e&&fw(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();fS(fE,"EPS",1e-4);var f_=function(t){var e=Object.keys(t).reduce(function(e,r){return fj(fj({},e),{},fS({},r,fE.create(t[r])))},{});return fj(fj({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,i=r.position;return sy()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:i})})},isInRange:function(t){return sm()(t,function(t,r){return e[r].isInRange(t)})}})},fM=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,i=(n%180+180)%180*Math.PI/180,o=Math.atan(r/e);return Math.abs(i>o&&i<Math.PI-o?r/Math.sin(i):e/Math.cos(i))};function fk(){return(fk=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function fT(t){return(fT="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function fN(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function fC(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?fN(Object(r),!0).forEach(function(e){fR(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):fN(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function fI(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(fI=function(){return!!t})()}function fD(t){return(fD=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function fB(t,e){return(fB=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function fR(t,e,r){return(e=fz(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function fz(t){var e=function(t,e){if("object"!=fT(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=fT(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==fT(e)?e:e+""}var fL=function(t){var e=t.x,r=t.y,n=t.xAxis,i=t.yAxis,o=f_({x:n.scale,y:i.scale}),a=o.apply({x:e,y:r},{bandAware:!0});return sh(t,"discard")&&!o.isInRange(a)?null:a},fU=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=fD(t),function(t,e){if(e&&("object"===fT(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,fI()?Reflect.construct(t,e||[],fD(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&fB(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,i=t.r,o=t.alwaysShow,c=t.clipPathId,u=W(e),l=W(n);if(J(void 0===o,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!l)return null;var s=fL(this.props);if(!s)return null;var f=s.x,p=s.y,h=this.props,d=h.shape,y=h.className,v=fC(fC({clipPath:sh(this.props,"hidden")?"url(#".concat(c,")"):void 0},t_(this.props,!0)),{},{cx:f,cy:p});return a().createElement(tV,{className:(0,k.A)("recharts-reference-dot",y)},r.renderDot(d,v),sf.renderCallByParent(this.props,{x:f-i,y:p-i,width:2*i,height:2*i}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,fz(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);fR(fU,"displayName","ReferenceDot"),fR(fU,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),fR(fU,"renderDot",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):tn()(t)?t(e):a().createElement(rf,fk({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var f$=r(67367),fF=r.n(f$),fq=r(22964),fW=r.n(fq),fX=r(86451),fG=r.n(fX)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),fH=(0,o.createContext)(void 0),fV=(0,o.createContext)(void 0),fY=(0,o.createContext)(void 0),fZ=(0,o.createContext)({}),fK=(0,o.createContext)(void 0),fQ=(0,o.createContext)(0),fJ=(0,o.createContext)(0),f0=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,i=e.offset,o=t.clipPathId,c=t.children,u=t.width,l=t.height,s=fG(i);return a().createElement(fH.Provider,{value:r},a().createElement(fV.Provider,{value:n},a().createElement(fZ.Provider,{value:i},a().createElement(fY.Provider,{value:s},a().createElement(fK.Provider,{value:o},a().createElement(fQ.Provider,{value:l},a().createElement(fJ.Provider,{value:u},c)))))))},f1=function(t){var e=(0,o.useContext)(fH);null==e&&tF(!1);var r=e[t];return null==r&&tF(!1),r},f2=function(){var t=(0,o.useContext)(fV);return fW()(t,function(t){return sm()(t.domain,Number.isFinite)})||V(t)},f5=function(t){var e=(0,o.useContext)(fV);null==e&&tF(!1);var r=e[t];return null==r&&tF(!1),r},f4=function(){return(0,o.useContext)(fJ)},f3=function(){return(0,o.useContext)(fQ)};function f6(t){return(f6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f8(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f8=function(){return!!t})()}function f7(t){return(f7=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function f9(t,e){return(f9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pt(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function pe(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pt(Object(r),!0).forEach(function(e){pr(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pt(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function pr(t,e,r){return(e=pn(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pn(t){var e=function(t,e){if("object"!=f6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f6(e)?e:e+""}function pi(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function po(){return(po=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var pa=function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):tn()(t)?t(e):a().createElement("line",po({},e,{className:"recharts-reference-line-line"}))},pc=function(t,e,r,n,i,o,a,c,u){var l=i.x,s=i.y,f=i.width,p=i.height;if(r){var h=u.y,d=t.y.apply(h,{position:o});if(sh(u,"discard")&&!t.y.isInRange(d))return null;var y=[{x:l+f,y:d},{x:l,y:d}];return"left"===c?y.reverse():y}if(e){var v=u.x,m=t.x.apply(v,{position:o});if(sh(u,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:s+p},{x:m,y:s}];return"top"===a?b.reverse():b}if(n){var g=u.segment.map(function(e){return t.apply(e,{position:o})});return sh(u,"discard")&&fF()(g,function(e){return!t.isInRange(e)})?null:g}return null};function pu(t){var e,r=t.x,n=t.y,i=t.segment,c=t.xAxisId,u=t.yAxisId,l=t.shape,s=t.className,f=t.alwaysShow,p=(0,o.useContext)(fK),h=f1(c),d=f5(u),y=(0,o.useContext)(fY);if(!p||!y)return null;J(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=pc(f_({x:h.scale,y:d.scale}),W(r),W(n),i&&2===i.length,y,t.position,h.orientation,d.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{o=(r=r.call(t)).next,!1;for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(v,2)||function(t,e){if(t){if("string"==typeof t)return pi(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pi(t,e)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,x=b.y,w=m[1],O=w.x,j=w.y,S=pe(pe({clipPath:sh(t,"hidden")?"url(#".concat(p,")"):void 0},t_(t,!0)),{},{x1:g,y1:x,x2:O,y2:j});return a().createElement(tV,{className:(0,k.A)("recharts-reference-line",s)},pa(l,S),sf.renderCallByParent(t,fA({x:(e={x1:g,y1:x,x2:O,y2:j}).x1,y:e.y1},{x:e.x2,y:e.y2})))}var pl=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=f7(t),function(t,e){if(e&&("object"===f6(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,f8()?Reflect.construct(t,e||[],f7(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&f9(r,t),e=[{key:"render",value:function(){return a().createElement(pu,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pn(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function ps(){return(ps=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function pf(t){return(pf="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function pp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ph(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?pp(Object(r),!0).forEach(function(e){pm(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):pp(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pr(pl,"displayName","ReferenceLine"),pr(pl,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});function pd(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(pd=function(){return!!t})()}function py(t){return(py=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function pv(t,e){return(pv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function pm(t,e,r){return(e=pb(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pb(t){var e=function(t,e){if("object"!=pf(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pf(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pf(e)?e:e+""}var pg=function(t,e,r,n,i){var o=i.x1,a=i.x2,c=i.y1,u=i.y2,l=i.xAxis,s=i.yAxis;if(!l||!s)return null;var f=f_({x:l.scale,y:s.scale}),p={x:t?f.x.apply(o,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(c,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(u,{position:"end"}):f.y.rangeMax};return!sh(i,"discard")||f.isInRange(p)&&f.isInRange(h)?fA(p,h):null},px=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=py(t),function(t,e){if(e&&("object"===pf(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,pd()?Reflect.construct(t,e||[],py(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&pv(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,i=t.y1,o=t.y2,c=t.className,u=t.alwaysShow,l=t.clipPathId;J(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=W(e),f=W(n),p=W(i),h=W(o),d=this.props.shape;if(!s&&!f&&!p&&!h&&!d)return null;var y=pg(s,f,p,h,this.props);if(!y&&!d)return null;var v=sh(this.props,"hidden")?"url(#".concat(l,")"):void 0;return a().createElement(tV,{className:(0,k.A)("recharts-reference-area",c)},r.renderRect(d,ph(ph({clipPath:v},t_(this.props,!0)),y)),sf.renderCallByParent(this.props,y))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pb(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function pw(t){return function(t){if(Array.isArray(t))return pO(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return pO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return pO(t,e)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function pO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}pm(px,"displayName","ReferenceArea"),pm(px,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),pm(px,"renderRect",function(t,e){var r;return a().isValidElement(t)?a().cloneElement(t,e):tn()(t)?t(e):a().createElement(nk,ps({},e,{className:"recharts-reference-area-rect"}))});var pj=function(t,e,r,n,i){var o=tj(t,pl),a=tj(t,fU),c=[].concat(pw(o),pw(a)),u=tj(t,px),l="".concat(n,"Id"),s=n[0],f=e;if(c.length&&(f=c.reduce(function(t,e){if(e.props[l]===r&&sh(e.props,"extendDomain")&&q(e.props[s])){var n=e.props[s];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),u.length){var p="".concat(s,"1"),h="".concat(s,"2");f=u.reduce(function(t,e){if(e.props[l]===r&&sh(e.props,"extendDomain")&&q(e.props[p])&&q(e.props[h])){var n=e.props[p],i=e.props[h];return[Math.min(t[0],n,i),Math.max(t[1],n,i)]}return t},f)}return i&&i.length&&(f=i.reduce(function(t,e){return q(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},pS=r(11117),pP=new(r.n(pS)()),pA="recharts.syncMouseEvents";function pE(t){return(pE="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p_(t,e,r){return(e=pM(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pM(t){var e=function(t,e){if("object"!=pE(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pE(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pE(e)?e:e+""}var pk=function(){var t,e;return t=function t(){if(!(this instanceof t))throw TypeError("Cannot call a class as a function");p_(this,"activeIndex",0),p_(this,"coordinateList",[]),p_(this,"layout","horizontal")},e=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,i=t.container,o=void 0===i?null:i,a=t.layout,c=void 0===a?null:a,u=t.offset,l=void 0===u?null:u,s=t.mouseHandlerCallback,f=void 0===s?null:s;this.coordinateList=null!=(e=null!=n?n:this.coordinateList)?e:[],this.container=null!=o?o:this.container,this.layout=null!=c?c:this.layout,this.offset=null!=l?l:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,i=r.y,o=r.height,a=this.coordinateList[this.activeIndex].coordinate,c=(null==(t=window)?void 0:t.scrollX)||0,u=(null==(e=window)?void 0:e.scrollY)||0,l=i+this.offset.top+o/2+u;this.mouseHandlerCallback({pageX:n+a+c,pageY:l})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pM(n.key),n)}}(t.prototype,e),Object.defineProperty(t,"prototype",{writable:!1}),t}();function pT(){}function pN(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function pC(t){this._context=t}function pI(t){this._context=t}function pD(t){this._context=t}pC.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:pN(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:pN(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pI.prototype={areaStart:pT,areaEnd:pT,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:pN(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},pD.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:pN(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class pB{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function pR(t){this._context=t}function pz(t){this._context=t}function pL(t){return new pz(t)}pR.prototype={areaStart:pT,areaEnd:pT,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t*=1,e*=1,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}};function pU(t,e,r){var n=t._x1-t._x0,i=e-t._x1,o=(t._y1-t._y0)/(n||i<0&&-0),a=(r-t._y1)/(i||n<0&&-0);return((o<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(o),Math.abs(a),.5*Math.abs((o*i+a*n)/(n+i)))||0}function p$(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function pF(t,e,r){var n=t._x0,i=t._y0,o=t._x1,a=t._y1,c=(o-n)/3;t._context.bezierCurveTo(n+c,i+c*e,o-c,a-c*r,o,a)}function pq(t){this._context=t}function pW(t){this._context=new pX(t)}function pX(t){this._context=t}function pG(t){this._context=t}function pH(t){var e,r,n=t.length-1,i=Array(n),o=Array(n),a=Array(n);for(i[0]=0,o[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)i[e]=1,o[e]=4,a[e]=4*t[e]+2*t[e+1];for(i[n-1]=2,o[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=i[e]/o[e-1],o[e]-=r,a[e]-=r*a[e-1];for(i[n-1]=a[n-1]/o[n-1],e=n-2;e>=0;--e)i[e]=(a[e]-i[e+1])/o[e];for(e=0,o[n-1]=(t[n]+i[n-1])/2;e<n-1;++e)o[e]=2*t[e+1]-i[e+1];return[i,o]}function pV(t,e){this._context=t,this._t=e}function pY(t){return t[0]}function pZ(t){return t[1]}function pK(t,e){var r=eR(!0),n=null,i=pL,o=null,a=eq(c);function c(c){var u,l,s,f=(c=cX(c)).length,p=!1;for(null==n&&(o=i(s=a())),u=0;u<=f;++u)!(u<f&&r(l=c[u],u,c))===p&&((p=!p)?o.lineStart():o.lineEnd()),p&&o.point(+t(l,u,c),+e(l,u,c));if(s)return o=null,s+""||null}return t="function"==typeof t?t:void 0===t?pY:eR(t),e="function"==typeof e?e:void 0===e?pZ:eR(e),c.x=function(e){return arguments.length?(t="function"==typeof e?e:eR(+e),c):t},c.y=function(t){return arguments.length?(e="function"==typeof t?t:eR(+t),c):e},c.defined=function(t){return arguments.length?(r="function"==typeof t?t:eR(!!t),c):r},c.curve=function(t){return arguments.length?(i=t,null!=n&&(o=i(n)),c):i},c.context=function(t){return arguments.length?(null==t?n=o=null:o=i(n=t),c):n},c}function pQ(t,e,r){var n=null,i=eR(!0),o=null,a=pL,c=null,u=eq(l);function l(l){var s,f,p,h,d,y=(l=cX(l)).length,v=!1,m=Array(y),b=Array(y);for(null==o&&(c=a(d=u())),s=0;s<=y;++s){if(!(s<y&&i(h=l[s],s,l))===v)if(v=!v)f=s,c.areaStart(),c.lineStart();else{for(c.lineEnd(),c.lineStart(),p=s-1;p>=f;--p)c.point(m[p],b[p]);c.lineEnd(),c.areaEnd()}v&&(m[s]=+t(h,s,l),b[s]=+e(h,s,l),c.point(n?+n(h,s,l):m[s],r?+r(h,s,l):b[s]))}if(d)return c=null,d+""||null}function s(){return pK().defined(i).curve(a).context(o)}return t="function"==typeof t?t:void 0===t?pY:eR(+t),e="function"==typeof e?e:void 0===e?eR(0):eR(+e),r="function"==typeof r?r:void 0===r?pZ:eR(+r),l.x=function(e){return arguments.length?(t="function"==typeof e?e:eR(+e),n=null,l):t},l.x0=function(e){return arguments.length?(t="function"==typeof e?e:eR(+e),l):t},l.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:eR(+t),l):n},l.y=function(t){return arguments.length?(e="function"==typeof t?t:eR(+t),r=null,l):e},l.y0=function(t){return arguments.length?(e="function"==typeof t?t:eR(+t),l):e},l.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:eR(+t),l):r},l.lineX0=l.lineY0=function(){return s().x(t).y(e)},l.lineY1=function(){return s().x(t).y(r)},l.lineX1=function(){return s().x(n).y(e)},l.defined=function(t){return arguments.length?(i="function"==typeof t?t:eR(!!t),l):i},l.curve=function(t){return arguments.length?(a=t,null!=o&&(c=a(o)),l):a},l.context=function(t){return arguments.length?(null==t?o=c=null:c=a(o=t),l):o},l}function pJ(t){return(pJ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p0(){return(p0=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p1(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p1(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=pJ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=pJ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==pJ(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p1(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}pz.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},pq.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:pF(this,this._t0,p$(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e*=1,(t*=1)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,pF(this,p$(this,r=pU(this,t,e)),r);break;default:pF(this,this._t0,r=pU(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(pW.prototype=Object.create(pq.prototype)).point=function(t,e){pq.prototype.point.call(this,e,t)},pX.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,i,o){this._context.bezierCurveTo(e,t,n,r,o,i)}},pG.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r)if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=pH(t),i=pH(e),o=0,a=1;a<r;++o,++a)this._context.bezierCurveTo(n[0][o],i[0][o],n[1][o],i[1][o],t[a],e[a]);(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},pV.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t*=1,e*=1,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var p5={curveBasisClosed:function(t){return new pI(t)},curveBasisOpen:function(t){return new pD(t)},curveBasis:function(t){return new pC(t)},curveBumpX:function(t){return new pB(t,!0)},curveBumpY:function(t){return new pB(t,!1)},curveLinearClosed:function(t){return new pR(t)},curveLinear:pL,curveMonotoneX:function(t){return new pq(t)},curveMonotoneY:function(t){return new pW(t)},curveNatural:function(t){return new pG(t)},curveStep:function(t){return new pV(t,.5)},curveStepAfter:function(t){return new pV(t,1)},curveStepBefore:function(t){return new pV(t,0)}},p4=function(t){return t.x===+t.x&&t.y===+t.y},p3=function(t){return t.x},p6=function(t){return t.y},p8=function(t,e){if(tn()(t))return t;var r="curve".concat(ew()(t));return("curveMonotone"===r||"curveBump"===r)&&e?p5["".concat(r).concat("vertical"===e?"Y":"X")]:p5[r]||pL},p7=function(t){var e,r=t.type,n=t.points,i=void 0===n?[]:n,o=t.baseLine,a=t.layout,c=t.connectNulls,u=void 0!==c&&c,l=p8(void 0===r?"linear":r,a),s=u?i.filter(function(t){return p4(t)}):i;if(Array.isArray(o)){var f=u?o.filter(function(t){return p4(t)}):o,p=s.map(function(t,e){return p2(p2({},t),{},{base:f[e]})});return(e="vertical"===a?pQ().y(p6).x1(p3).x0(function(t){return t.base.x}):pQ().x(p3).y1(p6).y0(function(t){return t.base.y})).defined(p4).curve(l),e(p)}return(e="vertical"===a&&q(o)?pQ().y(p6).x1(p3).x0(o):q(o)?pQ().x(p3).y1(p6).y0(o):pK().x(p3).y(p6)).defined(p4).curve(l),e(s)},p9=function(t){var e=t.className,r=t.points,n=t.path,i=t.pathRef;if((!r||!r.length)&&!n)return null;var o=r&&r.length?p7(t):n;return a().createElement("path",p0({},t_(t,!1),th(t),{className:(0,k.A)("recharts-curve",e),d:o,ref:i}))};function ht(t){return(ht="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var he=["x","y","top","left","width","height","className"];function hr(){return(hr=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var hi=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,i=void 0===n?0:n,o=t.top,c=void 0===o?0:o,u=t.left,l=void 0===u?0:u,s=t.width,f=void 0===s?0:s,p=t.height,h=void 0===p?0:p,d=t.className,y=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hn(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=ht(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ht(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ht(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hn(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:i,top:c,left:l,width:f,height:h},function(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}(t,he));return q(r)&&q(i)&&q(f)&&q(h)&&q(c)&&q(l)?a().createElement("path",hr({},t_(y,!0),{className:(0,k.A)("recharts-cross",d),d:"M".concat(r,",").concat(c,"v").concat(h,"M").concat(l,",").concat(i,"h").concat(f)})):null};function ho(t){var e=t.cx,r=t.cy,n=t.radius,i=t.startAngle,o=t.endAngle;return{points:[l6(e,r,n,i),l6(e,r,n,o)],cx:e,cy:r,radius:n,startAngle:i,endAngle:o}}function ha(t){return(ha="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hc(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=ha(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ha(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ha(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hc(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hl(t){var e,r,n,i,a=t.element,c=t.tooltipEventType,u=t.isActive,l=t.activeCoordinate,s=t.activePayload,f=t.offset,p=t.activeTooltipIndex,h=t.tooltipAxisBandSize,d=t.layout,y=t.chartName,v=null!=(r=a.props.cursor)?r:null==(n=a.type.defaultProps)?void 0:n.cursor;if(!a||!v||!u||!l||"ScatterChart"!==y&&"axis"!==c)return null;var m=p9;if("ScatterChart"===y)i=l,m=hi;else if("BarChart"===y)e=h/2,i={stroke:"none",fill:"#ccc",x:"horizontal"===d?l.x-e:f.left+.5,y:"horizontal"===d?f.top+.5:l.y-e,width:"horizontal"===d?h:f.width-1,height:"horizontal"===d?f.height-1:h},m=nk;else if("radial"===d){var b=ho(l),g=b.cx,x=b.cy,w=b.radius;i={cx:g,cy:x,startAngle:b.startAngle,endAngle:b.endAngle,innerRadius:w,outerRadius:w},m=sK}else i={points:function(t,e,r){var n,i,o,a;if("horizontal"===t)o=n=e.x,i=r.top,a=r.top+r.height;else if("vertical"===t)a=i=e.y,n=r.left,o=r.left+r.width;else if(null!=e.cx&&null!=e.cy)if("centric"!==t)return ho(e);else{var c=e.cx,u=e.cy,l=e.innerRadius,s=e.outerRadius,f=e.angle,p=l6(c,u,l,f),h=l6(c,u,s,f);n=p.x,i=p.y,o=h.x,a=h.y}return[{x:n,y:i},{x:o,y:a}]}(d,l,f)},m=p9;var O=hu(hu(hu(hu({stroke:"#ccc",pointerEvents:"none"},f),i),t_(v,!1)),{},{payload:s,payloadIndex:p,className:(0,k.A)("recharts-tooltip-cursor",v.className)});return(0,o.isValidElement)(v)?(0,o.cloneElement)(v,O):(0,o.createElement)(m,O)}var hs=["item"],hf=["children","className","width","height","style","compact","title","desc"];function hp(t){return(hp="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hh(){return(hh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hd(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,i,o,a,c=[],u=!0,l=!1;try{if(o=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;u=!1}else for(;!(u=(n=o.call(r)).done)&&(c.push(n.value),c.length!==e);u=!0);}catch(t){l=!0,i=t}finally{try{if(!u&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(l)throw i}}return c}}(t,e)||hx(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hy(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function hv(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(hv=function(){return!!t})()}function hm(t){return(hm=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function hb(t,e){return(hb=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function hg(t){return function(t){if(Array.isArray(t))return hw(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||hx(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function hx(t,e){if(t){if("string"==typeof t)return hw(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return hw(t,e)}}function hw(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function hO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hO(Object(r),!0).forEach(function(e){hS(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hS(t,e,r){return(e=hP(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function hP(t){var e=function(t,e){if("object"!=hp(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hp(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hp(e)?e:e+""}var hA={xAxis:["bottom","top"],yAxis:["left","right"]},hE={width:"100%",height:"100%"},h_={x:0,y:0};function hM(t){return t}var hk=function(t,e,r,n){var i=e.find(function(t){return t&&t.index===r});if(i){if("horizontal"===t)return{x:i.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:i.coordinate};if("centric"===t){var o=i.coordinate,a=n.radius;return hj(hj(hj({},n),l6(n.cx,n.cy,a,o)),{},{angle:o,radius:a})}var c=i.coordinate,u=n.angle;return hj(hj(hj({},n),l6(n.cx,n.cy,c,u)),{},{angle:u,radius:c})}return h_},hT=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,i=e.dataEndIndex,o=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(hg(t),hg(r)):t},[]);return o.length>0?o:t&&t.length&&q(n)&&q(i)?t.slice(n,i+1):[]};function hN(t){return"number"===t?[0,"auto"]:void 0}var hC=function(t,e,r,n){var i=t.graphicalItems,o=t.tooltipAxis,a=hT(e,t);return r<0||!i||!i.length||r>=a.length?null:i.reduce(function(i,c){var u,l,s=null!=(u=c.props.data)?u:e;return(s&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(s=s.slice(t.dataStartIndex,t.dataEndIndex+1)),l=o.dataKey&&!o.allowDuplicatedCategory?K(void 0===s?a:s,o.dataKey,n):s&&s[r]||a[r])?[].concat(hg(i),[lB(c,l)]):i},[])},hI=function(t,e,r,n){var i=n||{x:t.chartX,y:t.chartY},o="horizontal"===r?i.x:"vertical"===r?i.y:"centric"===r?i.angle:i.radius,a=t.orderedTooltipTicks,c=t.tooltipAxis,u=t.tooltipTicks,l=la(o,a,u,c);if(l>=0&&u){var s=u[l]&&u[l].value,f=hC(t,e,l,s),p=hk(r,a,l,i);return{activeTooltipIndex:l,activeLabel:s,activePayload:f,activeCoordinate:p}}return null},hD=function(t,e){var r=e.axes,n=e.graphicalItems,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=t.stackOffset,p=ld(l,i);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?hj(hj({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,w=h[o];if(e[w])return e;var O=hT(t.data,{graphicalItems:n.filter(function(t){var e;return(o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o])===w}),dataStartIndex:c,dataEndIndex:u}),j=O.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],i=null==t?void 0:t[1];if(n&&i&&q(n)&&q(i))return!0}return!1})(h.domain,v,d)&&(A=lC(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(_=lo(O,y,"category")));var S=hN(d);if(!A||0===A.length){var P,A,E,_,M,k=null!=(M=h.domain)?M:S;if(y){if(A=lo(O,y,d),"category"===d&&p){var T=Y(A);m&&T?(E=A,A=tL()(0,j)):m||(A=lD(k,A,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(hg(t),[e])},[]))}else if("category"===d)A=m?A.filter(function(t){return""!==t&&!te()(t)}):lD(k,A,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||te()(e)?t:[].concat(hg(t),[e])},[]);else if("number"===d){var N=lp(O,n.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===w&&(x||!i)}),y,i,l);N&&(A=N)}p&&("number"===d||"auto"!==b)&&(_=lo(O,y,"category"))}else A=p?tL()(0,j):a&&a[w]&&a[w].hasStack&&"number"===d?"expand"===f?[0,1]:lk(a[w].stackGroups,c,u):lh(O,n.filter(function(t){var e=o in t.props?t.props[o]:t.type.defaultProps[o],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===w&&(x||!r)}),d,l,!0);"number"===d?(A=pj(s,A,w,i,g),k&&(A=lC(k,A,v))):"category"===d&&k&&A.every(function(t){return k.indexOf(t)>=0})&&(A=k)}return hj(hj({},e),{},hS({},w,hj(hj({},h),{},{axisType:i,domain:A,categoricalDomain:_,duplicateDomain:E,originalDomain:null!=(P=h.domain)?P:S,isCategorical:p,layout:l})))},{})},hB=function(t,e){var r=e.graphicalItems,n=e.Axis,i=e.axisType,o=e.axisIdKey,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.layout,s=t.children,f=hT(t.data,{graphicalItems:r,dataStartIndex:c,dataEndIndex:u}),p=f.length,h=ld(l,i),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?hj(hj({},e.type.defaultProps),e.props):e.props)[o],m=hN("number");return t[v]?t:(d++,y=h?tL()(0,p):a&&a[v]&&a[v].hasStack?pj(s,y=lk(a[v].stackGroups,c,u),v,i):pj(s,y=lC(m,lh(f,r.filter(function(t){var e,r,n=o in t.props?t.props[o]:null==(e=t.type.defaultProps)?void 0:e[o],i="hide"in t.props?t.props.hide:null==(r=t.type.defaultProps)?void 0:r.hide;return n===v&&!i}),"number",l),n.defaultProps.allowDataOverflow),v,i),hj(hj({},t),{},hS({},v,hj(hj({axisType:i},n.defaultProps),{},{hide:!0,orientation:z()(hA,"".concat(i,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:l}))))},{})},hR=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,i=e.AxisComp,o=e.graphicalItems,a=e.stackGroups,c=e.dataStartIndex,u=e.dataEndIndex,l=t.children,s="".concat(n,"Id"),f=tj(l,i),p={};return f&&f.length?p=hD(t,{axes:f,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u}):o&&o.length&&(p=hB(t,{Axis:i,graphicalItems:o,axisType:n,axisIdKey:s,stackGroups:a,dataStartIndex:c,dataEndIndex:u})),p},hz=function(t){var e=V(t),r=lv(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:t$()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:lI(e,r)}},hL=function(t){var e=t.children,r=t.defaultShowTooltip,n=tS(e,l1),i=0,o=0;return t.data&&0!==t.data.length&&(o=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(i=n.props.startIndex),n.props.endIndex>=0&&(o=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:i,dataEndIndex:o,activeTooltipIndex:-1,isTooltipActive:!!r}},hU=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},h$=function(t,e){var r=t.props,n=t.graphicalItems,i=t.xAxisMap,o=void 0===i?{}:i,a=t.yAxisMap,c=void 0===a?{}:a,u=r.width,l=r.height,s=r.children,f=r.margin||{},p=tS(s,l1),h=tS(s,rl),d=Object.keys(c).reduce(function(t,e){var r=c[e],n=r.orientation;return r.mirror||r.hide?t:hj(hj({},t),{},hS({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(o).reduce(function(t,e){var r=o[e],n=r.orientation;return r.mirror||r.hide?t:hj(hj({},t),{},hS({},n,z()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=hj(hj({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||l1.defaultProps.height),h&&e&&(v=ls(v,n,r,e));var b=u-v.left-v.right,g=l-v.top-v.bottom;return hj(hj({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})};function hF(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],i=0;i<t.length;i+=e)if(void 0!==r&&!0!==r(t[i]))return;else n.push(t[i]);return n}function hq(t,e,r,n,i){if(t*e<t*n||t*e>t*i)return!1;var o=r();return t*(e-t*o/2-n)>=0&&t*(e+t*o/2-i)<=0}function hW(t){return(hW="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hX(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function hG(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hX(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=hW(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hW(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hW(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hX(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function hH(t,e,r){var n,i,o,a,c,u=t.tick,l=t.ticks,s=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!l||!l.length||!u)return[];if(q(h)||ea.isSsr)return hF(l,("number"==typeof h&&q(h)?h:0)+1);var m=[],b="top"===p||"bottom"===p?"width":"height",g=y&&"width"===b?nH(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},x=function(t,n){var i,o=tn()(d)?d(t.value,n):t.value;return"width"===b?(i=nH(o,{fontSize:e,letterSpacing:r}),fM({width:i.width+g.width,height:i.height+g.height},v)):nH(o,{fontSize:e,letterSpacing:r})[b]},w=l.length>=2?$(l[1].coordinate-l[0].coordinate):1,O=(n="width"===b,i=s.x,o=s.y,a=s.width,c=s.height,1===w?{start:n?i:o,end:n?i+a:o+c}:{start:n?i+a:o+c,end:n?i:o});return"equidistantPreserveStart"===h?function(t,e,r,n,i){for(var o,a=(n||[]).slice(),c=e.start,u=e.end,l=0,s=1,f=c;s<=a.length;)if(o=function(){var e,o=null==n?void 0:n[l];if(void 0===o)return{v:hF(n,s)};var a=l,p=function(){return void 0===e&&(e=r(o,a)),e},h=o.coordinate,d=0===l||hq(t,h,p,f,u);d||(l=0,f=c,s+=1),d&&(f=h+t*(p()/2+i),l+=s)}())return o.v;return[]}(w,O,x,l,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,i,o){var a=(n||[]).slice(),c=a.length,u=e.start,l=e.end;if(o){var s=n[c-1],f=r(s,c-1),p=t*(s.coordinate+t*f/2-l);a[c-1]=s=hG(hG({},s),{},{tickCoord:p>0?s.coordinate-p*t:s.coordinate}),hq(t,s.tickCoord,function(){return f},u,l)&&(l=s.tickCoord-t*(f/2+i),a[c-1]=hG(hG({},s),{},{isShow:!0}))}for(var h=o?c-1:c,d=function(e){var n,o=a[e],c=function(){return void 0===n&&(n=r(o,e)),n};if(0===e){var s=t*(o.coordinate-t*c()/2-u);a[e]=o=hG(hG({},o),{},{tickCoord:s<0?o.coordinate-s*t:o.coordinate})}else a[e]=o=hG(hG({},o),{},{tickCoord:o.coordinate});hq(t,o.tickCoord,c,u,l)&&(u=o.tickCoord+t*(c()/2+i),a[e]=hG(hG({},o),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(w,O,x,l,f,"preserveStartEnd"===h):function(t,e,r,n,i){for(var o=(n||[]).slice(),a=o.length,c=e.start,u=e.end,l=function(e){var n,l=o[e],s=function(){return void 0===n&&(n=r(l,e)),n};if(e===a-1){var f=t*(l.coordinate+t*s()/2-u);o[e]=l=hG(hG({},l),{},{tickCoord:f>0?l.coordinate-f*t:l.coordinate})}else o[e]=l=hG(hG({},l),{},{tickCoord:l.coordinate});hq(t,l.tickCoord,s,c,u)&&(u=l.tickCoord-t*(s()/2+i),o[e]=hG(hG({},l),{},{isShow:!0}))},s=a-1;s>=0;s--)l(s);return o}(w,O,x,l,f)).filter(function(t){return t.isShow})}var hV=["viewBox"],hY=["viewBox"],hZ=["ticks"];function hK(t){return(hK="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function hQ(){return(hQ=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function hJ(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function h0(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?hJ(Object(r),!0).forEach(function(e){h6(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):hJ(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h1(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}function h2(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,h8(n.key),n)}}function h5(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(h5=function(){return!!t})()}function h4(t){return(h4=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h3(t,e){return(h3=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function h6(t,e,r){return(e=h8(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function h8(t){var e=function(t,e){if("object"!=hK(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=hK(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==hK(e)?e:e+""}var h7=function(t){var e,r;function n(t){var e,r,i;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return r=n,i=[t],r=h4(r),(e=function(t,e){if(e&&("object"===hK(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,h5()?Reflect.construct(r,i||[],h4(this).constructor):r.apply(this,i))).state={fontSize:"",letterSpacing:""},e}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&h3(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=h1(t,hV),i=this.props,o=i.viewBox,a=h1(i,hY);return!tc(r,o)||!tc(n,a)||!tc(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,i,o,a,c=this.props,u=c.x,l=c.y,s=c.width,f=c.height,p=c.orientation,h=c.tickSize,d=c.mirror,y=c.tickMargin,v=d?-1:1,m=t.tickSize||h,b=q(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(i=l+!d*f)-v*m)-v*y,o=b;break;case"left":n=i=t.coordinate,o=(e=(r=u+!d*s)-v*m)-v*y,a=b;break;case"right":n=i=t.coordinate,o=(e=(r=u+d*s)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(i=l+d*f)+v*m)+v*y,o=b}return{line:{x1:e,y1:n,x2:r,y2:i},tick:{x:o,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,i=t.height,o=t.orientation,c=t.mirror,u=t.axisLine,l=h0(h0(h0({},t_(this.props,!1)),t_(u,!1)),{},{fill:"none"});if("top"===o||"bottom"===o){var s=+("top"===o&&!c||"bottom"===o&&c);l=h0(h0({},l),{},{x1:e,y1:r+s*i,x2:e+n,y2:r+s*i})}else{var f=+("left"===o&&!c||"right"===o&&c);l=h0(h0({},l),{},{x1:e+f*n,y1:r,x2:e+f*n,y2:r+i})}return a().createElement("line",hQ({},l,{className:(0,k.A)("recharts-cartesian-axis-line",z()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var i=this,o=this.props,c=o.tickLine,u=o.stroke,l=o.tick,s=o.tickFormatter,f=o.unit,p=hH(h0(h0({},this.props),{},{ticks:t}),e,r),h=this.getTickTextAnchor(),d=this.getTickVerticalAnchor(),y=t_(this.props,!1),v=t_(l,!1),m=h0(h0({},y),{},{fill:"none"},t_(c,!1)),b=p.map(function(t,e){var r=i.getTickLineCoord(t),o=r.line,b=r.tick,g=h0(h0(h0(h0({textAnchor:h,verticalAnchor:d},y),{},{stroke:"none",fill:u},v),b),{},{index:e,payload:t,visibleTicksCount:p.length,tickFormatter:s});return a().createElement(tV,hQ({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},td(i.props,t,e)),c&&a().createElement("line",hQ({},m,o,{className:(0,k.A)("recharts-cartesian-axis-tick-line",z()(c,"className"))})),l&&n.renderTickItem(l,g,"".concat(tn()(s)?s(t.value,e):t.value).concat(f||"")))});return a().createElement("g",{className:"recharts-cartesian-axis-ticks"},b)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,i=e.height,o=e.ticksGenerator,c=e.className;if(e.hide)return null;var u=this.props,l=u.ticks,s=h1(u,hZ),f=l;return(tn()(o)&&(f=o(l&&l.length>0?this.props:s)),n<=0||i<=0||!f||!f.length)?null:a().createElement(tV,{className:(0,k.A)("recharts-cartesian-axis",c),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(f,this.state.fontSize,this.state.letterSpacing),sf.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){var n;return a().isValidElement(t)?a().cloneElement(t,e):tn()(t)?t(e):a().createElement(ip,hQ({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&h2(n.prototype,e),r&&h2(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);function h9(t){return(h9="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}h6(h7,"displayName","CartesianAxis"),h6(h7,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});function dt(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(dt=function(){return!!t})()}function de(t){return(de=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dr(t,e){return(dr=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dn(t,e,r){return(e=di(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function di(t){var e=function(t,e){if("object"!=h9(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=h9(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==h9(e)?e:e+""}function da(){return(da=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dc(t){var e=t.xAxisId,r=f4(),n=f3(),i=f1(e);return null==i?null:a().createElement(h7,da({},i,{className:(0,k.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return lv(t,!0)}}))}var du=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=de(t),function(t,e){if(e&&("object"===h9(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,dt()?Reflect.construct(t,e||[],de(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dr(r,t),e=[{key:"render",value:function(){return a().createElement(dc,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,di(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);function dl(t){return(dl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}dn(du,"displayName","XAxis"),dn(du,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});function ds(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ds=function(){return!!t})()}function df(t){return(df=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function dp(t,e){return(dp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function dh(t,e,r){return(e=dd(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function dd(t){var e=function(t,e){if("object"!=dl(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dl(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dl(e)?e:e+""}function dy(){return(dy=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var dv=function(t){var e=t.yAxisId,r=f4(),n=f3(),i=f5(e);return null==i?null:a().createElement(h7,dy({},i,{className:(0,k.A)("recharts-".concat(i.axisType," ").concat(i.axisType),i.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return lv(t,!0)}}))},dm=function(t){var e;function r(){var t,e;if(!(this instanceof r))throw TypeError("Cannot call a class as a function");return t=r,e=arguments,t=df(t),function(t,e){if(e&&("object"===dl(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,ds()?Reflect.construct(t,e||[],df(this).constructor):t.apply(this,e))}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return r.prototype=Object.create(t&&t.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),t&&dp(r,t),e=[{key:"render",value:function(){return a().createElement(dv,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,dd(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(a().Component);dh(dm,"displayName","YAxis"),dh(dm,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var db=function(t){var e=t.chartName,r=t.GraphicalChild,n=t.defaultTooltipEventType,i=void 0===n?"axis":n,c=t.validateTooltipEventTypes,u=void 0===c?["axis"]:c,l=t.axisComponents,s=t.legendContent,f=t.formatAxisMap,p=t.defaultProps,h=function(t,e){var r=e.graphicalItems,n=e.stackGroups,i=e.offset,o=e.updateId,a=e.dataStartIndex,c=e.dataEndIndex,u=t.barSize,s=t.layout,f=t.barGap,p=t.barCategoryGap,h=t.maxBarSize,d=hU(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=tg(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=hT(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:c}),x=void 0!==r.type.defaultProps?hj(hj({},r.type.defaultProps),r.props):r.props,w=x.dataKey,O=x.maxBarSize,j=x["".concat(y,"Id")],S=x["".concat(v,"Id")],P=l.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],i=x["".concat(r.axisType,"Id")];n&&n[i]||"zAxis"===r.axisType||tF(!1);var o=n[i];return hj(hj({},t),{},hS(hS({},r.axisType,o),"".concat(r.axisType,"Ticks"),lv(o)))},{}),A=P[v],E=P["".concat(v,"Ticks")],_=n&&n[j]&&n[j].hasStack&&lM(r,n[j].stackGroups),M=tg(r.type).indexOf("Bar")>=0,k=lI(A,E),T=[],N=m&&lu({barSize:u,stackGroups:n,totalSize:"xAxis"===v?P[v].width:"yAxis"===v?P[v].height:void 0});if(M){var C,I,D=te()(O)?h:O,B=null!=(C=null!=(I=lI(A,E,!0))?I:D)?C:0;T=ll({barGap:f,barCategoryGap:p,bandSize:B!==k?B:k,sizeList:N[S],maxBarSize:D}),B!==k&&(T=T.map(function(t){return hj(hj({},t),{},{position:hj(hj({},t.position),{},{offset:t.position.offset-B/2})})}))}var R=r&&r.type&&r.type.getComposedData;R&&b.push({props:hj(hj({},R(hj(hj({},P),{},{displayedData:g,props:t,dataKey:w,item:r,bandSize:k,barPosition:T,offset:i,stackedData:_,layout:s,dataStartIndex:a,dataEndIndex:c}))),{},hS(hS(hS({key:r.key||"item-".concat(d)},y,P[y]),v,P[v]),"animationId",o)),childIndex:tO(t.children).indexOf(r),item:r})}),b},d=function(t,n){var i=t.props,o=t.dataStartIndex,a=t.dataEndIndex,c=t.updateId;if(!tP({props:i}))return null;var u=i.children,s=i.layout,p=i.stackOffset,d=i.data,y=i.reverseStackOrder,v=hU(s),m=v.numericAxisName,b=v.cateAxisName,g=tj(u,r),x=lP(d,g,"".concat(m,"Id"),"".concat(b,"Id"),p,y),w=l.reduce(function(t,e){var r="".concat(e.axisType,"Map");return hj(hj({},t),{},hS({},r,hR(i,hj(hj({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:o,dataEndIndex:a}))))},{}),O=h$(hj(hj({},w),{},{props:i,graphicalItems:g}),null==n?void 0:n.legendBBox);Object.keys(w).forEach(function(t){w[t]=f(i,w[t],O,t.replace("Map",""),e)});var j=hz(w["".concat(b,"Map")]),S=h(i,hj(hj({},w),{},{dataStartIndex:o,dataEndIndex:a,updateId:c,graphicalItems:g,stackGroups:x,offset:O}));return hj(hj({formattedGraphicalItems:S,graphicalItems:g,offset:O,stackGroups:x},j),w)},y=function(t){var r;function n(t){var r,i,c,u,l;if(!(this instanceof n))throw TypeError("Cannot call a class as a function");return u=n,l=[t],u=hm(u),hS(c=function(t,e){if(e&&("object"===hp(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");var r=t;if(void 0===r)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return r}(this,hv()?Reflect.construct(u,l||[],hm(this).constructor):u.apply(this,l)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),hS(c,"accessibilityManager",new pk),hS(c,"handleLegendBBoxUpdate",function(t){if(t){var e=c.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;c.setState(hj({legendBBox:t},d({props:c.props,dataStartIndex:r,dataEndIndex:n,updateId:i},hj(hj({},c.state),{},{legendBBox:t}))))}}),hS(c,"handleReceiveSyncEvent",function(t,e,r){c.props.syncId===t&&(r!==c.eventEmitterSymbol||"function"==typeof c.props.syncMethod)&&c.applySyncEvent(e)}),hS(c,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==c.state.dataStartIndex||r!==c.state.dataEndIndex){var n=c.state.updateId;c.setState(function(){return hj({dataStartIndex:e,dataEndIndex:r},d({props:c.props,dataStartIndex:e,dataEndIndex:r,updateId:n},c.state))}),c.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),hS(c,"handleMouseEnter",function(t){var e=c.getMouseInfo(t);if(e){var r=hj(hj({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseEnter;tn()(n)&&n(r,t)}}),hS(c,"triggeredAfterMouseMove",function(t){var e=c.getMouseInfo(t),r=e?hj(hj({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};c.setState(r),c.triggerSyncEvent(r);var n=c.props.onMouseMove;tn()(n)&&n(r,t)}),hS(c,"handleItemMouseEnter",function(t){c.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),hS(c,"handleItemMouseLeave",function(){c.setState(function(){return{isTooltipActive:!1}})}),hS(c,"handleMouseMove",function(t){t.persist(),c.throttleTriggeredAfterMouseMove(t)}),hS(c,"handleMouseLeave",function(t){c.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};c.setState(e),c.triggerSyncEvent(e);var r=c.props.onMouseLeave;tn()(r)&&r(e,t)}),hS(c,"handleOuterEvent",function(t){var e,r,n=tN(t),i=z()(c.props,"".concat(n));n&&tn()(i)&&i(null!=(e=/.*touch.*/i.test(n)?c.getMouseInfo(t.changedTouches[0]):c.getMouseInfo(t))?e:{},t)}),hS(c,"handleClick",function(t){var e=c.getMouseInfo(t);if(e){var r=hj(hj({},e),{},{isTooltipActive:!0});c.setState(r),c.triggerSyncEvent(r);var n=c.props.onClick;tn()(n)&&n(r,t)}}),hS(c,"handleMouseDown",function(t){var e=c.props.onMouseDown;tn()(e)&&e(c.getMouseInfo(t),t)}),hS(c,"handleMouseUp",function(t){var e=c.props.onMouseUp;tn()(e)&&e(c.getMouseInfo(t),t)}),hS(c,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),hS(c,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseDown(t.changedTouches[0])}),hS(c,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&c.handleMouseUp(t.changedTouches[0])}),hS(c,"handleDoubleClick",function(t){var e=c.props.onDoubleClick;tn()(e)&&e(c.getMouseInfo(t),t)}),hS(c,"handleContextMenu",function(t){var e=c.props.onContextMenu;tn()(e)&&e(c.getMouseInfo(t),t)}),hS(c,"triggerSyncEvent",function(t){void 0!==c.props.syncId&&pP.emit(pA,c.props.syncId,t,c.eventEmitterSymbol)}),hS(c,"applySyncEvent",function(t){var e=c.props,r=e.layout,n=e.syncMethod,i=c.state.updateId,o=t.dataStartIndex,a=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)c.setState(hj({dataStartIndex:o,dataEndIndex:a},d({props:c.props,dataStartIndex:o,dataEndIndex:a,updateId:i},c.state)));else if(void 0!==t.activeTooltipIndex){var u=t.chartX,l=t.chartY,s=t.activeTooltipIndex,f=c.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)s=n(h,t);else if("value"===n){s=-1;for(var y=0;y<h.length;y++)if(h[y].value===t.activeLabel){s=y;break}}var v=hj(hj({},p),{},{x:p.left,y:p.top}),m=Math.min(u,v.x+v.width),b=Math.min(l,v.y+v.height),g=h[s]&&h[s].value,x=hC(c.state,c.props.data,s),w=h[s]?{x:"horizontal"===r?h[s].coordinate:m,y:"horizontal"===r?b:h[s].coordinate}:h_;c.setState(hj(hj({},t),{},{activeLabel:g,activeCoordinate:w,activePayload:x,activeTooltipIndex:s}))}else c.setState(t)}),hS(c,"renderCursor",function(t){var r,n=c.state,i=n.isTooltipActive,o=n.activeCoordinate,u=n.activePayload,l=n.offset,s=n.activeTooltipIndex,f=n.tooltipAxisBandSize,p=c.getTooltipEventType(),h=null!=(r=t.props.active)?r:i,d=c.props.layout,y=t.key||"_recharts-cursor";return a().createElement(hl,{key:y,activeCoordinate:o,activePayload:u,activeTooltipIndex:s,chartName:e,element:t,isActive:h,layout:d,offset:l,tooltipAxisBandSize:f,tooltipEventType:p})}),hS(c,"renderPolarAxis",function(t,e,r){var n=z()(t,"type.axisType"),i=z()(c.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?hj(hj({},a),t.props):t.props,l=i&&i[u["".concat(n,"Id")]];return(0,o.cloneElement)(t,hj(hj({},l),{},{className:(0,k.A)(n,l.className),key:t.key||"".concat(e,"-").concat(r),ticks:lv(l,!0)}))}),hS(c,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,i=e.polarRadius,a=c.state,u=a.radiusAxisMap,l=a.angleAxisMap,s=V(u),f=V(l),p=f.cx,h=f.cy,d=f.innerRadius,y=f.outerRadius;return(0,o.cloneElement)(t,{polarAngles:Array.isArray(n)?n:lv(f,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:lv(s,!0).map(function(t){return t.coordinate}),cx:p,cy:h,innerRadius:d,outerRadius:y,key:t.key||"polar-grid",radialLines:r})}),hS(c,"renderLegend",function(){var t=c.state.formattedGraphicalItems,e=c.props,r=e.children,n=e.width,i=e.height,a=c.props.margin||{},u=u8({children:r,formattedGraphicalItems:t,legendWidth:n-(a.left||0)-(a.right||0),legendContent:s});if(!u)return null;var l=u.item,f=hy(u,hs);return(0,o.cloneElement)(l,hj(hj({},f),{},{chartWidth:n,chartHeight:i,margin:a,onBBoxUpdate:c.handleLegendBBoxUpdate}))}),hS(c,"renderTooltip",function(){var t,e=c.props,r=e.children,n=e.accessibilityLayer,i=tS(r,eg);if(!i)return null;var a=c.state,u=a.isTooltipActive,l=a.activeCoordinate,s=a.activePayload,f=a.activeLabel,p=a.offset,h=null!=(t=i.props.active)?t:u;return(0,o.cloneElement)(i,{viewBox:hj(hj({},p),{},{x:p.left,y:p.top}),active:h,label:f,payload:h?s:[],coordinate:l,accessibilityLayer:n})}),hS(c,"renderBrush",function(t){var e=c.props,r=e.margin,n=e.data,i=c.state,a=i.offset,u=i.dataStartIndex,l=i.dataEndIndex,s=i.updateId;return(0,o.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:lb(c.handleBrushChange,t.props.onChange),data:n,x:q(t.props.x)?t.props.x:a.left,y:q(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:q(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:l,updateId:"brush-".concat(s)})}),hS(c,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=c.clipPathId,i=c.state,a=i.xAxisMap,u=i.yAxisMap,l=i.offset,s=t.type.defaultProps||{},f=t.props,p=f.xAxisId,h=void 0===p?s.xAxisId:p,d=f.yAxisId,y=void 0===d?s.yAxisId:d;return(0,o.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[h],yAxis:u[y],viewBox:{x:l.left,y:l.top,width:l.width,height:l.height},clipPathId:n})}),hS(c,"renderActivePoints",function(t){var e=t.item,r=t.activePoint,i=t.basePoint,o=t.childIndex,a=t.isRange,c=[],u=e.props.key,l=void 0!==e.item.type.defaultProps?hj(hj({},e.item.type.defaultProps),e.item.props):e.item.props,s=l.activeDot,f=hj(hj({index:o,dataKey:l.dataKey,cx:r.x,cy:r.y,r:4,fill:lc(e.item),strokeWidth:2,stroke:"#fff",payload:r.payload,value:r.value},t_(s,!1)),th(s));return c.push(n.renderActiveDot(s,f,"".concat(u,"-activePoint-").concat(o))),i?c.push(n.renderActiveDot(s,hj(hj({},f),{},{cx:i.x,cy:i.y}),"".concat(u,"-basePoint-").concat(o))):a&&c.push(null),c}),hS(c,"renderGraphicChild",function(t,e,r){var n=c.filterFormatItem(t,e,r);if(!n)return null;var i=c.getTooltipEventType(),a=c.state,u=a.isTooltipActive,l=a.tooltipAxis,s=a.activeTooltipIndex,f=a.activeLabel,p=tS(c.props.children,eg),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?hj(hj({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,x=m.activeBar,w=m.activeShape,O=!!(!g&&u&&p&&(b||x||w)),j={};"axis"!==i&&p&&"click"===p.props.trigger?j={onClick:lb(c.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(j={onMouseLeave:lb(c.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:lb(c.handleItemMouseEnter,t.props.onMouseEnter)});var S=(0,o.cloneElement)(t,hj(hj({},n.props),j));if(O)if(s>=0){if(l.dataKey&&!l.allowDuplicatedCategory){var P="function"==typeof l.dataKey?function(t){return"function"==typeof l.dataKey?l.dataKey(t.payload):null}:"payload.".concat(l.dataKey.toString());E=K(d,P,f),_=y&&v&&K(v,P,f)}else E=null==d?void 0:d[s],_=y&&v&&v[s];if(w||x){var A=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,o.cloneElement)(t,hj(hj(hj({},n.props),j),{},{activeIndex:A})),null,null]}if(!te()(E))return[S].concat(hg(c.renderActivePoints({item:n,activePoint:E,basePoint:_,childIndex:s,isRange:y})))}else{var E,_,M,k=(null!=(M=c.getItemByXY(c.state.activeCoordinate))?M:{graphicalItem:S}).graphicalItem,T=k.item,N=void 0===T?t:T,C=k.childIndex,I=hj(hj(hj({},n.props),j),{},{activeIndex:C});return[(0,o.cloneElement)(N,I),null,null]}return y?[S,null,null]:[S,null]}),hS(c,"renderCustomized",function(t,e,r){return(0,o.cloneElement)(t,hj(hj({key:"recharts-customized-".concat(r)},c.props),c.state))}),hS(c,"renderMap",{CartesianGrid:{handler:hM,once:!0},ReferenceArea:{handler:c.renderReferenceElement},ReferenceLine:{handler:hM},ReferenceDot:{handler:c.renderReferenceElement},XAxis:{handler:hM},YAxis:{handler:hM},Brush:{handler:c.renderBrush,once:!0},Bar:{handler:c.renderGraphicChild},Line:{handler:c.renderGraphicChild},Area:{handler:c.renderGraphicChild},Radar:{handler:c.renderGraphicChild},RadialBar:{handler:c.renderGraphicChild},Scatter:{handler:c.renderGraphicChild},Pie:{handler:c.renderGraphicChild},Funnel:{handler:c.renderGraphicChild},Tooltip:{handler:c.renderCursor,once:!0},PolarGrid:{handler:c.renderPolarGrid,once:!0},PolarAngleAxis:{handler:c.renderPolarAxis},PolarRadiusAxis:{handler:c.renderPolarAxis},Customized:{handler:c.renderCustomized}}),c.clipPathId="".concat(null!=(r=t.id)?r:G("recharts"),"-clip"),c.throttleTriggeredAfterMouseMove=N()(c.triggeredAfterMouseMove,null!=(i=t.throttleDelay)?i:1e3/60),c.state={},c}if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");return n.prototype=Object.create(t&&t.prototype,{constructor:{value:n,writable:!0,configurable:!0}}),Object.defineProperty(n,"prototype",{writable:!1}),t&&hb(n,t),r=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!=(t=this.props.margin.left)?t:0,top:null!=(e=this.props.margin.top)?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,i=t.layout,o=tS(e,eg);if(o){var a=o.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var c=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,u=hC(this.state,r,a,c),l=this.state.tooltipTicks[a].coordinate,s=(this.state.offset.top+n)/2,f="horizontal"===i?{x:l,y:s}:{y:l,x:s},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=hj(hj({},f),p.props.points[a].tooltipPosition),u=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:c,activePayload:u,activeCoordinate:f};this.setState(h),this.renderCursor(o),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!=(r=this.props.margin.left)?r:0,top:null!=(n=this.props.margin.top)?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){tM([tS(t.children,eg)],[tS(this.props.children,eg)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=tS(this.props.children,eg);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return u.indexOf(e)>=0?e:i}return i}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n={top:r.top+window.scrollY-document.documentElement.clientTop,left:r.left+window.scrollX-document.documentElement.clientLeft},i={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},o=r.width/e.offsetWidth||1,a=this.inRange(i.chartX,i.chartY,o);if(!a)return null;var c=this.state,u=c.xAxisMap,l=c.yAxisMap,s=this.getTooltipEventType(),f=hI(this.state,this.props.data,this.props.layout,a);if("axis"!==s&&u&&l){var p=V(u).scale,h=V(l).scale,d=p&&p.invert?p.invert(i.chartX):null,y=h&&h.invert?h.invert(i.chartY):null;return hj(hj({},i),{},{xValue:d,yValue:y},f)}return f?hj(hj({},i),f):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,i=t/r,o=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return i>=a.left&&i<=a.left+a.width&&o>=a.top&&o<=a.top+a.height?{x:i,y:o}:null}var c=this.state,u=c.angleAxisMap,l=c.radiusAxisMap;return u&&l?st({x:i,y:o},V(u)):null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=tS(t,eg),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),hj(hj({},th(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){pP.on(pA,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){pP.removeListener(pA,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,i=0,o=n.length;i<o;i++){var a=n[i];if(a.item===t||a.props.key===t.key||e===tg(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,i=e.height,o=e.width;return a().createElement("defs",null,a().createElement("clipPath",{id:t},a().createElement("rect",{x:r,y:n,height:i,width:o})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hd(e,2),n=r[0],i=r[1];return hj(hj({},t),{},hS({},n,i.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=hd(e,2),n=r[0],i=r[1];return hj(hj({},t),{},hS({},n,i.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null==(e=this.state.xAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null==(e=this.state.yAxisMap)||null==(e=e[t])?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var i=0,o=r.length;i<o;i++){var a=r[i],c=a.props,u=a.item,l=void 0!==u.type.defaultProps?hj(hj({},u.type.defaultProps),u.props):u.props,s=tg(u.type);if("Bar"===s){var f=(c.data||[]).find(function(e){return n_(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===s){var p=(c.data||[]).find(function(e){return st(t,e)});if(p)return{graphicalItem:a,payload:p}}else if(s4(a,n)||s3(a,n)||s6(a,n)){var h=function(t){var e,r,n,i=t.activeTooltipItem,o=t.graphicalItem,a=t.itemData,c=(s4(o,i)?e="trapezoids":s3(o,i)?e="sectors":s6(o,i)&&(e="points"),e),u=s4(o,i)?null==(r=i.tooltipPayload)||null==(r=r[0])||null==(r=r.payload)?void 0:r.payload:s3(o,i)?null==(n=i.tooltipPayload)||null==(n=n[0])||null==(n=n.payload)?void 0:n.payload:s6(o,i)?i.payload:{},l=a.filter(function(t,e){var r=ue()(u,t),n=o.props[c].filter(function(t){var e;return(s4(o,i)?e=s8:s3(o,i)?e=s7:s6(o,i)&&(e=s9),e)(t,i)}),a=o.props[c].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(l[l.length-1])}({graphicalItem:a,activeTooltipItem:n,itemData:l.data}),d=void 0===l.activeIndex?h:l.activeIndex;return{graphicalItem:hj(hj({},a),{},{childIndex:d}),payload:s6(a,n)?l.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!tP(this))return null;var n=this.props,i=n.children,o=n.className,c=n.width,u=n.height,l=n.style,s=n.compact,f=n.title,p=n.desc,h=t_(hy(n,hf),!1);if(s)return a().createElement(f0,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement(tX,hh({},h,{width:c,height:u,title:f,desc:p}),this.renderClipPath(),tT(i,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!=(t=this.props.tabIndex)?t:0,h.role=null!=(e=this.props.role)?e:"application",h.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return a().createElement(f0,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},a().createElement("div",hh({className:(0,k.A)("recharts-wrapper",o),style:hj({position:"relative",cursor:"default",width:c,height:u},l)},d,{ref:function(t){r.container=t}}),a().createElement(tX,hh({},h,{width:c,height:u,title:f,desc:p,style:hE}),this.renderClipPath(),tT(i,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,hP(n.key),n)}}(n.prototype,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(o.Component);hS(y,"displayName",e),hS(y,"defaultProps",hj({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},p)),hS(y,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,i=t.children,o=t.width,a=t.height,c=t.layout,u=t.stackOffset,l=t.margin,s=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=hL(t);return hj(hj(hj({},p),{},{updateId:0},d(hj(hj({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:i})}if(r!==e.prevDataKey||n!==e.prevData||o!==e.prevWidth||a!==e.prevHeight||c!==e.prevLayout||u!==e.prevStackOffset||!tc(l,e.prevMargin)){var h=hL(t),y={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},v=hj(hj({},hI(e,n,c)),{},{updateId:e.updateId+1}),m=hj(hj(hj({},h),y),v);return hj(hj(hj({},m),d(hj({props:t},m),e)),{},{prevDataKey:r,prevData:n,prevWidth:o,prevHeight:a,prevLayout:c,prevStackOffset:u,prevMargin:l,prevChildren:i})}if(!tM(i,e.prevChildren)){var b,g,x,w,O=tS(i,l1),j=O&&null!=(b=null==(g=O.props)?void 0:g.startIndex)?b:s,S=O&&null!=(x=null==(w=O.props)?void 0:w.endIndex)?x:f,P=te()(n)||j!==s||S!==f?e.updateId+1:e.updateId;return hj(hj({updateId:P},d(hj(hj({props:t},e),{},{updateId:P,dataStartIndex:j,dataEndIndex:S}),e)),{},{prevChildren:i,dataStartIndex:j,dataEndIndex:S})}return null}),hS(y,"renderActiveDot",function(t,e,r){var n;return n=(0,o.isValidElement)(t)?(0,o.cloneElement)(t,e):tn()(t)?t(e):a().createElement(rf,e),a().createElement(tV,{className:"recharts-active-dot",key:r},n)});var v=(0,o.forwardRef)(function(t,e){return a().createElement(y,hh({},t,{ref:e}))});return v.displayName=y.displayName,v}({chartName:"BarChart",GraphicalChild:fg,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:du},{axisType:"yAxis",AxisComp:dm}],formatAxisMap:function(t,e,r,n,i){var o=t.width,a=t.height,c=t.layout,u=t.children,l=Object.keys(e),s={left:r.left,leftMirror:r.left,right:o-r.right,rightMirror:o-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!tS(u,fg);return l.reduce(function(o,a){var u,l,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,w=y.reversed,O="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort(Q);if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(u=A*E/2),"no-gap"===y.padding){var _=H(t.barCategoryGap,A*E),M=A*E/2;u=M-_-(M-_)/E*_}}}l="xAxis"===n?[r.left+(g.left||0)+(u||0),r.left+r.width-(g.right||0)-(u||0)]:"yAxis"===n?"horizontal"===c?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(u||0),r.top+r.height-(g.bottom||0)-(u||0)]:y.range,w&&(l=[l[1],l[0]]);var k=lg(y,i,f),T=k.scale,N=k.realScaleType;T.domain(m).range(l),lx(T);var C=lA(T,fj(fj({},y),{},{realScaleType:N}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=s[O]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=s[O]-d*y.width,h=r.top);var I=fj(fj(fj({},y),C),{},{realScaleType:N,x:p,y:h,scale:T,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return I.bandSize=lI(I,C),y.hide||"xAxis"!==n?y.hide||(s[O]+=(d?-1:1)*I.width):s[O]+=(d?-1:1)*I.height,fj(fj({},o),{},fS({},a,I))},{})}}),dg=["x1","y1","x2","y2","key"],dx=["offset"];function dw(t){return(dw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function dO(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function dj(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?dO(Object(r),!0).forEach(function(e){var n,i,o;n=t,i=e,o=r[e],(i=function(t){var e=function(t,e){if("object"!=dw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=dw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==dw(e)?e:e+""}(i))in n?Object.defineProperty(n,i,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[i]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):dO(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function dS(){return(dS=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function dP(t,e){if(null==t)return{};var r,n,i=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);for(n=0;n<o.length;n++)r=o[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(i[r]=t[r])}return i}var dA=function(t){var e=t.fill;if(!e||"none"===e)return null;var r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,u=t.ry;return a().createElement("rect",{x:n,y:i,ry:u,width:o,height:c,stroke:"none",fill:e,fillOpacity:r,className:"recharts-cartesian-grid-bg"})};function dE(t,e){var r;if(a().isValidElement(t))r=a().cloneElement(t,e);else if(tn()(t))r=t(e);else{var n=e.x1,i=e.y1,o=e.x2,c=e.y2,u=e.key,l=t_(dP(e,dg),!1),s=(l.offset,dP(l,dx));r=a().createElement("line",dS({},s,{x1:n,y1:i,x2:o,y2:c,fill:"none",key:u}))}return r}function d_(t){var e=t.x,r=t.width,n=t.horizontal,i=void 0===n||n,o=t.horizontalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return dE(i,dj(dj({},t),{},{x1:e,y1:n,x2:e+r,y2:n,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-horizontal"},c)}function dM(t){var e=t.y,r=t.height,n=t.vertical,i=void 0===n||n,o=t.verticalPoints;if(!i||!o||!o.length)return null;var c=o.map(function(n,o){return dE(i,dj(dj({},t),{},{x1:n,y1:e,x2:n,y2:e+r,key:"line-".concat(o),index:o}))});return a().createElement("g",{className:"recharts-cartesian-grid-vertical"},c)}function dk(t){var e=t.horizontalFill,r=t.fillOpacity,n=t.x,i=t.y,o=t.width,c=t.height,u=t.horizontalPoints,l=t.horizontal;if(!(void 0===l||l)||!e||!e.length)return null;var s=u.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,u){var l=s[u+1]?s[u+1]-t:i+c-t;if(l<=0)return null;var f=u%e.length;return a().createElement("rect",{key:"react-".concat(u),y:t,x:n,height:l,width:o,stroke:"none",fill:e[f],fillOpacity:r,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-horizontal"},f)}function dT(t){var e=t.vertical,r=t.verticalFill,n=t.fillOpacity,i=t.x,o=t.y,c=t.width,u=t.height,l=t.verticalPoints;if(!(void 0===e||e)||!r||!r.length)return null;var s=l.map(function(t){return Math.round(t+i-i)}).sort(function(t,e){return t-e});i!==s[0]&&s.unshift(0);var f=s.map(function(t,e){var l=s[e+1]?s[e+1]-t:i+c-t;if(l<=0)return null;var f=e%r.length;return a().createElement("rect",{key:"react-".concat(e),x:t,y:o,width:l,height:u,stroke:"none",fill:r[f],fillOpacity:n,className:"recharts-cartesian-grid-bg"})});return a().createElement("g",{className:"recharts-cartesian-gridstripes-vertical"},f)}var dN=function(t,e){var r=t.xAxis,n=t.width,i=t.height,o=t.offset;return ly(hH(dj(dj(dj({},h7.defaultProps),r),{},{ticks:lv(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.left,o.left+o.width,e)},dC=function(t,e){var r=t.yAxis,n=t.width,i=t.height,o=t.offset;return ly(hH(dj(dj(dj({},h7.defaultProps),r),{},{ticks:lv(r,!0),viewBox:{x:0,y:0,width:n,height:i}})),o.top,o.top+o.height,e)},dI={horizontal:!0,vertical:!0,stroke:"#ccc",fill:"none",verticalFill:[],horizontalFill:[]};function dD(t){var e,r,n,i,c,u,l=f4(),s=f3(),f=(0,o.useContext)(fZ),p=dj(dj({},t),{},{stroke:null!=(e=t.stroke)?e:dI.stroke,fill:null!=(r=t.fill)?r:dI.fill,horizontal:null!=(n=t.horizontal)?n:dI.horizontal,horizontalFill:null!=(i=t.horizontalFill)?i:dI.horizontalFill,vertical:null!=(c=t.vertical)?c:dI.vertical,verticalFill:null!=(u=t.verticalFill)?u:dI.verticalFill,x:q(t.x)?t.x:f.left,y:q(t.y)?t.y:f.top,width:q(t.width)?t.width:f.width,height:q(t.height)?t.height:f.height}),h=p.x,d=p.y,y=p.width,v=p.height,m=p.syncWithTicks,b=p.horizontalValues,g=p.verticalValues,x=V((0,o.useContext)(fH)),w=f2();if(!q(y)||y<=0||!q(v)||v<=0||!q(h)||h!==+h||!q(d)||d!==+d)return null;var O=p.verticalCoordinatesGenerator||dN,j=p.horizontalCoordinatesGenerator||dC,S=p.horizontalPoints,P=p.verticalPoints;if((!S||!S.length)&&tn()(j)){var A=b&&b.length,E=j({yAxis:w?dj(dj({},w),{},{ticks:A?b:w.ticks}):void 0,width:l,height:s,offset:f},!!A||m);J(Array.isArray(E),"horizontalCoordinatesGenerator should return Array but instead it returned [".concat(dw(E),"]")),Array.isArray(E)&&(S=E)}if((!P||!P.length)&&tn()(O)){var _=g&&g.length,M=O({xAxis:x?dj(dj({},x),{},{ticks:_?g:x.ticks}):void 0,width:l,height:s,offset:f},!!_||m);J(Array.isArray(M),"verticalCoordinatesGenerator should return Array but instead it returned [".concat(dw(M),"]")),Array.isArray(M)&&(P=M)}return a().createElement("g",{className:"recharts-cartesian-grid"},a().createElement(dA,{fill:p.fill,fillOpacity:p.fillOpacity,x:p.x,y:p.y,width:p.width,height:p.height,ry:p.ry}),a().createElement(d_,dS({},p,{offset:f,horizontalPoints:S,xAxis:x,yAxis:w})),a().createElement(dM,dS({},p,{offset:f,verticalPoints:P,xAxis:x,yAxis:w})),a().createElement(dk,dS({},p,{horizontalPoints:S})),a().createElement(dT,dS({},p,{verticalPoints:P})))}function dB(){let[t,e]=(0,o.useState)("avanzamento"),[r,n]=(0,o.useState)("month"),[a,k]=(0,o.useState)(null),[T,N]=(0,o.useState)(null),[C,I]=(0,o.useState)(null),[D,B]=(0,o.useState)(null),[R,z]=(0,o.useState)(!0),[L,U]=(0,o.useState)(""),{user:$,isLoading:F}=(0,p.A)(),{cantiereId:q,cantiere:W,isValidCantiere:X,isLoading:G,error:H}=(0,h.jV)(),V=()=>{X&&q?(console.log("\uD83C\uDFD7️ ReportsPage: Refresh report per cantiere:",q),z(!0),U(""),B(null),N(null),I(null),k(null),loadAllReports()):console.warn("\uD83C\uDFD7️ ReportsPage: Impossibile fare refresh, nessun cantiere valido")},Y=async(t,e="pdf")=>{try{let e,r=W?.id_cantiere;if(!r)return;switch(t){case"progress":e=await y.ug.getReportProgress(r);break;case"boq":e=await y.ug.getReportBOQ(r);break;case"utilizzo-bobine":e=await y.ug.getReportUtilizzoBobine(r);break;default:return}e.data.file_url&&window.open(e.data.file_url,"_blank")}catch(t){}};return(0,i.jsx)(d.u,{children:(0,i.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,i.jsxs)("div",{className:"max-w-[90%] mx-auto space-y-6",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold text-slate-900",children:"Reports"}),(0,i.jsx)("p",{className:"text-slate-600 mt-1",children:W?.commessa?`Cantiere: ${W.commessa}`:"Seleziona un cantiere per visualizzare i report"})]}),(0,i.jsx)("div",{className:"flex gap-2",children:(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:V,children:[(0,i.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Aggiorna"]})})]}),R||F?(0,i.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsx)(m.A,{className:"h-6 w-6 animate-spin"}),(0,i.jsx)("span",{children:"Caricamento report..."})]})}):L?(0,i.jsxs)("div",{className:"p-6 border border-amber-200 rounded-lg bg-amber-50",children:[(0,i.jsxs)("div",{className:"flex items-center mb-4",children:[(0,i.jsx)(b.A,{className:"h-5 w-5 text-amber-600 mr-2"}),(0,i.jsx)("span",{className:"text-amber-800 font-medium",children:L.includes("Nessun cantiere selezionato")||L.includes("Cantiere non selezionato")?"Cantiere non selezionato":L.includes("timeout")||L.includes("Timeout")?"Timeout API":"Errore caricamento report"})]}),(0,i.jsx)("p",{className:"text-amber-700 mb-4",children:L}),L.includes("timeout")||L.includes("Timeout")?(0,i.jsx)("div",{className:"mb-4 p-3 bg-blue-50 border border-blue-200 rounded",children:(0,i.jsxs)("p",{className:"text-blue-800 text-sm",children:["\uD83D\uDCA1 ",(0,i.jsx)("strong",{children:"Suggerimento:"})," Le API stanno impiegando pi\xf9 tempo del previsto. Prova ad aggiornare la pagina o riprova tra qualche minuto."]})}):null,(0,i.jsxs)("div",{className:"flex gap-2",children:[(0,i.jsx)(u.$,{onClick:()=>window.location.href="/cantieri",className:"bg-amber-600 hover:bg-amber-700 text-white",children:"Gestisci Cantieri"}),(0,i.jsxs)(u.$,{variant:"outline",onClick:V,className:"border-amber-600 text-amber-700 hover:bg-amber-100",children:[(0,i.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Riprova"]})]})]}):(0,i.jsxs)(f.tU,{value:t,onValueChange:e,className:"w-full",children:[(0,i.jsxs)(f.j7,{className:"grid w-full grid-cols-4",children:[(0,i.jsxs)(f.Xi,{value:"avanzamento",className:"flex items-center gap-2",children:[(0,i.jsx)(g.A,{className:"h-4 w-4"}),"Avanzamento"]}),(0,i.jsxs)(f.Xi,{value:"boq",className:"flex items-center gap-2",children:[(0,i.jsx)(x.A,{className:"h-4 w-4"}),"BOQ"]}),(0,i.jsxs)(f.Xi,{value:"bobine",className:"flex items-center gap-2",children:[(0,i.jsx)(w.A,{className:"h-4 w-4"}),"Bobine"]}),(0,i.jsxs)(f.Xi,{value:"produttivita",className:"flex items-center gap-2",children:[(0,i.jsx)(O.A,{className:"h-4 w-4"}),"Produttivit\xe0"]})]}),(0,i.jsx)(f.av,{value:"avanzamento",className:"space-y-6",children:D?.content?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Totali"}),(0,i.jsx)(g.A,{className:"h-4 w-4 text-blue-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[D.content.metri_totali?.toLocaleString()||0,"m"]}),(0,i.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[D.content.totale_cavi||0," cavi totali"]})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-green-500",children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Posati"}),(0,i.jsx)(j.A,{className:"h-4 w-4 text-green-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[D.content.metri_posati?.toLocaleString()||0,"m"]}),(0,i.jsx)(s.k,{value:D.content.percentuale_avanzamento||0,className:"mt-2"}),(0,i.jsxs)("p",{className:"text-xs text-slate-500 mt-2",children:[D.content.percentuale_avanzamento?.toFixed(1)||0,"% completato"]})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Media Giornaliera"}),(0,i.jsx)(S.A,{className:"h-4 w-4 text-purple-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[D.content.media_giornaliera?.toFixed(1)||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"metri/giorno"}),(0,i.jsxs)("div",{className:"flex items-center mt-2",children:[(0,i.jsx)(P.A,{className:"h-3 w-3 text-purple-500 mr-1"}),(0,i.jsxs)("span",{className:"text-xs text-purple-600",children:[D.content.giorni_lavorativi_effettivi||0," giorni attivi"]})]})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento"}),(0,i.jsx)(A.A,{className:"h-4 w-4 text-orange-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:D.content.data_completamento||"N/A"}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"stima completamento"}),(0,i.jsxs)("div",{className:"flex items-center mt-2",children:[(0,i.jsx)(E.A,{className:"h-3 w-3 text-orange-500 mr-1"}),(0,i.jsxs)("span",{className:"text-xs text-orange-600",children:[D.content.giorni_stimati||0," giorni rimanenti"]})]})]})]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)(c.ZB,{children:"Posa Recente"}),(0,i.jsx)(c.BT,{children:"Ultimi 10 giorni di attivit\xe0"})]}),(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>Y("progress","pdf"),children:[(0,i.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"PDF"]})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)(tR,{width:"100%",height:300,children:(0,i.jsxs)(db,{data:D.content.posa_recente||[],children:[(0,i.jsx)(dD,{strokeDasharray:"3 3"}),(0,i.jsx)(du,{dataKey:"data"}),(0,i.jsx)(dm,{}),(0,i.jsx)(eg,{}),(0,i.jsx)(fg,{dataKey:"metri",fill:"#3b82f6",name:"Metri Posati"})]})})})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsx)(c.ZB,{children:"Stato Cavi"}),(0,i.jsx)(c.BT,{children:"Distribuzione per stato di avanzamento"})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Cavi Posati"}),(0,i.jsx)(l.E,{variant:"secondary",children:D.content.cavi_posati||0})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Cavi Rimanenti"}),(0,i.jsx)(l.E,{variant:"outline",children:D.content.cavi_rimanenti||0})]}),(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("span",{className:"text-sm font-medium",children:"Percentuale Cavi"}),(0,i.jsxs)(l.E,{variant:"default",children:[D.content.percentuale_cavi?.toFixed(1)||0,"%"]})]})]})})]})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(g.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato di avanzamento disponibile"})]})}),(0,i.jsx)(f.av,{value:"boq",className:"space-y-6",children:T?.error?(0,i.jsxs)("div",{className:"text-center py-12",children:[(0,i.jsx)(b.A,{className:"h-12 w-12 mx-auto mb-4 text-amber-500"}),(0,i.jsx)("h3",{className:"text-lg font-semibold text-slate-700 mb-2",children:"API BOQ Temporaneamente Non Disponibile"}),(0,i.jsx)("p",{className:"text-slate-500 mb-4",children:"Il servizio BOQ sta riscontrando problemi di performance."}),(0,i.jsx)("p",{className:"text-sm text-slate-400",children:"Stiamo lavorando per risolvere il problema."})]}):T?.content?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"\uD83D\uDCCB Bill of Quantities - Distinta Materiali"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Riepilogo completo dei materiali per tipologia di cavo"})]}),(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>Y("boq","excel"),children:[(0,i.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Excel"]})]}),T.content.metri_orfani&&T.content.metri_orfani.metri_orfani_totali>0&&(0,i.jsxs)(c.Zp,{className:"border-red-200 bg-red-50",children:[(0,i.jsx)(c.aR,{className:"pb-3",children:(0,i.jsxs)(c.ZB,{className:"text-red-700 flex items-center",children:[(0,i.jsx)(M.A,{className:"h-5 w-5 mr-2"}),"\uD83D\uDEA8 METRI POSATI SENZA TRACCIABILIT\xc0 BOBINA"]})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("p",{className:"text-red-800 font-medium mb-2",children:[(0,i.jsxs)("strong",{children:[T.content.metri_orfani.metri_orfani_totali,"m"]})," installati con BOBINA_VUOTA (",T.content.metri_orfani.num_cavi_orfani," cavi)"]}),(0,i.jsx)("div",{className:"text-sm text-red-700 space-y-1",children:Array.isArray(T.content.metri_orfani.dettaglio_per_categoria)?T.content.metri_orfani.dettaglio_per_categoria.map((t,e)=>(0,i.jsxs)("div",{children:["• ",(0,i.jsxs)("strong",{children:[t.tipologia," ",t.formazione]}),": ",t.metri_orfani,"m (",t.num_cavi," cavi)"]},e)):(0,i.jsx)("div",{children:"Dettaglio metri orfani non disponibile"})}),(0,i.jsx)("div",{className:"mt-3 p-3 bg-amber-50 border border-amber-200 rounded",children:(0,i.jsxs)("p",{className:"text-amber-800 text-sm",children:["⚠️ ",(0,i.jsx)("strong",{children:"NOTA:"})," I metri orfani NON sono inclusi nel calcolo acquisti. Prima di acquistare, verificare se questi metri possono essere associati a bobine esistenti."]})})]})]}),T.content.riepilogo&&(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-blue-500",children:[(0,i.jsx)(c.aR,{className:"pb-2",children:(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri da Acquistare"})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-blue-600",children:[T.content.riepilogo.totale_metri_mancanti?.toLocaleString()||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"per completamento progetto"})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-green-500",children:[(0,i.jsx)(c.aR,{className:"pb-2",children:(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Metri Residui"})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-green-600",children:[T.content.riepilogo.totale_metri_residui?.toLocaleString()||0,"m"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"disponibili in magazzino"})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-purple-500",children:[(0,i.jsx)(c.aR,{className:"pb-2",children:(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Completamento"})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-purple-600",children:[T.content.riepilogo.percentuale_completamento?.toFixed(1)||0,"%"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"progetto completato"})]})]}),(0,i.jsxs)(c.Zp,{className:"border-l-4 border-l-orange-500",children:[(0,i.jsx)(c.aR,{className:"pb-2",children:(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Categorie"})}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-orange-600",children:T.content.riepilogo.categorie_necessitano_acquisto||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500 mt-1",children:"necessitano acquisto"})]})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsx)(c.ZB,{children:"Distinta Materiali"}),(0,i.jsx)(c.BT,{children:"Fabbisogno materiali raggruppati per tipologia e formazione"})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Formazione"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Cavi"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Teorici"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Posati"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri da Posare"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Bobine"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Residui"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Mancanti"}),(0,i.jsx)("th",{className:"text-center p-2",children:"Acquisto"})]})}),(0,i.jsx)("tbody",{children:T.content.distinta_materiali?.map((t,e)=>(0,i.jsxs)("tr",{className:`border-b hover:bg-slate-50 ${t.ha_bobina_vuota?"bg-red-50":""}`,children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:t.tipologia}),(0,i.jsx)("td",{className:"p-2",children:t.formazione}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.num_cavi}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_teorici_totali?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_reali_posati?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_da_posare?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.num_bobine}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_residui?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right font-medium",children:t.metri_mancanti>0?(0,i.jsxs)("span",{className:"text-red-600",children:[t.metri_mancanti?.toLocaleString(),"m"]}):(0,i.jsx)("span",{className:"text-green-600",children:"0m"})}),(0,i.jsx)("td",{className:"p-2 text-center",children:t.necessita_acquisto?(0,i.jsx)(l.E,{variant:"destructive",children:"S\xec"}):(0,i.jsx)(l.E,{variant:"secondary",children:"No"})})]},e))})]})})})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsx)(c.ZB,{children:"Bobine Disponibili"}),(0,i.jsx)(c.BT,{children:"Inventario bobine per tipologia"})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Formazione"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Numero Bobine"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Disponibili"})]})}),(0,i.jsx)("tbody",{children:T.content.bobine_per_tipo?.map((t,e)=>(0,i.jsxs)("tr",{className:"border-b hover:bg-slate-50",children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:t.tipologia}),(0,i.jsx)("td",{className:"p-2",children:t.formazione}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.num_bobine}),(0,i.jsx)("td",{className:"p-2 text-right",children:(0,i.jsx)("span",{className:t.metri_disponibili>0?"text-green-600":"text-red-600",children:t.metri_disponibili?.toLocaleString()})})]},e))})]})})})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(x.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato BOQ disponibile"})]})}),(0,i.jsx)(f.av,{value:"bobine",className:"space-y-6",children:C?.content?(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"text-lg font-semibold",children:"Utilizzo Bobine"}),(0,i.jsx)("p",{className:"text-sm text-slate-600",children:"Stato e utilizzo delle bobine"})]}),(0,i.jsxs)(u.$,{variant:"outline",size:"sm",onClick:()=>Y("utilizzo-bobine","excel"),children:[(0,i.jsx)(_.A,{className:"h-4 w-4 mr-2"}),"Excel"]})]}),(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Totale Bobine"}),(0,i.jsx)(w.A,{className:"h-4 w-4 text-blue-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:C.content.totale_bobine||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"bobine nel cantiere"})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Bobine Attive"}),(0,i.jsx)(P.A,{className:"h-4 w-4 text-green-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsx)("div",{className:"text-2xl font-bold text-slate-900",children:C.content.bobine?.filter(t=>"In uso"===t.stato||"Disponibile"===t.stato).length||0}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"disponibili/in uso"})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,i.jsx)(c.ZB,{className:"text-sm font-medium text-slate-600",children:"Utilizzo Medio"}),(0,i.jsx)(S.A,{className:"h-4 w-4 text-purple-500"})]}),(0,i.jsxs)(c.Wu,{children:[(0,i.jsxs)("div",{className:"text-2xl font-bold text-slate-900",children:[C.content.bobine?.length>0?(C.content.bobine.reduce((t,e)=>t+(e.percentuale_utilizzo||0),0)/C.content.bobine.length).toFixed(1):0,"%"]}),(0,i.jsx)("p",{className:"text-xs text-slate-500",children:"utilizzo medio"})]})]})]}),(0,i.jsxs)(c.Zp,{children:[(0,i.jsxs)(c.aR,{children:[(0,i.jsx)(c.ZB,{children:"Dettaglio Bobine"}),(0,i.jsx)(c.BT,{children:"Stato dettagliato di tutte le bobine"})]}),(0,i.jsx)(c.Wu,{children:(0,i.jsx)("div",{className:"overflow-x-auto",children:(0,i.jsxs)("table",{className:"w-full text-sm",children:[(0,i.jsx)("thead",{children:(0,i.jsxs)("tr",{className:"border-b",children:[(0,i.jsx)("th",{className:"text-left p-2",children:"Codice"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Tipologia"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Totali"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Utilizzati"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Metri Residui"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Utilizzo %"}),(0,i.jsx)("th",{className:"text-left p-2",children:"Stato"}),(0,i.jsx)("th",{className:"text-right p-2",children:"Cavi"})]})}),(0,i.jsx)("tbody",{children:C.content.bobine?.map((t,e)=>(0,i.jsxs)("tr",{className:"border-b hover:bg-slate-50",children:[(0,i.jsx)("td",{className:"p-2 font-medium",children:t.codice}),(0,i.jsx)("td",{className:"p-2",children:t.tipologia}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_totali?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_utilizzati?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.metri_residui?.toLocaleString()}),(0,i.jsx)("td",{className:"p-2 text-right",children:(0,i.jsxs)("div",{className:"flex items-center gap-2",children:[(0,i.jsxs)("span",{children:[t.percentuale_utilizzo?.toFixed(1),"%"]}),(0,i.jsx)(s.k,{value:Math.min(t.percentuale_utilizzo||0,100),className:"w-16 h-2"})]})}),(0,i.jsx)("td",{className:"p-2",children:(0,i.jsx)(l.E,{variant:"Disponibile"===t.stato?"default":"In uso"===t.stato?"secondary":"Terminata"===t.stato?"outline":"Over"===t.stato?"destructive":"outline",children:t.stato})}),(0,i.jsx)("td",{className:"p-2 text-right",children:t.totale_cavi_associati||0})]},e))})]})})})]})]}):(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(w.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("p",{children:"Nessun dato bobine disponibile"})]})}),(0,i.jsx)(f.av,{value:"produttivita",className:"space-y-6",children:(0,i.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,i.jsx)(O.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-300"}),(0,i.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"Produttivit\xe0"}),(0,i.jsx)("p",{children:"Funzionalit\xe0 in fase di sviluppo"}),(0,i.jsx)("p",{className:"text-sm mt-2",children:"Includer\xe0 calcoli IAP, statistiche team e analisi performance"})]})})]})]})})})}dD.displayName="CartesianGrid"},7383:(t,e,r)=>{var n=r(67009),i=r(32269),o=r(38428),a=r(55048);t.exports=function(t,e,r){if(!a(r))return!1;var c=typeof e;return("number"==c?!!(i(r)&&o(e,r.length)):"string"==c&&e in r)&&n(r[e],t)}},7651:(t,e,r)=>{var n=r(82038),i=r(52931),o=r(32269);t.exports=function(t){return o(t)?n(t):i(t)}},8336:(t,e,r)=>{var n=r(45803);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},8852:(t,e,r)=>{var n=r(1707);t.exports=function(t){return function(e){return n(e,t)}}},10090:(t,e,r)=>{var n=r(80458),i=r(89624),o=r(47282),a=o&&o.isTypedArray;t.exports=a?i(a):n},10653:(t,e,r)=>{var n=r(21456),i=r(63979),o=r(7651);t.exports=function(t){return n(t,o,i)}},10663:t=>{t.exports="object"==typeof global&&global&&global.Object===Object&&global},10846:t=>{"use strict";t.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11117:t=>{"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function o(t,e,n,o,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var c=new i(n,o||t,a),u=r?r+e:e;return t._events[u]?t._events[u].fn?t._events[u]=[t._events[u],c]:t._events[u].push(c):(t._events[u]=c,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function c(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),c.prototype.eventNames=function(){var t,n,i=[];if(0===this._eventsCount)return i;for(n in t=this._events)e.call(t,n)&&i.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?i.concat(Object.getOwnPropertySymbols(t)):i},c.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var i=0,o=n.length,a=Array(o);i<o;i++)a[i]=n[i].fn;return a},c.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},c.prototype.emit=function(t,e,n,i,o,a){var c=r?r+t:t;if(!this._events[c])return!1;var u,l,s=this._events[c],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,n),!0;case 4:return s.fn.call(s.context,e,n,i),!0;case 5:return s.fn.call(s.context,e,n,i,o),!0;case 6:return s.fn.call(s.context,e,n,i,o,a),!0}for(l=1,u=Array(f-1);l<f;l++)u[l-1]=arguments[l];s.fn.apply(s.context,u)}else{var p,h=s.length;for(l=0;l<h;l++)switch(s[l].once&&this.removeListener(t,s[l].fn,void 0,!0),f){case 1:s[l].fn.call(s[l].context);break;case 2:s[l].fn.call(s[l].context,e);break;case 3:s[l].fn.call(s[l].context,e,n);break;case 4:s[l].fn.call(s[l].context,e,n,i);break;default:if(!u)for(p=1,u=Array(f-1);p<f;p++)u[p-1]=arguments[p];s[l].fn.apply(s[l].context,u)}}return!0},c.prototype.on=function(t,e,r){return o(this,t,e,r,!1)},c.prototype.once=function(t,e,r){return o(this,t,e,r,!0)},c.prototype.removeListener=function(t,e,n,i){var o=r?r+t:t;if(!this._events[o])return this;if(!e)return a(this,o),this;var c=this._events[o];if(c.fn)c.fn!==e||i&&!c.once||n&&c.context!==n||a(this,o);else{for(var u=0,l=[],s=c.length;u<s;u++)(c[u].fn!==e||i&&!c[u].once||n&&c[u].context!==n)&&l.push(c[u]);l.length?this._events[o]=1===l.length?l[0]:l:a(this,o)}return this},c.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},c.prototype.off=c.prototype.removeListener,c.prototype.addListener=c.prototype.on,c.prefixed=r,c.EventEmitter=c,t.exports=c},11273:(t,e,r)=>{"use strict";r.d(e,{A:()=>a,q:()=>o});var n=r(43210),i=r(60687);function o(t,e){let r=n.createContext(e),o=t=>{let{children:e,...o}=t,a=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:a,children:e})};return o.displayName=t+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==e)return e;throw Error(`\`${i}\` must be used within \`${t}\``)}]}function a(t,e=[]){let r=[],o=()=>{let e=r.map(t=>n.createContext(t));return function(r){let i=r?.[t]||e;return n.useMemo(()=>({[`__scope${t}`]:{...r,[t]:i}}),[r,i])}};return o.scopeName=t,[function(e,o){let a=n.createContext(o),c=r.length;r=[...r,o];let u=e=>{let{scope:r,children:o,...u}=e,l=r?.[t]?.[c]||a,s=n.useMemo(()=>u,Object.values(u));return(0,i.jsx)(l.Provider,{value:s,children:o})};return u.displayName=e+"Provider",[u,function(r,i){let u=i?.[t]?.[c]||a,l=n.useContext(u);if(l)return l;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${e}\``)}]},function(...t){let e=t[0];if(1===t.length)return e;let r=()=>{let r=t.map(t=>({useScope:t(),scopeName:t.scopeName}));return function(t){let i=r.reduce((e,{useScope:r,scopeName:n})=>{let i=r(t)[`__scope${n}`];return{...e,...i}},{});return n.useMemo(()=>({[`__scope${e.scopeName}`]:i}),[i])}};return r.scopeName=e.scopeName,r}(o,...e)]}},11424:(t,e,r)=>{var n=r(47603);t.exports=r(66400)(n)},11539:(t,e,r)=>{var n=r(37643),i=r(55048),o=r(49227),a=0/0,c=/^[-+]0x[0-9a-f]+$/i,u=/^0b[01]+$/i,l=/^0o[0-7]+$/i,s=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(o(t))return a;if(i(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=i(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=u.test(t);return r||l.test(t)?s(t.slice(2),r?2:8):c.test(t)?a:+t}},12290:t=>{var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},12344:(t,e,r)=>{t.exports=r(65984)()},12412:t=>{"use strict";t.exports=require("assert")},13495:(t,e,r)=>{"use strict";r.d(e,{c:()=>i});var n=r(43210);function i(t){let e=n.useRef(t);return n.useEffect(()=>{e.current=t}),n.useMemo(()=>(...t)=>e.current?.(...t),[])}},14163:(t,e,r)=>{"use strict";r.d(e,{hO:()=>u,sG:()=>c});var n=r(43210),i=r(51215),o=r(8730),a=r(60687),c=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((t,e)=>{let r=(0,o.TL)(`Primitive.${e}`),i=n.forwardRef((t,n)=>{let{asChild:i,...o}=t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(i?r:e,{...o,ref:n})});return i.displayName=`Primitive.${e}`,{...t,[e]:i}},{});function u(t,e){t&&i.flushSync(()=>t.dispatchEvent(e))}},14675:t=>{t.exports=function(t){return function(){return t}}},15451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return i(t)&&"[object Arguments]"==n(t)}},15871:(t,e,r)=>{var n=r(36341),i=r(27467);t.exports=function t(e,r,o,a,c){return e===r||(null!=e&&null!=r&&(i(e)||i(r))?n(e,r,o,a,t,c):e!=e&&r!=r)}},15883:(t,e,r)=>{var n=r(2984),i=r(46063),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},15909:(t,e,r)=>{var n=r(87506),i=r(66930),o=r(658);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(o||i),string:new n}}},16854:t=>{t.exports=function(t){return this.__data__.has(t)}},17518:(t,e,r)=>{var n=r(21367),i=r(1707),o=r(22),a=r(54765),c=r(43378),u=r(89624),l=r(65727),s=r(48169),f=r(40542);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return i(e,1===t.length?t[0]:t)}:t}):[s];var p=-1;return e=n(e,u(o)),c(a(t,function(t,r,i){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return l(t,e,r)})}},17830:(t,e,r)=>{t.exports=r(41547)(r(85718),"WeakMap")},18234:(t,e,r)=>{var n=r(91290),i=r(22),o=r(84482),a=Math.max;t.exports=function(t,e,r){var c=null==t?0:t.length;if(!c)return -1;var u=null==r?0:o(r);return u<0&&(u=a(c+u,0)),n(t,i(e,3),u)}},19121:t=>{"use strict";t.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19976:(t,e,r)=>{var n=r(8336);t.exports=function(t,e){var r=n(this,t),i=r.size;return r.set(t,e),this.size+=+(r.size!=i),this}},20540:(t,e,r)=>{var n=r(55048),i=r(70151),o=r(11539),a=Math.max,c=Math.min;t.exports=function(t,e,r){var u,l,s,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=u,n=l;return u=l=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=s}function x(){var t,r,n,o=i();if(g(o))return w(o);p=setTimeout(x,(t=o-h,r=o-d,n=e-t,v?c(n,s-r):n))}function w(t){return(p=void 0,m&&u)?b(t):(u=l=void 0,f)}function O(){var t,r=i(),n=g(r);if(u=arguments,l=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=o(e)||0,n(r)&&(y=!!r.leading,s=(v="maxWait"in r)?a(o(r.maxWait)||0,e):s,m="trailing"in r?!!r.trailing:m),O.cancel=function(){void 0!==p&&clearTimeout(p),d=0,u=h=l=p=void 0},O.flush=function(){return void 0===p?f:w(i())},O}},20623:(t,e,r)=>{var n=r(15871),i=r(40491),o=r(2896),a=r(67619),c=r(34883),u=r(41132),l=r(46436);t.exports=function(t,e){return a(t)&&c(e)?u(l(t),e):function(r){var a=i(r,t);return void 0===a&&a===e?o(r,t):n(e,a,3)}}},21367:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}},21456:(t,e,r)=>{var n=r(41693),i=r(40542);t.exports=function(t,e,r){var o=e(t);return i(t)?o:n(o,r(t))}},21592:(t,e,r)=>{var n=r(42205),i=r(61837);t.exports=function(t,e){return n(i(t,e),1)}},21630:(t,e,r)=>{var n=r(10653),i=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,o,a,c){var u=1&r,l=n(t),s=l.length;if(s!=n(e).length&&!u)return!1;for(var f=s;f--;){var p=l[f];if(!(u?p in e:i.call(e,p)))return!1}var h=c.get(t),d=c.get(e);if(h&&d)return h==e&&d==t;var y=!0;c.set(t,e),c.set(e,t);for(var v=u;++f<s;){var m=t[p=l[f]],b=e[p];if(o)var g=u?o(b,m,p,e,t,c):o(m,b,p,t,e,c);if(!(void 0===g?m===b||a(m,b,r,o,c):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,w=e.constructor;x!=w&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof w&&w instanceof w)&&(y=!1)}return c.delete(t),c.delete(e),y}},21820:t=>{"use strict";t.exports=require("os")},22964:(t,e,r)=>{t.exports=r(23729)(r(18234))},23729:(t,e,r)=>{var n=r(22),i=r(32269),o=r(7651);t.exports=function(t){return function(e,r,a){var c=Object(e);if(!i(e)){var u=n(r,3);e=o(e),r=function(t){return u(c[t],t,c)}}var l=t(e,r,a);return l>-1?c[u?e[l]:l]:void 0}}},25118:t=>{t.exports=function(t){return this.__data__.has(t)}},25541:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("trending-up",[["path",{d:"M16 7h6v6",key:"box55l"}],["path",{d:"m22 7-8.5 8.5-5-5L2 17",key:"1t1m79"}]])},27006:(t,e,r)=>{var n=r(46328),i=r(99525),o=r(58276);t.exports=function(t,e,r,a,c,u){var l=1&r,s=t.length,f=e.length;if(s!=f&&!(l&&f>s))return!1;var p=u.get(t),h=u.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(u.set(t,e),u.set(e,t);++d<s;){var m=t[d],b=e[d];if(a)var g=l?a(b,m,d,e,t,u):a(m,b,d,t,e,u);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!i(e,function(t,e){if(!o(v,e)&&(m===t||c(m,t,r,a,u)))return v.push(e)})){y=!1;break}}else if(!(m===b||c(m,b,r,a,u))){y=!1;break}}return u.delete(t),u.delete(e),y}},27467:t=>{t.exports=function(t){return null!=t&&"object"==typeof t}},27669:t=>{t.exports=function(){this.__data__=[],this.size=0}},27910:t=>{"use strict";t.exports=require("stream")},28354:t=>{"use strict";t.exports=require("util")},28837:(t,e,r)=>{var n=r(57797),i=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():i.call(e,r,1),--this.size,!0)}},28947:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},28977:(t,e,r)=>{var n=r(11539),i=1/0;t.exports=function(t){return t?(t=n(t))===i||t===-i?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},29021:t=>{"use strict";t.exports=require("fs")},29205:(t,e,r)=>{var n=r(8336);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=!!e,e}},29294:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29395:(t,e,r)=>{var n=r(79474),i=r(70222),o=r(84713),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?i(t):o(t)}},29508:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).get(t)}},30316:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r=!0;return n(t,function(t,n,i){return r=!!e(t,n,i)}),r}},30401:(t,e,r)=>{t.exports=r(41547)(r(85718),"Promise")},30854:(t,e,r)=>{var n=r(66930),i=r(658),o=r(95746);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!i||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new o(a)}return r.set(t,e),this.size=r.size,this}},31158:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]])},32269:(t,e,r)=>{var n=r(5231),i=r(69619);t.exports=function(t){return null!=t&&i(t.length)&&!n(t)}},33873:t=>{"use strict";t.exports=require("path")},34117:t=>{var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},34452:t=>{"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},34746:t=>{t.exports=function(t){return this.__data__.get(t)}},34772:(t,e,r)=>{t.exports=r(41547)(r(85718),"Set")},34821:t=>{t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},34883:(t,e,r)=>{var n=r(55048);t.exports=function(t){return t==t&&!n(t)}},34990:(t,e,r)=>{t.exports=r(87321)()},35142:(t,e,r)=>{var n=r(40542),i=r(67619),o=r(51449),a=r(42403);t.exports=function(t,e){return n(t)?t:i(t,e)?[t]:o(a(t))}},35163:(t,e,r)=>{var n=r(15451),i=r(27467),o=Object.prototype,a=o.hasOwnProperty,c=o.propertyIsEnumerable;t.exports=n(function(){return arguments}())?n:function(t){return i(t)&&a.call(t,"callee")&&!c.call(t,"callee")}},35697:(t,e,r)=>{var n=r(79474),i=r(4999),o=r(67009),a=r(27006),c=r(59774),u=r(2408),l=n?n.prototype:void 0,s=l?l.valueOf:void 0;t.exports=function(t,e,r,n,l,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new i(t),new i(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return o(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=c;case"[object Set]":var d=1&n;if(h||(h=u),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,l,f,p);return p.delete(t),v;case"[object Symbol]":if(s)return s.call(t)==s.call(e)}return!1}},35800:(t,e,r)=>{var n=r(57797);t.exports=function(t){return n(this.__data__,t)>-1}},36315:(t,e,r)=>{var n=r(22),i=r(92662);t.exports=function(t,e){return t&&t.length?i(t,n(e,2)):[]}},36341:(t,e,r)=>{var n=r(67200),i=r(27006),o=r(35697),a=r(21630),c=r(1566),u=r(40542),l=r(80329),s=r(10090),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=u(t),g=u(e),x=b?p:c(t),w=g?p:c(e);x=x==f?h:x,w=w==f?h:w;var O=x==h,j=w==h,S=x==w;if(S&&l(t)){if(!l(e))return!1;b=!0,O=!1}if(S&&!O)return m||(m=new n),b||s(t)?i(t,e,r,y,v,m):o(t,e,x,r,y,v,m);if(!(1&r)){var P=O&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,_=A?e.value():e;return m||(m=new n),v(E,_,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},36959:t=>{t.exports=function(){}},37456:t=>{t.exports=function(t){return null==t}},37575:(t,e,r)=>{var n=r(66930);t.exports=function(){this.__data__=new n,this.size=0}},37643:(t,e,r)=>{var n=r(6053),i=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(i,""):t}},38404:(t,e,r)=>{var n=r(29395),i=r(65932),o=r(27467),a=Object.prototype,c=Function.prototype.toString,u=a.hasOwnProperty,l=c.call(Object);t.exports=function(t){if(!o(t)||"[object Object]"!=n(t))return!1;var e=i(t);if(null===e)return!0;var r=u.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&c.call(r)==l}},38428:t=>{var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?0x1fffffffffffff:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},39672:(t,e,r)=>{var n=r(58141);t.exports=function(t,e){var r=this.__data__;return this.size+=+!this.has(t),r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},39774:t=>{t.exports=function(t){return t!=t}},40491:(t,e,r)=>{var n=r(1707);t.exports=function(t,e,r){var i=null==t?void 0:n(t,e);return void 0===i?r:i}},40542:t=>{t.exports=Array.isArray},41011:(t,e,r)=>{var n=r(41353);t.exports=function(t,e,r){var i=t.length;return r=void 0===r?i:r,!e&&r>=i?t:n(t,e,r)}},41132:t=>{t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},41157:(t,e,r)=>{var n=r(91928);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},41353:t=>{t.exports=function(t,e,r){var n=-1,i=t.length;e<0&&(e=-e>i?0:i+e),(r=r>i?i:r)<0&&(r+=i),i=e>r?0:r-e>>>0,e>>>=0;for(var o=Array(i);++n<i;)o[n]=t[n+e];return o}},41547:(t,e,r)=>{var n=r(61548),i=r(90851);t.exports=function(t,e){var r=i(t,e);return n(r)?r:void 0}},41693:t=>{t.exports=function(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}},41862:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},42082:t=>{t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},42205:(t,e,r)=>{var n=r(41693),i=r(85450);t.exports=function t(e,r,o,a,c){var u=-1,l=e.length;for(o||(o=i),c||(c=[]);++u<l;){var s=e[u];r>0&&o(s)?r>1?t(s,r-1,o,a,c):n(c,s):a||(c[c.length]=s)}return c}},42403:(t,e,r)=>{var n=r(80195);t.exports=function(t){return null==t?"":n(t)}},43378:t=>{t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},44493:(t,e,r)=>{"use strict";r.d(e,{BT:()=>u,Wu:()=>l,ZB:()=>c,Zp:()=>o,aR:()=>a});var n=r(60687);r(43210);var i=r(4780);function o({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card",className:(0,i.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...e})}function a({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-header",className:(0,i.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...e})}function c({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-title",className:(0,i.cn)("leading-none font-semibold",t),...e})}function u({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-description",className:(0,i.cn)("text-muted-foreground text-sm",t),...e})}function l({className:t,...e}){return(0,n.jsx)("div",{"data-slot":"card-content",className:(0,i.cn)("px-6",t),...e})}},45058:(t,e,r)=>{var n=r(42082),i=r(8852),o=r(67619),a=r(46436);t.exports=function(t){return o(t)?n(a(t)):i(t)}},45180:(t,e,r)=>{"use strict";r.r(e),r.d(e,{GlobalError:()=>a.a,__next_app__:()=>f,pages:()=>s,routeModule:()=>p,tree:()=>l});var n=r(65239),i=r(48088),o=r(88170),a=r.n(o),c=r(30893),u={};for(let t in c)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(t)&&(u[t]=()=>c[t]);r.d(e,u);let l={children:["",{children:["reports",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,75900)),"C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx"]}]},{metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async t=>(await Promise.resolve().then(r.bind(r,70440))).default(t)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,s=["C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx"],f={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/reports/page",pathname:"/reports",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},45583:(t,e,r)=>{"use strict";r.d(e,{A:()=>n});let n=(0,r(62688).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},45603:(t,e,r)=>{var n=r(20540),i=r(55048);t.exports=function(t,e,r){var o=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return i(r)&&(o="leading"in r?!!r.leading:o,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:o,maxWait:e,trailing:a})}},45803:t=>{t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},46059:(t,e,r)=>{"use strict";r.d(e,{C:()=>a});var n=r(43210),i=r(98599),o=r(66156),a=t=>{let{present:e,children:r}=t,a=function(t){var e,r;let[i,a]=n.useState(),u=n.useRef(null),l=n.useRef(t),s=n.useRef("none"),[f,p]=(e=t?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((t,e)=>r[t][e]??t,e));return n.useEffect(()=>{let t=c(u.current);s.current="mounted"===f?t:"none"},[f]),(0,o.N)(()=>{let e=u.current,r=l.current;if(r!==t){let n=s.current,i=c(e);t?p("MOUNT"):"none"===i||e?.display==="none"?p("UNMOUNT"):r&&n!==i?p("ANIMATION_OUT"):p("UNMOUNT"),l.current=t}},[t,p]),(0,o.N)(()=>{if(i){let t,e=i.ownerDocument.defaultView??window,r=r=>{let n=c(u.current).includes(r.animationName);if(r.target===i&&n&&(p("ANIMATION_END"),!l.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",t=e.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=t=>{t.target===i&&(s.current=c(u.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{e.clearTimeout(t),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}p("ANIMATION_END")},[i,p]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:n.useCallback(t=>{u.current=t?getComputedStyle(t):null,a(t)},[])}}(e),u="function"==typeof r?r({present:a.isPresent}):n.Children.only(r),l=(0,i.s)(a.ref,function(t){let e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get,r=e&&"isReactWarning"in e&&e.isReactWarning;return r?t.ref:(r=(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?t.props.ref:t.props.ref||t.ref}(u));return"function"==typeof r||a.isPresent?n.cloneElement(u,{ref:l}):null};function c(t){return t?.animationName||"none"}a.displayName="Presence"},46063:t=>{t.exports=function(t,e){return t<e}},46229:(t,e,r)=>{var n=r(48169),i=r(66354),o=r(11424);t.exports=function(t,e){return o(i(t,e,n),t+"")}},46328:(t,e,r)=>{var n=r(95746),i=r(89185),o=r(16854);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=i,a.prototype.has=o,t.exports=a},46436:(t,e,r)=>{var n=r(49227),i=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-i?"-0":e}},46657:(t,e,r)=>{"use strict";r.d(e,{k:()=>w});var n=r(60687),i=r(43210),o=r(11273),a=r(14163),c="Progress",[u,l]=(0,o.A)(c),[s,f]=u(c),p=i.forwardRef((t,e)=>{var r,i;let{__scopeProgress:o,value:c=null,max:u,getValueLabel:l=y,...f}=t;(u||0===u)&&!b(u)&&console.error((r=`${u}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let p=b(u)?u:100;null===c||g(c,p)||console.error((i=`${c}`,`Invalid prop \`value\` of value \`${i}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=g(c,p)?c:null,d=m(h)?l(h,p):void 0;return(0,n.jsx)(s,{scope:o,value:h,max:p,children:(0,n.jsx)(a.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":m(h)?h:void 0,"aria-valuetext":d,role:"progressbar","data-state":v(h,p),"data-value":h??void 0,"data-max":p,...f,ref:e})})});p.displayName=c;var h="ProgressIndicator",d=i.forwardRef((t,e)=>{let{__scopeProgress:r,...i}=t,o=f(h,r);return(0,n.jsx)(a.sG.div,{"data-state":v(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...i,ref:e})});function y(t,e){return`${Math.round(t/e*100)}%`}function v(t,e){return null==t?"indeterminate":t===e?"complete":"loading"}function m(t){return"number"==typeof t}function b(t){return m(t)&&!isNaN(t)&&t>0}function g(t,e){return m(t)&&!isNaN(t)&&t<=e&&t>=0}d.displayName=h;var x=r(4780);function w({className:t,value:e,...r}){return(0,n.jsx)(p,{"data-slot":"progress",className:(0,x.cn)("bg-primary/20 relative h-2 w-full overflow-hidden rounded-full",t),...r,children:(0,n.jsx)(d,{"data-slot":"progress-indicator",className:"bg-primary h-full w-full flex-1 transition-all",style:{transform:`translateX(-${100-(e||0)}%)`}})})}},47212:(t,e,r)=>{var n=r(87270),i=r(30316),o=r(22),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:i;return r&&c(t,e,r)&&(e=void 0),u(t,o(e,3))}},47282:(t,e,r)=>{t=r.nmd(t);var n=r(10663),i=e&&!e.nodeType&&e,o=i&&t&&!t.nodeType&&t,a=o&&o.exports===i&&n.process,c=function(){try{var t=o&&o.require&&o.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=c},47603:(t,e,r)=>{var n=r(14675),i=r(91928),o=r(48169);t.exports=i?function(t,e){return i(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:o},48169:t=>{t.exports=function(t){return t}},48385:t=>{var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",i="[^"+e+"]",o="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",c="(?:"+r+"|"+n+")?",u="[\\ufe0e\\ufe0f]?",l="(?:\\u200d(?:"+[i,o,a].join("|")+")"+u+c+")*",s=RegExp(n+"(?="+n+")|"+("(?:"+[i+r+"?",r,o,a,"["+e+"]"].join("|"))+")"+(u+c+l),"g");t.exports=function(t){return t.match(s)||[]}},49227:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"symbol"==typeof t||i(t)&&"[object Symbol]"==n(t)}},51449:(t,e,r)=>{var n=r(85745),i=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,o=/\\(\\)?/g;t.exports=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(i,function(t,r,n,i){e.push(n?i.replace(o,"$1"):r||t)}),e})},52599:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,i=0,o=[];++r<n;){var a=t[r];e(a,r,t)&&(o[i++]=a)}return o}},52823:(t,e,r)=>{var n=r(85406),i=function(){var t=/[^.]+$/.exec(n&&n.keys&&n.keys.IE_PROTO||"");return t?"Symbol(src)_1."+t:""}();t.exports=function(t){return!!i&&i in t}},52931:(t,e,r)=>{var n=r(77834),i=r(89605),o=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return i(t);var e=[];for(var r in Object(t))o.call(t,r)&&"constructor"!=r&&e.push(r);return e}},54765:(t,e,r)=>{var n=r(67554),i=r(32269);t.exports=function(t,e){var r=-1,o=i(t)?Array(t.length):[];return n(t,function(t,n,i){o[++r]=e(t,n,i)}),o}},55048:t=>{t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},55511:t=>{"use strict";t.exports=require("crypto")},55591:t=>{"use strict";t.exports=require("https")},56506:(t,e,r)=>{var n=r(32269);t.exports=function(t,e){return function(r,i){if(null==r)return r;if(!n(r))return t(r,i);for(var o=r.length,a=e?o:-1,c=Object(r);(e?a--:++a<o)&&!1!==i(c[a],a,c););return r}}},56770:(t,e,r)=>{"use strict";r.d(e,{tU:()=>H,av:()=>Z,j7:()=>V,Xi:()=>Y});var n=r(60687),i=r(43210),o=r(70569),a=r(11273),c=r(9510),u=r(98599),l=r(96963),s=r(14163),f=r(13495),p=r(65551),h=r(43),d="rovingFocusGroup.onEntryFocus",y={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[m,b,g]=(0,c.N)(v),[x,w]=(0,a.A)(v,[g]),[O,j]=x(v),S=i.forwardRef((t,e)=>(0,n.jsx)(m.Provider,{scope:t.__scopeRovingFocusGroup,children:(0,n.jsx)(m.Slot,{scope:t.__scopeRovingFocusGroup,children:(0,n.jsx)(P,{...t,ref:e})})}));S.displayName=v;var P=i.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:r,orientation:a,loop:c=!1,dir:l,currentTabStopId:m,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:x,onEntryFocus:w,preventScrollOnEntryFocus:j=!1,...S}=t,P=i.useRef(null),A=(0,u.s)(e,P),E=(0,h.jH)(l),[_,k]=(0,p.i)({prop:m,defaultProp:g??null,onChange:x,caller:v}),[T,N]=i.useState(!1),C=(0,f.c)(w),I=b(r),D=i.useRef(!1),[B,R]=i.useState(0);return i.useEffect(()=>{let t=P.current;if(t)return t.addEventListener(d,C),()=>t.removeEventListener(d,C)},[C]),(0,n.jsx)(O,{scope:r,orientation:a,dir:E,loop:c,currentTabStopId:_,onItemFocus:i.useCallback(t=>k(t),[k]),onItemShiftTab:i.useCallback(()=>N(!0),[]),onFocusableItemAdd:i.useCallback(()=>R(t=>t+1),[]),onFocusableItemRemove:i.useCallback(()=>R(t=>t-1),[]),children:(0,n.jsx)(s.sG.div,{tabIndex:T||0===B?-1:0,"data-orientation":a,...S,ref:A,style:{outline:"none",...t.style},onMouseDown:(0,o.m)(t.onMouseDown,()=>{D.current=!0}),onFocus:(0,o.m)(t.onFocus,t=>{let e=!D.current;if(t.target===t.currentTarget&&e&&!T){let e=new CustomEvent(d,y);if(t.currentTarget.dispatchEvent(e),!e.defaultPrevented){let t=I().filter(t=>t.focusable);M([t.find(t=>t.active),t.find(t=>t.id===_),...t].filter(Boolean).map(t=>t.ref.current),j)}}D.current=!1}),onBlur:(0,o.m)(t.onBlur,()=>N(!1))})})}),A="RovingFocusGroupItem",E=i.forwardRef((t,e)=>{let{__scopeRovingFocusGroup:r,focusable:a=!0,active:c=!1,tabStopId:u,children:f,...p}=t,h=(0,l.B)(),d=u||h,y=j(A,r),v=y.currentTabStopId===d,g=b(r),{onFocusableItemAdd:x,onFocusableItemRemove:w,currentTabStopId:O}=y;return i.useEffect(()=>{if(a)return x(),()=>w()},[a,x,w]),(0,n.jsx)(m.ItemSlot,{scope:r,id:d,focusable:a,active:c,children:(0,n.jsx)(s.sG.span,{tabIndex:v?0:-1,"data-orientation":y.orientation,...p,ref:e,onMouseDown:(0,o.m)(t.onMouseDown,t=>{a?y.onItemFocus(d):t.preventDefault()}),onFocus:(0,o.m)(t.onFocus,()=>y.onItemFocus(d)),onKeyDown:(0,o.m)(t.onKeyDown,t=>{if("Tab"===t.key&&t.shiftKey)return void y.onItemShiftTab();if(t.target!==t.currentTarget)return;let e=function(t,e,r){var n;let i=(n=t.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===e&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===e&&["ArrowUp","ArrowDown"].includes(i)))return _[i]}(t,y.orientation,y.dir);if(void 0!==e){if(t.metaKey||t.ctrlKey||t.altKey||t.shiftKey)return;t.preventDefault();let r=g().filter(t=>t.focusable).map(t=>t.ref.current);if("last"===e)r.reverse();else if("prev"===e||"next"===e){"prev"===e&&r.reverse();let n=r.indexOf(t.currentTarget);r=y.loop?function(t,e){return t.map((r,n)=>t[(e+n)%t.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>M(r))}}),children:"function"==typeof f?f({isCurrentTabStop:v,hasTabStop:null!=O}):f})})});E.displayName=A;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function M(t,e=!1){let r=document.activeElement;for(let n of t)if(n===r||(n.focus({preventScroll:e}),document.activeElement!==r))return}var k=r(46059),T="Tabs",[N,C]=(0,a.A)(T,[w]),I=w(),[D,B]=N(T),R=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:i,onValueChange:o,defaultValue:a,orientation:c="horizontal",dir:u,activationMode:f="automatic",...d}=t,y=(0,h.jH)(u),[v,m]=(0,p.i)({prop:i,onChange:o,defaultProp:a??"",caller:T});return(0,n.jsx)(D,{scope:r,baseId:(0,l.B)(),value:v,onValueChange:m,orientation:c,dir:y,activationMode:f,children:(0,n.jsx)(s.sG.div,{dir:y,"data-orientation":c,...d,ref:e})})});R.displayName=T;var z="TabsList",L=i.forwardRef((t,e)=>{let{__scopeTabs:r,loop:i=!0,...o}=t,a=B(z,r),c=I(r);return(0,n.jsx)(S,{asChild:!0,...c,orientation:a.orientation,dir:a.dir,loop:i,children:(0,n.jsx)(s.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:e})})});L.displayName=z;var U="TabsTrigger",$=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:i,disabled:a=!1,...c}=t,u=B(U,r),l=I(r),f=W(u.baseId,i),p=X(u.baseId,i),h=i===u.value;return(0,n.jsx)(E,{asChild:!0,...l,focusable:!a,active:h,children:(0,n.jsx)(s.sG.button,{type:"button",role:"tab","aria-selected":h,"aria-controls":p,"data-state":h?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:f,...c,ref:e,onMouseDown:(0,o.m)(t.onMouseDown,t=>{a||0!==t.button||!1!==t.ctrlKey?t.preventDefault():u.onValueChange(i)}),onKeyDown:(0,o.m)(t.onKeyDown,t=>{[" ","Enter"].includes(t.key)&&u.onValueChange(i)}),onFocus:(0,o.m)(t.onFocus,()=>{let t="manual"!==u.activationMode;h||a||!t||u.onValueChange(i)})})})});$.displayName=U;var F="TabsContent",q=i.forwardRef((t,e)=>{let{__scopeTabs:r,value:o,forceMount:a,children:c,...u}=t,l=B(F,r),f=W(l.baseId,o),p=X(l.baseId,o),h=o===l.value,d=i.useRef(h);return i.useEffect(()=>{let t=requestAnimationFrame(()=>d.current=!1);return()=>cancelAnimationFrame(t)},[]),(0,n.jsx)(k.C,{present:a||h,children:({present:r})=>(0,n.jsx)(s.sG.div,{"data-state":h?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":f,hidden:!r,id:p,tabIndex:0,...u,ref:e,style:{...t.style,animationDuration:d.current?"0s":void 0},children:r&&c})})});function W(t,e){return`${t}-trigger-${e}`}function X(t,e){return`${t}-content-${e}`}q.displayName=F;var G=r(4780);let H=R,V=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)(L,{ref:r,className:(0,G.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...e}));V.displayName=L.displayName;let Y=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)($,{ref:r,className:(0,G.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...e}));Y.displayName=$.displayName;let Z=i.forwardRef(({className:t,...e},r)=>(0,n.jsx)(q,{ref:r,className:(0,G.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...e}));Z.displayName=q.displayName},57797:(t,e,r)=>{var n=r(67009);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},58141:(t,e,r)=>{t.exports=r(41547)(Object,"create")},58276:t=>{t.exports=function(t,e){return t.has(e)}},58744:(t,e,r)=>{var n=r(57797);t.exports=function(t,e){var r=this.__data__,i=n(r,t);return i<0?(++this.size,r.push([t,e])):r[i][1]=e,this}},59467:(t,e,r)=>{var n=r(35142),i=r(35163),o=r(40542),a=r(38428),c=r(69619),u=r(46436);t.exports=function(t,e,r){e=n(e,t);for(var l=-1,s=e.length,f=!1;++l<s;){var p=u(e[l]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++l!=s?f:!!(s=null==t?0:t.length)&&c(s)&&a(p,s)&&(o(t)||i(t))}},59774:t=>{t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},61320:(t,e,r)=>{var n=r(8336);t.exports=function(t){return n(this,t).has(t)}},61548:(t,e,r)=>{var n=r(5231),i=r(52823),o=r(55048),a=r(12290),c=/^\[object .+?Constructor\]$/,u=Object.prototype,l=Function.prototype.toString,s=u.hasOwnProperty,f=RegExp("^"+l.call(s).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!o(t)||i(t))&&(n(t)?f:c).test(a(t))}},61837:(t,e,r)=>{var n=r(21367),i=r(22),o=r(54765),a=r(40542);t.exports=function(t,e){return(a(t)?n:o)(t,i(e,3))}},63033:t=>{"use strict";t.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63866:(t,e,r)=>{var n=r(29395),i=r(40542),o=r(27467);t.exports=function(t){return"string"==typeof t||!i(t)&&o(t)&&"[object String]"==n(t)}},63979:(t,e,r)=>{var n=r(52599),i=r(6330),o=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols;t.exports=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return o.call(t,e)})}:i},65551:(t,e,r)=>{"use strict";r.d(e,{i:()=>c});var n,i=r(43210),o=r(66156),a=(n||(n=r.t(i,2)))[" useInsertionEffect ".trim().toString()]||o.N;function c({prop:t,defaultProp:e,onChange:r=()=>{},caller:n}){let[o,c,u]=function({defaultProp:t,onChange:e}){let[r,n]=i.useState(t),o=i.useRef(r),c=i.useRef(e);return a(()=>{c.current=e},[e]),i.useEffect(()=>{o.current!==r&&(c.current?.(r),o.current=r)},[r,o]),[r,n,c]}({defaultProp:e,onChange:r}),l=void 0!==t,s=l?t:o;{let e=i.useRef(void 0!==t);i.useEffect(()=>{let t=e.current;if(t!==l){let e=l?"controlled":"uncontrolled";console.warn(`${n} is changing from ${t?"controlled":"uncontrolled"} to ${e}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}e.current=l},[l,n])}return[s,i.useCallback(e=>{if(l){let r="function"==typeof e?e(t):e;r!==t&&u.current?.(r)}else c(e)},[l,t,c,u])]}Symbol("RADIX:SYNC_STATE")},65662:t=>{t.exports=function(t,e){return function(r){return t(e(r))}}},65727:(t,e,r)=>{var n=r(81957);t.exports=function(t,e,r){for(var i=-1,o=t.criteria,a=e.criteria,c=o.length,u=r.length;++i<c;){var l=n(o[i],a[i]);if(l){if(i>=u)return l;return l*("desc"==r[i]?-1:1)}}return t.index-e.index}},65932:(t,e,r)=>{t.exports=r(65662)(Object.getPrototypeOf,Object)},65984:t=>{t.exports=function(t){return function(e,r,n){for(var i=-1,o=Object(e),a=n(e),c=a.length;c--;){var u=a[t?c:++i];if(!1===r(o[u],u,o))break}return e}}},66156:(t,e,r)=>{"use strict";r.d(e,{N:()=>i});var n=r(43210),i=globalThis?.document?n.useLayoutEffect:()=>{}},66354:(t,e,r)=>{var n=r(85244),i=Math.max;t.exports=function(t,e,r){return e=i(void 0===e?t.length-1:e,0),function(){for(var o=arguments,a=-1,c=i(o.length-e,0),u=Array(c);++a<c;)u[a]=o[e+a];a=-1;for(var l=Array(e+1);++a<e;)l[a]=o[a];return l[e]=r(u),n(t,this,l)}}},66400:t=>{var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var i=e(),o=16-(i-n);if(n=i,o>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},66713:(t,e,r)=>{var n=r(3105),i=r(34117),o=r(48385);t.exports=function(t){return i(t)?o(t):n(t)}},66837:(t,e,r)=>{var n=r(58141);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},66930:(t,e,r)=>{var n=r(27669),i=r(28837),o=r(94388),a=r(35800),c=r(58744);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},66992:(t,e)=>{"use strict";var r,n=Symbol.for("react.element"),i=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),c=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),l=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),p=Symbol.for("react.suspense"),h=Symbol.for("react.suspense_list"),d=Symbol.for("react.memo"),y=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case o:case c:case a:case p:case h:return t;default:switch(t=t&&t.$$typeof){case s:case l:case f:case y:case d:case u:return t;default:return e}}case i:return e}}}(t)===o}},67009:t=>{t.exports=function(t,e){return t===e||t!=t&&e!=e}},67200:(t,e,r)=>{var n=r(66930),i=r(37575),o=r(75411),a=r(34746),c=r(25118),u=r(30854);function l(t){var e=this.__data__=new n(t);this.size=e.size}l.prototype.clear=i,l.prototype.delete=o,l.prototype.get=a,l.prototype.has=c,l.prototype.set=u,t.exports=l},67367:(t,e,r)=>{var n=r(99525),i=r(22),o=r(75847),a=r(40542),c=r(7383);t.exports=function(t,e,r){var u=a(t)?n:o;return r&&c(t,e,r)&&(e=void 0),u(t,i(e,3))}},67554:(t,e,r)=>{var n=r(99114);t.exports=r(56506)(n)},67619:(t,e,r)=>{var n=r(40542),i=r(49227),o=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||i(t))||a.test(t)||!o.test(t)||null!=e&&t in Object(e)}},69433:(t,e,r)=>{t.exports=r(5566)("toUpperCase")},69619:t=>{t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=0x1fffffffffffff}},69691:(t,e,r)=>{var n=r(41157),i=r(99114),o=r(22);t.exports=function(t,e){var r={};return e=o(e,3),i(t,function(t,i,o){n(r,i,e(t,i,o))}),r}},70151:(t,e,r)=>{var n=r(85718);t.exports=function(){return n.Date.now()}},70222:(t,e,r)=>{var n=r(79474),i=Object.prototype,o=i.hasOwnProperty,a=i.toString,c=n?n.toStringTag:void 0;t.exports=function(t){var e=o.call(t,c),r=t[c];try{t[c]=void 0;var n=!0}catch(t){}var i=a.call(t);return n&&(e?t[c]=r:delete t[c]),i}},70440:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>i});var n=r(31658);let i=async t=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await t.params,"favicon.ico")+""}]},70569:(t,e,r)=>{"use strict";function n(t,e,{checkForDefaultPrevented:r=!0}={}){return function(n){if(t?.(n),!1===r||!n.defaultPrevented)return e?.(n)}}r.d(e,{m:()=>n})},71960:t=>{t.exports=function(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}},71967:(t,e,r)=>{var n=r(15871);t.exports=function(t,e){return n(t,e)}},74075:t=>{"use strict";t.exports=require("zlib")},74610:t=>{t.exports=function(t,e,r){for(var n=r-1,i=t.length;++n<i;)if(t[n]===e)return n;return -1}},75254:(t,e,r)=>{var n=r(78418),i=r(93311),o=r(41132);t.exports=function(t){var e=i(t);return 1==e.length&&e[0][2]?o(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},75411:t=>{t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},75847:(t,e,r)=>{var n=r(67554);t.exports=function(t,e){var r;return n(t,function(t,n,i){return!(r=e(t,n,i))}),!!r}},75900:(t,e,r)=>{"use strict";r.r(e),r.d(e,{default:()=>n});let n=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\reports\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\reports\\page.tsx","default")},77822:(t,e,r)=>{var n=r(93490);t.exports=function(t){return n(t)&&t!=+t}},77834:t=>{var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},78418:(t,e,r)=>{var n=r(67200),i=r(15871);t.exports=function(t,e,r,o){var a=r.length,c=a,u=!o;if(null==t)return!c;for(t=Object(t);a--;){var l=r[a];if(u&&l[2]?l[1]!==t[l[0]]:!(l[0]in t))return!1}for(;++a<c;){var s=(l=r[a])[0],f=t[s],p=l[1];if(u&&l[2]){if(void 0===f&&!(s in t))return!1}else{var h=new n;if(o)var d=o(f,p,s,t,e,h);if(!(void 0===d?i(p,f,3,o,h):d))return!1}}return!0}},79474:(t,e,r)=>{t.exports=r(85718).Symbol},79551:t=>{"use strict";t.exports=require("url")},80195:(t,e,r)=>{var n=r(79474),i=r(21367),o=r(40542),a=r(49227),c=1/0,u=n?n.prototype:void 0,l=u?u.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(o(e))return i(e,t)+"";if(a(e))return l?l.call(e):"";var r=e+"";return"0"==r&&1/e==-c?"-0":r}},80329:(t,e,r)=>{t=r.nmd(t);var n=r(85718),i=r(1944),o=e&&!e.nodeType&&e,a=o&&t&&!t.nodeType&&t,c=a&&a.exports===o?n.Buffer:void 0,u=c?c.isBuffer:void 0;t.exports=u||i},80458:(t,e,r)=>{var n=r(29395),i=r(69619),o=r(27467),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return o(t)&&i(t.length)&&!!a[n(t)]}},80704:(t,e,r)=>{var n=r(96678);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},81488:t=>{t.exports=function(t,e){return null!=t&&e in Object(t)}},81630:t=>{"use strict";t.exports=require("http")},81957:(t,e,r)=>{var n=r(49227);t.exports=function(t,e){if(t!==e){var r=void 0!==t,i=null===t,o=t==t,a=n(t),c=void 0!==e,u=null===e,l=e==e,s=n(e);if(!u&&!s&&!a&&t>e||a&&c&&l&&!u&&!s||i&&c&&l||!r&&l||!o)return 1;if(!i&&!a&&!s&&t<e||s&&r&&o&&!i&&!a||u&&r&&o||!c&&o||!l)return -1}return 0}},82038:(t,e,r)=>{var n=r(34821),i=r(35163),o=r(40542),a=r(80329),c=r(38428),u=r(10090),l=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=o(t),s=!r&&i(t),f=!r&&!s&&a(t),p=!r&&!s&&!f&&u(t),h=r||s||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||l.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||c(v,y)))&&d.push(v);return d}},83997:t=>{"use strict";t.exports=require("tty")},84031:(t,e,r)=>{"use strict";var n=r(34452);function i(){}function o(){}o.resetWarningCache=i,t.exports=function(){function t(t,e,r,i,o,a){if(a!==n){var c=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:o,resetWarningCache:i};return r.PropTypes=r,r}},84261:t=>{t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=!!e,e}},84482:(t,e,r)=>{var n=r(28977);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},84713:t=>{var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},85244:t=>{t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},85406:(t,e,r)=>{t.exports=r(85718)["__core-js_shared__"]},85450:(t,e,r)=>{var n=r(79474),i=r(35163),o=r(40542),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return o(t)||i(t)||!!(a&&t&&t[a])}},85718:(t,e,r)=>{var n=r(10663),i="object"==typeof self&&self&&self.Object===Object&&self;t.exports=n||i||Function("return this")()},85745:(t,e,r)=>{var n=r(86451);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},85938:(t,e,r)=>{var n=r(42205),i=r(17518),o=r(46229),a=r(7383);t.exports=o(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),i(t,n(e,1),[])})},86451:(t,e,r)=>{var n=r(95746);function i(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,i=e?e.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var a=t.apply(this,n);return r.cache=o.set(i,a)||o,a};return r.cache=new(i.Cache||n),r}i.Cache=n,t.exports=i},87270:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},87321:(t,e,r)=>{var n=r(98798),i=r(7383),o=r(28977);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&i(e,r,a)&&(r=a=void 0),e=o(e),void 0===r?(r=e,e=0):r=o(r),a=void 0===a?e<r?1:-1:o(a),n(e,r,a,t)}}},87506:(t,e,r)=>{var n=r(66837),i=r(84261),o=r(89492),a=r(90200),c=r(39672);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},87955:(t,e,r)=>{t.exports=r(84031)()},89167:(t,e,r)=>{t.exports=r(41547)(r(85718),"DataView")},89185:t=>{t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},89492:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return i.call(e,t)?e[t]:void 0}},89605:(t,e,r)=>{t.exports=r(65662)(Object.keys,Object)},89624:t=>{t.exports=function(t){return function(e){return t(e)}}},90200:(t,e,r)=>{var n=r(58141),i=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:i.call(e,t)}},90453:(t,e,r)=>{var n=r(2984),i=r(99180),o=r(48169);t.exports=function(t){return t&&t.length?n(t,o,i):void 0}},90851:t=>{t.exports=function(t,e){return null==t?void 0:t[e]}},91290:t=>{t.exports=function(t,e,r,n){for(var i=t.length,o=r+(n?1:-1);n?o--:++o<i;)if(e(t[o],o,t))return o;return -1}},91928:(t,e,r)=>{var n=r(41547);t.exports=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}()},92311:(t,e,r)=>{Promise.resolve().then(r.bind(r,6335))},92662:(t,e,r)=>{var n=r(46328),i=r(80704),o=r(71960),a=r(58276),c=r(95308),u=r(2408);t.exports=function(t,e,r){var l=-1,s=i,f=t.length,p=!0,h=[],d=h;if(r)p=!1,s=o;else if(f>=200){var y=e?null:c(t);if(y)return u(y);p=!1,s=a,d=new n}else d=e?[]:h;e:for(;++l<f;){var v=t[l],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue e;e&&d.push(m),h.push(v)}else s(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},93311:(t,e,r)=>{var n=r(34883),i=r(7651);t.exports=function(t){for(var e=i(t),r=e.length;r--;){var o=e[r],a=t[o];e[r]=[o,a,n(a)]}return e}},93490:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return"number"==typeof t||i(t)&&"[object Number]"==n(t)}},93780:(t,e,r)=>{"use strict";t.exports=r(66992)},94388:(t,e,r)=>{var n=r(57797);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},94735:t=>{"use strict";t.exports=require("events")},95308:(t,e,r)=>{var n=r(34772),i=r(36959),o=r(2408);t.exports=n&&1/o(new n([,-0]))[1]==1/0?function(t){return new n(t)}:i},95746:(t,e,r)=>{var n=r(15909),i=r(29205),o=r(29508),a=r(61320),c=r(19976);function u(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}u.prototype.clear=n,u.prototype.delete=i,u.prototype.get=o,u.prototype.has=a,u.prototype.set=c,t.exports=u},96678:(t,e,r)=>{var n=r(91290),i=r(39774),o=r(74610);t.exports=function(t,e,r){return e==e?o(t,e,r):n(t,i,r)}},96834:(t,e,r)=>{"use strict";r.d(e,{E:()=>u});var n=r(60687);r(43210);var i=r(8730),o=r(24224),a=r(4780);let c=(0,o.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function u({className:t,variant:e,asChild:r=!1,...o}){let u=r?i.DX:"span";return(0,n.jsx)(u,{"data-slot":"badge",className:(0,a.cn)(c({variant:e}),t),...o})}},96963:(t,e,r)=>{"use strict";r.d(e,{B:()=>u});var n,i=r(43210),o=r(66156),a=(n||(n=r.t(i,2)))[" useId ".trim().toString()]||(()=>void 0),c=0;function u(t){let[e,r]=i.useState(a());return(0,o.N)(()=>{t||r(t=>t??String(c++))},[t]),t||(e?`radix-${e}`:"")}},97887:(t,e,r)=>{Promise.resolve().then(r.bind(r,75900))},98451:(t,e,r)=>{var n=r(29395),i=r(27467);t.exports=function(t){return!0===t||!1===t||i(t)&&"[object Boolean]"==n(t)}},98798:t=>{var e=Math.ceil,r=Math.max;t.exports=function(t,n,i,o){for(var a=-1,c=r(e((n-t)/(i||1)),0),u=Array(c);c--;)u[o?c:++a]=t,t+=i;return u}},99114:(t,e,r)=>{var n=r(12344),i=r(7651);t.exports=function(t,e){return t&&n(t,e,i)}},99180:t=>{t.exports=function(t,e){return t>e}},99525:t=>{t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}}};var e=require("../../webpack-runtime.js");e.C(t);var r=t=>e(e.s=t),n=e.X(0,[447,991,658,400,639,653],()=>r(45180));module.exports=n})();