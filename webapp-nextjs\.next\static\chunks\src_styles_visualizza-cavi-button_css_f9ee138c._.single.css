/* [project]/src/styles/visualizza-cavi-button.css [app-client] (css) */
.visualizza-cavi-button {
  user-select: none;
  white-space: nowrap;
  cursor: pointer;
  height: 50px;
  color: inherit;
  background: #e8e8e8;
  border: 2px solid #000;
  justify-content: center;
  align-items: center;
  padding: 0 30px;
  font-family: inherit;
  text-decoration: none;
  transition: all 50ms linear;
  display: inline-flex;
  position: relative;
}

.visualizza-cavi-button:before, .visualizza-cavi-button:after {
  content: "";
  background: #e8e8e8;
  transition: all .2s linear;
  position: absolute;
}

.visualizza-cavi-button:before {
  width: calc(100% + 6px);
  height: calc(100% - 16px);
  top: 8px;
  left: -3px;
}

.visualizza-cavi-button:after {
  width: calc(100% - 16px);
  height: calc(100% + 6px);
  top: -3px;
  left: 8px;
}

.visualizza-cavi-button:hover {
  cursor: crosshair;
}

.visualizza-cavi-button:active {
  transform: scale(.95);
}

.visualizza-cavi-button:hover:before {
  height: calc(100% - 32px);
  top: 16px;
}

.visualizza-cavi-button:hover:after {
  width: calc(100% - 32px);
  left: 16px;
}

.visualizza-cavi-button span {
  z-index: 3;
  font-size: 15px;
  font-weight: 600;
  position: relative;
}

.visualizza-cavi-button.active {
  background: #d4edda;
  border-color: #28a745;
}

.visualizza-cavi-button.active:before, .visualizza-cavi-button.active:after {
  background: #d4edda;
}

.visualizza-cavi-button .icon {
  z-index: 3;
  margin-right: 8px;
  position: relative;
}

@media (width <= 768px) {
  .visualizza-cavi-button {
    height: 40px;
    padding: 0 20px;
  }

  .visualizza-cavi-button span {
    font-size: 14px;
  }
}

/*# sourceMappingURL=src_styles_visualizza-cavi-button_css_f9ee138c._.single.css.map*/