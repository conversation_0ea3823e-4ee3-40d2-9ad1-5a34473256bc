"""
Modulo per la generazione di rapportini stampabili in formato PDF.
Permette di creare rapportini A3/A4 per uso senza mobile app.
"""

import logging
import os
from datetime import date, datetime
from typing import Dict, List, Any, Optional
from reportlab.lib.pagesizes import A4, A3
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus.frames import Frame
from reportlab.platypus.doctemplate import PageTemplate, BaseDocTemplate

# Import del modulo database
try:
    from modules.database_pg import Database
except ImportError:
    logging.error("❌ Impossibile importare il modulo database")
    raise


class GeneratoreRapportino:
    """Classe per la generazione di rapportini stampabili."""
    
    def __init__(self):
        self.database = Database()
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
    
    def _setup_custom_styles(self):
        """Configura gli stili personalizzati per il documento."""
        # Stile per il titolo principale
        self.styles.add(ParagraphStyle(
            name='TitoloPrincipale',
            parent=self.styles['Title'],
            fontSize=16,
            spaceAfter=20,
            alignment=1,  # Centrato
            textColor=colors.darkblue
        ))
        
        # Stile per i sottotitoli
        self.styles.add(ParagraphStyle(
            name='Sottotitolo',
            parent=self.styles['Heading2'],
            fontSize=12,
            spaceAfter=10,
            textColor=colors.darkblue
        ))
        
        # Stile per il testo normale
        self.styles.add(ParagraphStyle(
            name='TestoNormale',
            parent=self.styles['Normal'],
            fontSize=10,
            spaceAfter=6
        ))
    
    def genera_rapportino_comanda(self, codice_comanda: str, formato_pagina: str = "A4",
                                 includi_dettagli_cavi: bool = True,
                                 includi_note_lavoro: bool = True) -> str:
        """
        Genera un rapportino stampabile per una comanda.
        
        Args:
            codice_comanda: Codice della comanda
            formato_pagina: "A4" o "A3"
            includi_dettagli_cavi: Se includere i dettagli dei cavi
            includi_note_lavoro: Se includere le note di lavoro
            
        Returns:
            str: Percorso del file PDF generato
        """
        try:
            # Ottieni i dati della comanda
            dati_comanda = self._ottieni_dati_comanda(codice_comanda)
            if not dati_comanda:
                raise ValueError(f"Comanda {codice_comanda} non trovata")
            
            # Configura il formato pagina
            page_size = A3 if formato_pagina.upper() == "A3" else A4
            
            # Crea il nome del file
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            nome_file = f"rapportino_{codice_comanda}_{timestamp}.pdf"
            percorso_file = os.path.join("webapp", "static", "rapportini", nome_file)
            
            # Assicurati che la directory esista
            os.makedirs(os.path.dirname(percorso_file), exist_ok=True)
            
            # Crea il documento PDF
            doc = SimpleDocTemplate(percorso_file, pagesize=page_size,
                                  rightMargin=2*cm, leftMargin=2*cm,
                                  topMargin=2*cm, bottomMargin=2*cm)
            
            # Costruisci il contenuto
            story = []
            
            # Intestazione
            story.extend(self._crea_intestazione(dati_comanda))
            
            # Informazioni comanda
            story.extend(self._crea_sezione_comanda(dati_comanda))
            
            # Dettagli cavi se richiesto
            if includi_dettagli_cavi:
                story.extend(self._crea_sezione_cavi(dati_comanda))
            
            # Sezione per compilazione manuale
            story.extend(self._crea_sezione_compilazione())
            
            # Note di lavoro se richiesto
            if includi_note_lavoro:
                story.extend(self._crea_sezione_note())
            
            # Firma
            story.extend(self._crea_sezione_firma())
            
            # Genera il PDF
            doc.build(story)
            
            logging.info(f"✅ Rapportino generato: {percorso_file}")
            return percorso_file
            
        except Exception as e:
            logging.error(f"❌ Errore nella generazione del rapportino: {str(e)}")
            raise
    
    def _ottieni_dati_comanda(self, codice_comanda: str) -> Optional[Dict[str, Any]]:
        """Ottiene i dati della comanda dal database."""
        try:
            with self.database.get_connection() as conn:
                cursor = conn.cursor()
                
                # Query per ottenere i dati della comanda
                cursor.execute("""
                    SELECT c.*, cant.commessa, cant.nome_cliente, cant.indirizzo_cantiere,
                           cant.citta_cantiere, cant.nazione_cantiere
                    FROM Comande c
                    JOIN Cantieri cant ON c.id_cantiere = cant.id_cantiere
                    WHERE c.codice_comanda = %s
                """, (codice_comanda,))
                
                comanda = cursor.fetchone()
                if not comanda:
                    return None
                
                # Ottieni i cavi assegnati
                cursor.execute("""
                    SELECT cd.id_cavo, cv.tipologia, cv.formazione, cv.lunghezza_prevista,
                           cv.stato, cv.metri_installati
                    FROM ComandeDettaglio cd
                    JOIN Cavi cv ON cd.id_cavo = cv.id_cavo AND cd.id_cantiere = cv.id_cantiere
                    WHERE cd.codice_comanda = %s
                    ORDER BY cd.id_cavo
                """, (codice_comanda,))
                
                cavi = cursor.fetchall()
                
                return {
                    'comanda': dict(comanda),
                    'cavi': [dict(cavo) for cavo in cavi]
                }
                
        except Exception as e:
            logging.error(f"❌ Errore nel recupero dati comanda: {str(e)}")
            return None
    
    def _crea_intestazione(self, dati: Dict[str, Any]) -> List:
        """Crea l'intestazione del rapportino."""
        story = []
        
        # Titolo principale
        titolo = f"RAPPORTINO DI LAVORO - COMANDA {dati['comanda']['codice_comanda']}"
        story.append(Paragraph(titolo, self.styles['TitoloPrincipale']))
        story.append(Spacer(1, 20))
        
        # Informazioni cantiere
        cantiere_info = f"""
        <b>Cantiere:</b> {dati['comanda']['commessa']}<br/>
        <b>Cliente:</b> {dati['comanda'].get('nome_cliente', 'N/A')}<br/>
        <b>Indirizzo:</b> {dati['comanda'].get('indirizzo_cantiere', 'N/A')}, 
        {dati['comanda'].get('citta_cantiere', 'N/A')}, {dati['comanda'].get('nazione_cantiere', 'N/A')}<br/>
        <b>Data generazione:</b> {date.today().strftime('%d/%m/%Y')}
        """
        story.append(Paragraph(cantiere_info, self.styles['TestoNormale']))
        story.append(Spacer(1, 15))
        
        return story
    
    def _crea_sezione_comanda(self, dati: Dict[str, Any]) -> List:
        """Crea la sezione con le informazioni della comanda."""
        story = []
        
        story.append(Paragraph("INFORMAZIONI COMANDA", self.styles['Sottotitolo']))
        
        # Tabella con i dati della comanda
        data_comanda = [
            ['Codice Comanda:', dati['comanda']['codice_comanda']],
            ['Tipo Comanda:', dati['comanda']['tipo_comanda']],
            ['Descrizione:', dati['comanda'].get('descrizione', 'N/A')],
            ['Responsabile:', dati['comanda'].get('responsabile', 'N/A')],
            ['Data Creazione:', dati['comanda']['data_creazione'].strftime('%d/%m/%Y') if dati['comanda']['data_creazione'] else 'N/A'],
            ['Data Scadenza:', dati['comanda']['data_scadenza'].strftime('%d/%m/%Y') if dati['comanda']['data_scadenza'] else 'N/A'],
            ['Stato:', dati['comanda']['stato']],
            ['N. Componenti Squadra:', str(dati['comanda'].get('numero_componenti_squadra', 1))]
        ]
        
        table = Table(data_comanda, colWidths=[4*cm, 8*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _crea_sezione_cavi(self, dati: Dict[str, Any]) -> List:
        """Crea la sezione con l'elenco dei cavi."""
        story = []
        
        story.append(Paragraph("CAVI ASSEGNATI", self.styles['Sottotitolo']))
        
        if not dati['cavi']:
            story.append(Paragraph("Nessun cavo assegnato a questa comanda.", self.styles['TestoNormale']))
        else:
            # Intestazione tabella
            headers = ['ID Cavo', 'Tipologia', 'Formazione', 'Lunghezza\nPrevista (m)', 'Stato', 'Metri\nInstallati']
            table_data = [headers]
            
            # Dati dei cavi
            for cavo in dati['cavi']:
                row = [
                    cavo['id_cavo'],
                    cavo.get('tipologia', 'N/A'),
                    cavo.get('formazione', 'N/A'),
                    str(cavo.get('lunghezza_prevista', 0)),
                    cavo.get('stato', 'N/A'),
                    str(cavo.get('metri_installati', 0))
                ]
                table_data.append(row)
            
            table = Table(table_data, colWidths=[3*cm, 3*cm, 3*cm, 2*cm, 2*cm, 2*cm])
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ]))
            
            story.append(table)
        
        story.append(Spacer(1, 20))
        return story
    
    def _crea_sezione_compilazione(self) -> List:
        """Crea la sezione per la compilazione manuale."""
        story = []
        
        story.append(Paragraph("DATI DI LAVORO (da compilare)", self.styles['Sottotitolo']))
        
        # Tabella per i dati di lavoro
        data_lavoro = [
            ['Data Lavoro:', '___/___/______'],
            ['Ore Inizio Lavoro:', '___:___'],
            ['Ore Fine Lavoro:', '___:___'],
            ['Totale Ore Lavorate:', '______'],
            ['Metri Posati:', '______'],
            ['Bobina Utilizzata:', '________________________'],
            ['Attività Svolta:', '☐ POSATO  ☐ COLLEGATO  ☐ TESTATO'],
        ]
        
        table = Table(data_lavoro, colWidths=[5*cm, 8*cm])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), colors.lightgrey),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(table)
        story.append(Spacer(1, 20))
        
        return story
    
    def _crea_sezione_note(self) -> List:
        """Crea la sezione per le note di lavoro."""
        story = []
        
        story.append(Paragraph("NOTE DI LAVORO", self.styles['Sottotitolo']))
        
        # Area per le note
        note_text = """
        <b>Note del responsabile:</b><br/>
        _______________________________________________________________________________<br/>
        _______________________________________________________________________________<br/>
        _______________________________________________________________________________<br/>
        _______________________________________________________________________________<br/><br/>
        
        <b>Problemi riscontrati:</b><br/>
        _______________________________________________________________________________<br/>
        _______________________________________________________________________________<br/>
        _______________________________________________________________________________<br/>
        _______________________________________________________________________________<br/>
        """
        
        story.append(Paragraph(note_text, self.styles['TestoNormale']))
        story.append(Spacer(1, 20))
        
        return story
    
    def _crea_sezione_firma(self) -> List:
        """Crea la sezione per le firme."""
        story = []
        
        story.append(Paragraph("FIRME", self.styles['Sottotitolo']))
        
        # Tabella per le firme
        firma_data = [
            ['Responsabile Lavori:', '________________________', 'Data: ___/___/______'],
            ['Capo Cantiere:', '________________________', 'Data: ___/___/______'],
        ]
        
        table = Table(firma_data, colWidths=[4*cm, 6*cm, 4*cm])
        table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('VALIGN', (0, 0), (-1, -1), 'TOP'),
        ]))
        
        story.append(table)
        
        return story


def genera_rapportino_stampa(codice_comanda: str, formato_pagina: str = "A4",
                           includi_dettagli_cavi: bool = True,
                           includi_note_lavoro: bool = True) -> str:
    """
    Funzione di utilità per generare un rapportino stampabile.
    
    Args:
        codice_comanda: Codice della comanda
        formato_pagina: "A4" o "A3"
        includi_dettagli_cavi: Se includere i dettagli dei cavi
        includi_note_lavoro: Se includere le note di lavoro
        
    Returns:
        str: Percorso del file PDF generato
    """
    generatore = GeneratoreRapportino()
    return generatore.genera_rapportino_comanda(
        codice_comanda, formato_pagina, includi_dettagli_cavi, includi_note_lavoro
    )
