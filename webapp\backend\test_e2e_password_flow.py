#!/usr/bin/env python3
"""
Test End-to-End del Flusso Completo di Gestione Password
Simula il comportamento reale degli utenti per tutti e 3 i tipi.
"""

import os
import sys
import requests
import json
import time
from pathlib import Path
from urllib.parse import parse_qs, urlparse

# Aggiungi il path del backend
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Carica le variabili d'ambiente
try:
    from dotenv import load_dotenv
    env_file = backend_dir / '.env'
    if env_file.exists():
        load_dotenv(env_file)
except ImportError:
    pass

# Configurazione
API_BASE_URL = "http://localhost:8001/api"
FRONTEND_BASE_URL = "http://localhost:3000/api"

class PasswordFlowTester:
    """Tester per il flusso completo di gestione password."""
    
    def __init__(self):
        self.session = requests.Session()
        self.reset_tokens = {}
    
    def test_user_password_change_flow(self):
        """Testa il flusso di cambio password per utente autenticato."""
        print("\n👤 Test Flusso Cambio Password - Utente Autenticato")
        print("=" * 60)
        
        # Simula login utente (normalmente avresti un token JWT)
        # Per questo test, usiamo un mock token
        mock_token = "mock-jwt-token-for-testing"
        
        # Test validazione password
        print("🔍 Step 1: Validazione nuova password")
        test_passwords = [
            ("weak", "Password debole"),
            ("Strong123!", "Password forte")
        ]
        
        for password, desc in test_passwords:
            response = self.session.post(
                f"{API_BASE_URL}/password/validate-password",
                json={"password": password},
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ {desc}: Score {data['strength_score']}, Valida: {data['is_valid']}")
            else:
                print(f"   ❌ Errore validazione: {response.status_code}")
                return False
        
        # Test cambio password (simulato - in realtà richiederebbe autenticazione)
        print("\n🔄 Step 2: Tentativo cambio password")
        change_data = {
            "current_password": "old_password_123",
            "new_password": "StrongPass123!",
            "confirm_password": "StrongPass123!"
        }
        
        # Nota: Questo fallirà perché non siamo autenticati, ma testa l'endpoint
        response = self.session.post(
            f"{API_BASE_URL}/password/change-password",
            json=change_data,
            headers={
                "Content-Type": "application/json",
                "Authorization": f"Bearer {mock_token}"
            }
        )
        
        print(f"   Status: {response.status_code}")
        if response.status_code == 401:
            print("   ✅ Endpoint protetto correttamente (richiede autenticazione)")
        elif response.status_code == 200:
            print("   ✅ Cambio password completato")
        else:
            print(f"   ⚠️  Risposta inaspettata: {response.status_code}")
        
        return True
    
    def test_password_reset_flow(self):
        """Testa il flusso completo di reset password."""
        print("\n🔄 Test Flusso Reset Password Completo")
        print("=" * 60)
        
        test_users = [
            {
                "email": "<EMAIL>",
                "user_type": "user",
                "description": "Admin/Utente Standard"
            },
            {
                "email": "<EMAIL>",
                "user_type": "cantiere", 
                "description": "Utente Cantiere"
            }
        ]
        
        for user in test_users:
            print(f"\n📧 Test per {user['description']}")
            print("-" * 40)
            
            # Step 1: Richiesta reset password
            print("Step 1: Richiesta reset password")
            response = self.session.post(
                f"{API_BASE_URL}/password/request-password-reset",
                json={
                    "email": user["email"],
                    "user_type": user["user_type"]
                },
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Richiesta inviata: {data.get('message')}")
                
                # In un test reale, qui estrarresti il token dall'email
                # Per questo test, generiamo un token mock
                mock_token = f"mock-reset-token-{user['user_type']}-{int(time.time())}"
                self.reset_tokens[user["email"]] = mock_token
                
            else:
                print(f"   ❌ Errore richiesta: {response.status_code}")
                continue
            
            # Step 2: Verifica token (simulato)
            print("Step 2: Verifica token reset")
            response = self.session.get(
                f"{API_BASE_URL}/password/verify-reset-token",
                params={"token": mock_token}
            )
            
            print(f"   Status verifica token: {response.status_code}")
            if response.status_code == 400:
                print("   ✅ Token mock correttamente rifiutato")
            
            # Step 3: Conferma reset con nuova password
            print("Step 3: Conferma reset password")
            response = self.session.post(
                f"{API_BASE_URL}/password/confirm-password-reset",
                json={
                    "token": mock_token,
                    "new_password": "NewStrongPass123!",
                    "confirm_password": "NewStrongPass123!"
                },
                headers={"Content-Type": "application/json"}
            )
            
            print(f"   Status conferma: {response.status_code}")
            if response.status_code == 400:
                print("   ✅ Token mock correttamente rifiutato")
            elif response.status_code == 200:
                print("   ✅ Reset completato")
        
        return True
    
    def test_frontend_integration(self):
        """Testa l'integrazione con il frontend."""
        print("\n🌐 Test Integrazione Frontend")
        print("=" * 60)
        
        # Test API routes del frontend
        frontend_tests = [
            {
                "endpoint": "/password/validate-password",
                "method": "POST",
                "data": {"password": "TestPass123!"},
                "description": "Validazione password via frontend"
            },
            {
                "endpoint": "/password/request-password-reset",
                "method": "POST", 
                "data": {"email": "<EMAIL>", "user_type": "user"},
                "description": "Richiesta reset via frontend"
            }
        ]
        
        for test in frontend_tests:
            print(f"\n🔧 {test['description']}")
            
            if test["method"] == "POST":
                response = self.session.post(
                    f"{FRONTEND_BASE_URL}{test['endpoint']}",
                    json=test["data"],
                    headers={"Content-Type": "application/json"}
                )
            else:
                response = self.session.get(
                    f"{FRONTEND_BASE_URL}{test['endpoint']}"
                )
            
            print(f"   Status: {response.status_code}")
            if response.status_code == 200:
                print("   ✅ API route funzionante")
            else:
                print(f"   ⚠️  Risposta: {response.status_code}")
        
        return True
    
    def test_security_features(self):
        """Testa le funzionalità di sicurezza."""
        print("\n🔒 Test Funzionalità di Sicurezza")
        print("=" * 60)
        
        # Test rate limiting
        print("⏱️  Test Rate Limiting")
        email = f"ratelimit-{int(time.time())}@test.com"
        
        for i in range(3):
            response = self.session.post(
                f"{API_BASE_URL}/password/request-password-reset",
                json={"email": email, "user_type": "user"},
                headers={"Content-Type": "application/json"}
            )
            print(f"   Richiesta {i+1}: {response.status_code}")
        
        # Test validazione input
        print("\n🛡️  Test Validazione Input")
        invalid_tests = [
            {"email": "", "user_type": "user"},  # Email vuota
            {"email": "invalid-email", "user_type": "user"},  # Email non valida
            {"email": "<EMAIL>", "user_type": "invalid"},  # Tipo utente non valido
        ]
        
        for invalid_data in invalid_tests:
            response = self.session.post(
                f"{API_BASE_URL}/password/request-password-reset",
                json=invalid_data,
                headers={"Content-Type": "application/json"}
            )
            print(f"   Input non valido: {response.status_code}")
        
        return True
    
    def run_all_tests(self):
        """Esegue tutti i test end-to-end."""
        print("🧪 TEST END-TO-END GESTIONE PASSWORD")
        print("=" * 80)
        
        tests = [
            ("Flusso Cambio Password", self.test_user_password_change_flow),
            ("Flusso Reset Password", self.test_password_reset_flow),
            ("Integrazione Frontend", self.test_frontend_integration),
            ("Funzionalità Sicurezza", self.test_security_features)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🔄 Esecuzione: {test_name}")
            try:
                result = test_func()
                results.append((test_name, result))
            except Exception as e:
                print(f"❌ Errore durante {test_name}: {e}")
                results.append((test_name, False))
        
        # Risultati finali
        print("\n" + "=" * 80)
        print("📊 RISULTATI TEST END-TO-END")
        print("=" * 80)
        
        passed = 0
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
            if result:
                passed += 1
        
        total = len(results)
        print(f"\n🎯 Risultato: {passed}/{total} test superati")
        
        if passed == total:
            print("🎉 Tutti i test end-to-end superati!")
            print("💡 Il sistema è pronto per l'uso in produzione.")
            return True
        else:
            print("🔧 Alcuni test falliti. Verifica la configurazione.")
            return False

def main():
    """Funzione principale."""
    tester = PasswordFlowTester()
    success = tester.run_all_tests()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
