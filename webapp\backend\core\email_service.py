"""
Servizio Email Sicuro per Reset Password
Gestisce l'invio di email per il recupero password con template HTML.
"""

import smtplib
import os
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, Dict, Any
import logging
from dataclasses import dataclass
from jinja2 import Template
from datetime import datetime

from .email_config import email_config_manager, email_template_manager

logger = logging.getLogger(__name__)

class EmailService:
    """Servizio per l'invio di email sicure."""

    def __init__(self):
        self.config = email_config_manager.get_current_config()
        
    def send_password_reset_email(self, 
                                  to_email: str, 
                                  reset_link: str, 
                                  user_name: str = None,
                                  user_type: str = "utente") -> bool:
        """
        Invia email per il reset della password.
        
        Args:
            to_email: Email destinatario
            reset_link: Link per il reset
            user_name: Nome dell'utente (opzionale)
            user_type: Tipo di utente (utente/cantiere)
            
        Returns:
            bool: True se email inviata con successo
        """
        try:
            # Usa il template HTML migliorato
            html_template = email_template_manager.get_password_reset_template()

            # Renderizza il template
            template = Template(html_template)
            html_content = template.render(
                reset_link=reset_link,
                user_name=user_name or "Utente",
                user_type=user_type,
                current_date=datetime.now().strftime('%d/%m/%Y'),
                current_time=datetime.now().strftime('%H:%M')
            )
            
            # Template testo semplice
            text_content = f"""
Reset Password - CMS

Ciao{f' {user_name}' if user_name else ''}!

Hai richiesto il reset della password per il tuo account {user_type} nel sistema CMS.

Per reimpostare la tua password, visita questo link:
{reset_link}

IMPORTANTE:
- Questo link è valido per 30 minuti
- Può essere utilizzato una sola volta
- Se non hai richiesto questo reset, ignora questa email

Questa email è stata generata automaticamente dal sistema CMS.
            """
            
            # Crea il messaggio
            msg = MIMEMultipart('alternative')
            msg['Subject'] = f"🔐 Reset Password CMS - {user_type.title()}"
            msg['From'] = f"{self.config.from_name} <{self.config.from_email}>"
            msg['To'] = to_email
            
            # Aggiungi entrambe le versioni
            part1 = MIMEText(text_content, 'plain', 'utf-8')
            part2 = MIMEText(html_content, 'html', 'utf-8')
            
            msg.attach(part1)
            msg.attach(part2)
            
            # Invia l'email
            return self._send_email(msg, to_email)
            
        except Exception as e:
            logger.error(f"Error sending password reset email to {to_email}: {str(e)}")
            return False
    
    def send_password_changed_notification(self, 
                                         to_email: str, 
                                         user_name: str = None,
                                         user_type: str = "utente") -> bool:
        """
        Invia notifica di cambio password avvenuto.
        
        Args:
            to_email: Email destinatario
            user_name: Nome dell'utente
            user_type: Tipo di utente
            
        Returns:
            bool: True se email inviata con successo
        """
        try:
            # Usa il template HTML migliorato
            html_template = email_template_manager.get_password_changed_template()

            # Renderizza il template
            template = Template(html_template)
            html_content = template.render(
                user_name=user_name or "Utente",
                user_type=user_type,
                current_date=datetime.now().strftime('%d/%m/%Y'),
                current_time=datetime.now().strftime('%H:%M')
            )
            
            text_content = f"""
Password Modificata - CMS

Ciao {user_name or 'Utente'}!

La password del tuo account {user_type} è stata modificata con successo.

Se non sei stato tu, contatta immediatamente l'amministratore del sistema.

Data e ora: {datetime.now().strftime('%d/%m/%Y alle %H:%M')}
            """
            
            msg = MIMEMultipart('alternative')
            msg['Subject'] = f"✅ Password Modificata - CMS {user_type.title()}"
            msg['From'] = f"{self.config.from_name} <{self.config.from_email}>"
            msg['To'] = to_email
            
            part1 = MIMEText(text_content, 'plain', 'utf-8')
            part2 = MIMEText(html_content, 'html', 'utf-8')
            
            msg.attach(part1)
            msg.attach(part2)
            
            return self._send_email(msg, to_email)
            
        except Exception as e:
            logger.error(f"Error sending password changed notification to {to_email}: {str(e)}")
            return False
    
    def _send_email(self, msg: MIMEMultipart, to_email: str) -> bool:
        """
        Invia effettivamente l'email tramite SMTP.

        Args:
            msg: Messaggio da inviare
            to_email: Email destinatario

        Returns:
            bool: True se inviata con successo
        """
        try:
            # Modalità testing - stampa invece di inviare
            testing_mode = os.getenv('EMAIL_TESTING_MODE', 'false').lower() == 'true'
            if testing_mode:
                print("\n" + "="*60)
                print("📧 EMAIL TESTING MODE - SIMULAZIONE INVIO")
                print("="*60)
                print(f"📤 To: {to_email}")
                print(f"📋 Subject: {msg['Subject']}")
                print(f"👤 From: {msg['From']}")
                print("\n📄 CONTENUTO:")
                print("-"*40)
                # Estrai il contenuto HTML se presente
                for part in msg.walk():
                    if part.get_content_type() == "text/html":
                        content = part.get_payload(decode=True).decode('utf-8')
                        # Mostra solo i primi 500 caratteri per brevità
                        print(content[:500] + "..." if len(content) > 500 else content)
                        break
                print("="*60)
                print("✅ Email simulata con successo!")
                return True

            # Modalità produzione - invio reale
            # Verifica configurazione
            if not self.config.username or not self.config.password:
                logger.error("SMTP credentials not configured")
                return False

            # Connessione SMTP
            if self.config.use_ssl:
                server = smtplib.SMTP_SSL(self.config.host, self.config.port)
            else:
                server = smtplib.SMTP(self.config.host, self.config.port)
                if self.config.use_tls:
                    server.starttls()

            server.login(self.config.username, self.config.password)

            # Invia email
            text = msg.as_string()
            server.sendmail(self.config.from_email, to_email, text)
            server.quit()

            logger.info(f"Email sent successfully to {to_email}")
            return True

        except Exception as e:
            logger.error(f"SMTP error sending email to {to_email}: {str(e)}")
            return False

# Istanza globale
email_service = EmailService()
