{"version": 3, "file": "static/js/700.3d337adf.chunk.js", "mappings": "gNASO,MAAMA,EAAa,CAExBC,MAAOC,EAAAA,EAAYD,MACnBE,OAAQD,EAAAA,EAAYC,OACpBC,eAAgBF,EAAAA,EAAYE,eAG5BC,YAAaC,EAAAA,EAAgBC,cAC7BC,YAAaF,EAAAA,EAAgBE,YAC7BC,eAAgBH,EAAAA,EAAgBG,eAChCC,eAAgBJ,EAAAA,EAAgBI,eAGhCC,QAASC,EAAAA,EAAYD,QACrBE,QAASD,EAAAA,EAAYC,QACrBC,WAAYF,EAAAA,EAAYE,WACxBC,WAAYH,EAAAA,EAAYG,WACxBC,WAAYJ,EAAAA,EAAYI,WACxBC,aAAcL,EAAAA,EAAYK,aAG1BC,kBAAmBC,EAAAA,EAAsBD,kBACzCE,kBAAmBD,EAAAA,EAAsBC,kBACzCC,qBAAsBF,EAAAA,EAAsBE,qBAC5CC,qBAAsBH,EAAAA,EAAsBG,qBAC5CC,qBAAsBJ,EAAAA,EAAsBI,qBAG5CC,aAAcL,EAAAA,EAAsBK,aACpCC,gBAAiBN,EAAAA,EAAsBM,gBACvCC,gBAAiBP,EAAAA,EAAsBO,gBACvCC,gBAAiBR,EAAAA,EAAsBQ,gBAGvCC,aAAcC,EAAAA,EAAiBD,aAC/BE,aAAcD,EAAAA,EAAiBC,aAC/BC,aAAcF,EAAAA,EAAiBE,aAC/BC,aAAcH,EAAAA,EAAiBG,aAG/BC,iBAAkBC,EAAAA,QAAaD,iBAC/BE,YAAaD,EAAAA,QAAaC,YAG1BC,WAAYC,EAAAA,EAAcD,W,0QCyJ5B,QAnLA,SAAsBE,GAA+C,IAA9C,UAAEC,EAAS,OAAEC,EAAM,SAAEC,EAAQ,WAAEC,GAAYJ,EAChE,MAAOK,EAAkBC,IAAuBC,EAAAA,EAAAA,WAAS,IAClDC,EAAmBC,IAAwBF,EAAAA,EAAAA,UAAS,OACpDG,EAASC,IAAcJ,EAAAA,EAAAA,WAAS,GAsBjCK,EAAcC,GACbA,EACE,IAAIC,KAAKD,GAAYE,mBAAmB,SADvB,IAIpBC,EAAwBC,IAC5B,IAAKA,EAAc,OAAO,EAC1B,MAAMC,EAAQ,IAAIJ,KAElB,OADiB,IAAIA,KAAKG,GACRC,CAAK,EAGnBC,EAA6BF,IACjC,IAAKA,EAAc,OAAO,EAC1B,MAAMC,EAAQ,IAAIJ,KAEZM,EADW,IAAIN,KAAKG,GACEC,EACtBG,EAAWC,KAAKC,KAAKH,EAAQ,OACnC,OAAOC,GAAY,IAAMA,EAAW,CAAC,EAavC,OAAyB,IAArBpB,EAAUuB,QAEVC,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CAACC,GAAI,CAAEC,EAAG,EAAGC,UAAW,UAAWC,SAAA,EACvCC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,KAAKC,MAAM,iBAAgBJ,SAAC,2CAGhDC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,QAAQC,MAAM,iBAAiBP,GAAI,CAAEQ,GAAI,GAAIL,SAAC,sEAQtEL,EAAAA,EAAAA,MAAAW,EAAAA,SAAA,CAAAN,SAAA,EACEC,EAAAA,EAAAA,KAACM,EAAAA,EAAc,CAACC,UAAWZ,EAAAA,EAAMI,UAC/BL,EAAAA,EAAAA,MAACc,EAAAA,EAAK,CAAAT,SAAA,EACJC,EAAAA,EAAAA,KAACS,EAAAA,EAAS,CAAAV,UACRL,EAAAA,EAAAA,MAACgB,EAAAA,EAAQ,CAAAX,SAAA,EACPC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,YACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,eACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,mBACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,yBACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,6BACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aACnBC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UAACC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,mBAGvBC,EAAAA,EAAAA,KAACY,EAAAA,EAAS,CAAAb,SACP7B,EAAU2C,KAAKC,IACd,MAAMC,GAzCY7B,EAyC6B4B,EAAUE,2BAxC/D/B,EAAqBC,GAChB,CAAE+B,MAAO,UAAWd,MAAO,SAEhCf,EAA0BF,GACrB,CAAE+B,MAAO,cAAed,MAAO,WAEjC,CAAEc,MAAO,SAAUd,MAAO,YAPLjB,MA2ClB,OACEQ,EAAAA,EAAAA,MAACgB,EAAAA,EAAQ,CAA8BQ,OAAK,EAAAnB,SAAA,EAC1CC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,QAAQiB,WAAW,OAAMpB,SAC1Ce,EAAUM,UAGfpB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,SAAEe,EAAUO,SACtBrB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,SAAEe,EAAUQ,WACtBtB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,QAAQqB,WAAW,YAAWxB,SAC/Ce,EAAUU,kBAGfxB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,SAAElB,EAAWiC,EAAUW,sBACjCzB,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRL,EAAAA,EAAAA,MAACgC,EAAAA,EAAG,CAAC9B,GAAI,CAAE+B,QAAS,OAAQC,WAAY,SAAUC,IAAK,GAAI9B,SAAA,CACxDlB,EAAWiC,EAAUE,4BACrB/B,EAAqB6B,EAAUE,8BAC9BhB,EAAAA,EAAAA,KAAC8B,EAAAA,EAAW,CAAC3B,MAAM,QAAQ4B,SAAS,UAErC3C,EAA0B0B,EAAUE,8BACnChB,EAAAA,EAAAA,KAAC8B,EAAAA,EAAW,CAAC3B,MAAM,UAAU4B,SAAS,gBAI5C/B,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRC,EAAAA,EAAAA,KAACgC,EAAAA,EAAI,CACHf,MAAOF,EAAkBE,MACzBd,MAAOY,EAAkBZ,MACzB8B,KAAK,aAGTjC,EAAAA,EAAAA,KAACW,EAAAA,EAAS,CAAAZ,UACRL,EAAAA,EAAAA,MAACgC,EAAAA,EAAG,CAAC9B,GAAI,CAAE+B,QAAS,OAAQE,IAAK,IAAM9B,SAAA,EACrCC,EAAAA,EAAAA,KAACkC,EAAAA,EAAU,CACTD,KAAK,QACLE,QAASA,IAAMhE,EAAO2C,GACtBsB,MAAM,WAAUrC,UAEhBC,EAAAA,EAAAA,KAACqC,EAAAA,EAAQ,CAACN,SAAS,aAErB/B,EAAAA,EAAAA,KAACkC,EAAAA,EAAU,CACTD,KAAK,QACLE,QAASA,IAhIJrB,KACzBpC,EAAqBoC,GACrBvC,GAAoB,EAAK,EA8HU+D,CAAkBxB,GACjCsB,MAAM,UACNjC,MAAM,QAAOJ,UAEbC,EAAAA,EAAAA,KAACuC,EAAAA,EAAU,CAACR,SAAS,mBA/CdjB,EAAU0B,aAmDd,YAQrB9C,EAAAA,EAAAA,MAAC+C,EAAAA,EAAM,CACLC,KAAMpE,EACNqE,QAASA,IAAMpE,GAAoB,GAAOwB,SAAA,EAE1CC,EAAAA,EAAAA,KAAC4C,EAAAA,EAAW,CAAA7C,SAAC,2BACbL,EAAAA,EAAAA,MAACmD,EAAAA,EAAa,CAAA9C,SAAA,EACZL,EAAAA,EAAAA,MAACO,EAAAA,EAAU,CAAAF,SAAA,CAAC,+CACoD,OAAjBtB,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmB2C,KAAK,IAAmB,OAAjB3C,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmB4C,MAAM,IAAmB,OAAjB5C,QAAiB,IAAjBA,OAAiB,EAAjBA,EAAmB6C,QAAQ,SAE/HtB,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,QAAQC,MAAM,iBAAiBP,GAAI,CAAEQ,GAAI,GAAIL,SAAC,kJAIpEL,EAAAA,EAAAA,MAACoD,EAAAA,EAAa,CAAA/C,SAAA,EACZC,EAAAA,EAAAA,KAAC+C,EAAAA,EAAM,CAACZ,QAASA,IAAM5D,GAAoB,GAAOwB,SAAC,aAGnDC,EAAAA,EAAAA,KAAC+C,EAAAA,EAAM,CACLZ,QA7JkBa,UAC1B,IACEpE,GAAW,SACLjD,EAAAA,WAAW2B,gBAAgBe,EAAYI,EAAkB+D,cAC/DjE,GAAoB,GACpBG,EAAqB,MACrBN,GACF,CAAE,MAAO6E,GACPC,QAAQD,MAAM,4BAA8BA,EAE9C,CAAC,QACCrE,GAAW,EACb,GAkJQuB,MAAM,QACNgD,SAAUxE,EAAQoB,SACnB,oBAOX,C", "sources": ["services/apiService.js", "components/certificazioni/StrumentiList.jsx"], "sourcesContent": ["// Servizio API unificato che espone tutti i servizi\nimport authService from './authService';\nimport cantieriService from './cantieriService';\nimport caviService from './caviService';\nimport certificazioneService from './certificazioneService';\nimport parcoCaviService from './parcoCaviService';\nimport excelService from './excelService';\nimport reportService from './reportService';\n\nexport const apiService = {\n  // Auth\n  login: authService.login,\n  logout: authService.logout,\n  getCurrentUser: authService.getCurrentUser,\n\n  // Cantieri\n  getCantieri: cantieriService.getMyCantieri,\n  getCantiere: cantieriService.getCantiere,\n  createCantiere: cantieriService.createCantiere,\n  deleteCantiere: cantieriService.deleteCantiere,\n\n  // Cavi\n  getCavi: caviService.getCavi,\n  getCavo: caviService.getCavo,\n  createCavo: caviService.createCavo,\n  updateCavo: caviService.updateCavo,\n  deleteCavo: caviService.deleteCavo,\n  aggiornaCavo: caviService.aggiornaCavo,\n\n  // Certificazioni\n  getCertificazioni: certificazioneService.getCertificazioni,\n  getCertificazione: certificazioneService.getCertificazione,\n  createCertificazione: certificazioneService.createCertificazione,\n  updateCertificazione: certificazioneService.updateCertificazione,\n  deleteCertificazione: certificazioneService.deleteCertificazione,\n\n  // Strumenti\n  getStrumenti: certificazioneService.getStrumenti,\n  createStrumento: certificazioneService.createStrumento,\n  updateStrumento: certificazioneService.updateStrumento,\n  deleteStrumento: certificazioneService.deleteStrumento,\n\n  // Parco Cavi\n  getParcoCavi: parcoCaviService.getParcoCavi,\n  createBobina: parcoCaviService.createBobina,\n  updateBobina: parcoCaviService.updateBobina,\n  deleteBobina: parcoCaviService.deleteBobina,\n\n  // Excel\n  generateTemplate: excelService.generateTemplate,\n  importExcel: excelService.importExcel,\n\n  // Reports\n  getReports: reportService.getReports\n};\n\nexport default apiService;\n", "import React, { useState } from 'react';\nimport {\n  Paper,\n  Table,\n  TableBody,\n  TableCell,\n  TableContainer,\n  TableHead,\n  TableRow,\n  IconButton,\n  Typography,\n  Box,\n  Chip,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  Button\n} from '@mui/material';\nimport {\n  Edit as EditIcon,\n  Delete as DeleteIcon,\n  Warning as WarningIcon\n} from '@mui/icons-material';\n\nimport { apiService } from '../../services/apiService';\n\nfunction StrumentiList({ strumenti, onEdit, onDelete, cantiereId }) {\n  const [showDeleteDialog, setShowDeleteDialog] = useState(false);\n  const [strumentoToDelete, setStrumentoToDelete] = useState(null);\n  const [loading, setLoading] = useState(false);\n\n  const handleDeleteClick = (strumento) => {\n    setStrumentoToDelete(strumento);\n    setShowDeleteDialog(true);\n  };\n\n  const handleDeleteConfirm = async () => {\n    try {\n      setLoading(true);\n      await apiService.deleteStrumento(cantiereId, strumentoToDelete.id_strumento);\n      setShowDeleteDialog(false);\n      setStrumentoToDelete(null);\n      onDelete();\n    } catch (error) {\n      console.error('Errore nell\\'eliminazione:', error);\n      // TODO: Mostrare errore all'utente\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString) => {\n    if (!dateString) return '-';\n    return new Date(dateString).toLocaleDateString('it-IT');\n  };\n\n  const isCalibrationExpired = (dataScadenza) => {\n    if (!dataScadenza) return false;\n    const today = new Date();\n    const scadenza = new Date(dataScadenza);\n    return scadenza < today;\n  };\n\n  const isCalibrationExpiringSoon = (dataScadenza) => {\n    if (!dataScadenza) return false;\n    const today = new Date();\n    const scadenza = new Date(dataScadenza);\n    const diffTime = scadenza - today;\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays <= 30 && diffDays > 0;\n  };\n\n  const getCalibrationStatus = (dataScadenza) => {\n    if (isCalibrationExpired(dataScadenza)) {\n      return { label: 'Scaduto', color: 'error' };\n    }\n    if (isCalibrationExpiringSoon(dataScadenza)) {\n      return { label: 'In scadenza', color: 'warning' };\n    }\n    return { label: 'Valido', color: 'success' };\n  };\n\n  if (strumenti.length === 0) {\n    return (\n      <Paper sx={{ p: 3, textAlign: 'center' }}>\n        <Typography variant=\"h6\" color=\"text.secondary\">\n          Nessuno strumento certificato trovato\n        </Typography>\n        <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n          Clicca su \"Nuovo Strumento\" per aggiungere il primo strumento\n        </Typography>\n      </Paper>\n    );\n  }\n\n  return (\n    <>\n      <TableContainer component={Paper}>\n        <Table>\n          <TableHead>\n            <TableRow>\n              <TableCell><strong>Nome</strong></TableCell>\n              <TableCell><strong>Marca</strong></TableCell>\n              <TableCell><strong>Modello</strong></TableCell>\n              <TableCell><strong>N° Serie</strong></TableCell>\n              <TableCell><strong>Data Calibrazione</strong></TableCell>\n              <TableCell><strong>Scadenza Calibrazione</strong></TableCell>\n              <TableCell><strong>Stato</strong></TableCell>\n              <TableCell><strong>Azioni</strong></TableCell>\n            </TableRow>\n          </TableHead>\n          <TableBody>\n            {strumenti.map((strumento) => {\n              const calibrationStatus = getCalibrationStatus(strumento.data_scadenza_calibrazione);\n              \n              return (\n                <TableRow key={strumento.id_strumento} hover>\n                  <TableCell>\n                    <Typography variant=\"body2\" fontWeight=\"bold\">\n                      {strumento.nome}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>{strumento.marca}</TableCell>\n                  <TableCell>{strumento.modello}</TableCell>\n                  <TableCell>\n                    <Typography variant=\"body2\" fontFamily=\"monospace\">\n                      {strumento.numero_serie}\n                    </Typography>\n                  </TableCell>\n                  <TableCell>{formatDate(strumento.data_calibrazione)}</TableCell>\n                  <TableCell>\n                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                      {formatDate(strumento.data_scadenza_calibrazione)}\n                      {isCalibrationExpired(strumento.data_scadenza_calibrazione) && (\n                        <WarningIcon color=\"error\" fontSize=\"small\" />\n                      )}\n                      {isCalibrationExpiringSoon(strumento.data_scadenza_calibrazione) && (\n                        <WarningIcon color=\"warning\" fontSize=\"small\" />\n                      )}\n                    </Box>\n                  </TableCell>\n                  <TableCell>\n                    <Chip\n                      label={calibrationStatus.label}\n                      color={calibrationStatus.color}\n                      size=\"small\"\n                    />\n                  </TableCell>\n                  <TableCell>\n                    <Box sx={{ display: 'flex', gap: 0.5 }}>\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => onEdit(strumento)}\n                        title=\"Modifica\"\n                      >\n                        <EditIcon fontSize=\"small\" />\n                      </IconButton>\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleDeleteClick(strumento)}\n                        title=\"Elimina\"\n                        color=\"error\"\n                      >\n                        <DeleteIcon fontSize=\"small\" />\n                      </IconButton>\n                    </Box>\n                  </TableCell>\n                </TableRow>\n              );\n            })}\n          </TableBody>\n        </Table>\n      </TableContainer>\n\n      {/* Dialog Conferma Eliminazione */}\n      <Dialog\n        open={showDeleteDialog}\n        onClose={() => setShowDeleteDialog(false)}\n      >\n        <DialogTitle>Conferma Eliminazione</DialogTitle>\n        <DialogContent>\n          <Typography>\n            Sei sicuro di voler eliminare lo strumento \"{strumentoToDelete?.nome} {strumentoToDelete?.marca} {strumentoToDelete?.modello}\"?\n          </Typography>\n          <Typography variant=\"body2\" color=\"text.secondary\" sx={{ mt: 1 }}>\n            Questa operazione non può essere annullata. Lo strumento non potrà essere eliminato se è utilizzato in certificazioni esistenti.\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <Button onClick={() => setShowDeleteDialog(false)}>\n            Annulla\n          </Button>\n          <Button \n            onClick={handleDeleteConfirm} \n            color=\"error\" \n            disabled={loading}\n          >\n            Elimina\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </>\n  );\n}\n\nexport default StrumentiList;\n"], "names": ["apiService", "login", "authService", "logout", "getCurrentUser", "getCantieri", "cantieriService", "getMyCantieri", "getCantiere", "createCantiere", "deleteCantiere", "get<PERSON><PERSON>", "caviService", "getCavo", "createCavo", "updateCavo", "deleteCavo", "aggiornaCavo", "getCertificazioni", "certificazioneService", "getCertificazione", "createCertificazione", "updateCertificazione", "deleteCertificazione", "getStrumenti", "createStrumento", "updateStrumento", "deleteStrumento", "getParcoCavi", "parcoCaviService", "createBobina", "updateBobina", "deleteBobina", "generateTemplate", "excelService", "importExcel", "getReports", "reportService", "_ref", "strumenti", "onEdit", "onDelete", "cantiereId", "showDeleteDialog", "setShowDeleteDialog", "useState", "strumentoToDelete", "setStrumentoToDelete", "loading", "setLoading", "formatDate", "dateString", "Date", "toLocaleDateString", "isCalibrationExpired", "dataScadenza", "today", "isCalibrationExpiringSoon", "diffTime", "diffDays", "Math", "ceil", "length", "_jsxs", "Paper", "sx", "p", "textAlign", "children", "_jsx", "Typography", "variant", "color", "mt", "_Fragment", "TableContainer", "component", "Table", "TableHead", "TableRow", "TableCell", "TableBody", "map", "strumento", "calibrationStatus", "data_scadenza_calibrazione", "label", "hover", "fontWeight", "nome", "marca", "modello", "fontFamily", "numero_serie", "data_calibrazione", "Box", "display", "alignItems", "gap", "WarningIcon", "fontSize", "Chip", "size", "IconButton", "onClick", "title", "EditIcon", "handleDeleteClick", "DeleteIcon", "id_strumento", "Dialog", "open", "onClose", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "async", "error", "console", "disabled"], "sourceRoot": ""}