{"version": 3, "file": "static/js/894.b2cc639d.chunk.js", "mappings": "4MASO,MAAMA,EAAa,CAExBC,MAAOC,EAAAA,EAAYD,MACnBE,OAAQD,EAAAA,EAAYC,OACpBC,eAAgBF,EAAAA,EAAYE,eAG5BC,YAAaC,EAAAA,EAAgBC,cAC7BC,YAAaF,EAAAA,EAAgBE,YAC7BC,eAAgBH,EAAAA,EAAgBG,eAChCC,eAAgBJ,EAAAA,EAAgBI,eAGhCC,QAASC,EAAAA,EAAYD,QACrBE,QAASD,EAAAA,EAAYC,QACrBC,WAAYF,EAAAA,EAAYE,WACxBC,WAAYH,EAAAA,EAAYG,WACxBC,WAAYJ,EAAAA,EAAYI,WACxBC,aAAcL,EAAAA,EAAYK,aAG1BC,kBAAmBC,EAAAA,EAAsBD,kBACzCE,kBAAmBD,EAAAA,EAAsBC,kBACzCC,qBAAsBF,EAAAA,EAAsBE,qBAC5CC,qBAAsBH,EAAAA,EAAsBG,qBAC5CC,qBAAsBJ,EAAAA,EAAsBI,qBAG5CC,aAAcL,EAAAA,EAAsBK,aACpCC,gBAAiBN,EAAAA,EAAsBM,gBACvCC,gBAAiBP,EAAAA,EAAsBO,gBACvCC,gBAAiBR,EAAAA,EAAsBQ,gBAGvCC,aAAcC,EAAAA,EAAiBD,aAC/BE,aAAcD,EAAAA,EAAiBC,aAC/BC,aAAcF,EAAAA,EAAiBE,aAC/BC,aAAcH,EAAAA,EAAiBG,aAG/BC,iBAAkBC,EAAAA,QAAaD,iBAC/BE,YAAaD,EAAAA,QAAaC,YAG1BC,WAAYC,EAAAA,EAAcD,W", "sources": ["services/apiService.js"], "sourcesContent": ["// Servizio API unificato che espone tutti i servizi\nimport authService from './authService';\nimport cantieriService from './cantieriService';\nimport caviService from './caviService';\nimport certificazioneService from './certificazioneService';\nimport parcoCaviService from './parcoCaviService';\nimport excelService from './excelService';\nimport reportService from './reportService';\n\nexport const apiService = {\n  // Auth\n  login: authService.login,\n  logout: authService.logout,\n  getCurrentUser: authService.getCurrentUser,\n\n  // Cantieri\n  getCantieri: cantieriService.getMyCantieri,\n  getCantiere: cantieriService.getCantiere,\n  createCantiere: cantieriService.createCantiere,\n  deleteCantiere: cantieriService.deleteCantiere,\n\n  // Cavi\n  getCavi: caviService.getCavi,\n  getCavo: caviService.getCavo,\n  createCavo: caviService.createCavo,\n  updateCavo: caviService.updateCavo,\n  deleteCavo: caviService.deleteCavo,\n  aggiornaCavo: caviService.aggiornaCavo,\n\n  // Certificazioni\n  getCertificazioni: certificazioneService.getCertificazioni,\n  getCertificazione: certificazioneService.getCertificazione,\n  createCertificazione: certificazioneService.createCertificazione,\n  updateCertificazione: certificazioneService.updateCertificazione,\n  deleteCertificazione: certificazioneService.deleteCertificazione,\n\n  // Strumenti\n  getStrumenti: certificazioneService.getStrumenti,\n  createStrumento: certificazioneService.createStrumento,\n  updateStrumento: certificazioneService.updateStrumento,\n  deleteStrumento: certificazioneService.deleteStrumento,\n\n  // Parco Cavi\n  getParcoCavi: parcoCaviService.getParcoCavi,\n  createBobina: parcoCaviService.createBobina,\n  updateBobina: parcoCaviService.updateBobina,\n  deleteBobina: parcoCaviService.deleteBobina,\n\n  // Excel\n  generateTemplate: excelService.generateTemplate,\n  importExcel: excelService.importExcel,\n\n  // Reports\n  getReports: reportService.getReports\n};\n\nexport default apiService;\n"], "names": ["apiService", "login", "authService", "logout", "getCurrentUser", "getCantieri", "cantieriService", "getMyCantieri", "getCantiere", "createCantiere", "deleteCantiere", "get<PERSON><PERSON>", "caviService", "getCavo", "createCavo", "updateCavo", "deleteCavo", "aggiornaCavo", "getCertificazioni", "certificazioneService", "getCertificazione", "createCertificazione", "updateCertificazione", "deleteCertificazione", "getStrumenti", "createStrumento", "updateStrumento", "deleteStrumento", "getParcoCavi", "parcoCaviService", "createBobina", "updateBobina", "deleteBobina", "generateTemplate", "excelService", "importExcel", "getReports", "reportService"], "sourceRoot": ""}