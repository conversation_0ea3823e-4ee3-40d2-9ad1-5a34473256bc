#!/usr/bin/env python3
"""
Script per pulire le password in chiaro dal database.
IMPORTANTE: Questo script rimuove tutte le password in chiaro per motivi di sicurezza.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere

def clean_user_passwords():
    """Rimuove tutte le password in chiaro degli utenti dal database."""
    db = next(get_db())
    
    try:
        print("🔒 Inizio pulizia password utenti...")
        
        # Ottieni tutti gli utenti con password in chiaro
        users_with_plain_passwords = db.query(User).filter(
            User.password_plain.isnot(None)
        ).all()
        
        print(f"Trovati {len(users_with_plain_passwords)} utenti con password in chiaro")
        
        # Rimuovi le password in chiaro
        for user in users_with_plain_passwords:
            print(f"  - Pulizia password per utente: {user.username}")
            user.password_plain = None
        
        # Commit delle modifiche
        db.commit()
        print(f"✅ Password in chiaro rimosse per {len(users_with_plain_passwords)} utenti")
        
    except Exception as e:
        print(f"❌ Errore durante la pulizia password utenti: {e}")
        db.rollback()
    finally:
        db.close()

def check_cantiere_passwords():
    """Verifica lo stato delle password dei cantieri."""
    db = next(get_db())
    
    try:
        print("\n🔍 Verifica password cantieri...")
        
        cantieri = db.query(Cantiere).all()
        
        hash_count = 0
        plain_count = 0
        encrypted_count = 0
        
        for cantiere in cantieri:
            if cantiere.password_cantiere:
                if cantiere.password_cantiere.startswith('$2b$'):
                    hash_count += 1
                    print(f"  - Cantiere {cantiere.commessa}: PASSWORD HASHATA ✅")
                else:
                    plain_count += 1
                    print(f"  - Cantiere {cantiere.commessa}: PASSWORD IN CHIARO ⚠️")
            
            if cantiere.password_cantiere_encrypted:
                encrypted_count += 1
                print(f"  - Cantiere {cantiere.commessa}: PASSWORD CRIPTATA ✅")
        
        print(f"\n📊 Riepilogo password cantieri:")
        print(f"  - Password hashate (sicure): {hash_count}")
        print(f"  - Password in chiaro (RISCHIO): {plain_count}")
        print(f"  - Password criptate (recuperabili): {encrypted_count}")
        
        if plain_count > 0:
            print(f"\n⚠️  ATTENZIONE: {plain_count} cantieri hanno password in chiaro!")
            print("   Considera di hashare anche queste per maggiore sicurezza.")
        
    except Exception as e:
        print(f"❌ Errore durante la verifica password cantieri: {e}")
    finally:
        db.close()

def main():
    """Funzione principale."""
    print("🔐 SCRIPT DI PULIZIA PASSWORD - SICUREZZA DATABASE")
    print("=" * 60)
    
    # Pulizia password utenti
    clean_user_passwords()
    
    # Verifica password cantieri
    check_cantiere_passwords()
    
    print("\n" + "=" * 60)
    print("✅ Script completato!")
    print("\n📋 RACCOMANDAZIONI:")
    print("1. Le password utenti sono ora sicure (solo hash)")
    print("2. Le password cantieri possono essere:")
    print("   - Hashate (sicure ma non recuperabili)")
    print("   - Criptate (sicure e recuperabili)")
    print("3. Evita sempre di salvare password in chiaro!")

if __name__ == "__main__":
    main()
