-- Migration: Aggiunta campi di sicurezza per gestione password
-- Data: 2024-12-19
-- Descrizione: Aggiunge campi per token di reset, audit trail, rate limiting

-- =====================================================
-- TABELLA UTENTI - Campi di sicurezza
-- =====================================================

-- Aggiunta campi per reset password
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255);
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP;
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS reset_token_used BOOLEAN DEFAULT FALSE;

-- Aggiunta campi per rate limiting e sicurezza
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0;
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP;
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS last_password_change TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Aggiunta campi per audit
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE Utenti ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP;

-- Rimozione del campo password_plain per sicurezza (da fare in produzione)
-- ALTER TABLE Utenti DROP COLUMN IF EXISTS password_plain;

-- =====================================================
-- TABELLA CANTIERI - Campi di sicurezza
-- =====================================================

-- Aggiunta campi per reset password cantieri
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS reset_token VARCHAR(255);
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS reset_token_expires TIMESTAMP;
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS reset_token_used BOOLEAN DEFAULT FALSE;

-- Aggiunta campi per rate limiting
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS failed_login_attempts INTEGER DEFAULT 0;
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS locked_until TIMESTAMP;
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS last_login TIMESTAMP;
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS last_password_change TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- Aggiunta campi per audit
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
ALTER TABLE Cantieri ADD COLUMN IF NOT EXISTS last_activity TIMESTAMP;

-- Rimozione del campo password_cantiere_encrypted per sicurezza (da fare in produzione)
-- ALTER TABLE Cantieri DROP COLUMN IF EXISTS password_cantiere_encrypted;

-- =====================================================
-- NUOVA TABELLA: SECURITY_EVENTS (Audit Log)
-- =====================================================

CREATE TABLE IF NOT EXISTS security_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    user_id INTEGER,
    cantiere_id INTEGER,
    username VARCHAR(255),
    email VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL DEFAULT TRUE,
    error_message TEXT,
    additional_data JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Indici per performance
    CONSTRAINT fk_security_events_user FOREIGN KEY (user_id) REFERENCES Utenti(id_utente) ON DELETE SET NULL,
    CONSTRAINT fk_security_events_cantiere FOREIGN KEY (cantiere_id) REFERENCES Cantieri(id_cantiere) ON DELETE SET NULL
);

-- Indici per ottimizzazione query
CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_user ON security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_security_events_cantiere ON security_events(cantiere_id);
CREATE INDEX IF NOT EXISTS idx_security_events_created_at ON security_events(created_at);
CREATE INDEX IF NOT EXISTS idx_security_events_ip ON security_events(ip_address);

-- =====================================================
-- NUOVA TABELLA: RATE_LIMITING (Rate Limit Tracking)
-- =====================================================

CREATE TABLE IF NOT EXISTS rate_limiting (
    id SERIAL PRIMARY KEY,
    identifier VARCHAR(255) NOT NULL, -- IP, user_id, email, etc.
    identifier_type VARCHAR(50) NOT NULL, -- 'ip', 'user_id', 'email'
    attempt_count INTEGER DEFAULT 1,
    window_start TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_attempt TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    blocked_until TIMESTAMP,
    
    -- Constraint per evitare duplicati
    UNIQUE(identifier, identifier_type)
);

-- Indici per performance
CREATE INDEX IF NOT EXISTS idx_rate_limiting_identifier ON rate_limiting(identifier, identifier_type);
CREATE INDEX IF NOT EXISTS idx_rate_limiting_window ON rate_limiting(window_start);
CREATE INDEX IF NOT EXISTS idx_rate_limiting_blocked ON rate_limiting(blocked_until);

-- =====================================================
-- NUOVA TABELLA: PASSWORD_RESET_TOKENS (Token Management)
-- =====================================================

CREATE TABLE IF NOT EXISTS password_reset_tokens (
    id SERIAL PRIMARY KEY,
    token_hash VARCHAR(255) NOT NULL UNIQUE, -- Hash del token per sicurezza
    user_id INTEGER,
    cantiere_id INTEGER,
    email VARCHAR(255) NOT NULL,
    token_type VARCHAR(20) NOT NULL DEFAULT 'password_reset', -- 'password_reset', 'email_verification'
    expires_at TIMESTAMP NOT NULL,
    used_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    ip_address INET,
    user_agent TEXT,
    
    -- Constraints
    CONSTRAINT fk_reset_tokens_user FOREIGN KEY (user_id) REFERENCES Utenti(id_utente) ON DELETE CASCADE,
    CONSTRAINT fk_reset_tokens_cantiere FOREIGN KEY (cantiere_id) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE,
    CONSTRAINT chk_reset_tokens_target CHECK (
        (user_id IS NOT NULL AND cantiere_id IS NULL) OR 
        (user_id IS NULL AND cantiere_id IS NOT NULL)
    )
);

-- Indici per performance
CREATE INDEX IF NOT EXISTS idx_reset_tokens_hash ON password_reset_tokens(token_hash);
CREATE INDEX IF NOT EXISTS idx_reset_tokens_user ON password_reset_tokens(user_id);
CREATE INDEX IF NOT EXISTS idx_reset_tokens_cantiere ON password_reset_tokens(cantiere_id);
CREATE INDEX IF NOT EXISTS idx_reset_tokens_email ON password_reset_tokens(email);
CREATE INDEX IF NOT EXISTS idx_reset_tokens_expires ON password_reset_tokens(expires_at);

-- =====================================================
-- TRIGGER PER AGGIORNAMENTO AUTOMATICO TIMESTAMP
-- =====================================================

-- Funzione per aggiornare updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger per Utenti
DROP TRIGGER IF EXISTS update_utenti_updated_at ON Utenti;
CREATE TRIGGER update_utenti_updated_at
    BEFORE UPDATE ON Utenti
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Trigger per Cantieri
DROP TRIGGER IF EXISTS update_cantieri_updated_at ON Cantieri;
CREATE TRIGGER update_cantieri_updated_at
    BEFORE UPDATE ON Cantieri
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- FUNZIONI DI UTILITÀ PER SICUREZZA
-- =====================================================

-- Funzione per pulire token scaduti
CREATE OR REPLACE FUNCTION cleanup_expired_tokens()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM password_reset_tokens 
    WHERE expires_at < CURRENT_TIMESTAMP OR used_at IS NOT NULL;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Funzione per pulire rate limiting vecchi
CREATE OR REPLACE FUNCTION cleanup_old_rate_limits()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM rate_limiting 
    WHERE window_start < CURRENT_TIMESTAMP - INTERVAL '1 day'
    AND (blocked_until IS NULL OR blocked_until < CURRENT_TIMESTAMP);
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Funzione per pulire eventi di sicurezza vecchi (mantieni solo ultimi 90 giorni)
CREATE OR REPLACE FUNCTION cleanup_old_security_events()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM security_events 
    WHERE created_at < CURRENT_TIMESTAMP - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- COMMENTI E DOCUMENTAZIONE
-- =====================================================

COMMENT ON TABLE security_events IS 'Log degli eventi di sicurezza per audit e monitoring';
COMMENT ON TABLE rate_limiting IS 'Tracking dei tentativi per rate limiting e protezione brute force';
COMMENT ON TABLE password_reset_tokens IS 'Gestione sicura dei token per reset password';

COMMENT ON COLUMN Utenti.reset_token IS 'Token temporaneo per reset password (deprecato, usare password_reset_tokens)';
COMMENT ON COLUMN Utenti.failed_login_attempts IS 'Numero di tentativi di login falliti consecutivi';
COMMENT ON COLUMN Utenti.locked_until IS 'Timestamp fino al quale l\'account è bloccato';
COMMENT ON COLUMN Utenti.last_password_change IS 'Timestamp dell\'ultimo cambio password';

-- =====================================================
-- GRANT PERMISSIONS (se necessario)
-- =====================================================

-- Assicurati che l'utente dell'applicazione abbia i permessi necessari
-- GRANT SELECT, INSERT, UPDATE, DELETE ON security_events TO cms_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON rate_limiting TO cms_app_user;
-- GRANT SELECT, INSERT, UPDATE, DELETE ON password_reset_tokens TO cms_app_user;
-- GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO cms_app_user;
