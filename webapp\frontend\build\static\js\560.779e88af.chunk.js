"use strict";(self.webpackChunkcms_frontend=self.webpackChunkcms_frontend||[]).push([[560,894],{1894:(e,t,a)=>{a.d(t,{apiService:()=>u});var i=a(8816),r=a(173),n=a(561),o=a(7079),l=a(9678),s=a(2043),c=a(184);const u={login:i.A.login,logout:i.A.logout,getCurrentUser:i.A.getCurrentUser,getCantieri:r.A.getMyCantieri,getCantiere:r.A.getCantiere,createCantiere:r.A.createCantiere,deleteCantiere:r.A.deleteCantiere,getCavi:n.A.getCavi,getCavo:n.A.getCavo,createCavo:n.A.createCavo,updateCavo:n.A.updateCavo,deleteCavo:n.A.deleteCavo,aggiornaCavo:n.A.aggiornaCavo,getCertificazioni:o.A.getCertificazioni,getCertificazione:o.A.getCertificazione,createCertificazione:o.A.createCertificazione,updateCertificazione:o.A.updateCertificazione,deleteCertificazione:o.A.deleteCertificazione,getStrumenti:o.A.getStrumenti,createStrumento:o.A.createStrumento,updateStrumento:o.A.updateStrumento,deleteStrumento:o.A.deleteStrumento,getParcoCavi:l.A.getParcoCavi,createBobina:l.A.createBobina,updateBobina:l.A.updateBobina,deleteBobina:l.A.deleteBobina,generateTemplate:s.default.generateTemplate,importExcel:s.default.importExcel,getReports:c.A.getReports}},6560:(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var i=a(5043),r=a(3336),n=a(5865),o=a(7254),l=a(8903),s=a(7108),c=a(7784),u=a(6446),d=a(9336),m=a(3193),v=a(8356),g=a(2221),z=a(2143),A=a(1906),h=a(1794),x=a(7141),_=a(1894),C=a(579);const f=function(e){let{cantiereId:t,certificazione:a,strumenti:f,onSuccess:p,onCancel:j}=e;const[b,y]=(0,i.useState)({id_cavo:"",id_operatore:"",strumento_utilizzato:"",id_strumento:"",lunghezza_misurata:"",valore_continuita:"OK",valore_isolamento:"500",valore_resistenza:"OK",note:""}),[S,W]=(0,i.useState)([]),[B,E]=(0,i.useState)(null),[O,w]=(0,i.useState)(!1),[I,M]=(0,i.useState)("");(0,i.useEffect)((()=>{T(),a&&y({id_cavo:a.id_cavo||"",id_operatore:a.id_operatore||"",strumento_utilizzato:a.strumento_utilizzato||"",id_strumento:a.id_strumento||"",lunghezza_misurata:a.lunghezza_misurata||"",valore_continuita:a.valore_continuita||"OK",valore_isolamento:a.valore_isolamento||"500",valore_resistenza:a.valore_resistenza||"OK",note:a.note||""})}),[a,t]);const T=async()=>{try{const e=(await _.apiService.getCavi(t)).filter((e=>"INSTALLATO"===e.stato_installazione));if(W(e),a){const t=e.find((e=>e.id_cavo===a.id_cavo));E(t)}}catch(e){console.error("Errore nel caricamento dei cavi:",e),M("Errore nel caricamento dei cavi")}},$=(e,t)=>{y((a=>({...a,[e]:t})))};return(0,C.jsxs)(r.A,{sx:{p:3},children:[(0,C.jsx)(n.A,{variant:"h6",gutterBottom:!0,children:a?"Modifica Certificazione":"Nuova Certificazione"}),I&&(0,C.jsx)(o.A,{severity:"error",sx:{mb:2},children:I}),(0,C.jsx)("form",{onSubmit:async e=>{if(e.preventDefault(),b.id_cavo)try{w(!0),M("");const e={...b,id_strumento:b.id_strumento||null,lunghezza_misurata:b.lunghezza_misurata?parseFloat(b.lunghezza_misurata):null};a?(await _.apiService.updateCertificazione(t,a.id_certificazione,e),p("Certificazione aggiornata con successo")):(await _.apiService.createCertificazione(t,e),p("Certificazione creata con successo"))}catch(n){var i,r;console.error("Errore nel salvataggio:",n),M((null===(i=n.response)||void 0===i||null===(r=i.data)||void 0===r?void 0:r.detail)||"Errore nel salvataggio della certificazione")}finally{w(!1)}else M("Seleziona un cavo da certificare")},children:(0,C.jsxs)(l.Ay,{container:!0,spacing:3,children:[(0,C.jsxs)(l.Ay,{item:!0,xs:12,children:[(0,C.jsx)(n.A,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Selezione Cavo"}),(0,C.jsx)(s.A,{value:B,onChange:(e,t)=>{E(t),y(t?e=>({...e,id_cavo:t.id_cavo,lunghezza_misurata:t.metratura_reale||""}):e=>({...e,id_cavo:"",lunghezza_misurata:""}))},options:S,getOptionLabel:e=>`${e.id_cavo} - ${e.tipologia||""} ${e.sezione||""}`,renderInput:e=>(0,C.jsx)(c.A,{...e,label:"Cavo da certificare",required:!0,fullWidth:!0}),disabled:!!a}),B&&(0,C.jsx)(u.A,{sx:{mt:1,p:2,bgcolor:"grey.50",borderRadius:1},children:(0,C.jsxs)(n.A,{variant:"body2",children:[(0,C.jsx)("strong",{children:"Partenza:"})," ",B.ubicazione_partenza||"-"," |",(0,C.jsx)("strong",{children:" Arrivo:"})," ",B.ubicazione_arrivo||"-"," |",(0,C.jsx)("strong",{children:" Metri Teorici:"})," ",B.metri_teorici||"-"," |",(0,C.jsx)("strong",{children:" Metri Reali:"})," ",B.metratura_reale||"-"]})})]}),(0,C.jsx)(l.Ay,{item:!0,xs:12,children:(0,C.jsx)(d.A,{})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,C.jsx)(c.A,{label:"Operatore",value:b.id_operatore,onChange:e=>$("id_operatore",e.target.value),fullWidth:!0})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,C.jsxs)(m.A,{fullWidth:!0,children:[(0,C.jsx)(v.A,{children:"Strumento Certificato"}),(0,C.jsxs)(g.A,{value:b.id_strumento,onChange:e=>{const t=e.target.value;if($("id_strumento",t),t){const e=f.find((e=>e.id_strumento===t));e&&$("strumento_utilizzato",`${e.nome} ${e.marca} ${e.modello}`)}else $("strumento_utilizzato","")},label:"Strumento Certificato",children:[(0,C.jsx)(z.A,{value:"",children:"Nessuno"}),f.map((e=>(0,C.jsxs)(z.A,{value:e.id_strumento,children:[e.nome," ",e.marca," ",e.modello]},e.id_strumento)))]})]})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,children:(0,C.jsx)(c.A,{label:"Descrizione Strumento (alternativa)",value:b.strumento_utilizzato,onChange:e=>$("strumento_utilizzato",e.target.value),fullWidth:!0,helperText:"Utilizzare solo se lo strumento non \xe8 presente nell'elenco sopra"})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,children:(0,C.jsx)(d.A,{})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,C.jsx)(c.A,{label:"Lunghezza Misurata (m)",type:"number",value:b.lunghezza_misurata,onChange:e=>$("lunghezza_misurata",e.target.value),fullWidth:!0,inputProps:{step:.01,min:0}})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,C.jsx)(c.A,{label:"Valore Isolamento (M\u03a9)",value:b.valore_isolamento,onChange:e=>$("valore_isolamento",e.target.value),fullWidth:!0})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,C.jsx)(c.A,{label:"Valore Continuit\xe0",value:b.valore_continuita,onChange:e=>$("valore_continuita",e.target.value),fullWidth:!0})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,C.jsx)(c.A,{label:"Valore Resistenza",value:b.valore_resistenza,onChange:e=>$("valore_resistenza",e.target.value),fullWidth:!0})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,children:(0,C.jsx)(c.A,{label:"Note",value:b.note,onChange:e=>$("note",e.target.value),fullWidth:!0,multiline:!0,rows:3})}),(0,C.jsx)(l.Ay,{item:!0,xs:12,children:(0,C.jsxs)(u.A,{sx:{display:"flex",gap:2,justifyContent:"flex-end"},children:[(0,C.jsx)(A.A,{variant:"outlined",startIcon:(0,C.jsx)(h.A,{}),onClick:j,disabled:O,children:"Annulla"}),(0,C.jsx)(A.A,{type:"submit",variant:"contained",startIcon:(0,C.jsx)(x.A,{}),disabled:O,children:O?"Salvataggio...":"Salva Certificazione"})]})})]})})]})}}}]);
//# sourceMappingURL=560.779e88af.chunk.js.map