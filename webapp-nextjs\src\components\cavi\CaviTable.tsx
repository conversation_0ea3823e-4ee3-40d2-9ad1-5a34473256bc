'use client'

import { useState, useMemo, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { getCavoColorClasses, getPrimaryActionClasses, getSecondaryActionClasses, getClickableBadgeClasses, getUniformButtonClasses, getUnavailableClasses, CABLYS_COLORS } from '@/utils/softColors'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { TableRow, TableCell } from '@/components/ui/table'
import { Cavo } from '@/types'
import FilterableTable, { ColumnDef } from '@/components/common/FilterableTable'
import SmartCaviFilter from './SmartCaviFilter'
import TruncatedText from '@/components/common/TruncatedText'
import { ActionTooltip } from './tooltips/CableTooltips'
import {
  MoreHorizontal,
  Cable,
  Settings,
  Zap,
  CheckCircle,
  AlertCircle,
  Clock,
  Package,
  Link,
  Unlink,
  Award,
  Play,
  Pause,
  X,
  Check,
  FileText,
  Download,
  AlertTriangle,
  Wrench,
  ChevronDown,
  Info
} from 'lucide-react'

interface CaviTableProps {
  cavi: Cavo[]
  loading?: boolean
  selectionEnabled?: boolean
  selectedCavi?: string[]
  onSelectionChange?: (selectedIds: string[]) => void
  onStatusAction?: (cavo: Cavo, action: string) => void
  onContextMenuAction?: (cavo: Cavo, action: string) => void
}

export default function CaviTable({
  cavi = [],
  loading = false,
  selectionEnabled = false,
  selectedCavi = [],
  onSelectionChange,
  onStatusAction,
  onContextMenuAction
}: CaviTableProps) {
  const [smartFilteredCavi, setSmartFilteredCavi] = useState(cavi)
  const [filteredCavi, setFilteredCavi] = useState(cavi)
  const [internalSelectionEnabled, setInternalSelectionEnabled] = useState(selectionEnabled)

  // Aggiorna i cavi quando cambiano i cavi originali
  useEffect(() => {
    setSmartFilteredCavi(cavi)
    setFilteredCavi(cavi)
  }, [cavi])

  // Gestione filtri intelligenti
  const handleSmartFilterChange = (filtered: Cavo[]) => {
    setSmartFilteredCavi(filtered)
  }

  // Gestione filtri tabella
  const handleTableFilterChange = (filtered: Cavo[]) => {
    setFilteredCavi(filtered)
  }

  const handleSelectionToggle = () => {
    setInternalSelectionEnabled(!internalSelectionEnabled)
  }

  // Gestione selezione
  const handleSelectAll = (checked: boolean) => {
    if (onSelectionChange) {
      onSelectionChange(checked ? filteredCavi.map(c => c.id_cavo) : [])
    }
  }

  const handleSelectCavo = (cavoId: string, checked: boolean) => {
    if (onSelectionChange) {
      const newSelection = checked
        ? [...selectedCavi, cavoId]
        : selectedCavi.filter(id => id !== cavoId)
      onSelectionChange(newSelection)
    }
  }

  // Bulk action handlers
  const handleBulkExport = async () => {
    try {
      const response = await fetch('/api/cavi/export', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1 // TODO: Get from context
        })
      })

      if (response.ok) {
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `cavi_export_${new Date().toISOString().split('T')[0]}.csv`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      } else {
        const error = await response.json()
        alert(`Errore durante l'esportazione: ${error.error}`)
      }
    } catch (error) {
      alert('Errore durante l\'esportazione')
    }
  }

  const handleBulkStatusChange = async () => {
    const newStatus = prompt('Inserisci il nuovo stato (Da installare, In corso, Installato):')
    if (!newStatus) return

    try {
      const response = await fetch('/api/cavi/bulk-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1, // TODO: Get from context
          newStatus
        })
      })

      const result = await response.json()
      if (result.success) {
        alert(result.message)
        // TODO: Refresh data
      } else {
        alert(`Errore: ${result.error}`)
      }
    } catch (error) {
      alert('Errore durante il cambio stato')
    }
  }

  const handleBulkAssignCommand = () => {
    // TODO: Implementare modal per selezione comanda
    alert(`Assegnazione comanda per ${selectedCavi.length} cavi`)
  }

  const handleBulkDelete = async () => {
    if (!confirm(`Sei sicuro di voler eliminare ${selectedCavi.length} cavi?`)) {
      return
    }

    try {
      const response = await fetch('/api/cavi/bulk-delete', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          selectedIds: selectedCavi,
          cantiereId: 1 // TODO: Get from context
        })
      })

      const result = await response.json()
      if (result.success) {
        alert(result.message)
        // TODO: Refresh data
      } else {
        alert(`Errore: ${result.error}`)
      }
    } catch (error) {
      alert('Errore durante l\'eliminazione')
    }
  }

  // Define columns matching original webapp structure
  const columns: ColumnDef[] = useMemo(() => {
    const baseColumns: ColumnDef[] = [
      {
        field: 'id_cavo',
        headerName: 'ID',
        dataType: 'text',
        width: 70,
        align: 'left',
        renderCell: (row: Cavo) => (
          <span className="font-semibold text-mariner-900">{row.id_cavo}</span>
        )
      },
      {
        field: 'sistema',
        headerName: 'Sistema',
        dataType: 'text',
        width: 80,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.sistema || ''} maxLength={8} />
        )
      },
      {
        field: 'utility',
        headerName: 'Utility',
        dataType: 'text',
        width: 80,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.utility || ''} maxLength={8} />
        )
      },
      {
        field: 'tipologia',
        headerName: 'Tipologia',
        dataType: 'text',
        width: 100,
        renderCell: (row: Cavo) => (
          <TruncatedText text={row.tipologia || ''} maxLength={12} />
        )
      },
      {
        field: 'formazione',
        headerName: 'Form.',
        dataType: 'text',
        align: 'left',
        width: 60,
        renderCell: (row: Cavo) => row.formazione || row.sezione
      },
      {
        field: 'metri_teorici',
        headerName: 'M.Teor.',
        dataType: 'number',
        align: 'left',
        width: 70,
        renderCell: (row: Cavo) => row.metri_teorici ? row.metri_teorici.toFixed(1) : '0'
      },
      {
        field: 'metri_posati',
        headerName: 'M.Reali',
        dataType: 'number',
        align: 'left',
        width: 70,
        renderCell: (row: Cavo) => {
          const metri = row.metri_posati || row.metratura_reale || 0
          return metri ? metri.toFixed(1) : '0'
        }
      },
      {
        field: 'ubicazione_partenza',
        headerName: 'Da',
        dataType: 'text',
        width: 140,
        renderCell: (row: Cavo) => (
          <TruncatedText
            text={row.da || row.ubicazione_partenza || ''}
            maxLength={18}
          />
        )
      },
      {
        field: 'ubicazione_arrivo',
        headerName: 'A',
        dataType: 'text',
        width: 140,
        renderCell: (row: Cavo) => (
          <TruncatedText
            text={row.a || row.ubicazione_arrivo || ''}
            maxLength={18}
          />
        )
      },
      {
        field: 'id_bobina',
        headerName: 'Bobina',
        dataType: 'text',
        width: 80,
        align: 'center',
        renderCell: (row: Cavo) => getBobinaDisplay(row)
      },
      {
        field: 'stato_installazione',
        headerName: 'Stato',
        dataType: 'text',
        align: 'left',
        width: 120,
        disableSort: true,
        getFilterValue: (row: Cavo) => getStatoFilterValue(row),
        renderCell: (row: Cavo) => getStatusBadge(row)
      },
      {
        field: 'collegamenti',
        headerName: 'Collegamenti',
        dataType: 'text',
        align: 'left',
        width: 180,
        disableSort: true,
        getFilterValue: (row: Cavo) => getCollegamentiFilterValue(row),
        renderCell: (row: Cavo) => getConnectionButton(row)
      },
      {
        field: 'certificato',
        headerName: 'Certificato',
        dataType: 'text',
        align: 'left',
        width: 130,
        disableSort: true,
        getFilterValue: (row: Cavo) => getCertificatoFilterValue(row),
        renderCell: (row: Cavo) => getCertificationDisplay(row)
      },

    ]

    // Add selection column if enabled
    if (internalSelectionEnabled) {
      baseColumns.unshift({
        field: 'selection',
        headerName: '',
        disableFilter: true,
        disableSort: true,
        width: 50,
        align: 'left',
        renderHeader: () => (
          <Checkbox
            checked={selectedCavi.length === filteredCavi.length && filteredCavi.length > 0}
            onCheckedChange={handleSelectAll}
          />
        ),
        renderCell: (row: Cavo) => (
          <Checkbox
            checked={selectedCavi.includes(row.id_cavo)}
            onCheckedChange={(checked) => handleSelectCavo(row.id_cavo, checked as boolean)}
            onClick={(e) => e.stopPropagation()}
          />
        )
      })
    }

    return baseColumns
  }, [internalSelectionEnabled, selectedCavi, filteredCavi, handleSelectAll, handleSelectCavo])

  // Custom row renderer for selection and context menu
  const renderRow = (row: Cavo, index: number) => {
    const isSelected = selectedCavi.includes(row.id_cavo)

    return (
      <TableRow
        key={row.id_cavo}
        className={`
          ${isSelected ? 'bg-blue-50 border-blue-200' : 'bg-white'}
          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm
          cursor-pointer border-b border-gray-200
          transition-all duration-200 ease-in-out
          ${isSelected ? 'ring-1 ring-blue-300' : ''}
        `}
        onClick={() => internalSelectionEnabled && handleSelectCavo(row.id_cavo, !isSelected)}
        onContextMenu={(e) => {
          e.preventDefault()
          onContextMenuAction?.(row, 'context_menu')
        }}
      >
        {columns.map((column) => (
          <TableCell
            key={column.field}
            className={`
              py-2 px-2 text-sm text-left
              ${isSelected ? 'text-blue-900' : 'text-gray-900'}
              transition-colors duration-200
            `}
            style={{ width: column.width, ...column.cellStyle }}
            onClick={(e) => {
              // Prevent row click for action columns
              if (['stato_installazione', 'collegamenti', 'certificato'].includes(column.field)) {
                e.stopPropagation()
              }
            }}
          >
            {column.renderCell ? column.renderCell(row) : (row[column.field] || <span className="text-gray-400">-</span>)}
          </TableCell>
        ))}
      </TableRow>
    )
  }

  // ===== COMPONENTI UI MIGLIORATI SECONDO LE SPECIFICHE =====

  // ===== PALETTE COLORI PROFESSIONALE "CORPORATE TECH" =====
  // Primario (Brand): #007bff (Blu)
  // Successo: #28A745 (Verde)
  // Avviso: #FFC107 (Arancione)
  // Neutro: #6c757d (Grigio)
  // Sfondi tenui: molto desaturati, quasi bianchi

  // ===== FUNZIONI HELPER PER FILTRI =====

  // Ottieni valore filtro per Stato
  const getStatoFilterValue = (cavo: Cavo) => {
    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0
    const isInstalled = metriInstallati > 0
    const stato = cavo.stato_installazione || 'Da installare'

    // Verifica comanda attiva
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    if (comandaAttiva && stato === 'In corso') {
      return `In corso (${comandaAttiva})`
    }

    if (stato === 'Installato' || isInstalled) {
      return 'Installato'
    }

    return stato
  }

  // Ottieni valore filtro per Collegamenti
  const getCollegamentiFilterValue = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const collegamento = cavo.collegamento || cavo.collegamenti || 0

    if (!isInstalled) {
      return 'Non disponibile'
    }

    switch (collegamento) {
      case 0: return 'Collega'
      case 1: return 'Completa collegamento'
      case 2: return 'Completa collegamento'
      case 3: return 'Scollega'
      default: return 'Gestisci collegamenti'
    }
  }

  // Ottieni valore filtro per Certificato
  const getCertificatoFilterValue = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'

    if (!isInstalled) {
      return 'Non disponibile'
    }

    if (isCertified) {
      return 'Genera PDF'
    }

    return 'Certifica'
  }

  // 1. COLONNA BOBINA - Pulsanti Personalizzati
  const getBobinaDisplay = (cavo: Cavo) => {
    const idBobina = cavo.id_bobina

    if (!idBobina || idBobina === 'N/A') {
      // Nessuna bobina - Testo statico grigio
      return (
        <span className={`inline-flex items-center px-2 py-1 text-xs font-medium ${CABLYS_COLORS.NEUTRAL.text_light}`}>
          -
        </span>
      )
    }

    if (idBobina === 'BOBINA_VUOTA' || idBobina === 'VUOTA') {
      // Stato "Vuota" - Pulsante personalizzato per MODIFICARE bobina
      return (
        <button
          className="cavi-table-button bobina"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'modify_reel')
          }}
          title="Bobina Vuota - Clicca per modificare"
        >
          <span>Vuota</span>
          <ChevronDown className="icon w-3 h-3 opacity-70" />
        </button>
      )
    }

    // Estrai il numero della bobina usando la stessa logica della versione originale
    let numeroDisplay = idBobina

    // Pattern dalla webapp originale: /_B(.+)$/
    let match = idBobina.match(/_B(.+)$/)
    if (match) {
      numeroDisplay = match[1]
    } else {
      // Pattern alternativo: _b (minuscolo)
      match = idBobina.match(/_b(.+)$/)
      if (match) {
        numeroDisplay = match[1]
      } else {
        // Pattern per cX_bY o cX_BY
        match = idBobina.match(/c\d+_[bB](\d+)$/)
        if (match) {
          numeroDisplay = match[1]
        } else {
          // Pattern generale per numeri alla fine
          match = idBobina.match(/(\d+)$/)
          if (match) {
            numeroDisplay = match[1]
          }
        }
      }
    }

    // Bobina assegnata - Pulsante personalizzato con numero bobina
    return (
      <button
        className="cavi-table-button bobina"
        onClick={(e) => {
          e.stopPropagation()
          onStatusAction?.(cavo, 'modify_reel')
        }}
        title={`Bobina ${numeroDisplay} - Clicca per modificare`}
      >
        <span>{numeroDisplay}</span>
        <ChevronDown className="icon w-3 h-3 opacity-70" />
      </button>
    )
  }

  // 2. COLONNA STATO - Pulsanti Uniformi + Badge Informativi v3.3
  const getStatusBadge = (cavo: Cavo) => {
    const metriInstallati = cavo.metri_posati || cavo.metratura_reale || 0
    const isInstalled = metriInstallati > 0
    const stato = cavo.stato_installazione || 'Da installare'

    // Verifica se il cavo è assegnato a una comanda attiva
    const comandaPosa = cavo.comanda_posa
    const comandaPartenza = cavo.comanda_partenza
    const comandaArrivo = cavo.comanda_arrivo
    const comandaCertificazione = cavo.comanda_certificazione
    const comandaAttiva = comandaPosa || comandaPartenza || comandaArrivo || comandaCertificazione

    // Determina lo stato effettivo e il colore
    let statoDisplay = stato
    let colorClasses = getCavoColorClasses(stato)

    // Se c'è una comanda attiva, mostra il codice comanda come stato
    if (comandaAttiva && stato === 'In corso') {
      statoDisplay = comandaAttiva
      colorClasses = getCavoColorClasses('IN_CORSO')
    }

    // Se è installato, forza lo stato "Installato"
    if (isInstalled && stato !== 'Installato') {
      statoDisplay = 'Installato'
      colorClasses = getCavoColorClasses('INSTALLATO')
    }

    // Icone per ogni stato
    const getStatusIcon = (stato: string) => {
      switch (stato.toLowerCase()) {
        case 'installato':
          return <CheckCircle className="icon w-3 h-3" />
        case 'in corso':
          return <Clock className="icon w-3 h-3" />
        case 'da installare':
          return <AlertCircle className="icon w-3 h-3" />
        default:
          return comandaAttiva ? <Play className="icon w-3 h-3" /> : <AlertCircle className="icon w-3 h-3" />
      }
    }

    // "Da installare" è cliccabile per inserire metri posati
    if (stato.toLowerCase() === 'da installare' && !isInstalled) {
      return (
        <button
          className="cavi-table-button stato"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'insert_meters')
          }}
          title="Clicca per inserire metri posati"
        >
          {getStatusIcon(statoDisplay)}
          <span>{statoDisplay}</span>
        </button>
      )
    }

    // Altri stati usano lo stesso stile ma NON sono cliccabili
    return (
      <span
        className={`inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ${colorClasses.text} ${colorClasses.bg} ${colorClasses.border}`}
        title={comandaAttiva ? `Comanda attiva: ${comandaAttiva}` : `Stato: ${statoDisplay}`}
      >
        {getStatusIcon(statoDisplay)}
        <span>{statoDisplay}</span>
      </span>
    )
  }

  // 3. COLONNA COLLEGAMENTI - Pulsanti Uniformi v3.3
  const getConnectionButton = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const collegamento = cavo.collegamento || cavo.collegamenti || 0
    const primaryAction = getPrimaryActionClasses()
    const unavailable = getUnavailableClasses()

    if (!isInstalled) {
      // Non disponibile - Testo grigio statico (NON cliccabile)
      return (
        <span
          className={unavailable.text}
          title="Collegamento disponibile solo per cavi installati"
        >
          <Info className="w-3 h-3" />
          <span>Non disponibile</span>
        </span>
      )
    }

    // Pulsante personalizzato con icone originali webapp
    const getActionButton = (label: string, actionType: string, iconText: string) => {
      return (
        <button
          className="cavi-table-button collegamenti"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, actionType)
          }}
          title={`Clicca per ${label.toLowerCase()}`}
        >
          <span className="icon text-sm mr-1">{iconText}</span>
          <span>{label}</span>
        </button>
      )
    }

    // Usa le stesse icone e testi della webapp originale (senza "cavo")
    switch (collegamento) {
      case 0:
        return getActionButton("Collega", "connect_cable", "⚪⚪")
      case 1:
        return getActionButton("Completa collegamento", "connect_arrival", "🟢⚪")
      case 2:
        return getActionButton("Completa collegamento", "connect_departure", "⚪🟢")
      case 3:
        return getActionButton("Scollega", "disconnect_cable", "🟢🟢")
      default:
        return getActionButton("Gestisci collegamenti", "manage_connections", "⚙️")
    }
  }

  // 4. COLONNA CERTIFICATO - Pulsanti Personalizzati
  const getCertificationDisplay = (cavo: Cavo) => {
    const isInstalled = cavo.metri_posati > 0 || cavo.metratura_reale > 0
    const isCertified = cavo.certificato === true || cavo.certificato === 'SI' || cavo.certificato === 'CERTIFICATO'
    const unavailable = getUnavailableClasses()

    if (!isInstalled) {
      // Non disponibile - Testo grigio statico (NON cliccabile)
      return (
        <span
          className={unavailable.text}
          title="Certificazione disponibile solo per cavi installati"
        >
          <Info className="w-3 h-3" />
          <span>Non disponibile</span>
        </span>
      )
    }

    if (isCertified) {
      // Certificato - Pulsante personalizzato per PDF
      return (
        <button
          className="cavi-table-button certificazioni"
          onClick={(e) => {
            e.stopPropagation()
            onStatusAction?.(cavo, 'generate_pdf')
          }}
          title="Certificato - Clicca per generare PDF"
        >
          <CheckCircle className="icon w-3 h-3" />
          <span>PDF</span>
        </button>
      )
    }

    // Non certificato - Pulsante personalizzato per certificare
    return (
      <button
        className="cavi-table-button certificazioni"
        onClick={(e) => {
          e.stopPropagation()
          onStatusAction?.(cavo, 'create_certificate')
        }}
        title="Clicca per certificare il cavo"
      >
        <Award className="icon w-3 h-3" />
        <span>Certifica</span>
      </button>
    )
  }

  return (
    <div className="relative">
      {/* Smart Filter */}
      <SmartCaviFilter
        cavi={cavi}
        onFilteredDataChange={handleSmartFilterChange}
        loading={loading}
        selectionEnabled={internalSelectionEnabled}
        onSelectionToggle={handleSelectionToggle}
      />

      {/* Filterable Table */}
      <FilterableTable
        data={smartFilteredCavi}
        columns={columns}
        loading={loading}
        emptyMessage="Nessun cavo disponibile"
        onFilteredDataChange={handleTableFilterChange}
        renderRow={renderRow}
      />

      {/* Contextual Action Bar - appears only when items are selected */}
      {internalSelectionEnabled && selectedCavi.length > 0 && (
        <div className="sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10">
          <div className="flex items-center justify-between p-3">
            <div className="flex items-center space-x-3">
              <Badge variant="secondary" className="bg-mariner-100 text-mariner-800">
                {selectedCavi.length} cavi selezionati
              </Badge>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleSelectAll(false)}
                className="text-xs"
              >
                Deseleziona tutto
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkExport()}
                className="flex items-center space-x-1"
              >
                <span>📊</span>
                <span>Esporta</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkStatusChange()}
                className="flex items-center space-x-1"
              >
                <span>🔄</span>
                <span>Cambia Stato</span>
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={() => handleBulkAssignCommand()}
                className="flex items-center space-x-1"
              >
                <span>📋</span>
                <span>Assegna Comanda</span>
              </Button>

              <Button
                variant="destructive"
                size="sm"
                onClick={() => handleBulkDelete()}
                className="flex items-center space-x-1"
              >
                <span>🗑️</span>
                <span>Elimina</span>
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
