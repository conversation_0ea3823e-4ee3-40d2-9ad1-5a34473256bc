{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function Home() {\n  const { user, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (!isAuthenticated) {\n        // Se non autenticato, reindirizza al login\n        router.replace('/login')\n      }\n      // Se autenticato, rimani sulla homepage - non reindirizzare automaticamente\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  // Mostra un indicatore di caricamento durante il reindirizzamento (come nel React originale)\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 flex items-center justify-center\">\n      <div className=\"text-center\">\n        <h1 className=\"text-3xl font-bold text-slate-900 mb-4\">\n          Benvenuto nel Sistema di Gestione Cantieri\n        </h1>\n        <p className=\"text-slate-600 mb-6\">\n          Reindirizzamento in corso...\n        </p>\n        <div className=\"w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto\"></div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACnD,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,CAAC,iBAAiB;oBACpB,2CAA2C;oBAC3C,OAAO,OAAO,CAAC;gBACjB;YACA,4EAA4E;YAC9E;QACF;yBAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,6FAA6F;IAC7F,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BAGvD,6LAAC;oBAAE,WAAU;8BAAsB;;;;;;8BAGnC,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;;AAIvB;GA5BwB;;QACuB,kIAAA,CAAA,UAAO;QACrC,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}