from sqlalchemy import Column, Integer, String, Text, Date, ForeignKey
from sqlalchemy.orm import relationship

from backend.database import Base

class NonConformita(Base):
    """
    Modello SQLAlchemy per la tabella NonConformita.
    Gestisce le non conformità per conformità CEI 64-8.
    """
    __tablename__ = "nonconformita"
    
    id_nc = Column(Integer, primary_key=True, index=True)
    id_certificazione = Column(Integer, ForeignKey("certificazionicavi.id_certificazione"), nullable=True)
    id_rapporto = Column(Integer, ForeignKey("rapportigeneralicollaudo.id_rapporto"), nullable=True)
    
    # Identificazione NC
    codice_nc = Column(String, nullable=False, unique=True)
    data_rilevazione = Column(Date, nullable=False)
    tipo_nc = Column(String, nullable=True)  # 'CRITICA', 'MAGGIORE', 'MINORE'
    
    # Descrizione
    descrizione = Column(Text, nullable=False)
    riferimento_cavo = Column(String, nullable=True)
    riferimento_prova = Column(String, nullable=True)
    
    # Azioni Correttive
    azione_correttiva = Column(Text, nullable=True)
    responsabile_azione = Column(String, nullable=True)
    data_scadenza_azione = Column(Date, nullable=True)
    data_completamento_azione = Column(Date, nullable=True)
    
    # Ri-verifica
    esito_riverifica = Column(String, nullable=True)  # 'CONFORME', 'NON_CONFORME', 'PENDING'
    note_riverifica = Column(Text, nullable=True)
    
    # Stato
    stato_nc = Column(String, default='APERTA')  # 'APERTA', 'IN_CORSO', 'CHIUSA'
    
    # Relazioni
    certificazione = relationship("CertificazioneCavo", back_populates="non_conformita")
    rapporto = relationship("RapportoGeneraleCollaudo", back_populates="non_conformita")
