"""
Modulo per la gestione dei report del sistema CMS.
Fornisce funzionalità per generare vari tipi di report relativi ai cavi,
alle bobine e all'avanzamento dei lavori.

Include report di avanzamento (Progress Report) e distinta materiali (Bill of Quantities).
"""

# Importazioni standard
import os
import psycopg2
import logging
import math
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from contextlib import contextmanager
# Importazione rimossa per evitare problemi di importazione circolare
# from modules.utils import StatoInstallazione
from .database_pg import database_connection, Database

# Importazioni per data analysis
import pandas as pd

# Importazioni per la generazione di PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import cm
    REPORTLAB_AVAILABLE = True
except ImportError:
    logging.warning("ReportLab non disponibile. La generazione di PDF non sarà possibile.")
    REPORTLAB_AVAILABLE = False

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Directory per i report
REPORTS_DIR = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'reports')
PDF_REPORTS_DIR = os.path.join(REPORTS_DIR, 'pdf')
EXCEL_REPORTS_DIR = os.path.join(REPORTS_DIR, 'excel')

# Crea le directory se non esistono
try:
    os.makedirs(REPORTS_DIR, exist_ok=True)
    os.makedirs(PDF_REPORTS_DIR, exist_ok=True)
    os.makedirs(EXCEL_REPORTS_DIR, exist_ok=True)
    logging.info(f"Directory per i report create: {REPORTS_DIR}")
except Exception as e:
    logging.error(f"Errore nella creazione delle directory per i report: {str(e)}")

# Costanti per i report
# Valore hardcoded corrispondente a StatoInstallazione.INSTALLATO.value
STATO_INSTALLATO = "Installato"

# Log per verificare il valore
logging.info(f"Modulo reports.py inizializzato con STATO_INSTALLATO = '{STATO_INSTALLATO}'")

# Il context manager database_connection è ora importato dal modulo database


# Miglioramenti alla funzione crea_pdf_report
def crea_pdf_report(titolo, sottotitolo, dati, colonne, nome_file, tabelle_aggiuntive=None):
    """
    Crea un report PDF generico con supporto per tabelle multiple.
    Versione migliorata con:
    - Gestione ottimizzata delle tabelle multiple
    - Stili più professionali
    - Migliore formattazione

    Returns:
        str: Percorso del file PDF generato, o None se ReportLab non è disponibile
    """
    # Verifica se ReportLab è disponibile
    if not REPORTLAB_AVAILABLE:
        logging.error("Impossibile generare il PDF: ReportLab non è installato.")
        print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
        print("Installa ReportLab con: pip install reportlab")
        return None
    # Percorso completo del file
    pdf_path = os.path.join(PDF_REPORTS_DIR, nome_file)

    # Crea il documento con margini appropriati
    doc = SimpleDocTemplate(pdf_path, pagesize=A4,
                            leftMargin=2 * cm, rightMargin=2 * cm,
                            topMargin=2 * cm, bottomMargin=2 * cm)

    # Contenuto del documento
    elements = []

    # Definizione stili personalizzati
    styles = getSampleStyleSheet()

    # Stile per il titolo principale
    title_style = ParagraphStyle(
        'TitleStyle',
        parent=styles['Heading1'],
        fontSize=16,
        leading=18,
        spaceAfter=12,
        alignment=1  # Centrato
    )

    # Stile per il sottotitolo
    subtitle_style = ParagraphStyle(
        'SubtitleStyle',
        parent=styles['Heading2'],
        fontSize=12,
        leading=14,
        spaceAfter=12,
        textColor=colors.darkblue,
        alignment=1  # Centrato
    )

    # Stile per le sezioni
    section_style = ParagraphStyle(
        'SectionStyle',
        parent=styles['Heading3'],
        fontSize=11,
        leading=13,
        spaceBefore=12,
        spaceAfter=6,
        textColor=colors.darkblue
    )

    # Stile per il testo normale
    normal_style = styles['Normal']

    # Aggiungi titolo e sottotitolo
    elements.append(Paragraph(titolo, title_style))
    elements.append(Paragraph(sottotitolo, subtitle_style))
    elements.append(Spacer(1, 0.5 * cm))

    # Aggiungi data generazione
    data_gen = f"<i>Report generato il {datetime.now().strftime('%d/%m/%Y alle %H:%M:%S')}</i>"
    elements.append(Paragraph(data_gen, normal_style))
    elements.append(Spacer(1, 1 * cm))

    # Funzione per creare una tabella con stile
    def crea_tabella_con_stile(dati_tabella, colonne_tabella, titolo_tabella=None):
        # Aggiungi titolo della sezione se presente
        if titolo_tabella:
            elements.append(Paragraph(titolo_tabella, section_style))
            elements.append(Spacer(1, 0.3 * cm))

        # Prepara i dati per la tabella
        table_data = [colonne_tabella]  # Prima riga: intestazioni
        table_data.extend(dati_tabella)  # Aggiungi i dati

        # Determina la larghezza delle colonne in base al contenuto
        num_cols = len(colonne_tabella)
        col_widths = ['*'] * num_cols  # Distribuisce uniformemente lo spazio

        # Per colonne con valori numerici, riduci la larghezza
        for i, col in enumerate(colonne_tabella):
            if any('.' in str(row[i]) for row in dati_tabella if i < len(row)):
                col_widths[i] = 1.5 * cm

        # Crea la tabella
        table = Table(table_data,
                      colWidths=col_widths,
                      repeatRows=1,
                      hAlign='LEFT')

        # Stile della tabella
        table_style = TableStyle([
            # Intestazione
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#4472C4')),  # Blu più scuro
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 8),

            # Righe dati
            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
            ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
            ('ALIGN', (-3, 1), (-1, -1), 'RIGHT'),  # Allinea a destra le ultime 3 colonne (presumibilmente numeriche)
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 0.5, colors.lightgrey),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),

            # Righe alternate
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.HexColor('#F2F2F2')])
        ])

        # Applica lo stile alla tabella
        table.setStyle(table_style)

        return table

    # Crea e aggiungi la tabella principale
    tabella_principale = crea_tabella_con_stile(dati, colonne)
    elements.append(tabella_principale)

    # Aggiungi tabelle aggiuntive se presenti
    if tabelle_aggiuntive:
        for idx, tabella in enumerate(tabelle_aggiuntive):
            # Aggiungi spaziatura tra le tabelle
            elements.append(Spacer(1, 0.8 * cm))

            # Crea e aggiungi la tabella
            tab = crea_tabella_con_stile(
                tabella['dati'],
                tabella['colonne'],
                tabella.get('titolo', '')
            )
            elements.append(tab)

    # Genera il PDF
    try:
        # Assicurati che la directory esista
        os.makedirs(os.path.dirname(pdf_path), exist_ok=True)

        # Genera il PDF
        doc.build(elements)

        # Verifica che il file sia stato creato
        if os.path.exists(pdf_path):
            logging.info(f"PDF generato con successo: {pdf_path}")
            return pdf_path
        else:
            logging.error(f"Il file PDF non è stato creato: {pdf_path}")
            print(f"\n❌ Il file PDF non è stato creato: {pdf_path}")
            return None
    except Exception as e:
        logging.error(f"Errore durante la generazione del PDF: {str(e)}")
        print(f"\n❌ Errore durante la generazione del PDF: {str(e)}")
        return None

def genera_report_avanzamento(id_cantiere, formato='pdf'):
    """
    Genera un report sull'avanzamento dei lavori per un cantiere specifico.

    Args:
        id_cantiere (int o str): ID del cantiere
        formato (str): Formato del report ('pdf', 'excel' o 'video')

    Returns:
        str: Percorso del file di report generato, o None in caso di errore
              Se formato='video', visualizza il report a schermo e restituisce True
    """
    # Assicurati che id_cantiere sia un intero
    try:
        id_cantiere = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
    except (ValueError, TypeError):
        logging.error(f"ID cantiere non valido: {id_cantiere}")
        return None
    try:
        with database_connection() as conn:
            # Ottieni il nome del cantiere
            c = conn.cursor()
            c.execute("SELECT commessa FROM Cantieri WHERE id_cantiere = ?", (id_cantiere,))
            result = c.fetchone()
            if not result:
                logging.error(f"Cantiere con ID {id_cantiere} non trovato")
                return None
            nome_cantiere = result['commessa']

            # Query per ottenere i dati di avanzamento
            c.execute("""
                SELECT
                    stato_installazione,
                    COUNT(*) as num_cavi,
                    SUM(metri_teorici) as metri_teorici_totali,
                    SUM(metratura_reale) as metri_reali_totali
                FROM Cavi
                WHERE id_cantiere = ? AND modificato_manualmente != 3
                GROUP BY stato_installazione
            """, (id_cantiere,))

            dati_avanzamento = [dict(row) for row in c.fetchall()]

            # Calcola le statistiche complessive
            c.execute("""
                SELECT
                    COUNT(*) as totale_cavi,
                    SUM(metri_teorici) as totale_metri_teorici,
                    SUM(metratura_reale) as totale_metri_reali,
                    SUM(CASE WHEN metratura_reale > 0 THEN 1 ELSE 0 END) as cavi_posati,
                    SUM(CASE WHEN stato_installazione != ? THEN metri_teorici ELSE 0 END) as metri_teorici_non_posati
                FROM Cavi
                WHERE id_cantiere = ? AND modificato_manualmente != 3
            """, (STATO_INSTALLATO, id_cantiere,))

            statistiche = dict(c.fetchone())

            # Calcola la percentuale di avanzamento secondo la nuova logica
            # Avanzamento = metri reali posati / (metri teorici non posati + metri reali posati) * 100
            metri_reali_posati = statistiche['totale_metri_reali'] or 0
            metri_teorici_non_posati = statistiche['metri_teorici_non_posati'] or 0

            denominatore = metri_teorici_non_posati + metri_reali_posati
            if denominatore > 0:
                percentuale_avanzamento = (metri_reali_posati / denominatore) * 100
            else:
                percentuale_avanzamento = 0

            # Crea nome file univoco
            timestamp = datetime.now().strftime('%Y%m%d_%H%M')

            # Visualizza il report a schermo se richiesto
            if formato.lower() == 'video':
                print("\n" + "=" * 80)
                print(f"REPORT AVANZAMENTO - {nome_cantiere} (ID: {id_cantiere})")
                print("=" * 80)

                # Sezione 1: Avanzamento per stato
                print("\n[AVANZAMENTO PER STATO]")
                print("{:<15} {:<10} {:<15} {:<15} {:<15}".format(
                    "Stato", "Num. Cavi", "Metri Teorici", "Metri Reali", "% Avanzamento"
                ))
                print("-" * 70)

                # Calcola i metri teorici totali dei cavi non posati
                metri_teorici_non_posati = sum(s['metri_teorici_totali'] or 0 for s in dati_avanzamento
                                             if s['stato_installazione'] != STATO_INSTALLATO)
                # Calcola i metri reali totali posati
                metri_reali_posati = sum(s['metri_reali_totali'] or 0 for s in dati_avanzamento)

                for stato in dati_avanzamento:
                    metri_teorici = stato['metri_teorici_totali'] or 0
                    metri_reali = stato['metri_reali_totali'] or 0

                    # Per lo stato "Installato", la percentuale è 100% per definizione
                    if stato['stato_installazione'] == STATO_INSTALLATO:
                        perc_avanzamento = 100.0
                    else:
                        # Per gli altri stati, calcola l'avanzamento secondo la nuova logica
                        denominatore = metri_teorici_non_posati + metri_reali_posati
                        perc_avanzamento = (metri_reali_posati / denominatore * 100) if denominatore > 0 else 0

                    print("{:<15} {:<10} {:<15.2f} {:<15.2f} {:<15.2f}%".format(
                        stato['stato_installazione'],
                        stato['num_cavi'],
                        metri_teorici,
                        metri_reali,
                        perc_avanzamento
                    ))

                # Sezione 2: Statistiche complessive
                print("\n[STATISTICHE COMPLESSIVE]")
                print(f"Totale Cavi: {statistiche['totale_cavi']}")
                print(f"Totale Metri Teorici: {statistiche['totale_metri_teorici']:.2f}")
                print(f"Totale Metri Reali: {statistiche['totale_metri_reali']:.2f}")
                print(f"Percentuale Avanzamento: {percentuale_avanzamento:.2f}%")

                return True

            # Genera il report nel formato richiesto
            elif formato.lower() == 'excel':
                # Crea DataFrame per il report
                # Calcola i metri teorici totali dei cavi non posati
                metri_teorici_non_posati = sum(s['metri_teorici_totali'] or 0 for s in dati_avanzamento
                                             if s['stato_installazione'] != STATO_INSTALLATO)
                # Calcola i metri reali totali posati
                metri_reali_posati = sum(s['metri_reali_totali'] or 0 for s in dati_avanzamento)

                # Calcola la percentuale di avanzamento per ogni stato
                for stato in dati_avanzamento:
                    metri_teorici = stato['metri_teorici_totali'] or 0
                    metri_reali = stato['metri_reali_totali'] or 0

                    # Per lo stato "Installato", la percentuale è 100% per definizione
                    if stato['stato_installazione'] == STATO_INSTALLATO:
                        stato['percentuale_avanzamento'] = 100.0
                    else:
                        # Per gli altri stati, calcola l'avanzamento secondo la nuova logica
                        denominatore = metri_teorici_non_posati + metri_reali_posati
                        stato['percentuale_avanzamento'] = (metri_reali_posati / denominatore * 100) if denominatore > 0 else 0

                    # Rinomina le colonne per maggiore chiarezza
                    stato['Stato'] = stato.pop('stato_installazione')
                    stato['Numero Cavi'] = stato.pop('num_cavi')
                    stato['Metri Teorici'] = stato.pop('metri_teorici_totali')
                    stato['Metri Reali'] = stato.pop('metri_reali_totali')
                    stato['% Avanzamento'] = stato.pop('percentuale_avanzamento')

                df_avanzamento = pd.DataFrame(dati_avanzamento)

                # Percorso del file Excel
                report_path = os.path.join(EXCEL_REPORTS_DIR, f"avanzamento_{nome_cantiere}_{timestamp}.xlsx")

                # Salva il report Excel
                with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
                    # Foglio con i dati di avanzamento per stato
                    if not df_avanzamento.empty:
                        df_avanzamento.to_excel(writer, sheet_name='Avanzamento per Stato', index=False)

                    # Foglio con le statistiche complessive
                    df_statistiche = pd.DataFrame([{
                        'Totale Cavi': statistiche['totale_cavi'],
                        'Totale Metri Teorici': statistiche['totale_metri_teorici'],
                        'Totale Metri Posati': statistiche['totale_metri_reali'],
                        'Cavi Posati': statistiche['cavi_posati'],
                        'Percentuale Avanzamento': f"{percentuale_avanzamento:.2f}%"
                    }])
                    df_statistiche.to_excel(writer, sheet_name='Statistiche Complessive', index=False)

                logging.info(f"Report Excel di avanzamento generato: {report_path}")

            else:  # formato PDF
                # Prepara i dati per il report PDF
                # 1. Tabella di avanzamento per stato
                colonne_avanzamento = ['Stato', 'Numero Cavi', 'Metri Teorici', 'Metri Posati', '% Completamento']
                dati_tabella = []

                # Utilizziamo i dati già trasformati per il formato Excel
                # Se non sono stati trasformati (perché siamo arrivati direttamente al formato PDF),
                # facciamo la trasformazione qui
                if 'Stato' not in dati_avanzamento[0] if dati_avanzamento else {}:
                    # Calcola i metri teorici totali dei cavi non posati
                    metri_teorici_non_posati = sum(s['metri_teorici_totali'] or 0 for s in dati_avanzamento
                                                 if s['stato_installazione'] != STATO_INSTALLATO)
                    # Calcola i metri reali totali posati
                    metri_reali_posati = sum(s['metri_reali_totali'] or 0 for s in dati_avanzamento)

                    # Calcola la percentuale di avanzamento per ogni stato
                    for stato in dati_avanzamento:
                        metri_teorici = stato['metri_teorici_totali'] or 0
                        metri_reali = stato['metri_reali_totali'] or 0

                        # Per lo stato "Installato", la percentuale è 100% per definizione
                        if stato['stato_installazione'] == STATO_INSTALLATO:
                            stato['percentuale_avanzamento'] = 100.0
                        else:
                            # Per gli altri stati, calcola l'avanzamento secondo la nuova logica
                            denominatore = metri_teorici_non_posati + metri_reali_posati
                            stato['percentuale_avanzamento'] = (metri_reali_posati / denominatore * 100) if denominatore > 0 else 0

                        # Rinomina le colonne per maggiore chiarezza
                        stato['Stato'] = stato.pop('stato_installazione')
                        stato['Numero Cavi'] = stato.pop('num_cavi')
                        stato['Metri Teorici'] = stato.pop('metri_teorici_totali')
                        stato['Metri Reali'] = stato.pop('metri_reali_totali')
                        stato['% Avanzamento'] = stato.pop('percentuale_avanzamento')

                for row in dati_avanzamento:
                    dati_tabella.append([
                        row['Stato'],
                        str(row['Numero Cavi']),
                        f"{row['Metri Teorici']:.2f}",
                        f"{row['Metri Reali']:.2f}",
                        f"{row['% Avanzamento']:.2f}%"
                    ])

                # 2. Statistiche complessive
                colonne_statistiche = ['Metrica', 'Valore']
                dati_statistiche = [
                    ['Totale Cavi', str(statistiche['totale_cavi'])],
                    ['Totale Metri Teorici', f"{statistiche['totale_metri_teorici']:.2f}"],
                    ['Totale Metri Posati', f"{statistiche['totale_metri_reali']:.2f}"],
                    ['Cavi Posati', str(statistiche['cavi_posati'])],
                    ['Percentuale Avanzamento', f"{percentuale_avanzamento:.2f}%"]
                ]

                # Crea il report PDF
                titolo = f"Report Avanzamento Lavori"
                sottotitolo = f"Cantiere: {nome_cantiere} (ID: {id_cantiere})"
                nome_file = f"avanzamento_{nome_cantiere}_{timestamp}.pdf"

                # Genera il PDF con la prima tabella (avanzamento per stato)
                if not REPORTLAB_AVAILABLE:
                    print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
                    print("Installa ReportLab con: pip install reportlab")
                    print("\nUtilizza il formato 'video' per visualizzare il report a schermo.")
                    return None
                else:
                    pdf_path = crea_pdf_report(
                        titolo,
                        sottotitolo,
                        dati_tabella,
                        colonne_avanzamento,
                        nome_file
                    )

                # Aggiungi la seconda tabella (statistiche complessive) allo stesso PDF
                # Nota: qui dovremmo estendere la funzione crea_pdf_report per supportare più tabelle
                # Per ora, includiamo solo la tabella principale

                logging.info(f"Report PDF di avanzamento generato: {pdf_path}")
                report_path = pdf_path

            return report_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del report di avanzamento: {str(e)}")
        return None


def genera_report_cavi_per_stato(id_cantiere, formato='pdf'):
    """
    Genera un report dettagliato dei cavi suddivisi per stato di installazione.

    Args:
        id_cantiere (int o str): ID del cantiere
        formato (str): Formato del report ('pdf', 'excel' o 'video')

    Returns:
        str: Percorso del file di report generato, o None in caso di errore
              Se formato='video', visualizza il report a schermo e restituisce True
    """
    # Assicurati che id_cantiere sia un intero
    try:
        id_cantiere = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
    except (ValueError, TypeError):
        logging.error(f"ID cantiere non valido: {id_cantiere}")
        return None
    try:
        with database_connection() as conn:
            # Ottieni il nome del cantiere
            c = conn.cursor()
            c.execute("SELECT commessa FROM Cantieri WHERE id_cantiere = ?", (id_cantiere,))
            result = c.fetchone()
            if not result:
                logging.error(f"Cantiere con ID {id_cantiere} non trovato")
                return None
            nome_cantiere = result['commessa']

            # Query per ottenere tutti i cavi attivi
            c.execute("""
                SELECT
                    id_cavo, utility, tipologia, n_conduttori, sezione,
                    ubicazione_partenza as partenza, ubicazione_arrivo as arrivo, metri_teorici, metratura_reale,
                    stato_installazione, revisione_ufficiale, id_bobina
                FROM Cavi
                WHERE id_cantiere = ? AND modificato_manualmente != 3
                ORDER BY stato_installazione, id_cavo
            """, (id_cantiere,))

            cavi = [dict(row) for row in c.fetchall()]

            # Crea nome file univoco
            timestamp = datetime.now().strftime('%Y%m%d_%H%M')

            # Visualizza il report a schermo se richiesto
            if formato.lower() == 'video':
                print("\n" + "=" * 80)
                print(f"REPORT CAVI PER STATO - {nome_cantiere} (ID: {id_cantiere})")
                print("=" * 80)

                # Raggruppa i cavi per stato
                cavi_per_stato = {}
                for cavo in cavi:
                    stato = cavo['stato_installazione']
                    if stato not in cavi_per_stato:
                        cavi_per_stato[stato] = []
                    cavi_per_stato[stato].append(cavo)

                # Mostra i cavi raggruppati per stato
                for stato, lista_cavi in cavi_per_stato.items():
                    print(f"\n[STATO: {stato}] - {len(lista_cavi)} cavi")
                    print("{:<15} {:<15} {:<10} {:<10} {:<15} {:<15}".format(
                        "ID Cavo", "Tipologia", "Conduttori", "Sezione", "Metri Teorici", "Metri Reali"
                    ))
                    print("-" * 80)

                    for cavo in lista_cavi:
                        print("{:<15} {:<15} {:<10} {:<10} {:<15.2f} {:<15.2f}".format(
                            cavo['id_cavo'],
                            cavo['tipologia'],
                            str(cavo['n_conduttori']),
                            str(cavo['sezione']),
                            cavo['metri_teorici'],
                            cavo['metratura_reale'] or 0
                        ))

                # Mostra statistiche complessive
                totale_metri_teorici = sum(cavo['metri_teorici'] for cavo in cavi)
                totale_metri_reali = sum(cavo['metratura_reale'] or 0 for cavo in cavi)

                print("\n[STATISTICHE COMPLESSIVE]")
                print(f"Totale Cavi: {len(cavi)}")
                print(f"Totale Metri Teorici: {totale_metri_teorici:.2f}")
                print(f"Totale Metri Reali: {totale_metri_reali:.2f}")

                return True

            # Genera il report nel formato richiesto
            elif formato.lower() == 'excel':
                # Crea DataFrame
                df_cavi = pd.DataFrame(cavi)

                # Percorso del file Excel
                report_path = os.path.join(EXCEL_REPORTS_DIR, f"cavi_per_stato_{nome_cantiere}_{timestamp}.xlsx")

                # Salva il report Excel
                with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
                    # Se ci sono cavi, crea un foglio per ogni stato
                    if not df_cavi.empty:
                        # Foglio con tutti i cavi
                        df_cavi.to_excel(writer, sheet_name='Tutti i Cavi', index=False)

                        # Fogli separati per ogni stato
                        stati = df_cavi['stato_installazione'].unique()
                        for stato in stati:
                            df_stato = df_cavi[df_cavi['stato_installazione'] == stato]
                            sheet_name = f"Stato - {stato}"[:31]  # Excel ha un limite di 31 caratteri per i nomi dei fogli
                            df_stato.to_excel(writer, sheet_name=sheet_name, index=False)
                    else:
                        # Se non ci sono cavi, crea un foglio vuoto
                        pd.DataFrame().to_excel(writer, sheet_name='Nessun Cavo', index=False)

                logging.info(f"Report Excel cavi per stato generato: {report_path}")

            else:  # formato PDF
                # Prepara i dati per il report PDF
                if not cavi:
                    # Se non ci sono cavi, crea un report vuoto
                    titolo = f"Report Cavi per Stato"
                    sottotitolo = f"Cantiere: {nome_cantiere} (ID: {id_cantiere})"
                    nome_file = f"cavi_per_stato_{nome_cantiere}_{timestamp}.pdf"

                    # Crea un report vuoto
                    pdf_path = crea_pdf_report(
                        titolo,
                        sottotitolo,
                        [["Nessun cavo trovato", "", "", "", ""]],
                        ["ID Cavo", "Tipologia", "Partenza", "Arrivo", "Stato"],
                        nome_file
                    )
                else:
                    # Raggruppa i cavi per stato
                    cavi_per_stato = {}
                    for cavo in cavi:
                        stato = cavo['stato_installazione']
                        if stato not in cavi_per_stato:
                            cavi_per_stato[stato] = []
                        cavi_per_stato[stato].append(cavo)

                    # Crea un report per ogni stato
                    # Per semplicità, creiamo un unico report con tutti i cavi
                    titolo = f"Report Cavi per Stato"
                    sottotitolo = f"Cantiere: {nome_cantiere} (ID: {id_cantiere})"
                    nome_file = f"cavi_per_stato_{nome_cantiere}_{timestamp}.pdf"

                    # Prepara i dati per la tabella
                    colonne = ["ID Cavo", "Tipologia", "Conduttori", "Sezione", "Partenza", "Arrivo", "Metri", "Stato"]
                    dati_tabella = []

                    for cavo in cavi:
                        dati_tabella.append([
                            cavo['id_cavo'],
                            cavo['tipologia'],
                            str(cavo['n_conduttori']),
                            str(cavo['sezione']),
                            cavo['partenza'],
                            cavo['arrivo'],
                            f"{cavo['metri_teorici']:.2f}",
                            cavo['stato_installazione']
                        ])

                    # Genera il PDF
                    if not REPORTLAB_AVAILABLE:
                        print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
                        print("Installa ReportLab con: pip install reportlab")
                        print("\nUtilizza il formato 'video' per visualizzare il report a schermo.")
                        return None
                    else:
                        pdf_path = crea_pdf_report(
                            titolo,
                            sottotitolo,
                            dati_tabella,
                            colonne,
                            nome_file
                        )

                logging.info(f"Report PDF cavi per stato generato: {pdf_path}")
                report_path = pdf_path

            return report_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del report cavi per stato: {str(e)}")
        return None


def genera_progress_report(id_cantiere, formato='pdf'):
    """
    Genera un report dettagliato sull'avanzamento dei lavori (Report Avanzamento).

    Il report include:
    - Metri totali da posare (teorici + reali)
    - Metri posati totali
    - Percentuale di avanzamento
    - Numero di cavi da posare e posati
    - Metri posati giornalieri, settimanali e mensili
    - Previsione dei giorni necessari per completare il progetto

    Args:
        id_cantiere (int o str): ID del cantiere
        formato (str): Formato del report ('pdf', 'excel' o 'video')

    Returns:
        str: Percorso del file di report generato, o None in caso di errore
              Se formato='video', visualizza il report a schermo e restituisce True
    """
    # Assicurati che id_cantiere sia un intero
    try:
        id_cantiere = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
    except (ValueError, TypeError):
        logging.error(f"ID cantiere non valido: {id_cantiere}")
        return None
    try:
        with database_connection() as conn:
            # Ottieni il nome del cantiere
            c = conn.cursor()
            c.execute("SELECT commessa FROM Cantieri WHERE id_cantiere = ?", (id_cantiere,))
            result = c.fetchone()
            if not result:
                logging.error(f"Cantiere con ID {id_cantiere} non trovato")
                return None
            nome_cantiere = result['commessa']

            # Inizializza stats con valori predefiniti
            stats = {
                'metri_teorici_da_posare': 0,
                'metri_reali_posati': 0,
                'totale_cavi': 0,
                'cavi_posati': 0
            }

            # Calcola i metri totali da posare e posati
            try:
                c.execute("""
                    SELECT
                        SUM(CASE WHEN stato_installazione != ? THEN metri_teorici ELSE 0 END) as metri_teorici_da_posare,
                        SUM(CASE WHEN stato_installazione = ? THEN metratura_reale ELSE 0 END) as metri_reali_posati,
                        COUNT(*) as totale_cavi,
                        SUM(CASE WHEN stato_installazione = ? THEN 1 ELSE 0 END) as cavi_posati
                    FROM Cavi
                    WHERE id_cantiere = ? AND modificato_manualmente != 3
                """, (STATO_INSTALLATO, STATO_INSTALLATO, STATO_INSTALLATO, id_cantiere))

                # Aggiorna stats con i risultati della query
                result = c.fetchone()
                if result:
                    stats.update({
                        'metri_teorici_da_posare': result['metri_teorici_da_posare'] or 0,
                        'metri_reali_posati': result['metri_reali_posati'] or 0,
                        'totale_cavi': result['totale_cavi'] or 0,
                        'cavi_posati': result['cavi_posati'] or 0
                    })

                # Debug: stampa i valori per verificare
                logging.info(f"Query per metri posati con STATO_INSTALLATO={STATO_INSTALLATO}")

                # Verifica se ci sono cavi installati
                c.execute("""
                    SELECT COUNT(*) as num_installati, SUM(metratura_reale) as totale_metri_reali
                    FROM Cavi
                    WHERE id_cantiere = ? AND stato_installazione = ? AND modificato_manualmente != 3
                """, (id_cantiere, STATO_INSTALLATO))

                debug_result = c.fetchone()
                logging.info(f"Cavi installati: {debug_result['num_installati'] if debug_result and debug_result['num_installati'] is not None else 0}, Metri reali totali: {debug_result['totale_metri_reali'] if debug_result and debug_result['totale_metri_reali'] is not None else 0}")

                # Verifica anche altri possibili valori di stato_installazione nel database
                c.execute("""
                    SELECT stato_installazione, COUNT(*) as num_cavi
                    FROM Cavi
                    WHERE id_cantiere = ? AND modificato_manualmente != 3
                    GROUP BY stato_installazione
                """, (id_cantiere,))

                # Verifica specifica per "INSTALLATO" (tutto maiuscolo)
                c.execute("""
                    SELECT COUNT(*) as num_cavi_maiuscolo, SUM(metratura_reale) as metri_reali_maiuscolo
                    FROM Cavi
                    WHERE id_cantiere = ? AND stato_installazione = 'INSTALLATO' AND modificato_manualmente != 3
                """, (id_cantiere,))

                result_maiuscolo = c.fetchone()
                if result_maiuscolo and result_maiuscolo['num_cavi_maiuscolo'] > 0:
                    logging.warning(f"Trovati {result_maiuscolo['num_cavi_maiuscolo']} cavi con stato 'INSTALLATO' (tutto maiuscolo) per un totale di {result_maiuscolo['metri_reali_maiuscolo']} metri")

                # Verifica tutti i cavi con metratura_reale > 0, indipendentemente dallo stato
                c.execute("""
                    SELECT COUNT(*) as num_cavi_con_metri, SUM(metratura_reale) as totale_metri_reali_tutti
                    FROM Cavi
                    WHERE id_cantiere = ? AND metratura_reale > 0 AND modificato_manualmente != 3
                """, (id_cantiere,))

                result_tutti_metri = c.fetchone()
                num_cavi_con_metri = result_tutti_metri['num_cavi_con_metri'] if result_tutti_metri and 'num_cavi_con_metri' in result_tutti_metri.keys() else 0
                totale_metri_reali_tutti = result_tutti_metri['totale_metri_reali_tutti'] if result_tutti_metri and 'totale_metri_reali_tutti' in result_tutti_metri.keys() else 0
                logging.info(f"Totale cavi con metratura_reale > 0: {num_cavi_con_metri}, Totale metri reali: {totale_metri_reali_tutti}")

                # Se non ci sono cavi con stato "Installato" ma ci sono cavi con metratura_reale > 0,
                # utilizziamo il totale dei metri reali indipendentemente dallo stato
                if (debug_result['num_installati'] == 0 or debug_result['totale_metri_reali'] == 0) and totale_metri_reali_tutti is not None and totale_metri_reali_tutti > 0:
                    logging.warning(f"Nessun cavo con stato '{STATO_INSTALLATO}' trovato, ma ci sono cavi con metratura_reale > 0. Utilizzo il totale dei metri reali.")
                    # Aggiorniamo stats con i valori corretti
                    stats = {
                        'metri_teorici_da_posare': stats.get('metri_teorici_da_posare', 0),
                        'metri_reali_posati': totale_metri_reali_tutti,
                        'totale_cavi': stats.get('totale_cavi', 0),
                        'cavi_posati': num_cavi_con_metri
                    }

                stati_presenti = c.fetchall()
                for stato in stati_presenti:
                    logging.info(f"Stato installazione presente nel DB: '{stato['stato_installazione']}', numero cavi: {stato['num_cavi']}")
            except Exception as e:
                logging.error(f"Errore nella query per calcolo metri: {str(e)}")
                raise

            # Non chiamiamo più c.fetchone() qui perché abbiamo già recuperato tutti i risultati
            # e abbiamo aggiornato stats se necessario

            # Calcola i metri totali da posare (teorici non installati + reali installati)
            try:
                c.execute("""
                    SELECT
                        SUM(CASE WHEN stato_installazione != ? THEN metri_teorici ELSE metratura_reale END) as metri_totali_progetto
                    FROM Cavi
                    WHERE id_cantiere = ? AND modificato_manualmente != 3
                """, (STATO_INSTALLATO, id_cantiere))

                # Debug: stampa i valori per verificare
                logging.info(f"Query per metri totali progetto con STATO_INSTALLATO={STATO_INSTALLATO}")
            except Exception as e:
                logging.error(f"Errore nella query per calcolo metri totali: {str(e)}")
                raise

            result = c.fetchone()
            metri_totali = result['metri_totali_progetto'] if result and result['metri_totali_progetto'] is not None else 0
            metri_posati = stats['metri_reali_posati']

            # Calcola la percentuale di avanzamento
            try:
                percentuale_avanzamento = (metri_posati / metri_totali * 100) if metri_totali > 0 else 0
            except Exception as e:
                logging.error(f"Errore nel calcolo della percentuale di avanzamento: {str(e)}")
                percentuale_avanzamento = 0

            # Calcola i metri posati per periodo (giornaliero, settimanale, mensile)
            oggi = datetime.now().date()
            ieri = oggi - timedelta(days=1)
            inizio_settimana = oggi - timedelta(days=oggi.weekday())
            inizio_mese = oggi.replace(day=1)

            # Query per metri posati giornalieri (oggi) usando la tabella Cavi
            try:
                c.execute("""
                    SELECT SUM(metratura_reale) as metri_posati
                    FROM Cavi
                    WHERE id_cantiere = ?
                    AND data_posa = ?
                    AND stato_installazione = ?
                """, (id_cantiere, oggi.isoformat(), STATO_INSTALLATO))
                result = c.fetchone()
                metri_giornalieri = result['metri_posati'] if result and result['metri_posati'] is not None else 0

                # Debug: stampa i valori per verificare
                logging.info(f"Metri posati oggi ({oggi.isoformat()}): {metri_giornalieri}")

                # Verifica dettagliata
                c.execute("""
                    SELECT id_cavo, metratura_reale as metri_posati, data_posa
                    FROM Cavi
                    WHERE id_cantiere = ?
                    AND data_posa = ?
                    AND stato_installazione = ?
                """, (id_cantiere, oggi.isoformat(), STATO_INSTALLATO))

                cavi_oggi = c.fetchall()
                for cavo in cavi_oggi:
                    logging.info(f"Cavo posato oggi: {cavo['id_cavo']}, metri: {cavo['metri_posati']}, data: {cavo['data_posa']}")
            except Exception as e:
                logging.error(f"Errore nella query per metri giornalieri: {str(e)}")
                metri_giornalieri = 0

            # Query per metri posati ieri usando la tabella Cavi
            try:
                c.execute("""
                    SELECT SUM(metratura_reale) as metri_posati
                    FROM Cavi
                    WHERE id_cantiere = ?
                    AND data_posa = ?
                    AND stato_installazione = ?
                """, (id_cantiere, ieri.isoformat(), STATO_INSTALLATO))
                result = c.fetchone()
                metri_ieri = result['metri_posati'] if result and result['metri_posati'] is not None else 0
            except Exception as e:
                logging.error(f"Errore nella query per metri ieri: {str(e)}")
                metri_ieri = 0

            # Query per metri posati settimanali usando la tabella Cavi
            try:
                c.execute("""
                    SELECT SUM(metratura_reale) as metri_posati
                    FROM Cavi
                    WHERE id_cantiere = ?
                    AND data_posa >= ?
                    AND stato_installazione = ?
                """, (id_cantiere, inizio_settimana.isoformat(), STATO_INSTALLATO))
                result = c.fetchone()
                metri_settimanali = result['metri_posati'] if result and result['metri_posati'] is not None else 0
            except Exception as e:
                logging.error(f"Errore nella query per metri settimanali: {str(e)}")
                metri_settimanali = 0

            # Query per metri posati mensili usando la tabella Cavi
            try:
                c.execute("""
                    SELECT SUM(metratura_reale) as metri_posati
                    FROM Cavi
                    WHERE id_cantiere = ?
                    AND data_posa >= ?
                    AND stato_installazione = ?
                """, (id_cantiere, inizio_mese.isoformat(), STATO_INSTALLATO))
                result = c.fetchone()
                metri_mensili = result['metri_posati'] if result and result['metri_posati'] is not None else 0
            except Exception as e:
                logging.error(f"Errore nella query per metri mensili: {str(e)}")
                metri_mensili = 0

            # Calcola la media giornaliera di posa (ultimi 7 giorni) usando la tabella Cavi
            try:
                c.execute("""
                    SELECT data_posa, SUM(metratura_reale) as metri_posati
                    FROM Cavi
                    WHERE id_cantiere = ?
                    AND data_posa >= date('now', '-7 days')
                    AND stato_installazione = ?
                    GROUP BY data_posa
                    ORDER BY data_posa DESC
                """, (id_cantiere, STATO_INSTALLATO))

                posa_ultimi_giorni = [dict(row) for row in c.fetchall()]
            except Exception as e:
                logging.error(f"Errore nella query per posa ultimi giorni: {str(e)}")
                posa_ultimi_giorni = []

            # Calcola la media giornaliera
            if posa_ultimi_giorni:
                totale_metri_recenti = sum(row['metri_posati'] for row in posa_ultimi_giorni)
                giorni_attivi = len(posa_ultimi_giorni)
                media_giornaliera = totale_metri_recenti / giorni_attivi if giorni_attivi > 0 else 0
            else:
                media_giornaliera = 0

            # Calcola i giorni stimati per completare il progetto
            try:
                metri_rimanenti = metri_totali - metri_posati
                giorni_stimati = round(metri_rimanenti / media_giornaliera) if media_giornaliera > 0 else float('inf')
            except Exception as e:
                logging.error(f"Errore nel calcolo dei metri rimanenti o giorni stimati: {str(e)}")
                metri_rimanenti = 0
                giorni_stimati = float('inf')

            # Crea nome file univoco
            timestamp = datetime.now().strftime('%Y%m%d_%H%M')

            # Visualizza il report a schermo se richiesto
            if formato.lower() == 'video':
                print("\n" + "=" * 80)
                print(f"REPORT AVANZAMENTO - {nome_cantiere} (ID: {id_cantiere})")
                print("=" * 80)

                # Sezione 1: Avanzamento generale
                print("\n[AVANZAMENTO GENERALE]")
                print(f"Metri Totali Progetto: {metri_totali:.2f}")
                print(f"Metri Posati: {metri_posati:.2f}")
                print(f"Metri Rimanenti: {metri_rimanenti:.2f}")
                print(f"Percentuale Avanzamento: {percentuale_avanzamento:.2f}%")
                print(f"Totale Cavi: {stats['totale_cavi']}")
                print(f"Cavi Posati: {stats['cavi_posati']}")
                print(f"Cavi Rimanenti: {stats['totale_cavi'] - stats['cavi_posati']}")
                perc_cavi = (stats['cavi_posati'] / stats['totale_cavi'] * 100) if stats['totale_cavi'] > 0 else 0
                print(f"Percentuale Cavi Posati: {perc_cavi:.2f}%")

                # La sezione "AVANZAMENTO PER STATO" è stata rimossa

                # Sezione rimossa: Posa per periodo (ora disponibile come report dedicato)

                # Sezione 3: Previsione completamento
                print("\n[PREVISIONE COMPLETAMENTO]")
                print(f"Media Giornaliera (ultimi 7 giorni): {media_giornaliera:.2f} m/giorno")
                if giorni_stimati != float('inf'):
                    print(f"Giorni Stimati per Completamento: {giorni_stimati} giorni")
                    data_completamento = (oggi + timedelta(days=giorni_stimati)).strftime('%d/%m/%Y')
                    print(f"Data Stimata Completamento: {data_completamento}")
                else:
                    print("Giorni Stimati per Completamento: N/A")
                    print("Data Stimata Completamento: N/A")

                # Dettaglio ultimi 7 giorni
                if posa_ultimi_giorni:
                    print("\n[DETTAGLIO ULTIMI 7 GIORNI]")
                    for giorno in posa_ultimi_giorni:
                        print(f"{giorno['data_posa']}: {giorno['metri_posati']:.2f} metri")

                return True

            # Prepara i dati per il report in formato file
            elif formato.lower() == 'excel':
                # Percorso del file Excel
                report_path = os.path.join(EXCEL_REPORTS_DIR, f"report_avanzamento_{nome_cantiere}_{timestamp}.xlsx")

                # Crea DataFrame per il report
                df_avanzamento = pd.DataFrame([
                    {'Metrica': 'Metri Totali Progetto', 'Valore': f"{metri_totali:.2f}"},
                    {'Metrica': 'Metri Posati', 'Valore': f"{metri_posati:.2f}"},
                    {'Metrica': 'Metri Rimanenti', 'Valore': f"{metri_rimanenti:.2f}"},
                    {'Metrica': 'Percentuale Avanzamento', 'Valore': f"{percentuale_avanzamento:.2f}%"},
                    {'Metrica': 'Totale Cavi', 'Valore': stats['totale_cavi']},
                    {'Metrica': 'Cavi Posati', 'Valore': stats['cavi_posati']},
                    {'Metrica': 'Cavi Rimanenti', 'Valore': stats['totale_cavi'] - stats['cavi_posati']},
                    {'Metrica': 'Percentuale Cavi Posati', 'Valore': f"{(stats['cavi_posati'] / stats['totale_cavi'] * 100):.2f}%" if stats['totale_cavi'] > 0 else "0.00%"},
                ])

                # DataFrame per posa per periodo rimosso (ora disponibile come report dedicato)

                df_previsione = pd.DataFrame([
                    {'Metrica': 'Media Giornaliera (ultimi 7 giorni)', 'Valore': f"{media_giornaliera:.2f} m/giorno"},
                    {'Metrica': 'Giorni Stimati per Completamento', 'Valore': f"{giorni_stimati} giorni" if giorni_stimati != float('inf') else "N/A"},
                    {'Metrica': 'Data Stimata Completamento', 'Valore': (oggi + timedelta(days=giorni_stimati)).strftime('%d/%m/%Y') if giorni_stimati != float('inf') else "N/A"},
                ])

                # Crea DataFrame per i dati giornalieri
                df_giorni = pd.DataFrame(posa_ultimi_giorni)
                if not df_giorni.empty:
                    df_giorni.columns = ['Data', 'Metri Posati']

                # La sezione "Avanzamento Per Stato" per Excel è stata rimossa

                # Salva il report Excel
                with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
                    df_avanzamento.to_excel(writer, sheet_name='Avanzamento Generale', index=False)
                    df_previsione.to_excel(writer, sheet_name='Previsione Completamento', index=False)
                    if not df_giorni.empty:
                        df_giorni.to_excel(writer, sheet_name='Dettaglio Ultimi 7 Giorni', index=False)

                logging.info(f"Report Avanzamento Excel generato: {report_path}")

            else:  # formato PDF
                # Prepara i dati per il report PDF
                titolo = f"Report Avanzamento"
                sottotitolo = f"Cantiere: {nome_cantiere} (ID: {id_cantiere})"
                nome_file = f"report_avanzamento_{nome_cantiere}_{timestamp}.pdf"

                # Tabella 1: Avanzamento generale
                colonne_avanzamento = ['Metrica', 'Valore']
                dati_avanzamento = [
                    ['Metri Totali Progetto', f"{metri_totali:.2f}"],
                    ['Metri Posati', f"{metri_posati:.2f}"],
                    ['Metri Rimanenti', f"{metri_rimanenti:.2f}"],
                    ['Percentuale Avanzamento', f"{percentuale_avanzamento:.2f}%"],
                    ['Totale Cavi', str(stats['totale_cavi'])],
                    ['Cavi Posati', str(stats['cavi_posati'])],
                    ['Cavi Rimanenti', str(stats['totale_cavi'] - stats['cavi_posati'])],
                    ['Percentuale Cavi Posati', f"{(stats['cavi_posati'] / stats['totale_cavi'] * 100):.2f}%" if stats['totale_cavi'] > 0 else "0.00%"],
                ]

                # Tabella 'Posa per periodo' rimossa (ora disponibile come report dedicato)

                # La sezione "Avanzamento Per Stato" per PDF è stata rimossa

                # Tabella 3: Previsione completamento
                colonne_previsione = ['Metrica', 'Valore']
                dati_previsione = [
                    ['Media Giornaliera (ultimi 7 giorni)', f"{media_giornaliera:.2f} m/giorno"],
                    ['Giorni Stimati per Completamento', f"{giorni_stimati} giorni" if giorni_stimati != float('inf') else "N/A"],
                    ['Data Stimata Completamento', (oggi + timedelta(days=giorni_stimati)).strftime('%d/%m/%Y') if giorni_stimati != float('inf') else "N/A"],
                ]

                # Prepara le tabelle aggiuntive
                tabelle_aggiuntive = [
                    {
                        'titolo': 'Previsione Completamento',
                        'colonne': colonne_previsione,
                        'dati': dati_previsione
                    }
                ]

                # Genera il PDF con tutte le tabelle
                if not REPORTLAB_AVAILABLE and formato.lower() == 'pdf':
                    print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
                    print("Installa ReportLab con: pip install reportlab")
                    print("\nVisualizzazione del report a schermo:")

                    # Visualizza direttamente a schermo invece di chiamare ricorsivamente
                    print("\n" + "=" * 80)
                    print(f"REPORT AVANZAMENTO - {nome_cantiere} (ID: {id_cantiere})")
                    print("=" * 80)

                    # Sezione 1: Avanzamento generale
                    print("\n[AVANZAMENTO GENERALE]")
                    print(f"Metri Totali Progetto: {metri_totali:.2f}")
                    print(f"Metri Posati: {metri_posati:.2f}")
                    print(f"Metri Rimanenti: {metri_rimanenti:.2f}")
                    print(f"Percentuale Avanzamento: {percentuale_avanzamento:.2f}%")
                    print(f"Totale Cavi: {stats['totale_cavi']}")
                    print(f"Cavi Posati: {stats['cavi_posati']}")
                    print(f"Cavi Rimanenti: {stats['totale_cavi'] - stats['cavi_posati']}")
                    perc_cavi = (stats['cavi_posati'] / stats['totale_cavi'] * 100) if stats['totale_cavi'] > 0 else 0
                    print(f"Percentuale Cavi Posati: {perc_cavi:.2f}%")

                    # Sezione rimossa: Posa per periodo (ora disponibile come report dedicato)

                    # Sezione 3: Previsione completamento
                    print("\n[PREVISIONE COMPLETAMENTO]")
                    print(f"Media Giornaliera (ultimi 7 giorni): {media_giornaliera:.2f} m/giorno")
                    if giorni_stimati != float('inf'):
                        print(f"Giorni Stimati per Completamento: {giorni_stimati} giorni")
                        data_completamento = (oggi + timedelta(days=giorni_stimati)).strftime('%d/%m/%Y')
                        print(f"Data Stimata Completamento: {data_completamento}")
                    else:
                        print("Giorni Stimati per Completamento: N/A")
                        print("Data Stimata Completamento: N/A")

                    return True
                else:
                    pdf_path = crea_pdf_report(
                        titolo,
                        sottotitolo,
                        dati_avanzamento,
                        colonne_avanzamento,
                        nome_file,
                        tabelle_aggiuntive
                    )

                logging.info(f"Report Avanzamento PDF generato: {pdf_path}")
                report_path = pdf_path

            return report_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del Progress Report: {str(e)}")
        return None


def genera_bill_of_quantities(id_cantiere, formato='pdf'):
    """
    Genera un report sulla distinta materiali (Bill of Quantities).

    Il report include:
    - Calcolo dei metri per tipologia, sezione e numero di conduttori dei cavi
    - Calcolo del residuo dei cavi posati per tipologia
    - Previsione della necessità di acquistare altro cavo per finire il progetto

    Args:
        id_cantiere (int o str): ID del cantiere
        formato (str): Formato del report ('pdf', 'excel' o 'video')

    Returns:
        str: Percorso del file di report generato, o None in caso di errore
              Se formato='video', visualizza il report a schermo e restituisce True
    """
    # Assicurati che id_cantiere sia un intero
    try:
        id_cantiere = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
    except (ValueError, TypeError):
        logging.error(f"ID cantiere non valido: {id_cantiere}")
        return None
    try:
        with database_connection() as conn:
            # Ottieni il nome del cantiere
            c = conn.cursor()
            c.execute("SELECT nome FROM Cantieri WHERE id_cantiere = ?", (id_cantiere,))
            result = c.fetchone()
            if not result:
                logging.error(f"Cantiere con ID {id_cantiere} non trovato")
                return None
            nome_cantiere = result['nome']

            # Query per ottenere i dati dei cavi raggruppati per tipologia, sezione e numero di conduttori
            c.execute("""
                SELECT
                    tipologia, n_conduttori, sezione,
                    COUNT(*) as num_cavi,
                    SUM(metri_teorici) as metri_teorici_totali,
                    SUM(metratura_reale) as metri_reali_posati,
                    SUM(CASE WHEN stato_installazione != ? THEN metri_teorici ELSE 0 END) as metri_da_posare
                FROM Cavi
                WHERE id_cantiere = ? AND modificato_manualmente != 3
                GROUP BY tipologia, n_conduttori, sezione
                ORDER BY tipologia, n_conduttori, sezione
            """, (STATO_INSTALLATO, id_cantiere))

            cavi_per_tipo = [dict(row) for row in c.fetchall()]

            # Query per ottenere i dati delle bobine disponibili
            c.execute("""
                SELECT
                    tipologia, n_conduttori, sezione,
                    COUNT(*) as num_bobine,
                    SUM(metri_residui) as metri_disponibili
                FROM parco_cavi
                WHERE id_cantiere = ? AND stato_bobina != 'ESAURITA'
                GROUP BY tipologia, n_conduttori, sezione
                ORDER BY tipologia, n_conduttori, sezione
            """, (id_cantiere,))

            bobine_per_tipo = [dict(row) for row in c.fetchall()]

            # Crea un dizionario per le bobine per facile accesso
            bobine_dict = {}
            for bobina in bobine_per_tipo:
                key = (bobina['tipologia'], bobina['n_conduttori'], bobina['sezione'])
                bobine_dict[key] = bobina

            # Calcola il fabbisogno residuo e se è necessario acquistare altro cavo
            for cavo in cavi_per_tipo:
                key = (cavo['tipologia'], cavo['n_conduttori'], cavo['sezione'])
                bobina = bobine_dict.get(key, {'metri_disponibili': 0, 'num_bobine': 0})

                metri_necessari = cavo['metri_da_posare']
                metri_disponibili = bobina.get('metri_disponibili', 0)

                cavo['metri_disponibili'] = metri_disponibili
                cavo['metri_mancanti'] = max(0, metri_necessari - metri_disponibili)
                cavo['necessita_acquisto'] = cavo['metri_mancanti'] > 0

            # Crea nome file univoco
            timestamp = datetime.now().strftime('%Y%m%d_%H%M')

            # Visualizza il report a schermo se richiesto
            if formato.lower() == 'video':
                print("\n" + "=" * 80)
                print(f"BILL OF QUANTITIES (DISTINTA MATERIALI) - {nome_cantiere} (ID: {id_cantiere})")
                print("=" * 80)

                if not cavi_per_tipo:
                    print("\nNessun dato disponibile per questo cantiere.")
                    return True

                # Intestazione tabella
                print("\n{:<15} {:<10} {:<10} {:<15} {:<15} {:<15} {:<15} {:<15} {:<10}".format(
                    "Tipologia", "Conduttori", "Sezione", "Metri Totali", "Metri Posati",
                    "Metri da Posare", "Metri Disponibili", "Metri Mancanti", "Necessita Acquisto"
                ))
                print("-" * 120)

                # Dati
                for cavo in cavi_per_tipo:
                    # Gestisci n_conduttori come stringa o numero
                    try:
                        n_cond_val = int(cavo['n_conduttori']) if cavo['n_conduttori'] else 0
                        n_conduttori = str(n_cond_val) if n_cond_val > 0 else '-'
                    except (ValueError, TypeError):
                        n_conduttori = str(cavo['n_conduttori']) if cavo['n_conduttori'] else '-'

                    # Gestisci sezione come stringa
                    sezione_val = cavo['sezione']
                    if isinstance(sezione_val, (int, float)) and sezione_val == 0:
                        sezione = '-'  # Se è 0, mostra '-'
                    else:
                        sezione = str(sezione_val) if sezione_val else '-'

                    print("{:<15} {:<10} {:<10} {:<15.2f} {:<15.2f} {:<15.2f} {:<15.2f} {:<15.2f} {:<10}".format(
                        cavo['tipologia'],
                        n_conduttori,
                        sezione,
                        cavo['metri_teorici_totali'],
                        cavo['metri_reali_posati'],
                        cavo['metri_da_posare'],
                        cavo['metri_disponibili'],
                        cavo['metri_mancanti'],
                        "Sì" if cavo['necessita_acquisto'] else "No"
                    ))

                # Riepilogo
                print("\n[RIEPILOGO]")
                totale_necessario = sum(cavo['metri_da_posare'] for cavo in cavi_per_tipo)
                totale_disponibile = sum(cavo['metri_disponibili'] for cavo in cavi_per_tipo)
                totale_mancante = sum(cavo['metri_mancanti'] for cavo in cavi_per_tipo)
                print(f"Totale metri necessari: {totale_necessario:.2f}")
                print(f"Totale metri disponibili: {totale_disponibile:.2f}")
                print(f"Totale metri mancanti: {totale_mancante:.2f}")

                # Nota: La sezione separata "TIPOLOGIE CHE NECESSITANO ACQUISTO" è stata rimossa
                # poiché queste informazioni sono già visibili nella tabella principale

                return True

            # Genera il report nel formato richiesto
            elif formato.lower() == 'excel':
                # Crea DataFrame
                df_cavi = pd.DataFrame(cavi_per_tipo)

                # Aggiungi colonne calcolate
                if not df_cavi.empty:
                    df_cavi['Necessita Acquisto'] = df_cavi['necessita_acquisto'].apply(lambda x: "Sì" if x else "No")

                # Percorso del file Excel
                report_path = os.path.join(EXCEL_REPORTS_DIR, f"bill_of_quantities_{nome_cantiere}_{timestamp}.xlsx")

                # Salva il report Excel
                with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
                    if not df_cavi.empty:
                        df_cavi.to_excel(writer, sheet_name='Distinta Materiali', index=False)
                    else:
                        pd.DataFrame().to_excel(writer, sheet_name='Nessun Dato', index=False)

                logging.info(f"Bill of Quantities Excel generato: {report_path}")

            else:  # formato PDF
                # Prepara i dati per il report PDF
                titolo = f"Distinta Materiali (Bill of Quantities)"
                sottotitolo = f"Cantiere: {nome_cantiere} (ID: {id_cantiere})"
                nome_file = f"bill_of_quantities_{nome_cantiere}_{timestamp}.pdf"

                if not cavi_per_tipo:
                    # Se non ci sono dati, crea un report vuoto
                    pdf_path = crea_pdf_report(
                        titolo,
                        sottotitolo,
                        [["Nessun dato disponibile", "", "", "", ""]],
                        ["Tipologia", "Conduttori", "Sezione", "Metri Necessari", "Metri Disponibili"],
                        nome_file
                    )
                else:
                    # Prepara i dati per la tabella
                    colonne = ["Tipologia", "Conduttori", "Sezione", "Metri Totali", "Metri Posati", "Metri da Posare", "Metri Disponibili", "Metri Mancanti", "Necessita Acquisto"]
                    dati_tabella = []

                    for cavo in cavi_per_tipo:
                        dati_tabella.append([
                            cavo['tipologia'],
                            str(cavo['n_conduttori']),
                            str(cavo['sezione']),
                            f"{cavo['metri_teorici_totali']:.2f}",
                            f"{cavo['metri_reali_posati']:.2f}",
                            f"{cavo['metri_da_posare']:.2f}",
                            f"{cavo['metri_disponibili']:.2f}",
                            f"{cavo['metri_mancanti']:.2f}",
                            "Sì" if cavo['necessita_acquisto'] else "No"
                        ])

                    # Genera il PDF
                    if not REPORTLAB_AVAILABLE and formato.lower() == 'pdf':
                        print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
                        print("Installa ReportLab con: pip install reportlab")
                        print("\nVisualizzazione del report a schermo:")

                        # Visualizza direttamente a schermo invece di chiamare ricorsivamente
                        print("\n" + "=" * 80)
                        print(f"BILL OF QUANTITIES (DISTINTA MATERIALI) - {nome_cantiere} (ID: {id_cantiere})")
                        print("=" * 80)

                        # Intestazione tabella
                        print("\n{:<15} {:<10} {:<10} {:<15} {:<15} {:<15} {:<15} {:<15} {:<10}".format(
                            "Tipologia", "Conduttori", "Sezione", "Metri Totali", "Metri Posati",
                            "Metri da Posare", "Metri Disponibili", "Metri Mancanti", "Necessita Acquisto"
                        ))
                        print("-" * 120)

                        # Dati
                        for cavo in cavi_per_tipo:
                            # Gestisci n_conduttori come stringa o numero
                            try:
                                n_cond_val = int(cavo['n_conduttori']) if cavo['n_conduttori'] else 0
                                n_conduttori = str(n_cond_val) if n_cond_val > 0 else '-'
                            except (ValueError, TypeError):
                                n_conduttori = str(cavo['n_conduttori']) if cavo['n_conduttori'] else '-'

                            # Gestisci sezione come stringa
                            sezione_val = cavo['sezione']
                            if isinstance(sezione_val, (int, float)) and sezione_val == 0:
                                sezione = '-'  # Se è 0, mostra '-'
                            else:
                                sezione = str(sezione_val) if sezione_val else '-'

                            print("{:<15} {:<10} {:<10} {:<15.2f} {:<15.2f} {:<15.2f} {:<15.2f} {:<15.2f} {:<10}".format(
                                cavo['tipologia'],
                                n_conduttori,
                                sezione,
                                cavo['metri_teorici_totali'],
                                cavo['metri_reali_posati'],
                                cavo['metri_da_posare'],
                                cavo['metri_disponibili'],
                                cavo['metri_mancanti'],
                                "Sì" if cavo['necessita_acquisto'] else "No"
                            ))

                        # Riepilogo
                        print("\n[RIEPILOGO]")
                        totale_necessario = sum(cavo['metri_da_posare'] for cavo in cavi_per_tipo)
                        totale_disponibile = sum(cavo['metri_disponibili'] for cavo in cavi_per_tipo)
                        totale_mancante = sum(cavo['metri_mancanti'] for cavo in cavi_per_tipo)
                        print(f"Totale metri necessari: {totale_necessario:.2f}")
                        print(f"Totale metri disponibili: {totale_disponibile:.2f}")
                        print(f"Totale metri mancanti: {totale_mancante:.2f}")

                        # Nota: La sezione separata "TIPOLOGIE CHE NECESSITANO ACQUISTO" è stata rimossa
                        # poiché queste informazioni sono già visibili nella tabella principale

                        return True
                    else:
                        pdf_path = crea_pdf_report(
                            titolo,
                            sottotitolo,
                            dati_tabella,
                            colonne,
                            nome_file
                        )

                logging.info(f"Bill of Quantities PDF generato: {pdf_path}")
                report_path = pdf_path

            return report_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del Bill of Quantities: {str(e)}")
        return None


def genera_report_utilizzo_bobine(id_cantiere, formato='pdf'):
    """
    Genera un report sull'utilizzo delle bobine per un cantiere specifico.

    Args:
        id_cantiere (int o str): ID del cantiere
        formato (str): Formato del report ('pdf', 'excel' o 'video')

    Returns:
        str: Percorso del file di report generato, o None in caso di errore
              Se formato='video', visualizza il report a schermo e restituisce True
    """
    # Assicurati che id_cantiere sia un intero
    try:
        id_cantiere = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
    except (ValueError, TypeError):
        logging.error(f"ID cantiere non valido: {id_cantiere}")
        return None
    try:
        with database_connection() as conn:
            # Ottieni il nome del cantiere
            c = conn.cursor()
            c.execute("SELECT nome FROM Cantieri WHERE id_cantiere = ?", (id_cantiere,))
            result = c.fetchone()
            if not result:
                logging.error(f"Cantiere con ID {id_cantiere} non trovato")
                return None
            nome_cantiere = result['nome']

            # Query per ottenere i dati delle bobine
            c.execute("""
                SELECT
                    b.ID_BOBINA, b.tipologia, b.n_conduttori, b.sezione,
                    b.metri_totali, b.metri_residui as metri_disponibili,
                    (b.metri_totali - b.metri_residui) as metri_utilizzati,
                    CASE
                        WHEN b.metri_totali > 0
                        THEN ((b.metri_totali - b.metri_residui) / b.metri_totali) * 100
                        ELSE 0
                    END as percentuale_utilizzo
                FROM parco_cavi b
                WHERE b.id_cantiere = ?
                ORDER BY b.ID_BOBINA
            """, (id_cantiere,))

            bobine = [dict(row) for row in c.fetchall()]

            # Query per ottenere i cavi associati a ciascuna bobina
            c.execute("""
                SELECT
                    c.id_bobina, c.id_cavo, c.tipologia, c.n_conduttori, c.sezione,
                    c.metri_teorici, c.metratura_reale, c.stato_installazione
                FROM Cavi c
                WHERE c.id_cantiere = ? AND c.id_bobina IS NOT NULL AND c.id_bobina != ''
                ORDER BY c.id_bobina, c.id_cavo
            """, (id_cantiere,))

            cavi_bobine = [dict(row) for row in c.fetchall()]

            # Crea nome file univoco
            timestamp = datetime.now().strftime('%Y%m%d_%H%M')

            # Visualizza il report a schermo se richiesto
            if formato.lower() == 'video':
                print("\n" + "=" * 80)
                print(f"REPORT UTILIZZO BOBINE - {nome_cantiere} (ID: {id_cantiere})")
                print("=" * 80)

                if not bobine:
                    print("\nNessuna bobina trovata per questo cantiere.")
                    return True

                # Sezione 1: Riepilogo bobine
                print("\n[RIEPILOGO BOBINE]")
                print("{:<15} {:<15} {:<10} {:<10} {:<15} {:<15} {:<15} {:<10}".format(
                    "Bobina", "Tipologia", "Conduttori", "Sezione", "Metri Totali", "Metri Utilizzati", "Metri Residui", "% Utilizzo"
                ))
                print("-" * 100)

                for bobina in bobine:
                    # Estrai solo il numero della bobina (es. da "C1_B1" a "1")
                    numero_bobina = bobina['ID_BOBINA'].split('_B')[1] if '_B' in bobina['ID_BOBINA'] else bobina['ID_BOBINA']
                    print("{:<15} {:<15} {:<10} {:<10} {:<15.2f} {:<15.2f} {:<15.2f} {:<10.2f}%".format(
                        numero_bobina,
                        bobina['tipologia'],
                        str(bobina['n_conduttori']),
                        str(bobina['sezione']),
                        bobina['metri_totali'],
                        bobina['metri_utilizzati'],
                        bobina['metri_disponibili'],
                        bobina['percentuale_utilizzo']
                    ))

                # Sezione 2: Cavi associati alle bobine
                if cavi_bobine:
                    print("\n[CAVI ASSOCIATI ALLE BOBINE]")
                    print("{:<15} {:<15} {:<15} {:<10} {:<10} {:<15} {:<15} {:<15}".format(
                        "Bobina", "ID Cavo", "Tipologia", "Conduttori", "Sezione", "Metri Teorici", "Metri Reali", "Stato"
                    ))
                    print("-" * 110)

                    for cavo in cavi_bobine:
                        # Estrai solo il numero della bobina (es. da "C1_B1" a "1")
                        numero_bobina = cavo['id_bobina'].split('_B')[1] if '_B' in cavo['id_bobina'] else cavo['id_bobina']
                        print("{:<15} {:<15} {:<15} {:<10} {:<10} {:<15.2f} {:<15.2f} {:<15}".format(
                            numero_bobina,
                            cavo['id_cavo'],
                            cavo['tipologia'],
                            str(cavo['n_conduttori']),
                            str(cavo['sezione']),
                            cavo['metri_teorici'],
                            cavo['metratura_reale'] or 0,
                            cavo['stato_installazione']
                        ))
                else:
                    print("\nNessun cavo associato alle bobine.")

                # Statistiche complessive
                totale_metri_bobine = sum(bobina['metri_totali'] for bobina in bobine)
                totale_metri_utilizzati = sum(bobina['metri_utilizzati'] for bobina in bobine)
                totale_metri_residui = sum(bobina['metri_disponibili'] for bobina in bobine)

                print("\n[STATISTICHE COMPLESSIVE]")
                print(f"Totale Bobine: {len(bobine)}")
                print(f"Totale Metri Bobine: {totale_metri_bobine:.2f}")
                print(f"Totale Metri Utilizzati: {totale_metri_utilizzati:.2f}")
                print(f"Totale Metri Residui: {totale_metri_residui:.2f}")
                if totale_metri_bobine > 0:
                    print(f"Percentuale Utilizzo Complessiva: {(totale_metri_utilizzati / totale_metri_bobine * 100):.2f}%")

                return True

            # Genera il report nel formato richiesto
            elif formato.lower() == 'excel':
                # Crea DataFrames
                df_bobine = pd.DataFrame(bobine)
                df_cavi_bobine = pd.DataFrame(cavi_bobine)

                # Percorso del file Excel
                report_path = os.path.join(EXCEL_REPORTS_DIR, f"utilizzo_bobine_{nome_cantiere}_{timestamp}.xlsx")

                # Salva il report Excel
                with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
                    # Foglio con i dati delle bobine
                    if not df_bobine.empty:
                        df_bobine.to_excel(writer, sheet_name='Riepilogo Bobine', index=False)
                    else:
                        pd.DataFrame().to_excel(writer, sheet_name='Nessuna Bobina', index=False)

                    # Foglio con i cavi associati alle bobine
                    if not df_cavi_bobine.empty:
                        df_cavi_bobine.to_excel(writer, sheet_name='Cavi per Bobina', index=False)

                        # Fogli separati per ogni bobina
                        bobine_ids = df_cavi_bobine['id_bobina'].unique()
                        for bobina_id in bobine_ids:
                            if bobina_id:  # Verifica che l'ID bobina non sia vuoto
                                df_bobina = df_cavi_bobine[df_cavi_bobine['id_bobina'] == bobina_id]
                                sheet_name = f"Bobina - {bobina_id}"[:31]  # Excel ha un limite di 31 caratteri
                                df_bobina.to_excel(writer, sheet_name=sheet_name, index=False)

                logging.info(f"Report Excel utilizzo bobine generato: {report_path}")

            else:  # formato PDF
                # Prepara i dati per il report PDF
                titolo = f"Report Utilizzo Bobine"
                sottotitolo = f"Cantiere: {nome_cantiere} (ID: {id_cantiere})"
                nome_file = f"utilizzo_bobine_{nome_cantiere}_{timestamp}.pdf"

                if not bobine:
                    # Se non ci sono bobine, crea un report vuoto
                    if not REPORTLAB_AVAILABLE:
                        print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
                        print("Installa ReportLab con: pip install reportlab")
                        print("\nUtilizza il formato 'video' per visualizzare il report a schermo.")
                        return None
                    else:
                        pdf_path = crea_pdf_report(
                            titolo,
                            sottotitolo,
                            [["Nessuna bobina trovata", "", "", "", ""]],
                            ["ID Bobina", "Tipologia", "Metri Totali", "Metri Utilizzati", "% Utilizzo"],
                            nome_file
                        )
                else:
                    # Prepara i dati per la tabella delle bobine
                    colonne_bobine = ["ID Bobina", "Tipologia", "Conduttori", "Sezione", "Metri Totali", "Metri Disponibili", "Metri Utilizzati", "% Utilizzo"]
                    dati_bobine = []

                    for bobina in bobine:
                        dati_bobine.append([
                            bobina['ID_BOBINA'],
                            bobina['tipologia'],
                            str(bobina['n_conduttori']),
                            str(bobina['sezione']),
                            f"{bobina['metri_totali']:.2f}",
                            f"{bobina['metri_disponibili']:.2f}",
                            f"{bobina['metri_utilizzati']:.2f}",
                            f"{bobina['percentuale_utilizzo']:.2f}%"
                        ])

                    # Genera il PDF
                    if not REPORTLAB_AVAILABLE:
                        print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
                        print("Installa ReportLab con: pip install reportlab")
                        print("\nUtilizza il formato 'video' per visualizzare il report a schermo.")
                        return None
                    else:
                        pdf_path = crea_pdf_report(
                            titolo,
                            sottotitolo,
                            dati_bobine,
                            colonne_bobine,
                            nome_file
                        )

                logging.info(f"Report PDF utilizzo bobine generato: {pdf_path}")
                report_path = pdf_path

            return report_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del report utilizzo bobine: {str(e)}")
        return None


def genera_report_bobina_specifica(id_cantiere, id_bobina, formato='pdf'):
    """
    Genera un report dettagliato sull'utilizzo di una bobina specifica.

    Args:
        id_cantiere (int o str): ID del cantiere
        id_bobina (str): ID della bobina da analizzare
        formato (str): Formato del report ('pdf', 'excel' o 'video')

    Returns:
        str o bool: Percorso del file generato o True se visualizzato a schermo
    """
    if not id_cantiere or not id_bobina:
        logging.error("ID cantiere o ID bobina non validi")
        return None
    try:
        with database_connection() as conn:
            # Ottieni il nome del cantiere
            c = conn.cursor()
            c.execute("SELECT commessa FROM Cantieri WHERE id_cantiere = ?", (id_cantiere,))
            result = c.fetchone()
            if not result:
                logging.error(f"Cantiere con ID {id_cantiere} non trovato")
                return None
            nome_cantiere = result['commessa']

            # Query per ottenere i dati della bobina specifica
            c.execute("""
                SELECT
                    b.ID_BOBINA, b.tipologia, b.n_conduttori, b.sezione,
                    b.metri_totali, b.metri_residui as metri_disponibili,
                    (b.metri_totali - b.metri_residui) as metri_utilizzati,
                    CASE
                        WHEN b.metri_totali > 0
                        THEN ((b.metri_totali - b.metri_residui) / b.metri_totali) * 100
                        ELSE 0
                    END as percentuale_utilizzo,
                    b.stato_bobina, b.utility
                FROM parco_cavi b
                WHERE b.id_cantiere = ? AND b.ID_BOBINA = ?
            """, (id_cantiere, id_bobina))

            bobina = c.fetchone()
            if not bobina:
                print(f"\n❌ Bobina con ID {id_bobina} non trovata nel cantiere {nome_cantiere}")
                return None

            bobina = dict(bobina)

            # Query per ottenere i cavi associati alla bobina
            c.execute("""
                SELECT
                    c.id_bobina, c.id_cavo, c.tipologia, c.n_conduttori, c.sezione,
                    c.metri_teorici, c.metratura_reale, c.stato_installazione,
                    c.from_location, c.to_location, c.data_posa
                FROM Cavi c
                WHERE c.id_cantiere = ? AND c.id_bobina = ?
                ORDER BY c.id_cavo
            """, (id_cantiere, id_bobina))

            cavi_bobina = [dict(row) for row in c.fetchall()]

            # Crea nome file univoco
            timestamp = datetime.now().strftime('%Y%m%d_%H%M')

            # Visualizza il report a schermo se richiesto
            if formato.lower() == 'video':
                # Estrai solo il numero della bobina (es. da "C1_B1" a "1")
                numero_bobina = id_bobina.split('_B')[1] if '_B' in id_bobina else id_bobina

                print("\n" + "=" * 80)
                print(f"REPORT DETTAGLIATO BOBINA {numero_bobina} - {nome_cantiere} (ID: {id_cantiere})")
                print("=" * 80)

                # Sezione 1: Dettagli bobina
                print("\n[DETTAGLI BOBINA]")
                print(f"Bobina: {numero_bobina}")
                print(f"Tipologia: {bobina['tipologia']}")
                print(f"Conduttori: {bobina['n_conduttori']}")
                print(f"Sezione: {bobina['sezione']}")
                print(f"Utility: {bobina['utility']}")
                print(f"Stato: {bobina['stato_bobina']}")
                print(f"Metri Totali: {bobina['metri_totali']:.2f}")
                print(f"Metri Utilizzati: {bobina['metri_utilizzati']:.2f}")
                print(f"Metri Residui: {bobina['metri_disponibili']:.2f}")
                print(f"Percentuale Utilizzo: {bobina['percentuale_utilizzo']:.2f}%")

                # Sezione 2: Cavi associati
                if cavi_bobina:
                    print("\n[CAVI ASSOCIATI]")
                    print("{:<10} {:<15} {:<10} {:<10} {:<15} {:<15} {:<15} {:<20}".format(
                        "ID Cavo", "Tipologia", "Conduttori", "Sezione", "Metri Teorici", "Metri Reali", "Stato", "Data Posa"
                    ))
                    print("-" * 120)

                    for cavo in cavi_bobina:
                        data_posa = cavo['data_posa'] if cavo['data_posa'] else "-"
                        print("{:<10} {:<15} {:<10} {:<10} {:<15.2f} {:<15.2f} {:<15} {:<20}".format(
                            cavo['id_cavo'],
                            cavo['tipologia'],
                            str(cavo['n_conduttori']),
                            str(cavo['sezione']),
                            cavo['metri_teorici'],
                            cavo['metratura_reale'] or 0,
                            cavo['stato_installazione'],
                            data_posa
                        ))

                    # Statistiche cavi
                    totale_metri_teorici = sum(cavo['metri_teorici'] for cavo in cavi_bobina)
                    totale_metri_reali = sum(cavo['metratura_reale'] or 0 for cavo in cavi_bobina)

                    print("\n[STATISTICHE CAVI]")
                    print(f"Totale Cavi: {len(cavi_bobina)}")
                    print(f"Totale Metri Teorici: {totale_metri_teorici:.2f}")
                    print(f"Totale Metri Reali: {totale_metri_reali:.2f}")
                else:
                    print("\n[CAVI ASSOCIATI]")
                    print("Nessun cavo associato a questa bobina.")

                return True

            # Genera il report nel formato richiesto
            elif formato.lower() == 'excel':
                # Crea DataFrames
                df_bobina = pd.DataFrame([bobina])
                df_cavi_bobina = pd.DataFrame(cavi_bobina)

                # Percorso del file Excel
                report_path = os.path.join(EXCEL_REPORTS_DIR, f"bobina_{id_bobina}_{nome_cantiere}_{timestamp}.xlsx")

                # Salva il report Excel
                with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
                    # Foglio con i dati della bobina
                    df_bobina.to_excel(writer, sheet_name='Dettagli Bobina', index=False)

                    # Foglio con i cavi associati alla bobina
                    if not df_cavi_bobina.empty:
                        df_cavi_bobina.to_excel(writer, sheet_name='Cavi Associati', index=False)
                    else:
                        pd.DataFrame([{'Messaggio': 'Nessun cavo associato a questa bobina'}]).to_excel(writer, sheet_name='Cavi Associati', index=False)

                logging.info(f"Report Excel bobina {id_bobina} generato: {report_path}")
                return report_path

            else:  # formato PDF
                # Estrai solo il numero della bobina (es. da "C1_B1" a "1")
                numero_bobina = id_bobina.split('_B')[1] if '_B' in id_bobina else id_bobina

                # Prepara i dati per il report PDF
                titolo = f"Report Dettagliato Bobina {numero_bobina}"
                sottotitolo = f"Cantiere: {nome_cantiere} (ID: {id_cantiere})"
                nome_file = f"bobina_{numero_bobina}_{nome_cantiere}_{timestamp}.pdf"

                # Prepara i dati per la tabella dei dettagli bobina
                colonne_bobina = ["Attributo", "Valore"]
                dati_bobina = [
                    ["Bobina", numero_bobina],
                    ["Tipologia", bobina['tipologia']],
                    ["Conduttori", str(bobina['n_conduttori'])],
                    ["Sezione", str(bobina['sezione'])],
                    ["Utility", bobina['utility']],
                    ["Stato", bobina['stato_bobina']],
                    ["Metri Totali", f"{bobina['metri_totali']:.2f}"],
                    ["Metri Utilizzati", f"{bobina['metri_utilizzati']:.2f}"],
                    ["Metri Residui", f"{bobina['metri_disponibili']:.2f}"],
                    ["Percentuale Utilizzo", f"{bobina['percentuale_utilizzo']:.2f}%"]
                ]

                # Prepara i dati per la tabella dei cavi associati
                colonne_cavi = ["ID Cavo", "Tipologia", "Conduttori", "Sezione", "Metri Teorici", "Metri Reali", "Stato", "Data Posa"]
                dati_cavi = []

                if cavi_bobina:
                    for cavo in cavi_bobina:
                        data_posa = str(cavo['data_posa']) if cavo['data_posa'] else "-"
                        dati_cavi.append([
                            cavo['id_cavo'],
                            cavo['tipologia'],
                            str(cavo['n_conduttori']),
                            str(cavo['sezione']),
                            f"{cavo['metri_teorici']:.2f}",
                            f"{cavo['metratura_reale'] or 0:.2f}",
                            cavo['stato_installazione'],
                            data_posa
                        ])
                else:
                    dati_cavi = [["Nessun cavo associato", "", "", "", "", "", "", ""]]

                # Tabelle aggiuntive
                tabelle_aggiuntive = [
                    {
                        'titolo': 'Cavi Associati',
                        'colonne': colonne_cavi,
                        'dati': dati_cavi
                    }
                ]

                # Genera il PDF
                if not REPORTLAB_AVAILABLE:
                    print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
                    print("Installa ReportLab con: pip install reportlab")
                    print("\nUtilizza il formato 'video' per visualizzare il report a schermo.")
                    return None
                else:
                    pdf_path = crea_pdf_report(
                        titolo,
                        sottotitolo,
                        dati_bobina,
                        colonne_bobina,
                        nome_file,
                        tabelle_aggiuntive
                    )

                logging.info(f"Report PDF bobina {id_bobina} generato: {pdf_path}")
                return pdf_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del report bobina {id_bobina}: {str(e)}")
        return None


def genera_report_posa_per_periodo(id_cantiere, formato='pdf'):
    """
    Genera un report dettagliato sulla posa per periodo (giornaliero, settimanale, mensile).

    Il report include:
    - Posa giornaliera: elenco di tutti i giorni di posa con i relativi metri posati
    - Posa settimanale: metri posati per ogni settimana
    - Posa mensile: metri posati per ogni mese
    - Medie di posa per i vari periodi
    - Previsione di completamento basata sui metri residui e sulla media di posa

    Args:
        id_cantiere (int o str): ID del cantiere
        formato (str): Formato del report ('pdf', 'excel' o 'video')

    Returns:
        str: Percorso del file di report generato, o None in caso di errore
              Se formato='video', visualizza il report a schermo e restituisce True
    """
    # Assicurati che id_cantiere sia un intero
    try:
        id_cantiere = int(id_cantiere) if isinstance(id_cantiere, str) else id_cantiere
    except (ValueError, TypeError):
        logging.error(f"ID cantiere non valido: {id_cantiere}")
        return None

    # Data corrente per i calcoli
    oggi = datetime.now().date()

    try:
        with database_connection() as conn:
            # Ottieni il nome del cantiere
            c = conn.cursor()
            c.execute("SELECT commessa FROM Cantieri WHERE id_cantiere = ?", (id_cantiere,))
            result = c.fetchone()
            if not result:
                logging.error(f"Cantiere con ID {id_cantiere} non trovato")
                return None
            nome_cantiere = result['commessa']

            # Ottieni i dati di posa giornaliera per tutti i periodi
            c.execute("""
                SELECT data_posa, SUM(metratura_reale) as metri_posati
                FROM Cavi
                WHERE id_cantiere = ? AND data_posa IS NOT NULL AND stato_installazione = ?
                GROUP BY data_posa
                ORDER BY data_posa
            """, (id_cantiere, STATO_INSTALLATO))

            posa_giornaliera = [dict(row) for row in c.fetchall()]

            # Calcola la posa settimanale
            # Nota: SQLite non ha una funzione diretta per ottenere il numero della settimana
            # Quindi lo calcoliamo in Python
            posa_settimanale = {}
            for record in posa_giornaliera:
                data = datetime.strptime(record['data_posa'], '%Y-%m-%d')
                settimana = data.isocalendar()[1]  # Ottiene il numero della settimana (1-53)
                if settimana not in posa_settimanale:
                    posa_settimanale[settimana] = 0
                posa_settimanale[settimana] += record['metri_posati']

            # Converti il dizionario in una lista di dizionari per facilitare l'uso
            posa_settimanale_list = [
                {'settimana': settimana, 'metri_posati': metri}
                for settimana, metri in sorted(posa_settimanale.items())
            ]

            # Calcola la posa mensile
            c.execute("""
                SELECT
                    strftime('%Y', data_posa) as anno,
                    strftime('%m', data_posa) as mese,
                    SUM(metratura_reale) as metri_posati
                FROM Cavi
                WHERE id_cantiere = ? AND data_posa IS NOT NULL AND stato_installazione = ?
                GROUP BY strftime('%Y', data_posa), strftime('%m', data_posa)
                ORDER BY anno, mese
            """, (id_cantiere, STATO_INSTALLATO))

            posa_mensile_raw = [dict(row) for row in c.fetchall()]

            # Converti i numeri dei mesi in nomi
            nomi_mesi = ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno',
                         'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre']

            posa_mensile = []
            for record in posa_mensile_raw:
                mese_num = int(record['mese'])
                posa_mensile.append({
                    'mese': f"{nomi_mesi[mese_num - 1]} {record['anno']}",
                    'mese_num': mese_num,
                    'anno': record['anno'],
                    'metri_posati': record['metri_posati']
                })

            # Calcola i metri totali, posati e residui
            c.execute("""
                SELECT
                    SUM(CASE WHEN stato_installazione != ? THEN metri_teorici ELSE 0 END) as metri_da_posare,
                    SUM(CASE WHEN stato_installazione = ? THEN metratura_reale ELSE 0 END) as metri_posati,
                    SUM(CASE WHEN stato_installazione != ? THEN metri_teorici ELSE metratura_reale END) as metri_totali,
                    COUNT(*) as totale_cavi,
                    SUM(CASE WHEN stato_installazione = ? THEN 1 ELSE 0 END) as cavi_posati
                FROM Cavi
                WHERE id_cantiere = ? AND modificato_manualmente != 3
            """, (STATO_INSTALLATO, STATO_INSTALLATO, STATO_INSTALLATO, STATO_INSTALLATO, id_cantiere))

            stats = dict(c.fetchone())
            metri_totali = stats['metri_totali'] or 0
            metri_posati = stats['metri_posati'] or 0
            metri_da_posare = stats['metri_da_posare'] or 0
            totale_cavi = stats['totale_cavi'] or 0
            cavi_posati = stats['cavi_posati'] or 0

            # Ottieni statistiche sui test
            c.execute("""
                SELECT
                    COUNT(*) as totale_certificati,
                    SUM(CASE WHEN risultato_complessivo = 'Passato' THEN 1 ELSE 0 END) as test_passati
                FROM CertificatiTest
                WHERE id_cantiere = ?
            """, (id_cantiere,))

            stats_test = dict(c.fetchone() or {'totale_certificati': 0, 'test_passati': 0})
            totale_certificati = stats_test['totale_certificati'] or 0
            test_passati = stats_test['test_passati'] or 0

            # Ottieni statistiche sui collegamenti dalla tabella Cavi
            c.execute("""
                SELECT
                    SUM(CASE WHEN collegamenti & 1 > 0 THEN 1 ELSE 0 END) as collegamenti_from,
                    SUM(CASE WHEN collegamenti & 2 > 0 THEN 1 ELSE 0 END) as collegamenti_to
                FROM Cavi
                WHERE id_cantiere = ?
            """, (id_cantiere,))

            stats_collegamenti = dict(c.fetchone() or {'collegamenti_from': 0, 'collegamenti_to': 0})
            collegamenti_from = stats_collegamenti['collegamenti_from'] or 0
            collegamenti_to = stats_collegamenti['collegamenti_to'] or 0

            # Calcola le medie di posa
            # Media giornaliera (ultimi 30 giorni)
            giorni_posa = []
            for record in posa_giornaliera:
                data = datetime.strptime(record['data_posa'], '%Y-%m-%d').date()
                giorni_posa.append({
                    'data': data,
                    'metri_posati': record['metri_posati']
                })

            # Filtra per gli ultimi 30 giorni
            trenta_giorni_fa = oggi - timedelta(days=30)
            posa_ultimi_30_giorni = [g for g in giorni_posa if g['data'] >= trenta_giorni_fa]

            # Calcola la media giornaliera (considerando solo i giorni di posa effettiva)
            if posa_ultimi_30_giorni:
                media_giornaliera = sum(g['metri_posati'] for g in posa_ultimi_30_giorni) / len(posa_ultimi_30_giorni)
            else:
                media_giornaliera = 0

            # Calcola la media settimanale (ultimi 60 giorni)
            sessanta_giorni_fa = oggi - timedelta(days=60)
            posa_ultimi_60_giorni = [g for g in giorni_posa if g['data'] >= sessanta_giorni_fa]

            # Raggruppa per settimana
            posa_per_settimana = {}
            for record in posa_ultimi_60_giorni:
                anno_settimana = f"{record['data'].year}-{record['data'].isocalendar()[1]}"
                if anno_settimana not in posa_per_settimana:
                    posa_per_settimana[anno_settimana] = 0
                posa_per_settimana[anno_settimana] += record['metri_posati']

            # Calcola la media settimanale
            if posa_per_settimana:
                media_settimanale = sum(posa_per_settimana.values()) / len(posa_per_settimana)
            else:
                media_settimanale = 0

            # Calcola la media mensile (ultimi 6 mesi)
            sei_mesi_fa = oggi - relativedelta(months=6)

            # Filtra per gli ultimi 6 mesi
            posa_ultimi_6_mesi = []
            for record in posa_mensile:
                anno_mese = datetime.strptime(f"{record['anno']}-{record['mese_num']:02d}-01", '%Y-%m-%d').date()
                if anno_mese >= sei_mesi_fa:
                    posa_ultimi_6_mesi.append(record)

            # Calcola la media mensile
            if posa_ultimi_6_mesi:
                media_mensile = sum(m['metri_posati'] for m in posa_ultimi_6_mesi) / len(posa_ultimi_6_mesi)
            else:
                media_mensile = 0

            # Calcola la previsione di completamento
            # Usa la media giornaliera se disponibile, altrimenti usa la media settimanale / 5 (giorni lavorativi)
            if media_giornaliera > 0:
                media_per_previsione = media_giornaliera
                giorni_stimati = math.ceil(metri_da_posare / media_per_previsione) if media_per_previsione > 0 else float('inf')
                data_completamento = oggi + timedelta(days=giorni_stimati) if giorni_stimati != float('inf') else None
            elif media_settimanale > 0:
                media_per_previsione = media_settimanale / 5  # Assume 5 giorni lavorativi a settimana
                giorni_stimati = math.ceil(metri_da_posare / media_per_previsione) if media_per_previsione > 0 else float('inf')
                data_completamento = oggi + timedelta(days=giorni_stimati) if giorni_stimati != float('inf') else None
            else:
                media_per_previsione = 0
                giorni_stimati = float('inf')
                data_completamento = None

            # Crea nome file univoco
            timestamp = datetime.now().strftime('%Y%m%d_%H%M')

            # Visualizza il report a schermo se richiesto
            if formato.lower() == 'video':
                print("\n" + "=" * 80)
                print(f"REPORT POSA PER PERIODO - {nome_cantiere} (ID: {id_cantiere})")
                print("=" * 80)

                # Sezione 0: Riepilogo e Previsione
                print("\n[RIEPILOGO E PREVISIONE]")
                print(f"Metri Totali Progetto: {metri_totali:.2f}")
                print(f"Metri Posati: {metri_posati:.2f}")
                print(f"Metri Rimanenti: {metri_da_posare:.2f}")
                percentuale_avanzamento = (metri_posati / metri_totali * 100) if metri_totali > 0 else 0
                print(f"Percentuale Avanzamento: {percentuale_avanzamento:.2f}%")
                print(f"Cavi Totali: {totale_cavi}")
                print(f"Cavi Posati: {cavi_posati}")
                percentuale_cavi = (cavi_posati / totale_cavi * 100) if totale_cavi > 0 else 0
                print(f"Percentuale Cavi Posati: {percentuale_cavi:.2f}%")

                # Sezione Test e Collegamenti
                print("\n[TEST E COLLEGAMENTI]")
                print(f"Certificati Totali: {totale_certificati}")
                print(f"Test Passati: {test_passati}")
                percentuale_test = (test_passati / totale_certificati * 100) if totale_certificati > 0 else 0
                print(f"Percentuale Test Passati: {percentuale_test:.2f}%")
                print(f"Collegamenti Lato Partenza: {collegamenti_from}")
                print(f"Collegamenti Lato Arrivo: {collegamenti_to}")
                percentuale_collegamenti_from = (collegamenti_from / cavi_posati * 100) if cavi_posati > 0 else 0
                percentuale_collegamenti_to = (collegamenti_to / cavi_posati * 100) if cavi_posati > 0 else 0
                print(f"Percentuale Collegamenti Lato Partenza: {percentuale_collegamenti_from:.2f}%")
                print(f"Percentuale Collegamenti Lato Arrivo: {percentuale_collegamenti_to:.2f}%")
                print("\n[MEDIE DI POSA]")
                print(f"Media Giornaliera (ultimi 30 giorni): {media_giornaliera:.2f} m/giorno")
                print(f"Media Settimanale (ultimi 60 giorni): {media_settimanale:.2f} m/settimana")
                print(f"Media Mensile (ultimi 6 mesi): {media_mensile:.2f} m/mese")
                print("\n[PREVISIONE COMPLETAMENTO]")
                if giorni_stimati != float('inf'):
                    print(f"Giorni Stimati per Completamento: {giorni_stimati} giorni")
                    print(f"Data Stimata Completamento: {data_completamento.strftime('%d/%m/%Y')}")
                else:
                    print("Giorni Stimati per Completamento: N/A")
                    print("Data Stimata Completamento: N/A")

                # Sezione 1: Posa Mensile
                print("\n[POSA MENSILE]")
                print("{:<15} {:<15}".format("Mese", "Metri Posati"))
                print("-" * 30)

                totale_metri_anno = 0
                for record in posa_mensile:
                    print("{:<15} {:<15.2f}".format(
                        record['mese'],
                        record['metri_posati']
                    ))
                    totale_metri_anno += record['metri_posati']

                print("-" * 30)
                print("{:<15} {:<15.2f}".format("TOTALE ANNO", totale_metri_anno))

                # Sezione 2: Posa Settimanale
                print("\n[POSA SETTIMANALE]")
                print("{:<15} {:<15}".format("Settimana", "Metri Posati"))
                print("-" * 30)

                for record in posa_settimanale_list:
                    print("{:<15} {:<15.2f}".format(
                        f"Settimana {record['settimana']}",
                        record['metri_posati']
                    ))

                # Sezione 3: Posa Giornaliera
                print("\n[POSA GIORNALIERA]")
                print("{:<15} {:<15}".format("Data", "Metri Posati"))
                print("-" * 30)

                for record in posa_giornaliera:
                    data_formattata = datetime.strptime(record['data_posa'], '%Y-%m-%d').strftime('%d/%m/%Y')
                    print("{:<15} {:<15.2f}".format(
                        data_formattata,
                        record['metri_posati']
                    ))

                return True

            # Genera il report nel formato richiesto
            elif formato.lower() == 'excel':
                # Calcola le percentuali necessarie per il report Excel
                percentuale_avanzamento = (metri_posati / metri_totali * 100) if metri_totali > 0 else 0
                percentuale_cavi = (cavi_posati / totale_cavi * 100) if totale_cavi > 0 else 0
                percentuale_test = (test_passati / totale_certificati * 100) if totale_certificati > 0 else 0
                percentuale_collegamenti_from = (collegamenti_from / cavi_posati * 100) if cavi_posati > 0 else 0
                percentuale_collegamenti_to = (collegamenti_to / cavi_posati * 100) if cavi_posati > 0 else 0

                # Percorso del file Excel
                report_path = os.path.join(EXCEL_REPORTS_DIR, f"posa_per_periodo_{nome_cantiere}_{timestamp}.xlsx")

                # Crea DataFrames per il report
                df_mensile = pd.DataFrame(posa_mensile)
                df_settimanale = pd.DataFrame(posa_settimanale_list)
                df_giornaliera = pd.DataFrame(posa_giornaliera)

                # Formatta le date nel DataFrame giornaliero
                if not df_giornaliera.empty and 'data_posa' in df_giornaliera.columns:
                    df_giornaliera['data_posa'] = pd.to_datetime(df_giornaliera['data_posa']).dt.strftime('%d/%m/%Y')

                # Crea DataFrame per il riepilogo e previsione
                dati_riepilogo = [
                    {'Metrica': 'Metri Totali Progetto', 'Valore': f"{metri_totali:.2f}" if metri_totali is not None else "0.00"},
                    {'Metrica': 'Metri Posati', 'Valore': f"{metri_posati:.2f}" if metri_posati is not None else "0.00"},
                    {'Metrica': 'Metri Rimanenti', 'Valore': f"{metri_da_posare:.2f}" if metri_da_posare is not None else "0.00"},
                    {'Metrica': 'Percentuale Avanzamento', 'Valore': f"{percentuale_avanzamento:.2f}%" if percentuale_avanzamento is not None else "0.00%"},
                    {'Metrica': 'Cavi Totali', 'Valore': f"{totale_cavi}" if totale_cavi is not None else "0"},
                    {'Metrica': 'Cavi Posati', 'Valore': f"{cavi_posati}" if cavi_posati is not None else "0"},
                    {'Metrica': 'Percentuale Cavi Posati', 'Valore': f"{percentuale_cavi:.2f}%" if percentuale_cavi is not None else "0.00%"},
                    {'Metrica': 'Media Giornaliera (ultimi 30 giorni)', 'Valore': f"{media_giornaliera:.2f} m/giorno" if media_giornaliera is not None else "0.00 m/giorno"},
                    {'Metrica': 'Media Settimanale (ultimi 60 giorni)', 'Valore': f"{media_settimanale:.2f} m/settimana" if media_settimanale is not None else "0.00 m/settimana"},
                    {'Metrica': 'Media Mensile (ultimi 6 mesi)', 'Valore': f"{media_mensile:.2f} m/mese" if media_mensile is not None else "0.00 m/mese"},
                    {'Metrica': 'Giorni Stimati per Completamento', 'Valore': f"{giorni_stimati} giorni" if giorni_stimati is not None and giorni_stimati != float('inf') else "N/A"},
                    {'Metrica': 'Data Stimata Completamento', 'Valore': data_completamento.strftime('%d/%m/%Y') if data_completamento is not None else "N/A"}
                ]
                df_riepilogo = pd.DataFrame(dati_riepilogo)

                # Crea DataFrame per i test e collegamenti
                dati_test = [
                    {'Metrica': 'Certificati Totali', 'Valore': f"{totale_certificati}" if totale_certificati is not None else "0"},
                    {'Metrica': 'Test Passati', 'Valore': f"{test_passati}" if test_passati is not None else "0"},
                    {'Metrica': 'Percentuale Test Passati', 'Valore': f"{percentuale_test:.2f}%" if percentuale_test is not None else "0.00%"},
                    {'Metrica': 'Collegamenti Lato Partenza', 'Valore': f"{collegamenti_from}" if collegamenti_from is not None else "0"},
                    {'Metrica': 'Collegamenti Lato Arrivo', 'Valore': f"{collegamenti_to}" if collegamenti_to is not None else "0"},
                    {'Metrica': 'Percentuale Collegamenti Lato Partenza', 'Valore': f"{percentuale_collegamenti_from:.2f}%" if percentuale_collegamenti_from is not None else "0.00%"},
                    {'Metrica': 'Percentuale Collegamenti Lato Arrivo', 'Valore': f"{percentuale_collegamenti_to:.2f}%" if percentuale_collegamenti_to is not None else "0.00%"}
                ]
                df_test = pd.DataFrame(dati_test)

                # Salva il report Excel
                with pd.ExcelWriter(report_path, engine='openpyxl') as writer:
                    # Foglio con il riepilogo e previsione
                    df_riepilogo.to_excel(writer, sheet_name='Riepilogo e Previsione', index=False)

                    # Foglio con i dati di test e collegamenti
                    df_test.to_excel(writer, sheet_name='Test e Collegamenti', index=False)

                    # Foglio con i dati mensili
                    if not df_mensile.empty:
                        df_mensile.to_excel(writer, sheet_name='Posa Mensile', index=False)
                    else:
                        pd.DataFrame({'Mese': [], 'Metri Posati': []}).to_excel(writer, sheet_name='Posa Mensile', index=False)

                    # Foglio con i dati settimanali
                    if not df_settimanale.empty:
                        df_settimanale.to_excel(writer, sheet_name='Posa Settimanale', index=False)
                    else:
                        pd.DataFrame({'Settimana': [], 'Metri Posati': []}).to_excel(writer, sheet_name='Posa Settimanale', index=False)

                    # Foglio con i dati giornalieri
                    if not df_giornaliera.empty:
                        df_giornaliera.to_excel(writer, sheet_name='Posa Giornaliera', index=False)
                    else:
                        pd.DataFrame({'Data': [], 'Metri Posati': []}).to_excel(writer, sheet_name='Posa Giornaliera', index=False)

                logging.info(f"Report Excel posa per periodo generato: {report_path}")

            else:  # formato PDF
                # Calcola le percentuali necessarie per il report PDF
                percentuale_avanzamento = (metri_posati / metri_totali * 100) if metri_totali > 0 else 0
                percentuale_cavi = (cavi_posati / totale_cavi * 100) if totale_cavi > 0 else 0
                percentuale_test = (test_passati / totale_certificati * 100) if totale_certificati > 0 else 0
                percentuale_collegamenti_from = (collegamenti_from / cavi_posati * 100) if cavi_posati > 0 else 0
                percentuale_collegamenti_to = (collegamenti_to / cavi_posati * 100) if cavi_posati > 0 else 0

                # Prepara i dati per il report PDF
                titolo = f"Report Posa Per Periodo"
                sottotitolo = f"Cantiere: {nome_cantiere} (ID: {id_cantiere})"
                nome_file = f"posa_per_periodo_{nome_cantiere}_{timestamp}.pdf"

                # Tabella 1: Posa Mensile
                colonne_mensile = ['Mese', 'Metri Posati']
                dati_mensile = []

                totale_metri_anno = 0
                for record in posa_mensile:
                    dati_mensile.append([
                        record['mese'],
                        f"{record['metri_posati']:.2f}"
                    ])
                    totale_metri_anno += record['metri_posati']

                # Aggiungi riga del totale
                dati_mensile.append(['TOTALE ANNO', f"{totale_metri_anno:.2f}"])

                # Tabella 2: Posa Settimanale
                colonne_settimanale = ['Settimana', 'Metri Posati']
                dati_settimanale = []

                for record in posa_settimanale_list:
                    dati_settimanale.append([
                        f"Settimana {record['settimana']}",
                        f"{record['metri_posati']:.2f}"
                    ])

                # Tabella 3: Posa Giornaliera
                colonne_giornaliera = ['Data', 'Metri Posati']
                dati_giornaliera = []

                for record in posa_giornaliera:
                    data_formattata = datetime.strptime(record['data_posa'], '%Y-%m-%d').strftime('%d/%m/%Y')
                    dati_giornaliera.append([
                        data_formattata,
                        f"{record['metri_posati']:.2f}"
                    ])

                # Tabella 0: Riepilogo e Previsione
                colonne_riepilogo = ['Metrica', 'Valore']
                dati_riepilogo = [
                    ['Metri Totali Progetto', f"{metri_totali:.2f}" if metri_totali is not None else "0.00"],
                    ['Metri Posati', f"{metri_posati:.2f}" if metri_posati is not None else "0.00"],
                    ['Metri Rimanenti', f"{metri_da_posare:.2f}" if metri_da_posare is not None else "0.00"],
                    ['Percentuale Avanzamento', f"{percentuale_avanzamento:.2f}%" if percentuale_avanzamento is not None else "0.00%"],
                    ['Cavi Totali', f"{totale_cavi}" if totale_cavi is not None else "0"],
                    ['Cavi Posati', f"{cavi_posati}" if cavi_posati is not None else "0"],
                    ['Percentuale Cavi Posati', f"{percentuale_cavi:.2f}%" if percentuale_cavi is not None else "0.00%"],
                    ['Media Giornaliera (ultimi 30 giorni)', f"{media_giornaliera:.2f} m/giorno" if media_giornaliera is not None else "0.00 m/giorno"],
                    ['Media Settimanale (ultimi 60 giorni)', f"{media_settimanale:.2f} m/settimana" if media_settimanale is not None else "0.00 m/settimana"],
                    ['Media Mensile (ultimi 6 mesi)', f"{media_mensile:.2f} m/mese" if media_mensile is not None else "0.00 m/mese"],
                    ['Giorni Stimati per Completamento', f"{giorni_stimati} giorni" if giorni_stimati is not None and giorni_stimati != float('inf') else "N/A"],
                    ['Data Stimata Completamento', data_completamento.strftime('%d/%m/%Y') if data_completamento is not None else "N/A"]
                ]

                # Tabella Test e Collegamenti
                colonne_test = ['Metrica', 'Valore']
                dati_test = [
                    ['Certificati Totali', f"{totale_certificati}" if totale_certificati is not None else "0"],
                    ['Test Passati', f"{test_passati}" if test_passati is not None else "0"],
                    ['Percentuale Test Passati', f"{percentuale_test:.2f}%" if percentuale_test is not None else "0.00%"],
                    ['Collegamenti Lato Partenza', f"{collegamenti_from}" if collegamenti_from is not None else "0"],
                    ['Collegamenti Lato Arrivo', f"{collegamenti_to}" if collegamenti_to is not None else "0"],
                    ['Percentuale Collegamenti Lato Partenza', f"{percentuale_collegamenti_from:.2f}%" if percentuale_collegamenti_from is not None else "0.00%"],
                    ['Percentuale Collegamenti Lato Arrivo', f"{percentuale_collegamenti_to:.2f}%" if percentuale_collegamenti_to is not None else "0.00%"]
                ]

                # Prepara le tabelle aggiuntive
                tabelle_aggiuntive = [
                    {
                        'titolo': 'Riepilogo e Previsione',
                        'colonne': colonne_riepilogo,
                        'dati': dati_riepilogo
                    },
                    {
                        'titolo': 'Test e Collegamenti',
                        'colonne': colonne_test,
                        'dati': dati_test
                    },
                    {
                        'titolo': 'Posa Settimanale',
                        'colonne': colonne_settimanale,
                        'dati': dati_settimanale
                    },
                    {
                        'titolo': 'Posa Giornaliera',
                        'colonne': colonne_giornaliera,
                        'dati': dati_giornaliera
                    }
                ]

                # Genera il PDF con tutte le tabelle
                if not REPORTLAB_AVAILABLE:
                    print("\n❌ Impossibile generare il PDF: ReportLab non è installato.")
                    print("Installa ReportLab con: pip install reportlab")
                    print("\nUtilizza il formato 'video' per visualizzare il report a schermo.")
                    return None
                else:
                    pdf_path = crea_pdf_report(
                        titolo,
                        sottotitolo,
                        dati_mensile,
                        colonne_mensile,
                        nome_file,
                        tabelle_aggiuntive
                    )

                logging.info(f"Report PDF posa per periodo generato: {pdf_path}")
                report_path = pdf_path

            return report_path

    except Exception as e:
        logging.error(f"Errore durante la generazione del report posa per periodo: {str(e)}")
        return None
