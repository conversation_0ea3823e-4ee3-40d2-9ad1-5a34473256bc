(()=>{var e={};e.id=722,e.ids=[722],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},26491:(e,t,r)=>{Promise.resolve().then(r.bind(r,40640))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40640:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});let o=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\debug-login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\debug-login\\page.tsx","default")},46762:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var o=r(60687),s=r(43210),n=r(16189);function i(){let[e,t]=(0,s.useState)(""),[r,i]=(0,s.useState)(!1),a=(0,n.useRouter)(),d=async()=>{i(!0),t("Starting login test...\n");try{t(e=>e+"Step 1: Testing basic fetch...\n");let e=new FormData;e.append("username","admin"),e.append("password","admin"),t(e=>e+"Step 2: Sending login request...\n");let r=await fetch("http://localhost:8001/api/auth/login",{method:"POST",body:e});if(t(e=>e+`Step 3: Response status: ${r.status}
`),!r.ok)throw Error(`HTTP error! status: ${r.status}`);let o=await r.json();t(e=>e+`Step 4: Response data received
`),t(e=>e+`Data: ${JSON.stringify(o,null,2)}
`),t(e=>e+"Step 5: Saving token to localStorage...\n"),localStorage.setItem("token",o.access_token);let s=localStorage.getItem("token");t(e=>e+`Step 6: Token saved: ${s?"YES":"NO"}
`),t(e=>e+"Step 7: Testing router...\n"),"owner"===o.role&&(t(e=>e+"Step 8: Redirecting to /admin...\n"),setTimeout(()=>{t(e=>e+"Step 9: Using router.push...\n"),a.push("/admin")},1e3),setTimeout(()=>{t(e=>e+"Step 10: Using window.location...\n"),window.location.href="/admin"},3e3))}catch(e){t(t=>t+`ERROR: ${e.message}
`),console.error("Login test error:",e)}finally{i(!1)}};return(0,o.jsxs)("div",{style:{padding:"20px",fontFamily:"monospace"},children:[(0,o.jsx)("h1",{children:"Debug Login Page"}),(0,o.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,o.jsx)("button",{onClick:d,disabled:r,style:{padding:"10px 20px",marginRight:"10px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"5px",cursor:r?"not-allowed":"pointer"},children:r?"Testing...":"Full Login Test"}),(0,o.jsx)("button",{onClick:()=>{t("Testing router directly...\n");try{a.push("/admin"),t(e=>e+"Router.push called successfully\n")}catch(e){t(t=>t+`Router error: ${e.message}
`)}},style:{padding:"10px 20px",marginRight:"10px",backgroundColor:"#28a745",color:"white",border:"none",borderRadius:"5px",cursor:"pointer"},children:"Test Router Only"}),(0,o.jsx)("button",{onClick:()=>{t("Testing window.location...\n"),window.location.href="/admin"},style:{padding:"10px 20px",marginRight:"10px",backgroundColor:"#ffc107",color:"black",border:"none",borderRadius:"5px",cursor:"pointer"},children:"Test Window.location"}),(0,o.jsx)("button",{onClick:()=>{localStorage.clear(),t("LocalStorage cleared\n")},style:{padding:"10px 20px",backgroundColor:"#dc3545",color:"white",border:"none",borderRadius:"5px",cursor:"pointer"},children:"Clear Storage"})]}),(0,o.jsx)("div",{style:{backgroundColor:"#f8f9fa",padding:"15px",borderRadius:"5px",whiteSpace:"pre-wrap",minHeight:"400px",fontSize:"12px",fontFamily:"monospace",border:"1px solid #ddd"},children:e||"Click a button to start testing..."}),(0,o.jsxs)("div",{style:{marginTop:"20px"},children:[(0,o.jsx)("h3",{children:"Current localStorage:"}),(0,o.jsx)("div",{style:{backgroundColor:"#f8f9fa",padding:"10px",borderRadius:"5px",fontSize:"12px",fontFamily:"monospace"},children:"Not available (SSR)"})]})]})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},79635:(e,t,r)=>{Promise.resolve().then(r.bind(r,46762))},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},87236:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>c,tree:()=>p});var o=r(65239),s=r(48088),n=r(88170),i=r.n(n),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let p={children:["",{children:["debug-login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,40640)),"C:\\CMS\\webapp-nextjs\\src\\app\\debug-login\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\CMS\\webapp-nextjs\\src\\app\\debug-login\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},c=new o.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/debug-login/page",pathname:"/debug-login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[447,991,658,223],()=>r(87236));module.exports=o})();