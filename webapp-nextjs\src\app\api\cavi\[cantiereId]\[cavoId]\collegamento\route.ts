import { NextRequest, NextResponse } from 'next/server'

interface CollegamentoRequest {
  lato: 'partenza' | 'arrivo'
  responsabile?: string
}

interface ScollegamentoRequest {
  lato?: 'partenza' | 'arrivo'
}

// POST - Collega un lato del cavo
export async function POST(
  request: NextRequest,
  { params }: { params: { cantiereId: string; cavoId: string } }
) {
  try {
    const { cantiereId, cavoId } = params
    const body: CollegamentoRequest = await request.json()

    // Validazione parametri
    if (!cantiereId || !cavoId) {
      return NextResponse.json(
        { error: 'Parametri cantiere ID e cavo ID richiesti' },
        { status: 400 }
      )
    }

    if (!body.lato || !['partenza', 'arrivo'].includes(body.lato)) {
      return NextResponse.json(
        { error: 'Lato richiesto: "partenza" o "arrivo"' },
        { status: 400 }
      )
    }

    // Ottieni il token di autenticazione dalla richiesta
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Token di autenticazione richiesto' },
        { status: 401 }
      )
    }

    // Chiama l'API backend Python
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'
    const response = await fetch(
      `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader,
        },
        body: JSON.stringify({
          lato: body.lato,
          responsabile: body.responsabile || 'cantiere'
        }),
      }
    )

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      return NextResponse.json(
        { 
          error: errorData.detail || `Errore backend: ${response.status}`,
          detail: errorData.detail 
        },
        { status: response.status }
      )
    }

    const data = await response.json()
    return NextResponse.json(data)

  } catch (error: any) {
    console.error('Errore nel collegamento cavo:', error)
    return NextResponse.json(
      { error: 'Errore interno del server', detail: error.message },
      { status: 500 }
    )
  }
}

// DELETE - Scollega un lato del cavo
export async function DELETE(
  request: NextRequest,
  { params }: { params: { cantiereId: string; cavoId: string } }
) {
  try {
    const { cantiereId, cavoId } = params
    
    // Leggi il body per ottenere il lato da scollegare
    let body: ScollegamentoRequest = {}
    try {
      body = await request.json()
    } catch {
      // Se non c'è body, scollega entrambi i lati
      body = {}
    }

    // Validazione parametri
    if (!cantiereId || !cavoId) {
      return NextResponse.json(
        { error: 'Parametri cantiere ID e cavo ID richiesti' },
        { status: 400 }
      )
    }

    // Ottieni il token di autenticazione dalla richiesta
    const authHeader = request.headers.get('authorization')
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Token di autenticazione richiesto' },
        { status: 401 }
      )
    }

    // Chiama l'API backend Python
    const backendUrl = process.env.BACKEND_URL || 'http://localhost:8000'

    // Il backend Python richiede il lato nel path URL
    // Se non è specificato un lato, scolleghiamo entrambi i lati
    if (body.lato) {
      // Scollega un lato specifico
      const url = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/${body.lato}`
      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader,
        },
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        return NextResponse.json(
          {
            error: errorData.detail || `Errore backend: ${response.status}`,
            detail: errorData.detail
          },
          { status: response.status }
        )
      }

      const data = await response.json()
      return NextResponse.json(data)
    } else {
      // Scollega entrambi i lati (prima partenza, poi arrivo)
      let finalData = null

      // Scollega partenza
      try {
        const urlPartenza = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/partenza`
        const responsePartenza = await fetch(urlPartenza, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': authHeader,
          },
        })
        if (responsePartenza.ok) {
          finalData = await responsePartenza.json()
        }
      } catch (error) {
        // Ignora errori se il lato partenza non è collegato
      }

      // Scollega arrivo
      try {
        const urlArrivo = `${backendUrl}/cavi/${cantiereId}/${cavoId}/collegamento/arrivo`
        const responseArrivo = await fetch(urlArrivo, {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': authHeader,
          },
        })
        if (responseArrivo.ok) {
          finalData = await responseArrivo.json()
        }
      } catch (error) {
        // Ignora errori se il lato arrivo non è collegato
      }

      if (finalData) {
        return NextResponse.json(finalData)
      } else {
        return NextResponse.json(
          { error: 'Nessun collegamento da scollegare' },
          { status: 400 }
        )
      }
    }

  } catch (error: any) {
    console.error('Errore nello scollegamento cavo:', error)
    return NextResponse.json(
      { error: 'Errore interno del server', detail: error.message },
      { status: 500 }
    )
  }
}
