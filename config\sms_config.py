"""
Configurazione per il sistema di notifiche SMS.
Supporta diversi provider SMS: Twilio, TextMagic, API personalizzate.
"""

import os

# Configurazione generale SMS
SMS_CONFIG = {
    'provider': os.getenv('SMS_PROVIDER', 'twilio'),  # 'twilio', 'textmagic', 'custom'
    'enabled': os.getenv('SMS_ENABLED', 'false').lower() == 'true',
}

# Configurazione Twilio
TWILIO_CONFIG = {
    'account_sid': os.getenv('TWILIO_ACCOUNT_SID', ''),
    'auth_token': os.getenv('TWILIO_AUTH_TOKEN', ''),
    'from_number': os.getenv('TWILIO_FROM_NUMBER', ''),  # Es: +*********0
}

# Configurazione TextMagic
TEXTMAGIC_CONFIG = {
    'username': os.getenv('TEXTMAGIC_USERNAME', ''),
    'api_key': os.getenv('TEXTMAGIC_API_KEY', ''),
}

# Configurazione API personalizzata
CUSTOM_SMS_CONFIG = {
    'api_url': os.getenv('CUSTOM_SMS_API_URL', ''),
    'api_key': os.getenv('CUSTOM_SMS_API_KEY', ''),
    'from_name': os.getenv('CUSTOM_SMS_FROM', 'CMS'),
}

# Template SMS personalizzabili
SMS_TEMPLATES = {
    'comanda_creata': 'CMS: Nuova comanda {tipo_comanda}\nCodice: {codice_comanda}\nPer: {responsabile_nome}\nUsa il codice nell\'app mobile.',
    'test_sms': 'Test SMS Sistema CMS\n\nQuesto è un messaggio di test.\n\nSe ricevi questo SMS, la configurazione è corretta.\n\nData: {data_invio}'
}

# Istruzioni per la configurazione
SETUP_INSTRUCTIONS = """
CONFIGURAZIONE SMS - ISTRUZIONI:

1. TWILIO (Raccomandato):
   - Registrati su https://www.twilio.com
   - Ottieni Account SID e Auth Token dalla console
   - Acquista un numero di telefono Twilio
   - Installa la libreria: pip install twilio
   
   Variabili d'ambiente:
   SMS_PROVIDER=twilio
   TWILIO_ACCOUNT_SID=your_account_sid
   TWILIO_AUTH_TOKEN=your_auth_token
   TWILIO_FROM_NUMBER=+*********0

2. TEXTMAGIC:
   - Registrati su https://www.textmagic.com
   - Ottieni Username e API Key dal dashboard
   
   Variabili d'ambiente:
   SMS_PROVIDER=textmagic
   TEXTMAGIC_USERNAME=your_username
   TEXTMAGIC_API_KEY=your_api_key

3. API PERSONALIZZATA:
   - Configura il tuo endpoint API per l'invio SMS
   - L'API deve accettare POST con JSON: {"to": "numero", "message": "testo"}
   
   Variabili d'ambiente:
   SMS_PROVIDER=custom
   CUSTOM_SMS_API_URL=https://your-api.com/send-sms
   CUSTOM_SMS_API_KEY=your_api_key

4. PROVIDER ITALIANI:
   
   SKEBBY:
   - Registrati su https://www.skebby.it
   - Usa configurazione custom con loro API
   
   ESENDEX:
   - Registrati su https://www.esendex.it
   - Usa configurazione custom con loro API

5. ABILITAZIONE:
   SMS_ENABLED=true

6. FILE .ENV ESEMPIO:
   # SMS Configuration
   SMS_ENABLED=true
   SMS_PROVIDER=twilio
   
   # Twilio
   TWILIO_ACCOUNT_SID=ACxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
   TWILIO_AUTH_TOKEN=your_auth_token
   TWILIO_FROM_NUMBER=+*********0
   
   # TextMagic (alternativo)
   # TEXTMAGIC_USERNAME=your_username
   # TEXTMAGIC_API_KEY=your_api_key
   
   # Custom API (alternativo)
   # CUSTOM_SMS_API_URL=https://your-api.com/send-sms
   # CUSTOM_SMS_API_KEY=your_api_key

7. TEST:
   Usa la funzione test_configurazione_sms() per verificare la configurazione
   Usa invia_sms_test(numero) per inviare un SMS di prova

8. COSTI:
   - Twilio: ~0.0075€ per SMS in Italia
   - TextMagic: ~0.04€ per SMS in Italia
   - Provider italiani: variabili, spesso più economici

9. NUMERI DI TELEFONO:
   - Il sistema normalizza automaticamente i numeri italiani
   - Formati accettati: +39*********, 39*********, *********
   - Per numeri internazionali usa sempre il prefisso completo: +33*********
"""

def get_sms_config():
    """Restituisce la configurazione SMS corrente."""
    return SMS_CONFIG.copy()

def get_twilio_config():
    """Restituisce la configurazione Twilio."""
    return TWILIO_CONFIG.copy()

def get_textmagic_config():
    """Restituisce la configurazione TextMagic."""
    return TEXTMAGIC_CONFIG.copy()

def get_custom_config():
    """Restituisce la configurazione API personalizzata."""
    return CUSTOM_SMS_CONFIG.copy()

def get_sms_template(template_name):
    """Restituisce un template SMS specifico."""
    return SMS_TEMPLATES.get(template_name, SMS_TEMPLATES['comanda_creata'])

def is_sms_enabled():
    """Verifica se SMS è abilitato."""
    return SMS_CONFIG['enabled']

def is_provider_configured(provider=None):
    """Verifica se un provider specifico è configurato."""
    if provider is None:
        provider = SMS_CONFIG['provider']
    
    if provider == 'twilio':
        return all([TWILIO_CONFIG['account_sid'], TWILIO_CONFIG['auth_token'], TWILIO_CONFIG['from_number']])
    elif provider == 'textmagic':
        return all([TEXTMAGIC_CONFIG['username'], TEXTMAGIC_CONFIG['api_key']])
    elif provider == 'custom':
        return all([CUSTOM_SMS_CONFIG['api_url'], CUSTOM_SMS_CONFIG['api_key']])
    
    return False

def is_configured():
    """Verifica se la configurazione SMS è completa."""
    return is_sms_enabled() and is_provider_configured()

def print_setup_instructions():
    """Stampa le istruzioni per la configurazione."""
    print(SETUP_INSTRUCTIONS)

def get_provider_status():
    """Restituisce lo stato di configurazione di tutti i provider."""
    return {
        'current_provider': SMS_CONFIG['provider'],
        'sms_enabled': is_sms_enabled(),
        'twilio_configured': is_provider_configured('twilio'),
        'textmagic_configured': is_provider_configured('textmagic'),
        'custom_configured': is_provider_configured('custom'),
        'current_provider_configured': is_provider_configured()
    }

# Funzioni di utilità per i costi
def get_estimated_costs():
    """Restituisce una stima dei costi per provider."""
    return {
        'twilio': {
            'cost_per_sms_eur': 0.0075,
            'description': 'Costo per SMS in Italia (circa)',
            'currency': 'EUR'
        },
        'textmagic': {
            'cost_per_sms_eur': 0.04,
            'description': 'Costo per SMS in Italia (circa)',
            'currency': 'EUR'
        },
        'custom': {
            'cost_per_sms_eur': 'Variabile',
            'description': 'Dipende dal provider scelto',
            'currency': 'EUR'
        }
    }

def calcola_costo_mensile_stimato(sms_al_giorno: int, provider: str = None):
    """
    Calcola il costo mensile stimato per l'invio SMS.
    
    Args:
        sms_al_giorno: Numero di SMS inviati al giorno
        provider: Provider da usare per il calcolo (default: quello configurato)
        
    Returns:
        Dict con la stima dei costi
    """
    if provider is None:
        provider = SMS_CONFIG['provider']
    
    costs = get_estimated_costs()
    
    if provider not in costs or costs[provider]['cost_per_sms_eur'] == 'Variabile':
        return {
            'provider': provider,
            'sms_al_giorno': sms_al_giorno,
            'costo_mensile': 'Non calcolabile',
            'note': 'Costo variabile, consulta il tuo provider'
        }
    
    costo_per_sms = costs[provider]['cost_per_sms_eur']
    sms_al_mese = sms_al_giorno * 30
    costo_mensile = sms_al_mese * costo_per_sms
    
    return {
        'provider': provider,
        'sms_al_giorno': sms_al_giorno,
        'sms_al_mese': sms_al_mese,
        'costo_per_sms': f"{costo_per_sms}€",
        'costo_mensile': f"{costo_mensile:.2f}€",
        'currency': 'EUR'
    }
