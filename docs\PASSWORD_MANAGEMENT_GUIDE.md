# 🔐 Guida Completa alla Gestione Password CMS

## 📋 Indice

1. [Panoramica del Sistema](#panoramica-del-sistema)
2. [Architettura di Sicurezza](#architettura-di-sicurezza)
3. [Flussi di Gestione Password](#flussi-di-gestione-password)
4. [Configurazione e Deployment](#configurazione-e-deployment)
5. [Guida Utente](#guida-utente)
6. [Amministrazione](#amministrazione)
7. [Sicurezza e Best Practices](#sicurezza-e-best-practices)
8. [Troubleshooting](#troubleshooting)

---

## 🎯 Panoramica del Sistema

Il sistema di gestione password del CMS implementa un approccio **penetration-free** con due flussi principali:

### ✅ Flusso 1: Cambio Password (Password Conosciuta)
- Utente conosce la password attuale
- Validazione della password corrente
- Impostazione di una nuova password sicura
- Notifica email automatica

### ✅ Flusso 2: Recupero Password (Password Dimenticata)
- Richiesta reset via email
- Generazione token sicuro temporaneo
- Reset password tramite link sicuro
- Invalidazione automatica del token

---

## 🏗️ Architettura di Sicurezza

### Componenti Principali

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI Agent      │    │   API Layer     │    │  Security Core  │
│   Interface     │───▶│   Validation    │───▶│   Token Mgmt    │
│   UX/UI         │    │   Rate Limiting │    │   Email Service │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │    Database     │
                       │   Audit Trail   │
                       │  Secure Storage │
                       └─────────────────┘
```

### Livelli di Sicurezza

1. **Client-Side Validation**: Validazione immediata dell'input
2. **API Security Layer**: Rate limiting, autenticazione, autorizzazione
3. **Core Security**: Hashing, token management, audit logging
4. **Database Security**: Encrypted storage, audit trail

---

## 🔄 Flussi di Gestione Password

### Flusso Cambio Password

```mermaid
sequenceDiagram
    participant U as Utente
    participant AI as AI Agent
    participant API as API Security
    participant DB as Database
    participant Email as Email Service

    U->>AI: Richiesta cambio password
    AI->>U: Richiedi password attuale
    U->>AI: Inserisce credenziali
    AI->>API: Valida e invia richiesta
    API->>DB: Verifica password attuale
    API->>DB: Aggiorna con nuova password
    API->>Email: Invia notifica
    API->>AI: Conferma successo
    AI->>U: Mostra conferma
```

### Flusso Recupero Password

```mermaid
sequenceDiagram
    participant U as Utente
    participant AI as AI Agent
    participant API as API Security
    participant DB as Database
    participant Email as Email Service

    U->>AI: Richiesta reset password
    AI->>U: Richiedi email
    U->>AI: Inserisce email
    AI->>API: Invia richiesta reset
    API->>DB: Genera e salva token
    API->>Email: Invia link reset
    U->>API: Clicca link reset
    API->>U: Mostra form nuova password
    U->>API: Inserisce nuova password
    API->>DB: Aggiorna password
    API->>Email: Invia conferma
```

---

## ⚙️ Configurazione e Deployment

### Variabili d'Ambiente

```bash
# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=cantieri
DB_USER=postgres
DB_PASSWORD=your_secure_password

# Email Configuration
EMAIL_PROVIDER=gmail  # gmail, outlook, yahoo, custom
GMAIL_USERNAME=<EMAIL>
GMAIL_APP_PASSWORD=your_app_password
EMAIL_FROM_NAME="CMS Sistema"

# Security
SECRET_KEY=your_very_secure_secret_key_here
CANTIERE_MASTER_PASSWORD=your_master_password_for_encryption

# Rate Limiting
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=15
TOKEN_EXPIRY_MINUTES=30
```

### Setup Database

```sql
-- Esegui la migrazione di sicurezza
\i webapp/backend/migrations/add_security_fields.sql

-- Verifica tabelle create
\dt security_events
\dt rate_limiting
\dt password_reset_tokens
```

### Setup Email Provider

#### Gmail (Raccomandato)
1. Abilita 2FA sul tuo account Gmail
2. Genera una App Password:
   - Vai su Account Google → Sicurezza → App Password
   - Seleziona "Mail" e genera password
3. Usa la App Password (non la password normale)

#### Outlook
```bash
OUTLOOK_USERNAME=<EMAIL>
OUTLOOK_PASSWORD=your_password
```

#### Provider Personalizzato
```bash
SMTP_HOST=your.smtp.server.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USERNAME=your_username
SMTP_PASSWORD=your_password
```

### Deployment Steps

1. **Backup Database**
   ```bash
   pg_dump cantieri > backup_$(date +%Y%m%d).sql
   ```

2. **Aggiorna Codice**
   ```bash
   git pull origin main
   cd webapp/backend
   pip install -r requirements.txt
   ```

3. **Esegui Migrazioni**
   ```bash
   psql -d cantieri -f migrations/add_security_fields.sql
   ```

4. **Test Configurazione Email**
   ```bash
   python -c "from core.email_config import email_config_manager; print(email_config_manager.test_connection())"
   ```

5. **Restart Services**
   ```bash
   systemctl restart cms-backend
   systemctl restart cms-frontend
   ```

---

## 👤 Guida Utente

### Cambio Password

1. **Accedi al Sistema**
   - Login con le tue credenziali attuali

2. **Vai alle Impostazioni**
   - Clicca sul tuo profilo → "Cambia Password"

3. **Compila il Form**
   - Password attuale
   - Nuova password (segui i requisiti di sicurezza)
   - Conferma nuova password

4. **Requisiti Password**
   - Minimo 8 caratteri
   - Almeno 1 maiuscola
   - Almeno 1 minuscola  
   - Almeno 1 numero
   - Almeno 1 carattere speciale
   - Non deve contenere pattern comuni

### Recupero Password

1. **Vai alla Pagina di Login**
   - Clicca "Password dimenticata?"

2. **Inserisci Email**
   - Seleziona tipo account (Utente/Cantiere)
   - Inserisci la tua email registrata

3. **Controlla Email**
   - Riceverai un link di reset (valido 30 minuti)
   - Controlla anche la cartella spam

4. **Reset Password**
   - Clicca il link nell'email
   - Inserisci la nuova password
   - Conferma la password

5. **Login**
   - Usa la nuova password per accedere

---

## 🛡️ Amministrazione

### Monitoraggio Sicurezza

#### Log degli Eventi
```sql
-- Visualizza eventi di sicurezza recenti
SELECT event_type, username, ip_address, success, created_at 
FROM security_events 
ORDER BY created_at DESC 
LIMIT 50;

-- Tentativi di login falliti
SELECT username, COUNT(*) as failed_attempts, MAX(created_at) as last_attempt
FROM security_events 
WHERE event_type = 'login_failed' 
  AND created_at > NOW() - INTERVAL '24 hours'
GROUP BY username
ORDER BY failed_attempts DESC;
```

#### Rate Limiting
```sql
-- Visualizza rate limiting attivo
SELECT identifier, identifier_type, attempt_count, blocked_until
FROM rate_limiting 
WHERE blocked_until > NOW()
ORDER BY blocked_until DESC;
```

#### Token di Reset
```sql
-- Token attivi
SELECT email, token_type, expires_at, created_at
FROM password_reset_tokens 
WHERE expires_at > NOW() AND used_at IS NULL
ORDER BY created_at DESC;
```

### Pulizia Automatica

#### Script di Manutenzione
```sql
-- Pulisci token scaduti
SELECT cleanup_expired_tokens();

-- Pulisci rate limiting vecchi
SELECT cleanup_old_rate_limits();

-- Pulisci eventi di sicurezza vecchi (>90 giorni)
SELECT cleanup_old_security_events();
```

#### Cron Job (Esegui giornalmente)
```bash
# Aggiungi a crontab
0 2 * * * psql -d cantieri -c "SELECT cleanup_expired_tokens(), cleanup_old_rate_limits(), cleanup_old_security_events();"
```

### Gestione Emergenze

#### Sblocco Account
```sql
-- Sblocca utente specifico
UPDATE Utenti 
SET failed_login_attempts = 0, locked_until = NULL 
WHERE username = 'username_bloccato';

-- Sblocca cantiere specifico
UPDATE Cantieri 
SET failed_login_attempts = 0, locked_until = NULL 
WHERE codice_univoco = 'codice_bloccato';
```

#### Reset Rate Limiting
```sql
-- Reset rate limiting per IP
DELETE FROM rate_limiting WHERE identifier = '*************';

-- Reset rate limiting globale
TRUNCATE TABLE rate_limiting;
```

---

## 🔒 Sicurezza e Best Practices

### Politiche Password

- **Lunghezza minima**: 8 caratteri
- **Complessità**: Maiuscole, minuscole, numeri, simboli
- **Blacklist**: Password comuni bloccate
- **Rotazione**: Cambio periodico raccomandato
- **Unicità**: Diversa da password precedenti

### Rate Limiting

- **Login**: 5 tentativi per 15 minuti
- **Reset Password**: 10 richieste per 5 minuti
- **API Calls**: 100 richieste per minuto

### Token Security

- **Algoritmo**: HMAC-SHA256
- **Scadenza**: 30 minuti
- **Uso singolo**: Token invalidato dopo uso
- **Entropia**: 256 bit di casualità

### Audit Trail

- **Tutti gli eventi** di sicurezza loggati
- **Retention**: 90 giorni
- **Campi**: Timestamp, IP, User-Agent, Risultato
- **Anonimizzazione**: Dati sensibili mascherati

---

## 🔧 Troubleshooting

### Problemi Comuni

#### Email Non Ricevute

**Sintomi**: L'utente non riceve email di reset
**Soluzioni**:
1. Controlla configurazione SMTP
2. Verifica cartella spam
3. Controlla log email service
4. Testa connessione SMTP

```bash
# Test configurazione email
python -c "
from backend.core.email_config import email_config_manager
success, message = email_config_manager.test_connection()
print(f'Test: {success} - {message}')
"
```

#### Account Bloccato

**Sintomi**: Utente non riesce ad accedere
**Soluzioni**:
1. Verifica tentativi falliti
2. Controlla timestamp blocco
3. Sblocca manualmente se necessario

```sql
-- Verifica stato account
SELECT username, failed_login_attempts, locked_until, last_login
FROM Utenti 
WHERE username = 'username_problema';
```

#### Token Non Valido

**Sintomi**: Link di reset non funziona
**Soluzioni**:
1. Verifica scadenza token
2. Controlla se già utilizzato
3. Genera nuovo token

```sql
-- Verifica token
SELECT token_hash, expires_at, used_at, created_at
FROM password_reset_tokens 
WHERE email = '<EMAIL>'
ORDER BY created_at DESC;
```

### Log di Debug

#### Abilitare Debug Logging
```python
# In settings.py o config
import logging
logging.getLogger('security_audit').setLevel(logging.DEBUG)
logging.getLogger('email_service').setLevel(logging.DEBUG)
```

#### Monitoraggio Real-time
```bash
# Segui log di sicurezza
tail -f logs/security.log

# Filtra eventi specifici
grep "password_reset" logs/security.log | tail -20
```

---

## 📞 Supporto

### Contatti
- **Email Tecnico**: <EMAIL>
- **Documentazione**: [Link alla documentazione completa]
- **Repository**: [Link al repository GitHub]

### Segnalazione Vulnerabilità
Se trovi una vulnerabilità di sicurezza:
1. **NON** aprire issue pubblici
2. Invia <NAME_EMAIL>
3. Includi dettagli tecnici e proof-of-concept
4. Riceverai risposta entro 24 ore

---

*Documento aggiornato: Dicembre 2024*
*Versione: 1.0*
