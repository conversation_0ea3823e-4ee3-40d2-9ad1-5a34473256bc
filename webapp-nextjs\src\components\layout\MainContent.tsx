'use client'

import React from 'react'
import { useAuth } from '@/contexts/AuthContext'
import ExpirationWarning from '@/components/auth/ExpirationWarning'

interface MainContentProps {
  children: React.ReactNode
}

export default function MainContent({ children }: MainContentProps) {
  const { isAuthenticated } = useAuth()

  return (
    <main className="pt-16">
      {isAuthenticated && (
        <div className="container mx-auto px-4 py-2">
          <ExpirationWarning />
        </div>
      )}
      {children}
    </main>
  )
}
