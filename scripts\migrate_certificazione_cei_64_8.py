#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script di migrazione per aggiornare il sistema di certificazione cavi
per conformità CEI 64-8 e normative internazionali.

FASE 1: Estensione tabelle esistenti
- CertificazioniCavi: Aggiunta campi per prove dettagliate
- StrumentiCertificati: Aggiunta campi per gestione avanzata strumenti
"""

import sys
import os
import logging
from datetime import datetime

# Aggiungi la directory principale al path per importare i moduli
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Importa il modulo database_pg
from modules.database_pg import Database

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_column_exists(cursor, table_name, column_name):
    """Verifica se una colonna esiste in una tabella."""
    cursor.execute("""
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = %s AND column_name = %s
    """, (table_name.lower(), column_name.lower()))
    return cursor.fetchone() is not None

def migrate_certificazioni_cavi_table(cursor):
    """Aggiunge i nuovi campi alla tabella CertificazioniCavi."""
    logging.info("🔄 Aggiornamento tabella CertificazioniCavi...")
    
    # Lista dei nuovi campi da aggiungere
    new_columns = [
        ("tipo_certificato", "TEXT DEFAULT 'SINGOLO'"),
        ("stato_certificato", "TEXT DEFAULT 'BOZZA'"),
        ("designazione_funzionale", "TEXT"),
        ("tensione_nominale", "TEXT"),
        ("tensione_prova_isolamento", "INTEGER"),
        ("durata_prova_isolamento", "INTEGER"),
        ("valore_minimo_isolamento", "REAL"),
        ("temperatura_prova", "REAL"),
        ("umidita_prova", "REAL"),
        ("esito_complessivo", "TEXT")
    ]
    
    columns_added = 0
    for column_name, column_definition in new_columns:
        if not check_column_exists(cursor, 'certificazionicavi', column_name):
            try:
                cursor.execute(f"ALTER TABLE CertificazioniCavi ADD COLUMN {column_name} {column_definition}")
                logging.info(f"  ✅ Aggiunta colonna: {column_name}")
                columns_added += 1
            except Exception as e:
                logging.error(f"  ❌ Errore aggiungendo colonna {column_name}: {str(e)}")
        else:
            logging.info(f"  ⚠️ Colonna {column_name} già esistente")
    
    logging.info(f"✅ Tabella CertificazioniCavi aggiornata: {columns_added} nuove colonne aggiunte")
    return columns_added

def migrate_strumenti_certificati_table(cursor):
    """Aggiunge i nuovi campi alla tabella StrumentiCertificati."""
    logging.info("🔄 Aggiornamento tabella StrumentiCertificati...")
    
    # Lista dei nuovi campi da aggiungere
    new_columns = [
        ("tipo_strumento", "TEXT"),
        ("ente_certificatore", "TEXT"),
        ("range_misura", "TEXT"),
        ("precisione", "TEXT"),
        ("stato_strumento", "TEXT DEFAULT 'ATTIVO'")
    ]
    
    columns_added = 0
    for column_name, column_definition in new_columns:
        if not check_column_exists(cursor, 'strumenticertificati', column_name):
            try:
                cursor.execute(f"ALTER TABLE StrumentiCertificati ADD COLUMN {column_name} {column_definition}")
                logging.info(f"  ✅ Aggiunta colonna: {column_name}")
                columns_added += 1
            except Exception as e:
                logging.error(f"  ❌ Errore aggiungendo colonna {column_name}: {str(e)}")
        else:
            logging.info(f"  ⚠️ Colonna {column_name} già esistente")
    
    logging.info(f"✅ Tabella StrumentiCertificati aggiornata: {columns_added} nuove colonne aggiunte")
    return columns_added

def create_rapporti_generali_collaudo_table(cursor):
    """Crea la nuova tabella RapportiGeneraliCollaudo."""
    logging.info("🔄 Creazione tabella RapportiGeneraliCollaudo...")
    
    # Verifica se la tabella esiste già
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = 'rapportigeneralicollaudo'
    """)
    
    if cursor.fetchone():
        logging.info("  ⚠️ Tabella RapportiGeneraliCollaudo già esistente")
        return False
    
    try:
        cursor.execute("""
            CREATE TABLE RapportiGeneraliCollaudo (
                id_rapporto SERIAL PRIMARY KEY,
                id_cantiere INTEGER NOT NULL,
                numero_rapporto TEXT NOT NULL UNIQUE,
                data_rapporto DATE NOT NULL,
                
                -- Dati Progetto/Commessa
                nome_progetto TEXT,
                codice_progetto TEXT,
                cliente_finale TEXT,
                localita_impianto TEXT,
                societa_installatrice TEXT,
                societa_responsabile_prove TEXT,
                data_inizio_collaudo DATE,
                data_fine_collaudo DATE,
                
                -- Riferimenti Normativi (JSON per flessibilità)
                normative_applicate TEXT,
                documentazione_progetto TEXT,
                
                -- Scopo e Ambito
                scopo_rapporto TEXT,
                ambito_collaudo TEXT,
                
                -- Condizioni Ambientali
                temperatura_ambiente REAL,
                umidita_ambiente REAL,
                
                -- Riepilogo
                numero_cavi_totali INTEGER DEFAULT 0,
                numero_cavi_conformi INTEGER DEFAULT 0,
                numero_cavi_non_conformi INTEGER DEFAULT 0,
                
                -- Personale
                responsabile_tecnico TEXT,
                rappresentante_cliente TEXT,
                
                -- Stato e Conclusioni
                stato_rapporto TEXT DEFAULT 'BOZZA',
                conclusioni TEXT,
                dichiarazione_conformita BOOLEAN DEFAULT FALSE,
                
                -- Metadati
                timestamp_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                timestamp_modifica TIMESTAMP,
                
                FOREIGN KEY (id_cantiere) REFERENCES Cantieri(id_cantiere) ON DELETE CASCADE
            )
        """)
        logging.info("  ✅ Tabella RapportiGeneraliCollaudo creata con successo")
        return True
    except Exception as e:
        logging.error(f"  ❌ Errore creando tabella RapportiGeneraliCollaudo: {str(e)}")
        return False

def create_prove_dettagliate_table(cursor):
    """Crea la nuova tabella ProveDettagliate."""
    logging.info("🔄 Creazione tabella ProveDettagliate...")
    
    # Verifica se la tabella esiste già
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = 'provedettagliate'
    """)
    
    if cursor.fetchone():
        logging.info("  ⚠️ Tabella ProveDettagliate già esistente")
        return False
    
    try:
        cursor.execute("""
            CREATE TABLE ProveDettagliate (
                id_prova SERIAL PRIMARY KEY,
                id_certificazione INTEGER NOT NULL,
                tipo_prova TEXT NOT NULL,
                
                -- Dati Comuni
                data_prova TIMESTAMP,
                operatore TEXT,
                condizioni_ambientali JSONB,
                
                -- Risultati Specifici per Tipo
                risultati JSONB,
                valori_misurati JSONB,
                valori_attesi JSONB,
                
                -- Esito
                esito TEXT NOT NULL,
                note_prova TEXT,
                
                FOREIGN KEY (id_certificazione) REFERENCES CertificazioniCavi(id_certificazione) ON DELETE CASCADE
            )
        """)
        logging.info("  ✅ Tabella ProveDettagliate creata con successo")
        return True
    except Exception as e:
        logging.error(f"  ❌ Errore creando tabella ProveDettagliate: {str(e)}")
        return False

def create_non_conformita_table(cursor):
    """Crea la nuova tabella NonConformita."""
    logging.info("🔄 Creazione tabella NonConformita...")
    
    # Verifica se la tabella esiste già
    cursor.execute("""
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = 'nonconformita'
    """)
    
    if cursor.fetchone():
        logging.info("  ⚠️ Tabella NonConformita già esistente")
        return False
    
    try:
        cursor.execute("""
            CREATE TABLE NonConformita (
                id_nc SERIAL PRIMARY KEY,
                id_certificazione INTEGER,
                id_rapporto INTEGER,
                
                -- Identificazione NC
                codice_nc TEXT NOT NULL UNIQUE,
                data_rilevazione DATE NOT NULL,
                tipo_nc TEXT,
                
                -- Descrizione
                descrizione TEXT NOT NULL,
                riferimento_cavo TEXT,
                riferimento_prova TEXT,
                
                -- Azioni Correttive
                azione_correttiva TEXT,
                responsabile_azione TEXT,
                data_scadenza_azione DATE,
                data_completamento_azione DATE,
                
                -- Ri-verifica
                esito_riverifica TEXT,
                note_riverifica TEXT,
                
                -- Stato
                stato_nc TEXT DEFAULT 'APERTA',
                
                FOREIGN KEY (id_certificazione) REFERENCES CertificazioniCavi(id_certificazione) ON DELETE SET NULL,
                FOREIGN KEY (id_rapporto) REFERENCES RapportiGeneraliCollaudo(id_rapporto) ON DELETE SET NULL
            )
        """)
        logging.info("  ✅ Tabella NonConformita creata con successo")
        return True
    except Exception as e:
        logging.error(f"  ❌ Errore creando tabella NonConformita: {str(e)}")
        return False

def add_foreign_key_id_rapporto(cursor):
    """Aggiunge la colonna id_rapporto e foreign key alla tabella CertificazioniCavi."""
    logging.info("🔄 Aggiunta foreign key id_rapporto a CertificazioniCavi...")
    
    try:
        # Prima aggiungi la colonna se non esiste
        if not check_column_exists(cursor, 'certificazionicavi', 'id_rapporto'):
            cursor.execute("ALTER TABLE CertificazioniCavi ADD COLUMN id_rapporto INTEGER")
            logging.info("  ✅ Colonna id_rapporto aggiunta")
        else:
            logging.info("  ⚠️ Colonna id_rapporto già esistente")
        
        # Verifica se il constraint esiste già
        cursor.execute("""
            SELECT constraint_name 
            FROM information_schema.table_constraints 
            WHERE table_name = 'certificazionicavi' AND constraint_name = 'fk_rapporto'
        """)
        
        if not cursor.fetchone():
            cursor.execute("""
                ALTER TABLE CertificazioniCavi 
                ADD CONSTRAINT fk_rapporto 
                FOREIGN KEY (id_rapporto) REFERENCES RapportiGeneraliCollaudo(id_rapporto) ON DELETE SET NULL
            """)
            logging.info("  ✅ Foreign key fk_rapporto aggiunta")
        else:
            logging.info("  ⚠️ Foreign key fk_rapporto già esistente")
            
        return True
    except Exception as e:
        logging.error(f"  ❌ Errore aggiungendo foreign key: {str(e)}")
        return False

def migrate_cei_64_8():
    """
    Esegue la migrazione completa per conformità CEI 64-8.
    """
    logging.info("🚀 INIZIO MIGRAZIONE CERTIFICAZIONE CEI 64-8")
    
    db = Database()
    
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # Contatori per il riepilogo
            total_changes = 0
            
            # 1. Aggiorna tabelle esistenti
            total_changes += migrate_certificazioni_cavi_table(cursor)
            total_changes += migrate_strumenti_certificati_table(cursor)
            
            # 2. Crea nuove tabelle
            if create_rapporti_generali_collaudo_table(cursor):
                total_changes += 1
            
            if create_prove_dettagliate_table(cursor):
                total_changes += 1
                
            if create_non_conformita_table(cursor):
                total_changes += 1
            
            # 3. Aggiungi foreign key (solo dopo aver creato RapportiGeneraliCollaudo)
            if add_foreign_key_id_rapporto(cursor):
                total_changes += 1
            
            # Commit delle modifiche
            conn.commit()
            
            logging.info(f"✅ MIGRAZIONE COMPLETATA CON SUCCESSO")
            logging.info(f"📊 Totale modifiche applicate: {total_changes}")
            
            return True
            
    except Exception as e:
        logging.error(f"❌ Errore durante la migrazione: {str(e)}")
        return False

if __name__ == "__main__":
    migrate_cei_64_8()
