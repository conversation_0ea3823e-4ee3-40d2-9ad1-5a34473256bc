(()=>{var e={};e.id=628,e.ids=[628],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20518:(e,t,r)=>{Promise.resolve().then(r.bind(r,69274))},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},28559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31092:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var s=r(60687),i=r(43210),a=r(16189),n=r(29523),o=r(63213);r(62185);var d=r(41862),l=r(93613),p=r(28559);function c(){let{user:e,isAuthenticated:t,isLoading:r}=(0,o.A)(),c=(0,a.useRouter)();parseInt((0,a.useParams)().id);let[u,x]=(0,i.useState)(null),[m,h]=(0,i.useState)(!0),[f,v]=(0,i.useState)("");return r||m?(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsx)(d.A,{className:"h-8 w-8 animate-spin"})}):f||!u?(0,s.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[(0,s.jsx)("div",{className:"mb-4 p-4 border border-red-200 rounded-lg bg-red-50",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(l.A,{className:"h-4 w-4 text-red-600 mr-2"}),(0,s.jsx)("span",{className:"text-red-800",children:f||"Cantiere non trovato"})]})}),(0,s.jsxs)(n.$,{onClick:()=>{c.push("/cantieri")},children:[(0,s.jsx)(p.A,{className:"mr-2 h-4 w-4"}),"Torna alla Lista Cantieri"]})]}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)(d.A,{className:"h-8 w-8 animate-spin mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Caricamento gestione cavi..."})]})})}},33873:e=>{"use strict";e.exports=require("path")},41862:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69274:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\cantieri\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx","default")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(31658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},83997:e=>{"use strict";e.exports=require("tty")},90254:(e,t,r)=>{Promise.resolve().then(r.bind(r,31092))},94735:e=>{"use strict";e.exports=require("events")},99704:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>c,pages:()=>p,routeModule:()=>u,tree:()=>l});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["cantieri",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,69274)),"C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,p=["C:\\CMS\\webapp-nextjs\\src\\app\\cantieri\\[id]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/cantieri/[id]/page",pathname:"/cantieri/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,991,658,223],()=>r(99704));module.exports=s})();