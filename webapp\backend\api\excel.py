from fastapi import APIRout<PERSON>, Depends, HTTPException, UploadFile, File, Form, BackgroundTasks
from fastapi.responses import FileResponse, JSONResponse
from typing import Optional, List, Dict, Any
import os
import tempfile
import shutil
from pathlib import Path
import logging
from datetime import datetime

from backend.api.deps import get_current_user, get_db
from backend.schemas.user import User
from backend.config import settings

# Import the excel_manager module
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../')))

# Import only the necessary functions from excel_manager
from modules.excel_manager import (
    is_file_safe,
    leggi_file_excel,
    valida_colonne_excel,
    elabora_cavi_da_excel,
    inserisci_cavi_nel_database,
    trova_cavi_non_in_revisione,
    crea_backup_automatico,
    richiedi_revisione,
    crea_template_excel,
    crea_template_parco_bobine,
    esporta_cavi_excel,
    esporta_parco_bobine_excel
)

# Create a router for Excel operations
router = APIRouter()

# Create a directory for temporary files if it doesn't exist
TEMP_DIR = Path(settings.STATIC_DIR) / "temp"
TEMP_DIR.mkdir(parents=True, exist_ok=True)
print(f"Directory temporanea creata: {TEMP_DIR}")

# Helper function to create a temporary file
def save_upload_file_temp(upload_file: UploadFile) -> Path:
    try:
        suffix = Path(upload_file.filename).suffix
        with tempfile.NamedTemporaryFile(delete=False, suffix=suffix, dir=TEMP_DIR) as temp:
            shutil.copyfileobj(upload_file.file, temp)
            return Path(temp.name)
    finally:
        upload_file.file.close()

# Optimized import function for webapp
def importa_cavi_da_excel_webapp(id_cantiere: int, percorso_file: str, revisione_predefinita: str = None) -> Dict[str, Any]:
    """
    Versione ottimizzata dell'importazione cavi per la webapp.
    Utilizza la logica CLI ma con gestione errori e return values ottimizzati per l'ambiente web.

    Args:
        id_cantiere (int): ID del cantiere
        percorso_file (str): Percorso del file Excel
        revisione_predefinita (str): Revisione predefinita da utilizzare

    Returns:
        Dict[str, Any]: Risultato dell'importazione con dettagli
    """
    try:
        logging.info(f"🔄 Inizio importazione cavi per cantiere {id_cantiere}")

        # === 1. VALIDAZIONE FILE ===
        if not is_file_safe(percorso_file):
            return {
                "success": False,
                "message": "File non valido o non sicuro",
                "details": {"error_type": "file_validation"}
            }

        # === 2. BACKUP AUTOMATICO ===
        backup_path = crea_backup_automatico(id_cantiere)
        backup_info = "Backup creato" if backup_path else "Nessun backup necessario (primo import)"

        # === 3. LETTURA E VALIDAZIONE FILE ===
        df_originale = leggi_file_excel(percorso_file)
        if df_originale is None:
            return {
                "success": False,
                "message": "Errore nella lettura del file Excel",
                "details": {"error_type": "file_reading"}
            }

        df_validato = valida_colonne_excel(df_originale)
        if df_validato is None:
            return {
                "success": False,
                "message": "File Excel non valido: colonne obbligatorie mancanti",
                "details": {"error_type": "column_validation"}
            }

        # === 4. GESTIONE REVISIONE ===
        if not revisione_predefinita:
            return {
                "success": False,
                "message": "Codice revisione obbligatorio",
                "details": {"error_type": "missing_revision"}
            }
        revisione = revisione_predefinita

        # === 5. ELABORAZIONE DATI ===
        cavi_non_in_revisione = trova_cavi_non_in_revisione(id_cantiere, df_validato)
        batch_dati = elabora_cavi_da_excel(id_cantiere, df_validato, revisione, cavi_non_in_revisione)

        if not batch_dati:
            return {
                "success": False,
                "message": "Nessun dato valido da importare",
                "details": {"error_type": "no_valid_data"}
            }

        # === 6. IMPORT NEL DATABASE ===
        if not inserisci_cavi_nel_database(id_cantiere, revisione, batch_dati):
            return {
                "success": False,
                "message": "Errore durante il salvataggio nel database",
                "details": {"error_type": "database_error"}
            }

        # === 7. RISULTATO SUCCESSO ===
        return {
            "success": True,
            "message": f"Importazione completata con successo. {len(batch_dati)} cavi processati.",
            "details": {
                "cavi_processati": len(batch_dati),
                "revisione": revisione,
                "backup_info": backup_info,
                "cantiere_id": id_cantiere
            }
        }

    except Exception as e:
        logging.error(f"❌ Errore durante l'importazione cavi: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"Errore durante l'importazione: {str(e)}",
            "details": {"error_type": "general_error", "exception": str(e)}
        }

# Optimized import function for parco bobine in webapp
def importa_parco_bobine_da_excel_webapp(percorso_file: str, id_cantiere: int) -> Dict[str, Any]:
    """
    Versione ottimizzata dell'importazione parco bobine per la webapp.
    Utilizza la logica CLI ma con gestione errori e return values ottimizzati per l'ambiente web.

    Args:
        percorso_file (str): Percorso del file Excel
        id_cantiere (int): ID del cantiere

    Returns:
        Dict[str, Any]: Risultato dell'importazione con dettagli
    """
    try:
        logging.info(f"🔄 Inizio importazione parco bobine per cantiere {id_cantiere}")

        # === 1. VALIDAZIONE FILE ===
        if not is_file_safe(percorso_file):
            return {
                "success": False,
                "message": "File non valido o non sicuro",
                "details": {"error_type": "file_validation"}
            }

        # === 2. LETTURA FILE ===
        df = leggi_file_excel(percorso_file)
        if df is None or df.empty:
            return {
                "success": False,
                "message": "File vuoto o non valido",
                "details": {"error_type": "file_reading"}
            }

        # === 3. VALIDAZIONE COLONNE ===
        colonne_richieste = ["utility", "tipologia", "metri_totali"]
        for colonna in colonne_richieste:
            if colonna not in df.columns:
                return {
                    "success": False,
                    "message": f"Colonna '{colonna}' mancante nel file",
                    "details": {"error_type": "column_validation", "missing_column": colonna}
                }

        # === 4. PULIZIA DATI ===
        df_valido = df.dropna(subset=colonne_richieste)
        righe_invalide = len(df) - len(df_valido)

        if df_valido.empty:
            return {
                "success": False,
                "message": "Nessun dato valido trovato nel file",
                "details": {"error_type": "no_valid_data"}
            }

        # === 5. IMPORTAZIONE TRAMITE FUNZIONE CLI ===
        # Usa la funzione CLI esistente con parametri ottimizzati per webapp
        from modules.excel_manager import importa_parco_bobine_da_excel

        success, message, bobine_importate = importa_parco_bobine_da_excel(
            percorso_file=percorso_file,
            id_cantiere=id_cantiere,
            config_choice="1",  # Use progressive numbering
            auto_choice="2"     # Auto-generate codes with AUTO prefix
        )

        # === 6. RISULTATO ===
        if success:
            return {
                "success": True,
                "message": message,
                "details": {
                    "bobine_importate": bobine_importate,
                    "righe_invalide": righe_invalide,
                    "cantiere_id": id_cantiere
                }
            }
        else:
            return {
                "success": False,
                "message": message,
                "details": {"error_type": "import_error"}
            }

    except Exception as e:
        logging.error(f"❌ Errore durante l'importazione parco bobine: {str(e)}", exc_info=True)
        return {
            "success": False,
            "message": f"Errore durante l'importazione: {str(e)}",
            "details": {"error_type": "general_error", "exception": str(e)}
        }

# Helper function to get a relative URL for a file
def get_file_url(file_path: Path) -> str:
    relative_path = file_path.relative_to(settings.STATIC_DIR)
    return f"{settings.STATIC_URL}/{relative_path}"

# Endpoint to import cables from Excel
@router.post("/{cantiere_id}/import-cavi", response_model=Dict[str, Any])
async def import_cavi(
    cantiere_id: int,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    revisione: str = Form(..., description="Codice identificativo obbligatorio della revisione"),
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Import cables from an Excel file using optimized webapp logic.
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="File deve essere in formato Excel (.xlsx o .xls)")

        # Save the uploaded file to a temporary location
        temp_file = save_upload_file_temp(file)
        logging.info(f"File temporaneo salvato: {temp_file}")

        # LOGICA REVISIONI: La revisione è OBBLIGATORIA e deve essere fornita dall'utente
        if not revisione or not revisione.strip():
            raise HTTPException(
                status_code=400,
                detail="Codice revisione obbligatorio. Inserisci un codice identificativo per questa importazione (es. REV_001, V1.0, UFFICIALE_01, etc.)"
            )

        user_revision = revisione.strip()
        logging.info(f"Using user-provided revision: {user_revision}")

        # Import the cables using the optimized webapp function
        result = importa_cavi_da_excel_webapp(cantiere_id, str(temp_file), user_revision)

        # Clean up the temporary file - use try/except to handle file in use
        try:
            os.unlink(temp_file)
            logging.info(f"File temporaneo eliminato: {temp_file}")
        except Exception as e:
            logging.warning(f"Could not delete temporary file {temp_file}: {str(e)}")
            # Schedule file for deletion later
            background_tasks.add_task(lambda: os.unlink(temp_file) if os.path.exists(temp_file) else None)

        # Return the result from the optimized function
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logging.error(f"Error importing cables: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Errore durante l'importazione dei cavi: {str(e)}")

# Endpoint to import reel inventory from Excel
@router.post("/{cantiere_id}/import-parco-bobine", response_model=Dict[str, Any])
async def import_parco_bobine(
    cantiere_id: int,
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Import reel inventory from an Excel file using optimized webapp logic.
    """
    try:
        # Validate file type
        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            raise HTTPException(status_code=400, detail="File deve essere in formato Excel (.xlsx o .xls)")

        # Save the uploaded file to a temporary location
        temp_file = save_upload_file_temp(file)
        logging.info(f"File temporaneo salvato: {temp_file}")

        # Import the reel inventory using the optimized webapp function
        result = importa_parco_bobine_da_excel_webapp(str(temp_file), cantiere_id)

        # Clean up the temporary file - use try/except to handle file in use
        try:
            os.unlink(temp_file)
            logging.info(f"File temporaneo eliminato: {temp_file}")
        except Exception as e:
            logging.warning(f"Could not delete temporary file {temp_file}: {str(e)}")
            # Schedule file for deletion later
            background_tasks.add_task(lambda: os.unlink(temp_file) if os.path.exists(temp_file) else None)

        # Return the result from the optimized function
        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=400, detail=result["message"])

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logging.error(f"Error importing reel inventory: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Errore durante l'importazione del parco bobine: {str(e)}")

# Endpoint to create an Excel template for cables
@router.get("/template-cavi", response_class=FileResponse)
async def create_cavi_template(
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Create an Excel template for cables.
    """
    try:
        # Create the template directly in the static directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_filename = f"template_cavi_{timestamp}.xlsx"
        static_path = TEMP_DIR / template_filename

        # Create the template with the specific path
        template_path = crea_template_excel(str(static_path))

        if not template_path or not os.path.exists(template_path):
            raise HTTPException(status_code=500, detail="Errore durante la creazione del template")

        # Serve il file direttamente come download
        return FileResponse(
            path=template_path,
            filename=template_filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except Exception as e:
        logging.error(f"Error creating cable template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante la creazione del template: {str(e)}")

# Endpoint to create an Excel template for reel inventory
@router.get("/template-parco-bobine", response_class=FileResponse)
async def create_parco_bobine_template(
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Create an Excel template for reel inventory.
    """
    try:
        # Create the template directly in the static directory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        template_filename = f"template_parco_bobine_{timestamp}.xlsx"
        static_path = TEMP_DIR / template_filename

        # Create the template with the specific path
        template_path = crea_template_parco_bobine(str(static_path))

        if not template_path or not os.path.exists(template_path):
            raise HTTPException(status_code=500, detail="Errore durante la creazione del template")

        # Serve il file direttamente come download
        return FileResponse(
            path=template_path,
            filename=template_filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except Exception as e:
        logging.error(f"Error creating reel inventory template: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante la creazione del template: {str(e)}")

# Endpoint to export cables to Excel
@router.get("/{cantiere_id}/export-cavi", response_class=FileResponse)
async def export_cavi(
    cantiere_id: int,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Export cables to an Excel file.
    """
    try:
        # Export the cables
        export_path = esporta_cavi_excel(cantiere_id)

        if not export_path or not os.path.exists(export_path):
            raise HTTPException(status_code=500, detail="Errore durante l'esportazione dei cavi")

        # Copy the export to the static directory with a unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"export_cavi_{timestamp}.xlsx"
        static_path = TEMP_DIR / filename
        shutil.copy(export_path, static_path)

        # Serve il file direttamente come download
        return FileResponse(
            path=static_path,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except Exception as e:
        logging.error(f"Error exporting cables: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante l'esportazione dei cavi: {str(e)}")

# Endpoint to export reel inventory to Excel
@router.get("/{cantiere_id}/export-parco-bobine", response_class=FileResponse)
async def export_parco_bobine(
    cantiere_id: int,
    current_user: User = Depends(get_current_user),
    db = Depends(get_db)
):
    """
    Export reel inventory to an Excel file.
    """
    try:
        # Export the reel inventory
        export_path = esporta_parco_bobine_excel()

        if not export_path or not os.path.exists(export_path):
            raise HTTPException(status_code=500, detail="Errore durante l'esportazione del parco bobine")

        # Copy the export to the static directory with a unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"export_parco_bobine_{timestamp}.xlsx"
        static_path = TEMP_DIR / filename
        shutil.copy(export_path, static_path)

        # Serve il file direttamente come download
        return FileResponse(
            path=static_path,
            filename=filename,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )
    except Exception as e:
        logging.error(f"Error exporting reel inventory: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Errore durante l'esportazione del parco bobine: {str(e)}")
