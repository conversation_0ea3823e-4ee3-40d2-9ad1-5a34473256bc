from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship

from backend.database import Base

class ProvaDettagliata(Base):
    """
    Modello SQLAlchemy per la tabella ProveDettagliate.
    Gestisce le prove dettagliate per conformità CEI 64-8.
    """
    __tablename__ = "provedettagliate"
    
    id_prova = Column(Integer, primary_key=True, index=True)
    id_certificazione = Column(Integer, ForeignKey("certificazionicavi.id_certificazione"), nullable=False)
    tipo_prova = Column(String, nullable=False)  # 'ESAME_VISTA', 'CONTINUITA', 'ISOLAMENTO', 'RIGIDITA', 'SEQUENZA_FASI', 'ALTRO'
    
    # Dati Comuni
    data_prova = Column(DateTime, nullable=True)
    operatore = Column(String, nullable=True)
    condizioni_ambientali = Column(JSONB, nullable=True)  # {temperatura: 20, umidita: 60}
    
    # Risultati Specifici per Tipo
    risultati = Column(JSONB, nullable=True)  # Struttura flessibile per ogni tipo di prova
    valori_misurati = Column(JSONB, nullable=True)  # Valori numerici misurati
    valori_attesi = Column(JSONB, nullable=True)  # Valori attesi/limite
    
    # Esito
    esito = Column(String, nullable=False)  # 'CONFORME', 'NON_CONFORME', 'N_A'
    note_prova = Column(Text, nullable=True)
    
    # Relazioni
    certificazione = relationship("CertificazioneCavo", back_populates="prove_dettagliate")
