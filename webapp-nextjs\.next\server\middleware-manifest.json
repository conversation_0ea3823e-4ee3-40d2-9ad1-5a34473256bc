{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "4702d184d10374bd3c56ede6fffc6135", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bb16660714af194f5ceb18053e11335c9620bc35033f858cf951375f9cfd0a9b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "47da8da842ea7f78a415ee1012cdb1729d8f24d7e37014d1d38cd6cb93abd808"}}}, "sortedMiddleware": ["/"], "functions": {}}