{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "bF7oDIAQsiDlYBqvLR/WiZdX1cn2FuoutsR3XKrSPcA=", "__NEXT_PREVIEW_MODE_ID": "c42618205a36c7592cd41da11949942d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "1dcc90284f8b5e3bdc9ca7a372b4d5eb2751ee22057af781594b58204c4db5b7", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "92734dbb79a44b9c167380e2fde1e9436ad0def640ffb532310fa89060e5c388"}}}, "sortedMiddleware": ["/"], "functions": {}}