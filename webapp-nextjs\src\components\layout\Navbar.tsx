'use client'

import { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { PrimaryButton, QuickButton } from '@/components/ui/animated-button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import {
  Cable,
  Home,
  Activity,
  BarChart3,
  Settings,
  Users,
  Menu,
  X,
  Building2,
  ClipboardList,
  FileText,
  LogOut,
  Package,
  User,

} from 'lucide-react'

const getNavigation = (userRole: string | undefined, isImpersonating: boolean, impersonatedUser: any, cantiereId?: number) => {
  // Home button - testo personalizzato come nella webapp originale
  const homeButton = {
    name: userRole === 'owner' ? "Menu Admin" :
          userRole === 'user' ? "Lista Cantieri" :
          userRole === 'cantieri_user' ? "Gestione Cavi" : "Home",
    href: userRole === 'owner' ? '/admin' :
          userRole === 'user' ? '/cantieri' :
          userRole === 'cantieri_user' ? '/cavi' : '/',
    icon: Home
  }

  if (userRole === 'owner' && !isImpersonating) {
    // Solo amministratore - solo il pulsante Home che va al pannello admin
    return [homeButton]
  }

  if (userRole === 'user' || (isImpersonating && impersonatedUser?.role === 'user')) {
    // Utente standard - Home + eventualmente cantieri se impersonificato
    const nav = [homeButton]
    if (isImpersonating) {
      nav.push({ name: 'Cantieri', href: '/cantieri', icon: Building2 })
    }

    // Se un cantiere è selezionato, aggiungi i menu di gestione come nella webapp originale
    if (cantiereId) {
      nav.push(
        { name: 'Visualizza Cavi', href: '/cavi', icon: Cable },
        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },
        { name: 'Gestione Excel', href: '/excel', icon: FileText },
        { name: 'Report', href: '/reports', icon: BarChart3 },
        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },
        { name: 'Produttività', href: '/productivity', icon: Activity },
      )
    }

    return nav
  }

  if (userRole === 'cantieri_user' || (isImpersonating && impersonatedUser?.role === 'cantieri_user')) {
    // Utente cantiere - menu completo come nella webapp originale
    const nav = [homeButton]

    // Se un cantiere è selezionato, aggiungi i menu di gestione
    if (cantiereId) {
      // Se non è cantieri_user diretto, aggiungi Visualizza Cavi
      if (userRole !== 'cantieri_user') {
        nav.push({ name: 'Visualizza Cavi', href: '/cavi', icon: Cable })
      }

      nav.push(
        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },
        { name: 'Gestione Excel', href: '/excel', icon: FileText },
        { name: 'Report', href: '/reports', icon: BarChart3 },
        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },
        { name: 'Produttività', href: '/productivity', icon: Activity },
      )
    }

    return nav
  }

  // Default
  return [homeButton]
}

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false)
  const pathname = usePathname()
  const { user, cantiere, isAuthenticated, isImpersonating, impersonatedUser, logout } = useAuth()

  // Recupera l'ID del cantiere selezionato dal localStorage o dal context
  const cantiereId = cantiere?.id_cantiere || (typeof window !== 'undefined' ? parseInt(localStorage.getItem('selectedCantiereId') || '0') : 0)
  const cantiereName = cantiere?.commessa || (typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') : '') || `Cantiere ${cantiereId}`

  const navigation = getNavigation(user?.ruolo, isImpersonating, impersonatedUser, cantiereId)



  // Non mostrare navbar nella pagina di login
  if (pathname === '/login') {
    return null
  }

  // Se non autenticato, non mostrare navbar
  if (!isAuthenticated) {
    return null
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm">
      <div className="max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between h-16">

          {/* Tutto a sinistra: Logo + Menu Admin + Navigation */}
          <div className="flex items-center space-x-6">
            {/* Logo e Brand */}
            <div className="flex items-center space-x-3 cursor-default">
              <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center">
                <Cable className="w-5 h-5 text-white" />
              </div>
              <div className="hidden sm:block">
                <h1 className="text-xl font-bold text-slate-900">CABLYS</h1>
                <p className="text-xs text-slate-500 -mt-1">Cable Installation System</p>
              </div>
            </div>



            {/* Navigation Desktop - allontanata dal logo */}
            <div className="hidden md:flex items-center space-x-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href ||
                             (item.href !== '/' && pathname.startsWith(item.href))
              const Icon = item.icon

              // Applica lo stile speciale solo per "Visualizza Cavi"
              if (item.name === 'Visualizza Cavi') {
                return (
                  <Link key={item.name} href={item.href}>
                    <div className={`visualizza-cavi-button ${isActive ? 'active' : ''}`}>
                      <Icon className="icon w-4 h-4" />
                      <span className="hidden lg:inline">{item.name}</span>
                    </div>
                  </Link>
                )
              }

              // Stile normale per gli altri pulsanti
              return (
                <Link key={item.name} href={item.href}>
                  <div className={`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${
                    isActive
                      ? 'text-blue-700 bg-blue-50 border border-blue-200 font-medium'
                      : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 border border-transparent'
                  }`}>
                    <Icon className="w-4 h-4" />
                    <span className="hidden lg:inline">{item.name}</span>
                  </div>
                </Link>
              )
            })}
            </div>
          </div>

          {/* User Info a destra con più margine */}
          <div className="flex items-center space-x-4 ml-8">
            {/* Display cantiere selezionato - versione compatta */}
            {cantiereId && cantiereId > 0 && (
              <div className="hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md">
                <Building2 className="w-3 h-3 text-blue-600" />
                <div className="text-xs">
                  <span className="text-blue-900 font-medium">{cantiereName}</span>
                </div>
              </div>
            )}

            {/* User Info e Logout */}
            <div className="hidden sm:flex items-center space-x-3">
              <div className="text-right">
                <p className="text-sm font-medium text-slate-900">
                  {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}
                </p>
              </div>
              <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                <User className="w-3 h-3 text-white" />
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2"
                onClick={logout}
                title="Logout"
              >
                <LogOut className="w-4 h-4" />
                <span className="hidden lg:inline">Logout</span>
              </Button>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(!isOpen)}
                className="text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md"
              >
                {isOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isOpen && (
        <div className="md:hidden border-t border-slate-200 bg-white">
          <div className="px-2 pt-2 pb-3 space-y-1">
            {navigation.map((item) => {
              const isActive = pathname === item.href || 
                             (item.href !== '/' && pathname.startsWith(item.href))
              const Icon = item.icon
              
              return (
                <Link key={item.name} href={item.href}>
                  <div
                    className={`w-full flex items-center justify-start space-x-3 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ${
                      isActive
                        ? 'text-blue-700 bg-blue-50 border border-blue-200 font-medium'
                        : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 border border-transparent'
                    }`}
                    onClick={() => setIsOpen(false)}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{item.name}</span>
                  </div>
                </Link>
              )
            })}
          </div>
          
          {/* Mobile User Info e Actions */}
          <div className="border-t border-slate-200 px-4 py-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                  {user ? <User className="w-3 h-3 text-white" /> : <Building2 className="w-3 h-3 text-white" />}
                </div>
                <div>
                  <p className="text-sm font-medium text-slate-900">
                    {isImpersonating && impersonatedUser ? impersonatedUser.username : (user ? user.username : cantiere?.commessa)}
                  </p>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={logout}
                title="Logout"
                className="hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md"
              >
                <LogOut className="w-4 h-4" />
              </Button>
            </div>

            {/* Mobile Admin Menu - solo per owner */}
            {user?.ruolo === 'owner' && !isImpersonating && (
              <div className="mt-3 pt-3 border-t border-slate-200">
                <p className="text-xs font-medium text-slate-500 mb-2">AMMINISTRAZIONE</p>
                <div className="space-y-1">
                  <Link
                    href="/admin"
                    className="block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center space-x-3">
                      <Users className="w-4 h-4 text-slate-500" />
                      <span>Pannello Admin</span>
                    </div>
                  </Link>
                  <Link
                    href="/admin?tab=users"
                    className="block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150"
                    onClick={() => setIsOpen(false)}
                  >
                    <div className="flex items-center space-x-3">
                      <Users className="w-4 h-4 text-slate-500" />
                      <span>Gestione Utenti</span>
                    </div>
                  </Link>
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </nav>
  )
}
