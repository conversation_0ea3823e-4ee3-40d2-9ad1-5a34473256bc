# ✅ Checklist Deployment Sistema Gestione Password

## 🎯 Pre-Deployment

### Backup e Sicurezza
- [ ] **Backup completo database**
  ```bash
  pg_dump cantieri > backup_$(date +%Y%m%d_%H%M%S).sql
  ```
- [ ] **Backup configurazioni attuali**
  ```bash
  cp -r webapp/backend/.env webapp/backend/.env.backup
  ```
- [ ] **Test ambiente di staging**
- [ ] **Verifica spazio disco disponibile** (min 1GB libero)

### Dipendenze e Requisiti
- [ ] **Python 3.8+** installato
- [ ] **PostgreSQL 12+** funzionante
- [ ] **Librerie Python aggiornate**
  ```bash
  pip install -r requirements.txt
  ```
- [ ] **Node.js 18+** per frontend (se applicabile)

---

## 🔧 Configurazione

### Variabili d'Ambiente
- [ ] **Database configurato**
  ```bash
  DB_HOST=localhost
  DB_PORT=5432
  DB_NAME=cantieri
  DB_USER=postgres
  DB_PASSWORD=***SECURE_PASSWORD***
  ```

- [ ] **Email provider configurato**
  ```bash
  EMAIL_PROVIDER=gmail
  GMAIL_USERNAME=***YOUR_EMAIL***
  GMAIL_APP_PASSWORD=***APP_PASSWORD***
  EMAIL_FROM_NAME="CMS Sistema"
  ```

- [ ] **Chiavi di sicurezza generate**
  ```bash
  SECRET_KEY=***GENERATE_SECURE_KEY***
  CANTIERE_MASTER_PASSWORD=***MASTER_PASSWORD***
  ```

- [ ] **Rate limiting configurato**
  ```bash
  MAX_LOGIN_ATTEMPTS=5
  LOCKOUT_DURATION_MINUTES=15
  TOKEN_EXPIRY_MINUTES=30
  ```

### Test Configurazione
- [ ] **Test connessione database**
  ```bash
  psql -h $DB_HOST -p $DB_PORT -U $DB_USER -d $DB_NAME -c "SELECT 1;"
  ```

- [ ] **Test configurazione email**
  ```bash
  python -c "from backend.core.email_config import email_config_manager; print(email_config_manager.test_connection())"
  ```

---

## 🗄️ Database Migration

### Esecuzione Migrazioni
- [ ] **Backup pre-migrazione**
  ```bash
  pg_dump cantieri > pre_migration_backup.sql
  ```

- [ ] **Esegui migrazione sicurezza**
  ```bash
  psql -d cantieri -f webapp/backend/migrations/add_security_fields.sql
  ```

- [ ] **Verifica tabelle create**
  ```sql
  \dt security_events
  \dt rate_limiting  
  \dt password_reset_tokens
  ```

- [ ] **Test integrità dati**
  ```sql
  SELECT COUNT(*) FROM Utenti;
  SELECT COUNT(*) FROM Cantieri;
  ```

### Validazione Schema
- [ ] **Campi sicurezza aggiunti a Utenti**
  ```sql
  \d+ Utenti
  -- Verifica: reset_token, failed_login_attempts, locked_until, etc.
  ```

- [ ] **Campi sicurezza aggiunti a Cantieri**
  ```sql
  \d+ Cantieri
  -- Verifica: reset_token, failed_login_attempts, locked_until, etc.
  ```

- [ ] **Trigger funzionanti**
  ```sql
  SELECT tgname FROM pg_trigger WHERE tgrelid = 'Utenti'::regclass;
  ```

---

## 🚀 Deployment Applicazione

### Backend
- [ ] **Aggiorna codice backend**
  ```bash
  cd webapp/backend
  git pull origin main
  ```

- [ ] **Installa dipendenze**
  ```bash
  pip install -r requirements.txt
  ```

- [ ] **Test import moduli**
  ```bash
  python -c "from backend.core.password_security import password_validator; print('OK')"
  python -c "from backend.core.email_service import email_service; print('OK')"
  python -c "from backend.api.password_management import router; print('OK')"
  ```

- [ ] **Restart servizio backend**
  ```bash
  systemctl restart cms-backend
  # oppure
  supervisorctl restart cms-backend
  ```

### Frontend
- [ ] **Aggiorna codice frontend**
  ```bash
  cd webapp-nextjs
  git pull origin main
  ```

- [ ] **Installa dipendenze**
  ```bash
  npm install
  ```

- [ ] **Build produzione**
  ```bash
  npm run build
  ```

- [ ] **Restart servizio frontend**
  ```bash
  systemctl restart cms-frontend
  # oppure
  pm2 restart cms-frontend
  ```

---

## 🧪 Test Post-Deployment

### Test Funzionalità Base
- [ ] **Login utente standard**
  - Credenziali corrette: ✅
  - Credenziali errate: ❌ (messaggio appropriato)

- [ ] **Login cantiere**
  - Codice e password corretti: ✅
  - Credenziali errate: ❌

- [ ] **Cambio password**
  - Password attuale corretta: ✅
  - Password attuale errata: ❌
  - Validazione nuova password: ✅

### Test Recupero Password
- [ ] **Richiesta reset per utente esistente**
  - Email inviata: ✅
  - Link funzionante: ✅
  - Token valido: ✅

- [ ] **Richiesta reset per email inesistente**
  - Risposta generica: ✅ (non rivela se email esiste)

- [ ] **Reset password con token**
  - Token valido: ✅
  - Token scaduto: ❌
  - Token già usato: ❌

### Test Sicurezza
- [ ] **Rate limiting login**
  ```bash
  # Test 10 tentativi rapidi
  for i in {1..10}; do
    curl -X POST http://localhost:8001/api/auth/login \
         -d "username=test&password=wrong" \
         -H "Content-Type: application/x-www-form-urlencoded"
    echo "Tentativo $i"
  done
  ```

- [ ] **Rate limiting reset password**
  ```bash
  # Test 15 richieste rapide
  for i in {1..15}; do
    curl -X POST http://localhost:8001/api/password/request-password-reset \
         -H "Content-Type: application/json" \
         -d '{"email":"<EMAIL>","user_type":"user"}'
    echo "Richiesta $i"
  done
  ```

- [ ] **Validazione input**
  - SQL injection: ❌ (bloccato)
  - XSS: ❌ (sanitizzato)
  - Email malformate: ❌ (rifiutate)

---

## 📊 Monitoraggio

### Log e Audit
- [ ] **Log di sicurezza attivi**
  ```bash
  tail -f logs/security.log
  ```

- [ ] **Eventi loggati correttamente**
  ```sql
  SELECT event_type, COUNT(*) 
  FROM security_events 
  WHERE created_at > NOW() - INTERVAL '1 hour'
  GROUP BY event_type;
  ```

- [ ] **Rate limiting tracciato**
  ```sql
  SELECT identifier_type, COUNT(*) 
  FROM rate_limiting 
  GROUP BY identifier_type;
  ```

### Performance
- [ ] **Tempo risposta API < 2s**
  ```bash
  curl -w "@curl-format.txt" -o /dev/null -s http://localhost:8001/api/password/validate-password?password=test
  ```

- [ ] **Memoria utilizzata accettabile**
  ```bash
  ps aux | grep python | grep cms
  ```

- [ ] **Connessioni database stabili**
  ```sql
  SELECT count(*) FROM pg_stat_activity WHERE datname = 'cantieri';
  ```

---

## 🔒 Sicurezza Post-Deployment

### Hardening
- [ ] **Rimuovi password in chiaro dal database**
  ```sql
  -- ATTENZIONE: Esegui solo dopo conferma che tutto funziona
  -- UPDATE Utenti SET password_plain = NULL;
  -- ALTER TABLE Cantieri DROP COLUMN IF EXISTS password_cantiere_encrypted;
  ```

- [ ] **Verifica permessi file**
  ```bash
  chmod 600 webapp/backend/.env
  chmod 700 logs/
  ```

- [ ] **Firewall configurato**
  - Porta 8001 (backend): Solo da frontend
  - Porta 3000 (frontend): Solo da load balancer
  - Porta 5432 (database): Solo da backend

### Backup Automatico
- [ ] **Cron job backup database**
  ```bash
  # Aggiungi a crontab
  0 2 * * * pg_dump cantieri > /backup/cantieri_$(date +\%Y\%m\%d).sql
  ```

- [ ] **Rotazione log**
  ```bash
  # Logrotate per security.log
  /var/log/cms/security.log {
      daily
      rotate 30
      compress
      delaycompress
      missingok
      notifempty
  }
  ```

---

## 🚨 Rollback Plan

### In caso di problemi critici:

1. **Stop servizi**
   ```bash
   systemctl stop cms-backend cms-frontend
   ```

2. **Restore database**
   ```bash
   psql -d cantieri < backup_YYYYMMDD_HHMMSS.sql
   ```

3. **Restore codice**
   ```bash
   git checkout HEAD~1  # Torna al commit precedente
   ```

4. **Restart servizi**
   ```bash
   systemctl start cms-backend cms-frontend
   ```

---

## ✅ Sign-off

### Team Tecnico
- [ ] **Sviluppatore**: _________________ Data: _______
- [ ] **DevOps**: _________________ Data: _______
- [ ] **Security**: _________________ Data: _______

### Team Business
- [ ] **Product Owner**: _________________ Data: _______
- [ ] **QA**: _________________ Data: _______

### Note Finali
```
Deployment completato con successo: _______________

Problemi riscontrati: _____________________________

Azioni di follow-up: ______________________________

Prossima revisione: _______________________________
```

---

**🎉 Deployment Completato!**

*Ricorda di monitorare il sistema nelle prime 24-48 ore per identificare eventuali problemi.*
