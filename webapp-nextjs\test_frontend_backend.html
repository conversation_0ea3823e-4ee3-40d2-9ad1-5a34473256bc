<!DOCTYPE html>
<html lang="it">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Frontend-Backend Connection</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test Connessione Frontend-Backend</h1>
    
    <div class="test-section">
        <h2>Test 1: Connessione Base</h2>
        <button onclick="testConnection()">Test Connessione</button>
        <div id="connection-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 2: Login Admin</h2>
        <button onclick="testAdminLogin()">Test Login Admin</button>
        <div id="admin-login-result"></div>
    </div>

    <div class="test-section">
        <h2>Test 3: Login Cantiere</h2>
        <button onclick="testCantiereLogin()">Test Login Cantiere</button>
        <div id="cantiere-login-result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8001';

        function showResult(elementId, success, message, data = null) {
            const element = document.getElementById(elementId);
            element.className = success ? 'success' : 'error';
            element.innerHTML = `
                <p><strong>${success ? 'SUCCESS' : 'ERROR'}:</strong> ${message}</p>
                ${data ? `<pre>${JSON.stringify(data, null, 2)}</pre>` : ''}
            `;
        }

        async function testConnection() {
            try {
                console.log('Testing connection to:', API_BASE);
                const response = await fetch(`${API_BASE}/docs`);
                
                if (response.ok) {
                    showResult('connection-result', true, 'Backend raggiungibile');
                } else {
                    showResult('connection-result', false, `Backend risponde con status: ${response.status}`);
                }
            } catch (error) {
                console.error('Connection test error:', error);
                showResult('connection-result', false, `Errore di connessione: ${error.message}`);
            }
        }

        async function testAdminLogin() {
            try {
                console.log('Testing admin login...');
                
                const formData = new FormData();
                formData.append('username', 'admin');
                formData.append('password', 'admin');

                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();
                console.log('Admin login response:', data);

                if (response.ok) {
                    showResult('admin-login-result', true, 'Login admin riuscito', data);
                } else {
                    showResult('admin-login-result', false, `Login admin fallito: ${response.status}`, data);
                }
            } catch (error) {
                console.error('Admin login test error:', error);
                showResult('admin-login-result', false, `Errore login admin: ${error.message}`);
            }
        }

        async function testCantiereLogin() {
            try {
                console.log('Testing cantiere login...');
                
                const response = await fetch(`${API_BASE}/api/auth/login/cantiere`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        codice_univoco: 'TEST123',
                        password: 'test123'
                    })
                });

                const data = await response.json();
                console.log('Cantiere login response:', data);

                if (response.ok) {
                    showResult('cantiere-login-result', true, 'Login cantiere riuscito', data);
                } else {
                    showResult('cantiere-login-result', false, `Login cantiere fallito: ${response.status}`, data);
                }
            } catch (error) {
                console.error('Cantiere login test error:', error);
                showResult('cantiere-login-result', false, `Errore login cantiere: ${error.message}`);
            }
        }

        // Test automatico all'avvio
        window.onload = function() {
            console.log('Page loaded, running automatic tests...');
            testConnection();
        };
    </script>
</body>
</html>
