from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import Any
import os

from backend.database import get_db
from backend.models.user import User
from backend.models.rapporto_generale_collaudo import RapportoGeneraleCollaudo
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.services.pdf_cei_64_8_service import PDFCEI64_8Service
from backend.api.auth import get_current_active_user

router = APIRouter()

@router.get("/{cantiere_id}/rapporti/{rapporto_id}/pdf")
def generate_rapporto_generale_pdf(
    cantiere_id: int,
    rapporto_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Genera il PDF del Rapporto Generale di Collaudo.
    """
    # Verifica che il rapporto esista e appartenga al cantiere
    rapporto = db.query(RapportoGeneraleCollaudo).filter(
        RapportoGeneraleCollaudo.id_rapporto == rapporto_id,
        RapportoGeneraleCollaudo.id_cantiere == cantiere_id
    ).first()
    
    if not rapporto:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Rapporto con ID {rapporto_id} non trovato nel cantiere {cantiere_id}"
        )
    
    try:
        # Crea il servizio PDF
        pdf_service = PDFCEI64_8Service(db)
        
        # Genera il PDF
        pdf_path = pdf_service.generate_rapporto_generale_pdf(rapporto_id)
        
        # Verifica che il file sia stato creato
        if not os.path.exists(pdf_path):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Errore nella generazione del PDF"
            )
        
        # Restituisce il file PDF
        filename = f"rapporto_generale_{rapporto.numero_rapporto}.pdf"
        return FileResponse(
            path=pdf_path,
            filename=filename,
            media_type='application/pdf'
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella generazione del PDF: {str(e)}"
        )

@router.get("/{cantiere_id}/certificazioni/{certificazione_id}/pdf")
def generate_certificato_singolo_pdf(
    cantiere_id: int,
    certificazione_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Genera il PDF del Certificato di Prova Singolo Cavo.
    """
    # Verifica che la certificazione esista e appartenga al cantiere
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()
    
    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )
    
    try:
        # Crea il servizio PDF
        pdf_service = PDFCEI64_8Service(db)
        
        # Genera il PDF
        pdf_path = pdf_service.generate_certificato_singolo_pdf(certificazione_id)
        
        # Verifica che il file sia stato creato
        if not os.path.exists(pdf_path):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Errore nella generazione del PDF"
            )
        
        # Restituisce il file PDF
        filename = f"certificato_singolo_{certificazione.numero_certificato}.pdf"
        return FileResponse(
            path=pdf_path,
            filename=filename,
            media_type='application/pdf'
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella generazione del PDF: {str(e)}"
        )

@router.get("/{cantiere_id}/rapporti/{rapporto_id}/pdf-completo")
def generate_rapporto_completo_pdf(
    cantiere_id: int,
    rapporto_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Genera un PDF completo con il Rapporto Generale + tutti i Certificati Singoli.
    """
    # Verifica che il rapporto esista e appartenga al cantiere
    rapporto = db.query(RapportoGeneraleCollaudo).filter(
        RapportoGeneraleCollaudo.id_rapporto == rapporto_id,
        RapportoGeneraleCollaudo.id_cantiere == cantiere_id
    ).first()
    
    if not rapporto:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Rapporto con ID {rapporto_id} non trovato nel cantiere {cantiere_id}"
        )
    
    try:
        # Crea il servizio PDF
        pdf_service = PDFCEI64_8Service(db)
        
        # Genera il rapporto generale
        rapporto_pdf_path = pdf_service.generate_rapporto_generale_pdf(rapporto_id)
        
        # Recupera tutte le certificazioni associate al rapporto
        certificazioni = db.query(CertificazioneCavo).filter(
            CertificazioneCavo.id_rapporto == rapporto_id
        ).all()
        
        # Se ci sono certificazioni, genera anche i certificati singoli
        certificati_paths = []
        for certificazione in certificazioni:
            try:
                cert_path = pdf_service.generate_certificato_singolo_pdf(certificazione.id_certificazione)
                certificati_paths.append(cert_path)
            except Exception as e:
                # Log dell'errore ma continua con gli altri certificati
                print(f"Errore generando certificato {certificazione.numero_certificato}: {e}")
        
        # Per ora restituiamo solo il rapporto generale
        # In futuro si potrebbe implementare la fusione di più PDF
        filename = f"rapporto_completo_{rapporto.numero_rapporto}.pdf"
        return FileResponse(
            path=rapporto_pdf_path,
            filename=filename,
            media_type='application/pdf'
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella generazione del PDF completo: {str(e)}"
        )

@router.get("/test-pdf")
def test_pdf_generation(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Endpoint di test per verificare la generazione PDF.
    """
    try:
        # Crea il servizio PDF
        pdf_service = PDFCEI64_8Service(db)
        
        # Verifica che le dipendenze siano installate
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate
        
        return {
            "message": "Servizio PDF CEI 64-8 funzionante",
            "reportlab_version": "OK",
            "service_initialized": "OK"
        }
        
    except ImportError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Dipendenze PDF mancanti: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel servizio PDF: {str(e)}"
        )
