(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3986],{3469:(e,a,i)=>{Promise.resolve().then(i.bind(i,27134))},27134:(e,a,i)=>{"use strict";i.r(a),i.d(a,{default:()=>eB});var t=i(95155),s=i(12115),l=i(35695),n=i(66695),r=i(55365),o=i(40283),c=i(17522),d=i(13587),m=i(25731),x=i(30285),u=i(26126),p=i(47262),h=i(63743),b=i(85127),g=i(62523),v=i(59409),j=i(20547),f=i(59434);let N=j.bL,y=j.l9,C=s.forwardRef((e,a)=>{let{className:i,align:s="center",sideOffset:l=4,...n}=e;return(0,t.jsx)(j.ZL,{children:(0,t.jsx)(j.<PERSON>,{ref:a,align:s,sideOffset:l,className:(0,f.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",i),...n})})});C.displayName=j.UC.displayName;var w=i(21492),_=i(39881),A=i(58832),z=i(54416),S=i(66932),k=i(52278),E=i(42355),I=i(13052),T=i(12767);function O(e){let{data:a=[],columns:i=[],loading:l=!1,emptyMessage:r="Nessun dato disponibile",onFilteredDataChange:o,renderRow:c,className:d,pagination:m=!0,defaultRowsPerPage:p=25}=e,[h,g]=(0,s.useState)({key:null,direction:null}),[j,O]=(0,s.useState)({}),[D,L]=(0,s.useState)({}),[B,M]=(0,s.useState)(0),[R,P]=(0,s.useState)(p),$=e=>{let t=i.find(a=>a.field===e);return(null==t?void 0:t.getFilterValue)?[...new Set(a.map(e=>t.getFilterValue(e)).filter(Boolean))].sort():[...new Set(a.map(a=>a[e]).filter(Boolean))].sort()},V=(0,s.useMemo)(()=>{let e=[...a];return Object.entries(j).forEach(a=>{let[t,s]=a;!s.value||Array.isArray(s.value)&&0===s.value.length||"string"==typeof s.value&&""===s.value.trim()||(e=e.filter(e=>{let a=i.find(e=>e.field===t),l=(null==a?void 0:a.getFilterValue)?a.getFilterValue(e):e[t];if("select"===s.type)return(Array.isArray(s.value)?s.value:[s.value]).includes(l);if("text"===s.type){let e=s.value.toLowerCase(),a=String(l||"").toLowerCase();return"equals"===s.operator?a===e:a.includes(e)}if("number"===s.type){let e=parseFloat(l),a=parseFloat(s.value);if(isNaN(e)||isNaN(a))return!1;switch(s.operator){case"equals":default:return e===a;case"gt":return e>a;case"lt":return e<a;case"gte":return e>=a;case"lte":return e<=a}}return!0}))}),h.key&&h.direction&&e.sort((e,a)=>{let i=e[h.key],t=a[h.key];if(null==i&&null==t)return 0;if(null==i)return"asc"===h.direction?-1:1;if(null==t)return"asc"===h.direction?1:-1;let s=parseFloat(i),l=parseFloat(t),n=!isNaN(s)&&!isNaN(l),r=0;return r=n?s-l:String(i).localeCompare(String(t)),"asc"===h.direction?r:-r}),e},[a,j,h]),U=(0,s.useMemo)(()=>{if(!m)return V;let e=B*R,a=e+R;return V.slice(e,a)},[V,B,R,m]);(0,s.useEffect)(()=>{M(0)},[j]);let G=Math.ceil(V.length/R),J=B*R+1,q=Math.min((B+1)*R,V.length);(0,s.useEffect)(()=>{o&&o(V)},[V]);let Z=e=>{let a=i.find(a=>a.field===e);null!=a&&a.disableSort||g(a=>{if(a.key===e){if("asc"===a.direction)return{key:e,direction:"desc"};if("desc"===a.direction)return{key:null,direction:null}}return{key:e,direction:"asc"}})},W=(e,a)=>{O(i=>({...i,[e]:{...i[e],...a}}))},K=e=>{O(a=>{let i={...a};return delete i[e],i})},H=e=>h.key!==e?(0,t.jsx)(w.A,{className:"h-3 w-3"}):"asc"===h.direction?(0,t.jsx)(_.A,{className:"h-3 w-3"}):"desc"===h.direction?(0,t.jsx)(A.A,{className:"h-3 w-3"}):(0,t.jsx)(w.A,{className:"h-3 w-3"}),Q=Object.keys(j).length>0;return l?(0,t.jsx)(n.Zp,{className:d,children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"text-center",children:"Caricamento..."})})}):(0,t.jsxs)("div",{className:d,children:[Q&&(0,t.jsxs)("div",{className:"mb-4 flex flex-wrap gap-2 items-center",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Filtri attivi:"}),Object.entries(j).map(e=>{let[a,s]=e,l=i.find(e=>e.field===a);if(!l)return null;let n=Array.isArray(s.value)?s.value.join(", "):String(s.value);return(0,t.jsxs)(u.E,{variant:"secondary",className:"gap-1",children:[l.headerName,": ",n,(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-auto p-0 hover:bg-transparent",onClick:()=>K(a),children:(0,t.jsx)(z.A,{className:"h-3 w-3"})})]},a)}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>{O({})},className:"h-6 px-2 text-xs",children:"Pulisci tutti"})]}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-0",children:(0,t.jsxs)(b.XI,{children:[(0,t.jsx)(b.A0,{children:(0,t.jsx)(b.Hj,{className:"bg-mariner-50 hover:bg-mariner-50",children:i.map(e=>(0,t.jsx)(b.nd,{className:(0,f.cn)("font-semibold text-mariner-900 border-b border-mariner-200","center"===e.align&&"text-center","right"===e.align&&"text-right"),style:{width:e.width,...e.headerStyle},children:e.renderHeader?e.renderHeader():(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsx)("span",{className:"truncate",children:e.headerName}),(0,t.jsxs)("div",{className:"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity duration-200",children:[!e.disableSort&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"h-4 w-4 p-0 hover:bg-mariner-100",onClick:()=>Z(e.field),children:H(e.field)}),!e.disableFilter&&(0,t.jsxs)(N,{open:D[e.field],onOpenChange:a=>L(i=>({...i,[e.field]:a})),children:[(0,t.jsx)(y,{asChild:!0,children:(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:(0,f.cn)("h-4 w-4 p-0 hover:bg-mariner-100",j[e.field]&&"text-mariner-600 opacity-100"),children:(0,t.jsx)(S.A,{className:"h-2.5 w-2.5"})})}),(0,t.jsx)(C,{className:"w-64",align:"start",children:(0,t.jsx)(F,{column:e,data:a,currentFilter:j[e.field],onFilterChange:a=>W(e.field,a),onClearFilter:()=>K(e.field),getUniqueValues:()=>$(e.field)})})]})]})]}),j[e.field]&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 h-2 w-2 bg-mariner-600 rounded-full"})]})},e.field))})}),(0,t.jsx)(b.BF,{children:U.length>0?U.map((e,a)=>c?c(e,B*R+a):(0,t.jsx)(b.Hj,{className:"hover:bg-mariner-50 border-b border-mariner-100",children:i.map(a=>(0,t.jsx)(b.nA,{className:(0,f.cn)("py-2 px-4","center"===a.align&&"text-center","right"===a.align&&"text-right"),style:a.cellStyle,children:a.renderCell?a.renderCell(e):e[a.field]},a.field))},a)):(0,t.jsx)(b.Hj,{children:(0,t.jsx)(b.nA,{colSpan:i.length,className:"text-center py-8 text-muted-foreground",children:r})})})]})})}),m&&V.length>0&&(0,t.jsxs)("div",{className:"flex items-center justify-between mt-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:"Righe per pagina:"}),(0,t.jsxs)(v.l6,{value:R.toString(),onValueChange:e=>{P(Number(e)),M(0)},children:[(0,t.jsx)(v.bq,{className:"w-20",children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"10",children:"10"}),(0,t.jsx)(v.eb,{value:"25",children:"25"}),(0,t.jsx)(v.eb,{value:"50",children:"50"}),(0,t.jsx)(v.eb,{value:"100",children:"100"}),(0,t.jsx)(v.eb,{value:V.length.toString(),children:"Tutto"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-sm text-muted-foreground",children:V.length>0?"".concat(J,"-").concat(q," di ").concat(V.length):"0 di 0"}),(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>M(0),disabled:0===B,className:"h-8 w-8 p-0",children:(0,t.jsx)(k.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>M(e=>Math.max(0,e-1)),disabled:0===B,className:"h-8 w-8 p-0",children:(0,t.jsx)(E.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>M(e=>Math.min(G-1,e+1)),disabled:B>=G-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>M(G-1),disabled:B>=G-1,className:"h-8 w-8 p-0",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})})]})]})]})]})}function F(e){let{column:a,currentFilter:i,onFilterChange:l,onClearFilter:n,getUniqueValues:r}=e,[o,c]=(0,s.useState)((null==i?void 0:i.value)||""),[d,m]=(0,s.useState)((null==i?void 0:i.operator)||"contains"),u=r(),h="number"!==a.dataType&&u.length<=20,b="number"===a.dataType,j=()=>{h?l({type:"select",value:Array.isArray(o)?o:[o]}):b?l({type:"number",value:o,operator:d}):l({type:"text",value:o,operator:d})};return(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"font-medium text-sm",children:["Filtra ",a.headerName]}),h?(0,t.jsx)("div",{className:"space-y-2 max-h-48 overflow-y-auto",children:u.map(e=>(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.S,{id:"filter-".concat(e),checked:Array.isArray(o)?o.includes(e):o===e,onCheckedChange:a=>{Array.isArray(o)?c(a?[...o,e]:o.filter(a=>a!==e)):c(a?[e]:[])}}),(0,t.jsx)("label",{htmlFor:"filter-".concat(e),className:"text-sm",children:e})]},e))}):(0,t.jsxs)("div",{className:"space-y-2",children:[b&&(0,t.jsxs)(v.l6,{value:d,onValueChange:m,children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"equals",children:"Uguale a"}),(0,t.jsx)(v.eb,{value:"gt",children:"Maggiore di"}),(0,t.jsx)(v.eb,{value:"lt",children:"Minore di"}),(0,t.jsx)(v.eb,{value:"gte",children:"Maggiore o uguale"}),(0,t.jsx)(v.eb,{value:"lte",children:"Minore o uguale"})]})]}),!b&&(0,t.jsxs)(v.l6,{value:d,onValueChange:m,children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(v.eb,{value:"equals",children:"Uguale a"})]})]}),(0,t.jsx)(g.p,{placeholder:"Cerca ".concat(a.headerName.toLowerCase(),"..."),value:o,onChange:e=>c(e.target.value),onKeyDown:e=>"Enter"===e.key&&j()})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.$,{size:"sm",onClick:j,children:"Applica"}),(0,t.jsx)(x.$,{size:"sm",variant:"outline",onClick:n,children:"Pulisci"})]})]})}var D=i(47924),L=i(28826),B=i(18979),M=i(27882);function R(e){let{cavi:a=[],onFilteredDataChange:i,loading:l=!1,selectionEnabled:r=!1,onSelectionToggle:o,selectedCount:c=0,totalCount:d=0}=e,[m,u]=(0,s.useState)(""),[p,h]=(0,s.useState)("contains"),b=e=>e?e.toString().toLowerCase().trim():"",j=e=>{let a=e.match(/^([A-Z]+)(\d+)([A-Z]*)$/);return a?{prefix:a[1],number:a[2],suffix:a[3]||""}:{prefix:"",number:e,suffix:""}},f=(0,s.useCallback)((e,a,i)=>{let t=b(a);if(!t)return!0;let s=b(e.id_cavo),{prefix:l,number:n,suffix:r}=j(e.id_cavo||""),o=b(e.tipologia),c=b(e.formazione||e.sezione),d=b(e.utility),m=b(e.sistema),x=b(e.da||e.ubicazione_partenza),u=b(e.a||e.ubicazione_arrivo),p=b(e.utenza_partenza),h=b(e.utenza_arrivo),g=[s,l,n,r,o,c,d,m,x,u,p,h,b(e.id_bobina),"BOBINA_VUOTA"===e.id_bobina?"bobina vuota":null===e.id_bobina?"":b(e.id_bobina)],v=[{value:e.metri_teorici,name:"metri_teorici"},{value:e.metratura_reale||e.metri_posati,name:"metratura_reale"},{value:parseFloat(c),name:"formazione"}],f=t.match(/^([><=]+)(\d+(?:\.\d+)?)$/);if(f){let e=f[1],a=parseFloat(f[2]);return v.some(i=>{if(null==i.value||isNaN(i.value))return!1;switch(e){case">":return i.value>a;case">=":return i.value>=a;case"<":return i.value<a;case"<=":return i.value<=a;case"=":return i.value===a;default:return!1}})}let N=parseFloat(t);return!!(!isNaN(N)&&v.some(e=>null!=e.value&&!isNaN(e.value)&&e.value===N))||(i?g.some(e=>e===t):g.some(e=>e.includes(t)))},[]),N=(0,s.useCallback)(()=>{if(!m.trim()){null==i||i(a);return}let e=m.split(",").map(e=>e.trim()).filter(e=>e.length>0),t=[];t="equals"===p?1===e.length?a.filter(a=>f(a,e[0],!0)):a.filter(a=>e.every(e=>f(a,e,!0))):a.filter(a=>e.some(e=>f(a,e,!1))),null==i||i(t)},[m,p,a,f]);(0,s.useEffect)(()=>{N()},[m,p,a,f]);let y=e=>{u(e)},C=()=>{u(""),h("contains")};return(0,t.jsx)(n.Zp,{className:"mb-1",children:(0,t.jsxs)(n.Wu,{className:"p-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"flex-1 relative",children:[(0,t.jsx)(D.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(g.p,{placeholder:"Cerca per ID, sistema, utility, tipologia, ubicazione...",value:m,onChange:e=>y(e.target.value),disabled:l,className:"pl-10 pr-10 h-8","aria-label":"Campo di ricerca intelligente per cavi"}),m&&(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0",onClick:C,children:(0,t.jsx)(z.A,{className:"h-2.5 w-2.5"})})]}),(0,t.jsx)("div",{className:"w-32",children:(0,t.jsxs)(v.l6,{value:p,onValueChange:e=>h(e),children:[(0,t.jsx)(v.bq,{className:"h-8",children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"contains",children:"Contiene"}),(0,t.jsx)(v.eb,{value:"equals",children:"Uguale a"})]})]})}),m&&(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:C,disabled:l,className:"transition-all duration-200 hover:scale-105","aria-label":"Pulisci ricerca",children:[(0,t.jsx)(z.A,{className:"h-4 w-4 mr-1"}),"Pulisci"]}),o&&d>0&&(0,t.jsxs)(x.$,{variant:r?"default":"outline",size:"sm",onClick:o,className:"flex items-center gap-2 transition-all duration-200 hover:scale-105","aria-label":r?"Disabilita modalit\xe0 selezione":"Abilita modalit\xe0 selezione",children:[r?(0,t.jsx)(L.A,{className:"h-4 w-4"}):(0,t.jsx)(B.A,{className:"h-4 w-4"}),r?"Disabilita Selezione":"Abilita Selezione"]}),r&&c>0&&(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>{},className:"flex items-center gap-2 transition-all duration-200 hover:scale-105 text-orange-600 border-orange-300 hover:bg-orange-50","aria-label":"Deseleziona tutti i ".concat(c," cavi selezionati"),children:[(0,t.jsx)(M.A,{className:"h-4 w-4"}),"Deseleziona Tutto (",c,")"]})]}),m&&(0,t.jsx)("div",{className:"mt-0.5 text-xs text-muted-foreground",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCA1"}),(0,t.jsx)("span",{children:"• Virgole per multipli"}),(0,t.jsx)("span",{children:"• >100, <=50 per numeri"})]})})]})})}function P(e){let{text:a,maxLength:i=20,className:l=""}=e,[n,r]=(0,s.useState)(!1),[o,c]=(0,s.useState)({x:0,y:0});if(!a)return(0,t.jsx)("span",{className:"text-gray-400",children:"-"});let d=a.length>i,m=d?"".concat(a.substring(0,i),"..."):a;return d?(0,t.jsxs)("div",{className:"relative inline-block",children:[(0,t.jsx)("span",{className:"cursor-help ".concat(l),style:{textOverflow:"ellipsis",whiteSpace:"nowrap",overflow:"hidden",maxWidth:"100%",display:"inline-block"},onMouseEnter:e=>{c({x:e.clientX,y:e.clientY}),r(!0)},onMouseMove:e=>{c({x:e.clientX,y:e.clientY})},onMouseLeave:()=>r(!1),title:a,children:m}),n&&(0,t.jsxs)("div",{className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none",style:{top:o.y-40,left:o.x-150,maxWidth:"300px",wordWrap:"break-word",whiteSpace:"normal"},children:[a,(0,t.jsx)("div",{className:"absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0",style:{borderLeft:"5px solid transparent",borderRight:"5px solid transparent",borderTop:"5px solid #1f2937"}})]})]}):(0,t.jsx)("span",{className:l,children:a})}var $=i(66474),V=i(40646),U=i(14186),G=i(85339),J=i(85690),q=i(81284),Z=i(69037);function W(e){let{cavi:a=[],loading:i=!1,selectionEnabled:l=!1,selectedCavi:n=[],onSelectionChange:r,onStatusAction:o,onContextMenuAction:c}=e,[d,m]=(0,s.useState)(a),[g,v]=(0,s.useState)(a),[j,f]=(0,s.useState)(l);(0,s.useEffect)(()=>{m(a),v(a)},[a]);let N=e=>{r&&r(e?g.map(e=>e.id_cavo):[])},y=(e,a)=>{r&&r(a?[...n,e]:n.filter(a=>a!==e))},C=async()=>{try{let e=await fetch("/api/cavi/export",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:n,cantiereId:1})});if(e.ok){let a=await e.blob(),i=window.URL.createObjectURL(a),t=document.createElement("a");t.href=i,t.download="cavi_export_".concat(new Date().toISOString().split("T")[0],".csv"),document.body.appendChild(t),t.click(),window.URL.revokeObjectURL(i),document.body.removeChild(t)}else{let a=await e.json();alert("Errore durante l'esportazione: ".concat(a.error))}}catch(e){alert("Errore durante l'esportazione")}},w=async()=>{let e=prompt("Inserisci il nuovo stato (Da installare, In corso, Installato):");if(e)try{let a=await fetch("/api/cavi/bulk-status",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:n,cantiereId:1,newStatus:e})}),i=await a.json();i.success?alert(i.message):alert("Errore: ".concat(i.error))}catch(e){alert("Errore durante il cambio stato")}},_=()=>{alert("Assegnazione comanda per ".concat(n.length," cavi"))},A=async()=>{if(confirm("Sei sicuro di voler eliminare ".concat(n.length," cavi?")))try{let e=await fetch("/api/cavi/bulk-delete",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({selectedIds:n,cantiereId:1})}),a=await e.json();a.success?alert(a.message):alert("Errore: ".concat(a.error))}catch(e){alert("Errore durante l'eliminazione")}},z=(0,s.useMemo)(()=>{let e=[{field:"id_cavo",headerName:"ID",dataType:"text",width:70,align:"left",renderCell:e=>(0,t.jsx)("span",{className:"font-semibold text-mariner-900",children:e.id_cavo})},{field:"sistema",headerName:"Sistema",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(P,{text:e.sistema||"",maxLength:8})},{field:"utility",headerName:"Utility",dataType:"text",width:80,renderCell:e=>(0,t.jsx)(P,{text:e.utility||"",maxLength:8})},{field:"tipologia",headerName:"Tipologia",dataType:"text",width:100,renderCell:e=>(0,t.jsx)(P,{text:e.tipologia||"",maxLength:12})},{field:"formazione",headerName:"Form.",dataType:"text",align:"left",width:60,renderCell:e=>e.formazione||e.sezione},{field:"metri_teorici",headerName:"M.Teor.",dataType:"number",align:"left",width:70,renderCell:e=>e.metri_teorici?e.metri_teorici.toFixed(1):"0"},{field:"metri_posati",headerName:"M.Reali",dataType:"number",align:"left",width:70,renderCell:e=>{let a=e.metri_posati||e.metratura_reale||0;return a?a.toFixed(1):"0"}},{field:"ubicazione_partenza",headerName:"Da",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(P,{text:e.da||e.ubicazione_partenza||"",maxLength:18})},{field:"ubicazione_arrivo",headerName:"A",dataType:"text",width:140,renderCell:e=>(0,t.jsx)(P,{text:e.a||e.ubicazione_arrivo||"",maxLength:18})},{field:"id_bobina",headerName:"Bobina",dataType:"text",width:80,align:"center",renderCell:e=>I(e)},{field:"stato_installazione",headerName:"Stato",dataType:"text",align:"left",width:120,disableSort:!0,getFilterValue:e=>S(e),renderCell:e=>T(e)},{field:"collegamenti",headerName:"Collegamenti",dataType:"text",align:"left",width:180,disableSort:!0,getFilterValue:e=>k(e),renderCell:e=>F(e)},{field:"certificato",headerName:"Certificato",dataType:"text",align:"left",width:130,disableSort:!0,getFilterValue:e=>E(e),renderCell:e=>D(e)}];return j&&e.unshift({field:"selection",headerName:"",disableFilter:!0,disableSort:!0,width:50,align:"left",renderHeader:()=>(0,t.jsx)(p.S,{checked:n.length===g.length&&g.length>0,onCheckedChange:N}),renderCell:e=>(0,t.jsx)(p.S,{checked:n.includes(e.id_cavo),onCheckedChange:a=>y(e.id_cavo,a),onClick:e=>e.stopPropagation()})}),e},[j,n,g,N,y]),S=e=>{let a=e.metri_posati||e.metratura_reale||0,i=e.stato_installazione||"Da installare",t=e.comanda_posa,s=e.comanda_partenza,l=e.comanda_arrivo,n=e.comanda_certificazione,r=t||s||l||n;return r&&"In corso"===i?"In corso (".concat(r,")"):"Installato"===i||a>0?"Installato":i},k=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=e.collegamento||e.collegamenti||0;if(!a)return"Non disponibile";switch(i){case 0:return"Collega";case 1:case 2:return"Completa collegamento";case 3:return"Scollega";default:return"Gestisci collegamenti"}},E=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato;return a?i?"Genera PDF":"Certifica":"Non disponibile"},I=e=>{let a=e.id_bobina;if(!a||"N/A"===a)return(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 text-xs font-medium ".concat(h.mU.NEUTRAL.text_light),children:"-"});if("BOBINA_VUOTA"===a||"VUOTA"===a)return(0,t.jsxs)("button",{className:"cavi-table-button bobina",onClick:a=>{a.stopPropagation(),null==o||o(e,"modify_reel")},title:"Bobina Vuota - Clicca per modificare",children:[(0,t.jsx)("span",{children:"Vuota"}),(0,t.jsx)($.A,{className:"icon w-3 h-3 opacity-70"})]});let i=a,s=a.match(/_B(.+)$/);return s||(s=a.match(/_b(.+)$/))||(s=a.match(/c\d+_[bB](\d+)$/))?i=s[1]:(s=a.match(/(\d+)$/))&&(i=s[1]),(0,t.jsxs)("button",{className:"cavi-table-button bobina",onClick:a=>{a.stopPropagation(),null==o||o(e,"modify_reel")},title:"Bobina ".concat(i," - Clicca per modificare"),children:[(0,t.jsx)("span",{children:i}),(0,t.jsx)($.A,{className:"icon w-3 h-3 opacity-70"})]})},T=e=>{let a=(e.metri_posati||e.metratura_reale||0)>0,i=e.stato_installazione||"Da installare",s=e.comanda_posa,l=e.comanda_partenza,n=e.comanda_arrivo,r=e.comanda_certificazione,c=s||l||n||r,d=i,m=(0,h.Tr)(i);c&&"In corso"===i&&(d=c,m=(0,h.Tr)("IN_CORSO")),a&&"Installato"!==i&&(d="Installato",m=(0,h.Tr)("INSTALLATO"));let x=e=>{switch(e.toLowerCase()){case"installato":return(0,t.jsx)(V.A,{className:"icon w-3 h-3"});case"in corso":return(0,t.jsx)(U.A,{className:"icon w-3 h-3"});case"da installare":return(0,t.jsx)(G.A,{className:"icon w-3 h-3"});default:return c?(0,t.jsx)(J.A,{className:"icon w-3 h-3"}):(0,t.jsx)(G.A,{className:"icon w-3 h-3"})}};return"da installare"!==i.toLowerCase()||a?(0,t.jsxs)("span",{className:"inline-flex items-center gap-1 px-2 py-1 rounded text-xs font-medium ".concat(m.text," ").concat(m.bg," ").concat(m.border),title:c?"Comanda attiva: ".concat(c):"Stato: ".concat(d),children:[x(d),(0,t.jsx)("span",{children:d})]}):(0,t.jsxs)("button",{className:"cavi-table-button stato",onClick:a=>{a.stopPropagation(),null==o||o(e,"insert_meters")},title:"Clicca per inserire metri posati",children:[x(d),(0,t.jsx)("span",{children:d})]})},F=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=e.collegamento||e.collegamenti||0;(0,h.Nj)();let s=(0,h.NM)();if(!a)return(0,t.jsxs)("span",{className:s.text,title:"Collegamento disponibile solo per cavi installati",children:[(0,t.jsx)(q.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"Non disponibile"})]});let l=(a,i,s)=>(0,t.jsxs)("button",{className:"cavi-table-button collegamenti",onClick:a=>{a.stopPropagation(),null==o||o(e,i)},title:"Clicca per ".concat(a.toLowerCase()),children:[(0,t.jsx)("span",{className:"icon text-sm mr-1",children:s}),(0,t.jsx)("span",{children:a})]});switch(i){case 0:return l("Collega","connect_cable","⚪⚪");case 1:return l("Completa collegamento","connect_arrival","\uD83D\uDFE2⚪");case 2:return l("Completa collegamento","connect_departure","⚪\uD83D\uDFE2");case 3:return l("Scollega","disconnect_cable","\uD83D\uDFE2\uD83D\uDFE2");default:return l("Gestisci collegamenti","manage_connections","⚙️")}},D=e=>{let a=e.metri_posati>0||e.metratura_reale>0,i=!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato,s=(0,h.NM)();return a?i?(0,t.jsxs)("button",{className:"cavi-table-button certificazioni",onClick:a=>{a.stopPropagation(),null==o||o(e,"generate_pdf")},title:"Certificato - Clicca per generare PDF",children:[(0,t.jsx)(V.A,{className:"icon w-3 h-3"}),(0,t.jsx)("span",{children:"PDF"})]}):(0,t.jsxs)("button",{className:"cavi-table-button certificazioni",onClick:a=>{a.stopPropagation(),null==o||o(e,"create_certificate")},title:"Clicca per certificare il cavo",children:[(0,t.jsx)(Z.A,{className:"icon w-3 h-3"}),(0,t.jsx)("span",{children:"Certifica"})]}):(0,t.jsxs)("span",{className:s.text,title:"Certificazione disponibile solo per cavi installati",children:[(0,t.jsx)(q.A,{className:"w-3 h-3"}),(0,t.jsx)("span",{children:"Non disponibile"})]})};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(R,{cavi:a,onFilteredDataChange:e=>{m(e)},loading:i,selectionEnabled:j,onSelectionToggle:()=>{f(!j)}}),(0,t.jsx)(O,{data:d,columns:z,loading:i,emptyMessage:"Nessun cavo disponibile",onFilteredDataChange:e=>{v(e)},renderRow:(e,a)=>{let i=n.includes(e.id_cavo);return(0,t.jsx)(b.Hj,{className:"\n          ".concat(i?"bg-blue-50 border-blue-200":"bg-white","\n          hover:bg-blue-50 hover:border-blue-200 hover:shadow-sm\n          cursor-pointer border-b border-gray-200\n          transition-all duration-200 ease-in-out\n          ").concat(i?"ring-1 ring-blue-300":"","\n        "),onClick:()=>j&&y(e.id_cavo,!i),onContextMenu:a=>{a.preventDefault(),null==c||c(e,"context_menu")},children:z.map(a=>(0,t.jsx)(b.nA,{className:"\n              py-2 px-2 text-sm text-left\n              ".concat(i?"text-blue-900":"text-gray-900","\n              transition-colors duration-200\n            "),style:{width:a.width,...a.cellStyle},onClick:e=>{["stato_installazione","collegamenti","certificato"].includes(a.field)&&e.stopPropagation()},children:a.renderCell?a.renderCell(e):e[a.field]||(0,t.jsx)("span",{className:"text-gray-400",children:"-"})},a.field))},e.id_cavo)}}),j&&n.length>0&&(0,t.jsx)("div",{className:"sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg z-10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between p-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)(u.E,{variant:"secondary",className:"bg-mariner-100 text-mariner-800",children:[n.length," cavi selezionati"]}),(0,t.jsx)(x.$,{variant:"outline",size:"sm",onClick:()=>N(!1),className:"text-xs",children:"Deseleziona tutto"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>C(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCA"}),(0,t.jsx)("span",{children:"Esporta"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>w(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDD04"}),(0,t.jsx)("span",{children:"Cambia Stato"})]}),(0,t.jsxs)(x.$,{variant:"outline",size:"sm",onClick:()=>_(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDCCB"}),(0,t.jsx)("span",{children:"Assegna Comanda"})]}),(0,t.jsxs)(x.$,{variant:"destructive",size:"sm",onClick:()=>A(),className:"flex items-center space-x-1",children:[(0,t.jsx)("span",{children:"\uD83D\uDDD1️"}),(0,t.jsx)("span",{children:"Elimina"})]})]})]})})]})}var K=i(72713),H=i(3493),Q=i(1243),Y=i(71539),X=i(37108),ee=i(47650);let ea=e=>{let{content:a,children:i,position:l="auto",delay:n=500,className:r="",disabled:o=!1,maxWidth:c=250}=e,[d,m]=(0,s.useState)(!1),[x,u]=(0,s.useState)(null),p=(0,s.useRef)(null),h=(0,s.useRef)(null),b=(0,s.useRef)(null),g=()=>{if(!p.current)return null;let e=p.current.getBoundingClientRect(),a=window.innerWidth,i=window.innerHeight,t=l,s=0,n=0;if("auto"===l){let s=e.top,l=i-e.bottom;e.left;let n=a-e.right;t=s>40&&s>l?"top":l>40?"bottom":n>c?"right":"left"}switch(t){case"top":s=e.top-40-8,n=e.left+e.width/2-c/2;break;case"bottom":s=e.bottom+8,n=e.left+e.width/2-c/2;break;case"left":s=e.top+e.height/2-20,n=e.left-c-8;break;case"right":s=e.top+e.height/2-20,n=e.right+8}return n=Math.max(8,Math.min(n,a-c-8)),{top:s=Math.max(8,Math.min(s,i-40-8)),left:n,position:t}},v=()=>{o||(b.current&&clearTimeout(b.current),b.current=setTimeout(()=>{let e=g();e&&(u(e),m(!0))},n))},j=()=>{b.current&&(clearTimeout(b.current),b.current=null),m(!1),u(null)};(0,s.useEffect)(()=>()=>{b.current&&clearTimeout(b.current)},[]);let f=d&&x?(0,t.jsxs)("div",{ref:h,className:"fixed z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-md shadow-lg pointer-events-none transition-opacity duration-200 ".concat(r),style:{top:x.top,left:x.left,maxWidth:c,wordWrap:"break-word",whiteSpace:"normal"},role:"tooltip","aria-hidden":!d,children:[a,(0,t.jsx)("div",{className:(e=>{let a="absolute w-0 h-0 border-solid";switch(e){case"top":return"".concat(a," top-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-gray-900");case"bottom":return"".concat(a," bottom-full left-1/2 transform -translate-x-1/2 border-l-4 border-r-4 border-b-4 border-l-transparent border-r-transparent border-b-gray-900");case"left":return"".concat(a," left-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-l-4 border-t-transparent border-b-transparent border-l-gray-900");case"right":return"".concat(a," right-full top-1/2 transform -translate-y-1/2 border-t-4 border-b-4 border-r-4 border-t-transparent border-b-transparent border-r-gray-900");default:return a}})(x.position)})]}):null;return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{ref:p,onMouseEnter:v,onMouseLeave:j,onFocus:v,onBlur:j,className:"inline-block","aria-describedby":d?"tooltip":void 0,children:i}),"undefined"!=typeof document&&f&&(0,ee.createPortal)(f,document.body)]})},ei=e=>{let{type:a,count:i,percentage:s,children:l}=e;return(0,t.jsx)(ea,{content:(()=>{let e="".concat(i," cavi"),t=void 0!==s?" (".concat(s.toFixed(1),"%)"):"";switch(a){case"total":return"Totale cavi nel progetto: ".concat(e);case"installed":return"Cavi fisicamente installati: ".concat(e).concat(t);case"in_progress":return"Cavi in corso di installazione: ".concat(e).concat(t);case"to_install":return"Cavi ancora da installare: ".concat(e).concat(t);case"connected":return"Cavi completamente collegati: ".concat(e).concat(t);case"certified":return"Cavi certificati e collaudati: ".concat(e).concat(t);default:return e}})(),delay:200,position:"bottom",children:l})};function et(e){let{cavi:a,filteredCavi:i,className:l,revisioneCorrente:r}=e,o=(0,s.useMemo)(()=>{let e=a.length,t=i.length,s=i.filter(e=>"Installato"===e.stato_installazione||e.metri_posati&&e.metri_posati>0||e.metratura_reale&&e.metratura_reale>0).length,l=i.filter(e=>"In corso"===e.stato_installazione).length,n=i.filter(e=>3===(e.collegamento||e.collegamenti||0)).length,r=i.filter(e=>{let a=e.collegamento||e.collegamenti||0;return 1===a||2===a}).length,o=i.filter(e=>0===(e.collegamento||e.collegamenti||0)&&(e.metri_posati>0||e.metratura_reale>0)).length,c=i.filter(e=>!0===e.certificato||"SI"===e.certificato||"CERTIFICATO"===e.certificato).length,d=i.reduce((e,a)=>e+(a.metri_teorici||0),0),m=i.reduce((e,a)=>e+(a.metri_posati||a.metratura_reale||0),0),x=0===t?0:Math.round(100*(((s-n)*2+(n-c)*3.5+4*c)/(4*t)*100))/100;return{totalCavi:e,filteredCount:t,installati:s,inCorso:l,daInstallare:t-s-l,collegati:n,parzialmenteCollegati:r,nonCollegati:o,certificati:c,metriTotali:d,metriInstallati:m,percentualeInstallazione:x}},[a,i]);return(0,t.jsx)(n.Zp,{className:l,children:(0,t.jsxs)(n.Wu,{className:"p-1.5",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-1.5",children:[(0,t.jsx)(K.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsx)("span",{className:"text-xs font-semibold text-mariner-900",children:"Statistiche Cavi"})]}),(0,t.jsx)("div",{className:"flex items-center space-x-1",children:r&&(0,t.jsxs)(u.E,{variant:"outline",className:"text-xs font-medium py-0 px-1.5 h-5",children:["Rev. ",r]})})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2",children:[(0,t.jsx)(ei,{type:"total",count:o.totalCavi,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-mariner-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)(H.A,{className:"h-3.5 w-3.5 text-mariner-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-mariner-900 text-sm",children:o.filteredCount}),(0,t.jsxs)("div",{className:"text-xs text-mariner-600",children:["di ",o.totalCavi," cavi"]})]})]})}),(0,t.jsx)(ei,{type:"installed",count:o.installati,percentage:o.installati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-green-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi installati: ".concat(o.installati," cavi"),children:[(0,t.jsx)(V.A,{className:"h-3.5 w-3.5 text-green-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-green-700 text-sm",children:o.installati}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:"installati"})]})]})}),(0,t.jsx)(ei,{type:"in_progress",count:o.inCorso,percentage:o.inCorso/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-yellow-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi in corso: ".concat(o.inCorso," cavi"),children:[(0,t.jsx)(U.A,{className:"h-3.5 w-3.5 text-yellow-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-yellow-700 text-sm",children:o.inCorso}),(0,t.jsx)("div",{className:"text-xs text-yellow-600",children:"in corso"})]})]})}),(0,t.jsx)(ei,{type:"to_install",count:o.daInstallare,percentage:o.daInstallare/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi da installare: ".concat(o.daInstallare," cavi"),children:[(0,t.jsx)(Q.A,{className:"h-3.5 w-3.5 text-gray-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-gray-700 text-sm",children:o.daInstallare}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"da installare"})]})]})}),(0,t.jsx)(ei,{type:"connected",count:o.collegati,percentage:o.collegati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-blue-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi collegati: ".concat(o.collegati," cavi"),children:[(0,t.jsx)(Y.A,{className:"h-3.5 w-3.5 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-blue-700 text-sm",children:o.collegati}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:"collegati"})]})]})}),(0,t.jsx)(ei,{type:"certified",count:o.certificati,percentage:o.certificati/o.filteredCount*100,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-purple-50 px-1.5 py-1 rounded-lg","aria-label":"Cavi certificati: ".concat(o.certificati," cavi"),children:[(0,t.jsx)(X.A,{className:"h-3.5 w-3.5 text-purple-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-bold text-purple-700 text-sm",children:o.certificati}),(0,t.jsx)("div",{className:"text-xs text-purple-600",children:"certificati"})]})]})}),(0,t.jsx)(ei,{type:"total",count:o.metriInstallati,children:(0,t.jsxs)("div",{className:"flex items-center space-x-1.5 bg-indigo-50 px-1.5 py-1 rounded-lg",children:[(0,t.jsx)("div",{className:"h-3.5 w-3.5 flex items-center justify-center",children:(0,t.jsx)("div",{className:"h-2 w-2 bg-indigo-600 rounded-full"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"font-bold text-indigo-700 text-sm",children:[o.metriInstallati.toLocaleString(),"m"]}),(0,t.jsxs)("div",{className:"text-xs text-indigo-600",children:["di ",o.metriTotali.toLocaleString(),"m"]})]})]})})]}),o.filteredCount>0&&(0,t.jsxs)("div",{className:"mt-2 bg-gray-50 p-2 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs font-medium text-gray-700 mb-1",children:[(0,t.jsx)("span",{children:"IAP - Indice Avanzamento Ponderato"}),(0,t.jsxs)("span",{className:"font-bold ".concat(o.percentualeInstallazione>=80?"text-emerald-700":o.percentualeInstallazione>=50?"text-yellow-700":o.percentualeInstallazione>=25?"text-orange-700":"text-amber-700"),children:[o.percentualeInstallazione.toFixed(1),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-500 ease-in-out ".concat(o.percentualeInstallazione>=80?"bg-gradient-to-r from-emerald-500 to-emerald-600":o.percentualeInstallazione>=50?"bg-gradient-to-r from-yellow-500 to-yellow-600":o.percentualeInstallazione>=25?"bg-gradient-to-r from-orange-500 to-orange-600":"bg-gradient-to-r from-amber-500 to-amber-600"),style:{width:"".concat(Math.min(o.percentualeInstallazione,100),"%")}})}),(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mt-0.5",children:[(0,t.jsx)("span",{children:"Pesi: Posa(2.0) + Collegamento(1.5) + Certificazione(0.5)"}),(0,t.jsxs)("span",{children:[o.installati,"I + ",o.collegati,"C + ",o.certificati,"Cert"]})]})]})]})})}var es=i(54165),el=i(85057),en=i(51154),er=i(87481);function eo(e){let{open:a,onClose:i,onConfirm:l,title:n,description:o,isLoading:c,isDangerous:d=!1}=e,[m,u]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{a||u(!1)},[a]),(0,s.useEffect)(()=>{let e=e=>{"Escape"===e.key&&a&&!c&&i()};if(a)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,i,c]),(0,t.jsx)(es.lG,{open:a,onOpenChange:i,children:(0,t.jsx)(es.Cf,{className:"sm:max-w-[400px]","aria-describedby":"confirm-disconnect-description",children:m?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(es.c7,{children:(0,t.jsx)(es.L3,{className:"text-center text-red-600",children:"Conferma Finale"})}),(0,t.jsxs)("div",{className:"py-4 text-center",children:[(0,t.jsx)("div",{className:"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(Q.A,{className:"h-6 w-6 text-red-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Sei veramente sicuro?"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Questa azione scollegher\xe0 ",(0,t.jsx)("strong",{children:"entrambi i lati"})," del cavo."]}),(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"⚠️ Operazione irreversibile"})})]}),(0,t.jsxs)(es.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:()=>u(!1),disabled:c,className:"flex-1",children:"No, Annulla"}),(0,t.jsx)(x.$,{variant:"destructive",onClick:()=>{l()},disabled:c,className:"flex-1",children:c?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(es.c7,{children:[(0,t.jsxs)(es.L3,{className:"flex items-center gap-2 text-red-600",children:[(0,t.jsx)(Q.A,{className:"h-5 w-5"}),n]}),(0,t.jsx)(es.rr,{id:"confirm-disconnect-description",children:o})]}),(0,t.jsx)("div",{className:"py-4",children:(0,t.jsxs)(r.Fc,{variant:"destructive",className:"border-red-200 bg-red-50",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),(0,t.jsxs)(r.TN,{className:"text-red-800",children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Questa azione modificher\xe0 lo stato del collegamento del cavo."]})]})}),(0,t.jsxs)(es.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:c,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,t.jsxs)(x.$,{variant:"destructive",onClick:()=>{d?u(!0):l()},disabled:c,className:"flex-1 hover:bg-red-600",children:[(0,t.jsx)(Q.A,{className:"mr-2 h-4 w-4"}),d?"Procedi":"Conferma"]})]})]})})})}let ec=function(e){let{open:a,onClose:i,cavo:l,onSuccess:n,onError:c}=e,{cantiere:d}=(0,o.A)(),{toast:u}=(0,er.dj)(),[p,h]=(0,s.useState)(""),[b,g]=(0,s.useState)([]),[j,f]=(0,s.useState)(!1),[N,y]=(0,s.useState)(!1),[C,w]=(0,s.useState)(""),[_,A]=(0,s.useState)({open:!1,type:null,title:"",description:""}),S=(0,s.useRef)(null),k=(0,s.useRef)(null),E=(0,s.useRef)(null),[I,T]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&l&&(h(""),w(""),O())},[a,l]),(0,s.useEffect)(()=>{if(a&&S.current){let e=S.current.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])');e.length>0&&e[0].focus()}},[a]),(0,s.useEffect)(()=>{let e=e=>{if(a&&!j&&!_.open)switch(e.key){case"Escape":i();break;case"Tab":var t;let s=null==(t=S.current)?void 0:t.querySelectorAll('button:not([disabled]), [href], input:not([disabled]), select:not([disabled]), textarea:not([disabled]), [tabindex]:not([tabindex="-1"])'),l=null==s?void 0:s[0],n=null==s?void 0:s[s.length-1];e.shiftKey?document.activeElement===l&&(e.preventDefault(),null==n||n.focus()):document.activeElement===n&&(e.preventDefault(),null==l||l.focus())}};if(a)return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[a,i,j,_.open]);let O=async()=>{if(d)try{y(!0);let e=await m.AR.getResponsabili(d.id_cantiere);g(e.data)}catch(e){g([])}finally{y(!1)}},F=async()=>{if(l&&d)try{f(!0),w(""),await m.At.collegaCavo(d.id_cantiere,l.id_cavo,"partenza",p);let e="Collegamento lato partenza completato per il cavo ".concat(l.id_cavo);u({title:"Collegamento completato",description:e,variant:"default"}),T(e),window.location.reload()}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante il collegamento";u({title:"Errore collegamento",description:i,variant:"destructive"}),w(i)}finally{f(!1)}},D=async()=>{if(l&&d)try{f(!0),w(""),await m.At.collegaCavo(d.id_cantiere,l.id_cavo,"arrivo",p);let e="Collegamento lato arrivo completato per il cavo ".concat(l.id_cavo);u({title:"Collegamento completato",description:e,variant:"default"}),T(e),window.location.reload()}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante il collegamento";u({title:"Errore collegamento",description:i,variant:"destructive"}),w(i)}finally{f(!1)}},L=()=>{A({open:!1,type:null,title:"",description:""})},B=async()=>{if(l&&d&&_.type)try{f(!0),w("");let e="entrambi"===_.type?void 0:_.type;await m.At.scollegaCavo(d.id_cantiere,l.id_cavo,e);let a="entrambi"===_.type?" completo":" lato ".concat(_.type);u({title:"Scollegamento completato",description:"Scollegamento".concat(a," completato per il cavo ").concat(l.id_cavo),variant:"default"}),L(),i(),window.location.reload()}catch(t){var e,a;let i=(null==(a=t.response)||null==(e=a.data)?void 0:e.detail)||t.message||"Errore durante lo scollegamento";u({title:"Errore scollegamento",description:i,variant:"destructive"}),w(i)}finally{f(!1)}};if(!l)return null;let M=(()=>{if(!l)return{stato:"non_collegato",descrizione:"Non collegato",dettaglio:"Nessun lato collegato",progressoPercentuale:0,colore:"gray"};switch(l.collegamento||l.collegamenti||0){case 1:return{stato:"partenza",descrizione:"\uD83D\uDFE2⚪ Collegato lato partenza",dettaglio:"Collegamento parziale - manca lato arrivo",progressoPercentuale:50,colore:"amber"};case 2:return{stato:"arrivo",descrizione:"⚪\uD83D\uDFE2 Collegato lato arrivo",dettaglio:"Collegamento parziale - manca lato partenza",progressoPercentuale:50,colore:"amber"};case 3:return{stato:"completo",descrizione:"\uD83D\uDFE2\uD83D\uDFE2 Completamente collegato",dettaglio:"Entrambi i lati collegati correttamente",progressoPercentuale:100,colore:"green"};default:return{stato:"non_collegato",descrizione:"⚪⚪ Non collegato",dettaglio:"Nessun lato collegato",progressoPercentuale:0,colore:"gray"}}})(),R=(l.metri_posati||l.metratura_reale||0)>0;return(0,t.jsxs)(es.lG,{open:a,onOpenChange:i,children:[(0,t.jsxs)(es.Cf,{ref:S,className:"sm:max-w-[500px]","aria-describedby":"collegamenti-dialog-description",role:"dialog","aria-labelledby":"collegamenti-dialog-title","aria-modal":"true",children:[(0,t.jsxs)(es.c7,{children:[(0,t.jsxs)(es.L3,{id:"collegamenti-dialog-title",className:"flex items-center gap-2",children:[(0,t.jsx)(Y.A,{className:"h-5 w-5 text-blue-600"}),"Gestione Collegamenti - Cavo ",l.id_cavo]}),(0,t.jsx)(es.rr,{id:"collegamenti-dialog-description",children:"Gestisci i collegamenti elettrici del cavo selezionato"})]}),(0,t.jsxs)("div",{className:"space-y-6",role:"main",children:[(0,t.jsx)("div",{"aria-live":"polite","aria-atomic":"true",className:"sr-only",children:I}),(0,t.jsxs)("section",{className:"p-4 bg-gray-50 rounded-lg space-y-3","aria-labelledby":"stato-collegamenti",role:"region",children:[(0,t.jsx)("h3",{id:"stato-collegamenti",className:"text-sm font-medium",children:"Stato Attuale Collegamenti"}),(0,t.jsx)("div",{className:"mt-1 text-lg font-semibold","aria-live":"polite","aria-describedby":"stato-collegamenti",children:M.descrizione}),(0,t.jsxs)("div",{className:"mt-2 space-y-2",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600",children:M.dettaglio}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat("green"===M.colore?"bg-green-500":"amber"===M.colore?"bg-amber-500":"bg-gray-400"),style:{width:"".concat(M.progressoPercentuale,"%")},role:"progressbar","aria-valuenow":M.progressoPercentuale,"aria-valuemin":0,"aria-valuemax":100,"aria-label":"Progresso collegamento: ".concat(M.progressoPercentuale,"%")})}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 text-right",children:["Progresso: ",M.progressoPercentuale,"%"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",role:"group","aria-label":"Dettagli collegamenti per lato",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(el.J,{className:"text-xs text-gray-600",id:"lato-partenza-label",children:"Lato Partenza"}),(0,t.jsxs)("div",{className:"mt-1",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs ".concat(1&(l.collegamenti||0)?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),role:"status","aria-labelledby":"lato-partenza-label","aria-describedby":l.responsabile_partenza?"resp-partenza":void 0,children:1&(l.collegamenti||0)?"\uD83D\uDFE2 Collegato":"⚪ Non collegato"}),"arrivo"===M.stato&&(0,t.jsx)("div",{className:"text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded",children:"⚠️ Prossimo: collegare questo lato"})]}),l.responsabile_partenza&&(0,t.jsxs)("div",{id:"resp-partenza",className:"text-xs text-gray-600 mt-1","aria-label":"Responsabile lato partenza: ".concat(l.responsabile_partenza),children:["Resp: ",l.responsabile_partenza]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(el.J,{className:"text-xs text-gray-600",id:"lato-arrivo-label",children:"Lato Arrivo"}),(0,t.jsxs)("div",{className:"mt-1",children:[(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs ".concat(2&(l.collegamenti||0)?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),role:"status","aria-labelledby":"lato-arrivo-label","aria-describedby":l.responsabile_arrivo?"resp-arrivo":void 0,children:2&(l.collegamenti||0)?"\uD83D\uDFE2 Collegato":"⚪ Non collegato"}),"partenza"===M.stato&&(0,t.jsx)("div",{className:"text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded",children:"⚠️ Prossimo: collegare questo lato"})]}),l.responsabile_arrivo&&(0,t.jsxs)("div",{id:"resp-arrivo",className:"text-xs text-gray-600 mt-1","aria-label":"Responsabile lato arrivo: ".concat(l.responsabile_arrivo),children:["Resp: ",l.responsabile_arrivo]})]})]})]})]}),!R&&(0,t.jsxs)(r.Fc,{children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:"Il cavo deve essere installato prima di poter essere collegato."})]}),C&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:C})]}),R&&("partenza"===M.stato||"arrivo"===M.stato)&&(0,t.jsxs)(r.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(G.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsxs)(r.TN,{className:"text-amber-800",children:[(0,t.jsx)("strong",{children:"Collegamento parziale rilevato!"}),(0,t.jsx)("br",{}),"partenza"===M.stato?"Il lato partenza \xe8 collegato. Completa il collegamento collegando anche il lato arrivo.":"Il lato arrivo \xe8 collegato. Completa il collegamento collegando anche il lato partenza."]})]}),R&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("section",{className:"space-y-2","aria-labelledby":"responsabile-section",children:[(0,t.jsx)("h3",{id:"responsabile-section",className:"sr-only",children:"Selezione Responsabile Collegamento"}),(0,t.jsx)(el.J,{htmlFor:"responsabile-select",className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,t.jsxs)(v.l6,{value:p,onValueChange:h,disabled:j||N,children:[(0,t.jsx)(v.bq,{id:"responsabile-select",className:"w-full","aria-describedby":"responsabile-help",children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(v.gC,{children:b.map(e=>(0,t.jsx)(v.eb,{value:e.nome,children:e.nome},e.id))})]}),(0,t.jsx)("p",{id:"responsabile-help",className:"text-xs text-gray-600",children:"Seleziona il responsabile che effettuer\xe0 il collegamento"})]}),(0,t.jsxs)("section",{className:"space-y-3","aria-labelledby":"azioni-collegamento",children:[(0,t.jsx)("h3",{id:"azioni-collegamento",className:"text-sm font-medium",children:"Azioni Collegamento"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,t.jsxs)(x.$,{ref:k,onClick:F,disabled:j||!p||(1&(l.collegamenti||0))>0,className:"w-full","aria-describedby":"collega-partenza-help",children:[j?(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)(V.A,{className:"mr-2 h-4 w-4"}),"Collega Partenza"]}),(0,t.jsxs)(x.$,{onClick:D,disabled:j||!p||(2&(l.collegamenti||0))>0,className:"w-full","aria-describedby":"collega-arrivo-help",children:[j?(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}):(0,t.jsx)(V.A,{className:"mr-2 h-4 w-4"}),"Collega Arrivo"]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,t.jsx)("p",{id:"collega-partenza-help",children:"Collega il lato partenza del cavo"}),(0,t.jsx)("p",{id:"collega-arrivo-help",children:"Collega il lato arrivo del cavo"})]}),(l.collegamenti||0)>0&&(0,t.jsxs)("div",{className:"border-t pt-3 space-y-3",children:[(0,t.jsx)("h4",{className:"text-sm font-medium text-red-600",children:"Azioni Scollegamento"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-2",children:[(1&(l.collegamenti||0))>0&&(0,t.jsxs)(x.$,{variant:"destructive",onClick:()=>{A({open:!0,type:"partenza",title:"Scollega Lato Partenza",description:"Sei sicuro di voler scollegare il lato partenza del cavo ".concat(null==l?void 0:l.id_cavo,"?")})},disabled:j,className:"w-full","aria-describedby":"scollega-partenza-help",children:[(0,t.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Scollega Partenza"]}),(2&(l.collegamenti||0))>0&&(0,t.jsxs)(x.$,{variant:"destructive",onClick:()=>{A({open:!0,type:"arrivo",title:"Scollega Lato Arrivo",description:"Sei sicuro di voler scollegare il lato arrivo del cavo ".concat(null==l?void 0:l.id_cavo,"?")})},disabled:j,className:"w-full","aria-describedby":"scollega-arrivo-help",children:[(0,t.jsx)(z.A,{className:"mr-2 h-4 w-4"}),"Scollega Arrivo"]}),3===(l.collegamenti||0)&&(0,t.jsxs)(x.$,{variant:"destructive",onClick:()=>{A({open:!0,type:"entrambi",title:"Scollega Entrambi i Lati",description:"Sei sicuro di voler scollegare completamente il cavo ".concat(null==l?void 0:l.id_cavo,"? Questa azione rimuover\xe0 tutti i collegamenti.")})},disabled:j,className:"w-full bg-red-600 hover:bg-red-700","aria-describedby":"scollega-entrambi-help",children:[(0,t.jsx)(Q.A,{className:"mr-2 h-4 w-4"}),"Scollega Entrambi i Lati"]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,t.jsx)("p",{id:"scollega-partenza-help",children:"Rimuove il collegamento dal lato partenza"}),(0,t.jsx)("p",{id:"scollega-arrivo-help",children:"Rimuove il collegamento dal lato arrivo"}),(0,t.jsx)("p",{id:"scollega-entrambi-help",children:"Rimuove completamente tutti i collegamenti del cavo"})]})]})]})]})]}),(0,t.jsx)(es.Es,{className:"gap-2",children:(0,t.jsx)(x.$,{ref:E,variant:"outline",onClick:i,disabled:j,className:"flex-1",children:"Chiudi"})})]}),(0,t.jsx)(eo,{open:_.open,onClose:L,onConfirm:B,title:_.title,description:_.description,isLoading:j,isDangerous:"entrambi"===_.type})]})};var ed=i(94788),em=i(57434),ex=i(91788),eu=i(32919);let ep=e=>{let{icon:a,title:i,cableId:s,description:l}=e;return(0,t.jsxs)(es.c7,{children:[(0,t.jsxs)(es.L3,{className:"flex items-center gap-2",children:[a,(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[i,(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold",children:s})]})]}),l&&(0,t.jsx)(es.rr,{className:"text-sm text-muted-foreground",children:l})]})},eh=e=>{let{children:a,className:i="sm:max-w-md",onKeyDown:s,ariaLabelledBy:l,ariaDescribedBy:n}=e;return(0,t.jsx)(es.Cf,{className:i,onKeyDown:s,"aria-labelledby":l,"aria-describedby":n,onPointerDownOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>{s&&s(e)},children:a})},eb=e=>{let{open:a,onClose:i,cavo:l,onConfirm:n}=e,[o,c]=(0,s.useState)(!1),[d,m]=(0,s.useState)(!1),u=async()=>{if(l){c(!0);try{await n(l.id_cavo),i(),m(!1)}catch(e){console.error("Error disconnecting cable:",e)}finally{c(!1)}}},p=()=>{m(!1),i()};return l?(0,t.jsx)(es.lG,{open:a,onOpenChange:p,children:(0,t.jsxs)(eh,{className:"sm:max-w-md",onKeyDown:e=>{"Escape"===e.key&&p()},ariaLabelledBy:"disconnect-modal-title",ariaDescribedBy:"disconnect-modal-description",children:[(0,t.jsx)(ep,{icon:(0,t.jsx)(Y.A,{className:"h-5 w-5 text-orange-500"}),title:"Gestione Collegamenti",cableId:l.id_cavo,description:"Gestisci le connessioni del cavo selezionato"}),d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"py-4 text-center",children:[(0,t.jsx)("div",{className:"mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,t.jsx)(Q.A,{className:"h-6 w-6 text-red-600"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Conferma Scollegamento"}),(0,t.jsxs)("p",{className:"text-sm text-gray-600 mb-4",children:["Sei veramente sicuro di voler scollegare completamente il cavo ",(0,t.jsx)("strong",{children:l.id_cavo}),"?"]}),(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:(0,t.jsx)("p",{className:"text-sm text-red-800 font-medium",children:"⚠️ Questa azione \xe8 irreversibile"})})]}),(0,t.jsxs)(es.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:()=>m(!1),disabled:o,className:"flex-1",children:"No, Annulla"}),(0,t.jsx)(x.$,{variant:"destructive",onClick:u,disabled:o,className:"flex-1",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Scollegando..."]}):"S\xec, Scollega"})]})]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"py-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,t.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"})]}),(0,t.jsxs)("span",{className:"text-sm font-medium text-green-700",children:["Completamente collegato",(0,t.jsx)(ed.A,{className:"inline h-4 w-4 ml-1 cursor-help",title:"Cavo collegato sia all'origine che alla destinazione"})]})]}),(0,t.jsx)("div",{className:"space-y-3",children:(0,t.jsxs)("div",{children:[(0,t.jsx)(el.J,{htmlFor:"responsabile-collegamento",className:"text-sm font-medium",children:"Responsabile Collegamento"}),(0,t.jsxs)("select",{id:"responsabile-collegamento",className:"w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",defaultValue:"",children:[(0,t.jsx)("option",{value:"",disabled:!0,children:"Seleziona responsabile..."}),(0,t.jsx)("option",{value:"cantiere",children:"Cantiere"}),(0,t.jsx)("option",{value:"tecnico1",children:"Tecnico 1"}),(0,t.jsx)("option",{value:"tecnico2",children:"Tecnico 2"})]})]})})]}),(0,t.jsxs)(r.Fc,{className:"my-4 bg-orange-50 border-orange-200",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)(r.TN,{className:"text-orange-800",children:[(0,t.jsx)("strong",{children:"Attenzione:"})," Lo scollegamento rimuover\xe0 tutte le connessioni attive del cavo. Questa azione potrebbe influenzare altri componenti collegati."]})]}),(0,t.jsxs)(es.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:p,disabled:o,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,t.jsxs)(x.$,{variant:"destructive",onClick:()=>{m(!0)},disabled:o,className:"flex-1 hover:bg-red-600",children:[(0,t.jsx)(Q.A,{className:"mr-2 h-4 w-4"}),"Scollega Completamente"]})]})]})]})}):null},eg=e=>{let{open:a,onClose:i,cavo:l,onGenerate:n}=e,[r,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)({fileName:"",includeTestData:!0,format:"standard",emailRecipient:""}),[m,u]=(0,s.useState)({});(0,s.useEffect)(()=>{l&&a&&(d(e=>({...e,fileName:"Certificato_".concat(l.id_cavo,"_").concat(new Date().toISOString().split("T")[0],".pdf")})),u({}))},[l,a]);let h=()=>{let e={};return c.fileName.trim()?/^[a-zA-Z0-9_\-\s]+\.pdf$/i.test(c.fileName)||(e.fileName="Il nome del file deve terminare con .pdf e contenere solo caratteri validi"):e.fileName="Il nome del file \xe8 obbligatorio",c.emailRecipient&&!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(c.emailRecipient)&&(e.emailRecipient="Inserisci un indirizzo email valido"),u(e),0===Object.keys(e).length},b=async()=>{if(l&&h()){o(!0);try{await n(l.id_cavo,c),i()}catch(e){console.error("Error generating PDF:",e)}finally{o(!1)}}},v=c.fileName.trim()&&0===Object.keys(m).length;return l?(0,t.jsx)(es.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(eh,{className:"sm:max-w-lg",onKeyDown:e=>{"Escape"===e.key&&i()},ariaLabelledBy:"pdf-modal-title",children:[(0,t.jsx)(ep,{icon:(0,t.jsx)(em.A,{className:"h-5 w-5 text-blue-500"}),title:"Genera Certificato",cableId:l.id_cavo,description:"Configura le opzioni per la generazione del certificato PDF"}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"fileName",className:"text-sm font-medium",children:"Nome File *"}),(0,t.jsx)(g.p,{id:"fileName",value:c.fileName,onChange:e=>{d(a=>({...a,fileName:e.target.value})),m.fileName&&u(e=>({...e,fileName:""}))},onBlur:h,placeholder:"Certificato_C001_2025-06-29.pdf",className:m.fileName?"border-red-500 focus:ring-red-500":""}),m.fileName&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(G.A,{className:"h-3 w-3"}),m.fileName]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(el.J,{className:"text-sm font-medium",children:"Formato Certificato"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"format",value:"standard",checked:"standard"===c.format,onChange:e=>d(a=>({...a,format:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Standard"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Certificato con informazioni essenziali"})]})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"format",value:"detailed",checked:"detailed"===c.format,onChange:e=>d(a=>({...a,format:e.target.value})),className:"text-blue-600 focus:ring-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Dettagliato"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Certificato con tutti i dati tecnici"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-2 border rounded-md",children:[(0,t.jsx)(p.S,{id:"includeTestData",checked:c.includeTestData,onCheckedChange:e=>d(a=>({...a,includeTestData:e}))}),(0,t.jsxs)("div",{children:[(0,t.jsx)(el.J,{htmlFor:"includeTestData",className:"text-sm font-medium cursor-pointer",children:"Includi Dati di Collaudo"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Aggiunge i risultati dei test al certificato"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"emailRecipient",className:"text-sm font-medium",children:"Email Destinatario (Opzionale)"}),(0,t.jsx)(g.p,{id:"emailRecipient",type:"email",value:c.emailRecipient,onChange:e=>{d(a=>({...a,emailRecipient:e.target.value})),m.emailRecipient&&u(e=>({...e,emailRecipient:""}))},onBlur:h,placeholder:"<EMAIL>",className:m.emailRecipient?"border-red-500 focus:ring-red-500":""}),m.emailRecipient&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(G.A,{className:"h-3 w-3"}),m.emailRecipient]})]})]}),(0,t.jsxs)(es.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:r,className:"flex-1 hover:bg-gray-50",children:"Annulla"}),(0,t.jsx)(x.$,{onClick:b,disabled:r||!v,className:"flex-1 ".concat(v?"hover:bg-blue-600":"opacity-50 cursor-not-allowed"),children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Generando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(ex.A,{className:"mr-2 h-4 w-4"}),"Genera PDF"]})})]})]})}):null},ev=e=>{let{open:a,onClose:i,cavo:s,errorMessage:l,missingRequirements:n=[]}=e,o=n.length>0?n:['Il cavo deve essere nello stato "Installato"',"Il cavo deve essere completamente collegato","Tutti i dati di collaudo devono essere presenti"];return s?(0,t.jsx)(es.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(eh,{className:"sm:max-w-md",onKeyDown:e=>{"Escape"===e.key&&i()},ariaLabelledBy:"certification-error-title",children:[(0,t.jsx)(ep,{icon:(0,t.jsx)(G.A,{className:"h-5 w-5 text-red-500"}),title:"Impossibile Certificare Cavo",cableId:s.id_cavo,description:"Il cavo non pu\xf2 essere certificato nel suo stato attuale"}),(0,t.jsxs)("div",{className:"py-4",children:[l&&(0,t.jsxs)(r.Fc,{className:"mb-4 bg-red-50 border-red-200",children:[(0,t.jsx)(G.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(r.TN,{className:"text-red-800",children:l})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"text-sm font-semibold text-gray-900 mb-3 flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4 text-amber-500"}),"Requisiti mancanti:"]}),(0,t.jsx)("ul",{className:"space-y-3",children:o.map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-3 p-2 bg-red-50 border border-red-200 rounded-md",children:[(0,t.jsx)("div",{className:"flex-shrink-0 w-5 h-5 bg-red-100 rounded-full flex items-center justify-center mt-0.5",children:(0,t.jsx)(z.A,{className:"h-3 w-3 text-red-600"})}),(0,t.jsx)("span",{className:"text-sm text-red-800",children:e})]},a))})]}),(0,t.jsxs)(r.Fc,{className:"bg-blue-50 border-blue-200",children:[(0,t.jsx)(V.A,{className:"h-4 w-4 text-blue-600"}),(0,t.jsxs)(r.TN,{className:"text-blue-800",children:[(0,t.jsx)("strong",{children:"Prossimi passi:"})," Completa tutti i requisiti sopra elencati per abilitare la certificazione del cavo."]})]})]})]}),(0,t.jsx)(es.Es,{children:(0,t.jsxs)(x.$,{onClick:i,className:"w-full hover:bg-blue-600 focus:ring-2 focus:ring-blue-500",children:[(0,t.jsx)(V.A,{className:"mr-2 h-4 w-4"}),"Ho Capito"]})})]})}):null},ej=e=>{let{open:a,onClose:i,cavo:l,onCertify:n}=e,[r,o]=(0,s.useState)(!1),[c,d]=(0,s.useState)({responsabile:"",dataCertificazione:new Date().toISOString().split("T")[0],esitoCertificazione:"CONFORME",note:""}),[m,u]=(0,s.useState)({});(0,s.useEffect)(()=>{a&&l&&(d({responsabile:"",dataCertificazione:new Date().toISOString().split("T")[0],esitoCertificazione:"CONFORME",note:""}),u({}))},[a,l]);let p=()=>{let e={};if(c.responsabile.trim()||(e.responsabile="Il responsabile \xe8 obbligatorio"),c.dataCertificazione){let a=new Date(c.dataCertificazione),i=new Date;i.setHours(0,0,0,0),a>i&&(e.dataCertificazione="La data non pu\xf2 essere futura")}else e.dataCertificazione="La data di certificazione \xe8 obbligatoria";return u(e),0===Object.keys(e).length},h=async()=>{if(l&&p()){o(!0);try{await n(l.id_cavo,c),i()}catch(e){console.error("Error certifying cable:",e)}finally{o(!1)}}},b=c.responsabile.trim()&&c.dataCertificazione&&0===Object.keys(m).length;if(!l)return null;let v={installato:!0,collegato:!0,certificato:!1};return(0,t.jsx)(es.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(eh,{className:"sm:max-w-lg",onKeyDown:e=>{"Escape"===e.key&&i()},ariaLabelledBy:"certification-modal-title",children:[(0,t.jsx)(ep,{icon:(0,t.jsx)(eu.A,{className:"h-5 w-5 text-blue-500"}),title:"Gestione Certificazione",cableId:l.id_cavo,description:"Certifica il cavo dopo aver completato tutti i controlli"}),(0,t.jsxs)("div",{className:"space-y-4 py-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h4",{className:"text-sm font-semibold text-gray-900",children:"Stato Cavo:"}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-md",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(v.installato?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[v.installato?"✓":"✗"," Installato"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-md",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(v.collegato?"bg-green-500":"bg-red-500")}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[v.collegato?"✓":"✗"," Collegato"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2 p-2 bg-gray-50 rounded-md",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(v.certificato?"bg-green-500":"bg-orange-500")}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:[v.certificato?"✓":"⚠"," Non certificato"]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"responsabile",className:"text-sm font-medium",children:"Responsabile Certificazione *"}),(0,t.jsxs)("select",{id:"responsabile",value:c.responsabile,onChange:e=>{d(a=>({...a,responsabile:e.target.value})),m.responsabile&&u(e=>({...e,responsabile:""}))},onBlur:p,className:"w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ".concat(m.responsabile?"border-red-500":"border-gray-300"),children:[(0,t.jsx)("option",{value:"",disabled:!0,children:"Seleziona responsabile..."}),(0,t.jsx)("option",{value:"cantiere",children:"Cantiere"}),(0,t.jsx)("option",{value:"tecnico_1",children:"Tecnico 1"}),(0,t.jsx)("option",{value:"tecnico_2",children:"Tecnico 2"}),(0,t.jsx)("option",{value:"supervisore",children:"Supervisore"})]}),m.responsabile&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(G.A,{className:"h-3 w-3"}),m.responsabile]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"dataCertificazione",className:"text-sm font-medium",children:"Data Certificazione *"}),(0,t.jsx)(g.p,{id:"dataCertificazione",type:"date",value:c.dataCertificazione,onChange:e=>{d(a=>({...a,dataCertificazione:e.target.value})),m.dataCertificazione&&u(e=>({...e,dataCertificazione:""}))},onBlur:p,max:new Date().toISOString().split("T")[0],className:m.dataCertificazione?"border-red-500 focus:ring-red-500":""}),m.dataCertificazione&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(G.A,{className:"h-3 w-3"}),m.dataCertificazione]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{className:"text-sm font-medium",children:"Esito Certificazione"}),(0,t.jsxs)("select",{value:c.esitoCertificazione,onChange:e=>d(a=>({...a,esitoCertificazione:e.target.value})),className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",children:[(0,t.jsx)("option",{value:"CONFORME",children:"CONFORME"}),(0,t.jsx)("option",{value:"NON_CONFORME",children:"NON CONFORME"}),(0,t.jsx)("option",{value:"PARZIALMENTE_CONFORME",children:"PARZIALMENTE CONFORME"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"note",className:"text-sm font-medium",children:"Note (opzionale)"}),(0,t.jsx)("textarea",{id:"note",value:c.note,onChange:e=>d(a=>({...a,note:e.target.value})),placeholder:"Inserisci eventuali note sulla certificazione...",rows:3,className:"w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"})]})]}),(0,t.jsxs)(es.Es,{className:"gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:r,className:"flex-1 hover:bg-gray-50",children:"Chiudi"}),(0,t.jsx)(x.$,{onClick:h,disabled:r||!b,className:"flex-1 ".concat(b?"bg-blue-600 hover:bg-blue-700":"opacity-50 cursor-not-allowed bg-gray-400"),children:r?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Certificando..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(eu.A,{className:"mr-2 h-4 w-4"}),"Certifica Cavo"]})})]})]})})},ef=e=>{let{message:a,visible:i,onClose:l}=e;return((0,s.useEffect)(()=>{if(i){let e=setTimeout(()=>{l()},3e3);return()=>clearTimeout(e)}},[i,l]),i)?(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 animate-in slide-in-from-top-2",children:(0,t.jsxs)(r.Fc,{className:"bg-green-50 border-green-200 text-green-800 shadow-lg",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{className:"font-medium",children:a}),(0,t.jsx)(x.$,{variant:"ghost",size:"sm",className:"absolute top-2 right-2 h-6 w-6 p-0 hover:bg-green-100",onClick:l,children:(0,t.jsx)(z.A,{className:"h-3 w-3"})})]})}):null},eN=e=>{let{icon:a,title:i,cableId:s,description:l}=e;return(0,t.jsxs)(es.c7,{children:[(0,t.jsxs)(es.L3,{className:"flex items-center gap-2",children:[a,(0,t.jsxs)("span",{className:"flex items-center gap-2",children:[i,(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-sm font-mono font-semibold",children:s})]})]}),l&&(0,t.jsx)(es.rr,{className:"text-sm text-muted-foreground",children:l})]})},ey=e=>{let{children:a,className:i="sm:max-w-md",onKeyDown:s,ariaLabelledBy:l,ariaDescribedBy:n}=e;return(0,t.jsx)(es.Cf,{className:i,onKeyDown:s,"aria-labelledby":l,"aria-describedby":n,onPointerDownOutside:e=>e.preventDefault(),onEscapeKeyDown:e=>{s&&s(e)},children:a})},eC=e=>{let{cavo:a}=e;return(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,t.jsx)(X.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsxs)("h3",{className:"font-semibold text-blue-800",children:["Informazioni Cavo ",a.id_cavo]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"Tipologia"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:a.tipologia||"N/A"})]}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"Formazione"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:a.sezione||"N/A"})]}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"Da"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:a.ubicazione_partenza||"N/A"})]}),(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"A"}),(0,t.jsx)("span",{className:"text-gray-900 font-medium",children:a.ubicazione_arrivo||"N/A"})]}),(0,t.jsxs)("div",{className:"flex flex-col col-span-2",children:[(0,t.jsx)("span",{className:"text-gray-600 text-xs font-medium uppercase tracking-wide",children:"Metri Posati"}),(0,t.jsxs)("span",{className:"text-blue-600 font-bold text-lg",children:[a.metratura_reale||0," m"]})]})]})]})},ew=e=>{let{open:a,onClose:i,cavo:l,onSave:n}=e,{cantiere:c}=(0,o.A)(),[d,u]=(0,s.useState)(!1),[p,h]=(0,s.useState)(!1),[b,v]=(0,s.useState)(""),[j,f]=(0,s.useState)(""),[N,y]=(0,s.useState)("compatible"),[C,w]=(0,s.useState)(""),[_,A]=(0,s.useState)([]),[z,S]=(0,s.useState)(""),k=async()=>{if(console.log("\uD83C\uDFAF ModificaBobinaModal: Caricamento bobine:",{cavo:!!l,cantiere:!!c,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==c?void 0:c.id_cantiere}),l&&c)try{h(!0);let e=await m.Fw.getBobine(c.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);if(l){console.log("\uD83D\uDD0D ModificaBobinaModal: Filtro per cavo:",{tipologia:l.tipologia,sezione:l.sezione});let e=i.map(e=>({...e,compatible:e.tipologia===l.tipologia&&e.sezione===l.sezione}));A(e),console.log("✅ ModificaBobinaModal: Bobine caricate:",e.length)}else A([])}catch(e){console.error("Errore caricamento bobine:",e),S("Errore durante il caricamento delle bobine")}finally{h(!1)}};(0,s.useEffect)(()=>{a&&l&&(c?k():console.log("❌ ModificaBobinaModal: Cantiere non disponibile"),v(""),w(""),f(""),y("compatible"),S(""))},[a,l,c]);let E=_.filter(e=>{var a,i;let t=(null==(a=e.numero_bobina)?void 0:a.toLowerCase().includes(j.toLowerCase()))||(null==(i=e.tipologia)?void 0:i.toLowerCase().includes(j.toLowerCase())),s="compatible"===N?e.compatible:!e.compatible;return t&&s}),I=_.filter(e=>e.compatible).length,T=_.filter(e=>!e.compatible).length;console.log("\uD83D\uDD0D ModificaBobinaModal: Stato filtri",{totaleBobine:_.length,compatibili:I,incompatibili:T,filtrate:E.length,activeTab:N,searchTerm:j});let O=async()=>{if(l&&b){u(!0);try{await n(l.id_cavo,C,b),F()}catch(e){console.error("Error saving bobina modification:",e)}finally{u(!1)}}},F=()=>{v(""),w(""),f(""),y("compatible"),S(""),A([]),i()};return l?(0,t.jsx)(es.lG,{open:a,onOpenChange:F,children:(0,t.jsxs)(ey,{className:"sm:max-w-3xl max-h-[85vh] overflow-hidden",onKeyDown:e=>{"Escape"===e.key&&F()},ariaLabelledBy:"modifica-bobina-title",children:[(0,t.jsx)(eN,{icon:(0,t.jsx)(X.A,{className:"h-5 w-5 text-blue-500"}),title:"Modifica Bobina Cavo",cableId:l.id_cavo,description:"Seleziona una nuova bobina per il cavo o modifica i parametri"}),(0,t.jsxs)("div",{className:"space-y-4 py-4 overflow-y-auto max-h-[calc(85vh-200px)]",children:[(0,t.jsx)(eC,{cavo:l}),z&&(0,t.jsxs)(r.Fc,{className:"bg-red-50 border-red-200",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)(r.TN,{className:"text-red-800",children:z})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(el.J,{className:"text-sm font-semibold",children:"Opzioni di modifica"}),(0,t.jsxs)("div",{className:"grid grid-cols-1 gap-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"modifica-option",value:"cambia-bobina",checked:"cambia-bobina"===b,onChange:e=>v(e.target.value),className:"text-blue-600 focus:ring-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Cambia bobina"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Assegna una bobina diversa al cavo"})]})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"modifica-option",value:"bobina-vuota",checked:"bobina-vuota"===b,onChange:e=>v(e.target.value),className:"text-blue-600 focus:ring-blue-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium",children:"Bobina vuota"}),(0,t.jsx)("p",{className:"text-xs text-gray-500",children:"Rimuovi l'associazione con la bobina attuale"})]})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 border rounded-md hover:bg-gray-50 transition-colors",children:[(0,t.jsx)("input",{type:"radio",name:"modifica-option",value:"annulla-posa",checked:"annulla-posa"===b,onChange:e=>v(e.target.value),className:"text-red-600 focus:ring-red-500"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-sm font-medium text-red-700",children:"Annulla posa"}),(0,t.jsx)("p",{className:"text-xs text-red-500",children:"Annulla l'installazione e restituisci i metri alla bobina"})]})]})]})]}),"cambia-bobina"===b&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"search-bobina",className:"text-sm font-medium",children:"Cerca bobina"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(D.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(g.p,{id:"search-bobina",value:j,onChange:e=>f(e.target.value),placeholder:"Cerca bobina per ID, tipologia o numero...",className:"pl-10 h-8"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex border-b",children:[(0,t.jsxs)("button",{onClick:()=>y("compatible"),className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ".concat("compatible"===N?"border-green-500 text-green-700 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"),children:[(0,t.jsx)(V.A,{className:"h-3 w-3 text-green-500"}),"Compatibili (",I,")"]}),(0,t.jsxs)("button",{onClick:()=>y("incompatible"),className:"flex items-center gap-2 px-3 py-1.5 text-sm font-medium border-b-2 transition-colors ".concat("incompatible"===N?"border-yellow-500 text-yellow-700 bg-yellow-50":"border-transparent text-gray-500 hover:text-gray-700"),children:[(0,t.jsx)(Q.A,{className:"h-3 w-3 text-yellow-500"}),"Incompatibili (",T,")"]})]}),(0,t.jsx)("div",{className:"h-32 overflow-y-auto border rounded-md bg-gray-50",children:p?(0,t.jsxs)("div",{className:"p-3 text-center",children:[(0,t.jsx)(en.A,{className:"h-6 w-6 text-blue-500 mx-auto mb-2 animate-spin"}),(0,t.jsx)("p",{className:"text-xs text-gray-600",children:"Caricamento bobine..."})]}):0===E.length?(0,t.jsx)("div",{className:"p-3 text-center",children:(0,t.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-md p-3",children:[(0,t.jsx)(Q.A,{className:"h-6 w-6 text-yellow-500 mx-auto mb-1"}),(0,t.jsxs)("p",{className:"text-xs text-yellow-800 font-medium mb-1",children:["Nessuna bobina ","compatible"===N?"compatibile":"incompatibile"," trovata"]}),(0,t.jsx)("p",{className:"text-xs text-yellow-700",children:"Prova a modificare i criteri di ricerca"})]})}):(0,t.jsx)("div",{className:"space-y-1 p-1",children:E.map(e=>(0,t.jsxs)("label",{className:"flex items-center space-x-2 p-2 border rounded-md hover:bg-white cursor-pointer transition-colors bg-white",children:[(0,t.jsx)("input",{type:"radio",name:"selected-bobina",value:e.id_bobina,checked:C===e.id_bobina,onChange:e=>w(e.target.value),className:"text-blue-600 focus:ring-blue-500 w-3 h-3"}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("span",{className:"font-medium text-xs truncate",children:e.numero_bobina}),e.compatible?(0,t.jsx)(V.A,{className:"h-3 w-3 text-green-500 flex-shrink-0"}):(0,t.jsx)(Q.A,{className:"h-3 w-3 text-yellow-500 flex-shrink-0"})]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500 truncate",children:[e.tipologia," - ",e.sezione," - ",e.metri_residui,"m"]})]})]},e.id_bobina))})})]})]}),"annulla-posa"===b&&(0,t.jsxs)(r.Fc,{className:"bg-red-50 border-red-200",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(r.TN,{className:"text-red-800",children:[(0,t.jsx)("strong",{children:"ATTENZIONE:"}),' Questa operazione annuller\xe0 completamente l\'installazione del cavo. Tutti i metri posati saranno restituiti alla bobina originale e lo stato del cavo sar\xe0 resettato a "Da installare".']})]})]}),(0,t.jsxs)(es.Es,{className:"gap-2 pt-4 border-t",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:F,disabled:d,className:"px-6 py-2 hover:bg-gray-50",children:"Annulla"}),(0,t.jsx)(x.$,{onClick:O,disabled:d||!b||"cambia-bobina"===b&&!C,className:"px-6 py-2 ".concat("annulla-posa"===b?"bg-red-600 hover:bg-red-700 text-white":"bg-blue-600 hover:bg-blue-700 text-white"),variant:"annulla-posa"===b?"destructive":"default",children:d?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salvando..."]}):"annulla-posa"===b?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(Q.A,{className:"mr-2 h-4 w-4"}),"Annulla Posa"]}):"Salva Modifiche"})]})]})}):null};i(93304);var e_=i(6740);function eA(e){let{open:a,onClose:i,cavo:l,cantiere:n,onSuccess:c,onError:d}=e,{cantiere:u}=(0,o.A)(),p=n||u,[h,b]=(0,s.useState)("assegna_nuova"),[v,j]=(0,s.useState)(""),[f,N]=(0,s.useState)([]),[y,C]=(0,s.useState)(!1),[w,_]=(0,s.useState)(!1),[A,z]=(0,s.useState)(""),[S,k]=(0,s.useState)(""),[E,I]=(0,s.useState)("compatibili");(0,s.useEffect)(()=>{a&&(b("assegna_nuova"),j(""),k(""),I("compatibili"),z(""),(null==p?void 0:p.id_cantiere)&&T())},[a,null==p?void 0:p.id_cantiere]);let T=async()=>{if(!(null==p?void 0:p.id_cantiere))return void z("Cantiere non disponibile");try{C(!0),z(""),console.log("\uD83D\uDD04 ModificaBobinaDialog: Caricamento bobine per cantiere:",p.id_cantiere);let e=await m.Fw.getBobine(p.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);N(i),console.log("✅ ModificaBobinaDialog: Bobine caricate:",i.length),console.log("\uD83D\uDCCB ModificaBobinaDialog: Dettaglio bobine:",i.map(e=>({id:e.id_bobina,tipologia:e.tipologia,sezione:e.sezione,metri_residui:e.metri_residui,stato:e.stato_bobina})))}catch(e){console.error("❌ ModificaBobinaDialog: Errore caricamento bobine:",e),z("Errore nel caricamento delle bobine"),N([])}finally{C(!1)}},O=(()=>{if(!l)return[];let e=f.filter(e=>{let a=e.tipologia===l.tipologia&&e.sezione===l.sezione,i=""===S||e.id_bobina.toLowerCase().includes(S.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(S.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(S.toLowerCase());return a&&i&&e.metri_residui>0});return console.log("\uD83D\uDD0D ModificaBobinaDialog: Filtro compatibili:",{cavoTipologia:l.tipologia,cavoSezione:l.sezione,totaleBobine:f.length,bobineCompatibili:e.length,searchText:S}),e})(),F=l?f.filter(e=>{let a=e.tipologia!==l.tipologia||e.sezione!==l.sezione,i=""===S||e.id_bobina.toLowerCase().includes(S.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(S.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(S.toLowerCase());return a&&i&&e.metri_residui>0}):[],L=()=>{b("assegna_nuova"),j(""),k(""),z(""),i()},B=async()=>{if(console.log("\uD83D\uDD04 ModificaBobinaDialog: Salvataggio:",{selectedOption:h,selectedBobina:v,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==p?void 0:p.id_cantiere}),l)try{if(_(!0),z(""),"assegna_nuova"===h){if(!v)return void d("Selezionare una bobina");let e=await m.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:l.metratura_reale||0,id_bobina:v,force_over:!0});e.success?(c("Bobina aggiornata con successo per il cavo ".concat(l.id_cavo)),L()):d(e.message||"Errore durante l'aggiornamento della bobina")}else if("rimuovi_bobina"===h){let e=await m.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:l.metratura_reale||0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(c("Bobina rimossa dal cavo ".concat(l.id_cavo)),L()):d(e.message||"Errore durante la rimozione della bobina")}else if("annulla_installazione"===h){let e=await m.At.updateMetriPosati({id_cavo:l.id_cavo,metri_posati:0,id_bobina:"BOBINA_VUOTA",force_over:!1});e.success?(c("Installazione annullata per il cavo ".concat(l.id_cavo)),L()):d(e.message||"Errore durante l'annullamento dell'installazione")}}catch(e){console.error("❌ ModificaBobinaDialog: Errore salvataggio:",e),d("Errore durante il salvataggio")}finally{_(!1)}};return l?(0,t.jsx)(t.Fragment,{children:(0,t.jsx)(es.lG,{open:a,onOpenChange:L,children:(0,t.jsxs)(es.Cf,{className:"max-w-4xl max-h-[90vh] flex flex-col",children:[(0,t.jsx)(es.c7,{children:(0,t.jsxs)(es.L3,{children:["Modifica Bobina Cavo ",l.id_cavo]})}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(X.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"font-medium text-gray-900",children:"Cavo Selezionato"})]}),(0,t.jsx)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:(0,t.jsxs)("div",{className:"text-sm font-medium text-blue-800",children:["Informazioni Cavo / Tipologia: ",l.tipologia||"N/A"," / Da: ",l.ubicazione_partenza||"N/A"," / Formazione: ",l.sezione||"N/A"," / A: ",l.ubicazione_arrivo||"N/A"," / Metri Posati: ",l.metratura_reale||0," m"]})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Opzioni di modifica"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"assegna_nuova",checked:"assegna_nuova"===h,onChange:e=>b(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Cambia bobina"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"rimuovi_bobina",checked:"rimuovi_bobina"===h,onChange:e=>b(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm",children:"Bobina vuota"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer p-2 rounded hover:bg-gray-50",children:[(0,t.jsx)("input",{type:"radio",name:"operazione",value:"annulla_installazione",checked:"annulla_installazione"===h,onChange:e=>b(e.target.value),className:"w-4 h-4 text-blue-600"}),(0,t.jsx)("span",{className:"text-sm text-red-600",children:"Annulla posa"})]})]})]}),"assegna_nuova"===h&&(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)("h3",{className:"font-medium",children:"Seleziona bobina"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(D.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(g.p,{placeholder:"Cerca bobina per ID, tipologia o numero...",value:S,onChange:e=>k(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("div",{className:"flex space-x-1 border-b",children:[(0,t.jsx)("button",{onClick:()=>I("compatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("compatibili"===E?"border-green-500 text-green-600 bg-green-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Compatibili (",O.length,")"]})]})}),(0,t.jsx)("button",{onClick:()=>I("incompatibili"),className:"px-4 py-2 text-sm font-medium border-b-2 transition-colors ".concat("incompatibili"===E?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700"),children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsxs)("span",{children:["Bobine Incompatibili (",F.length,")"]})]})})]}),(0,t.jsx)("div",{className:"border rounded-lg h-64 overflow-y-auto",children:y?(0,t.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(en.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Caricamento bobine..."})]})}):(0,t.jsx)("div",{className:"p-2",children:"compatibili"===E?0===O.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-gray-500 text-sm mb-2",children:"Nessuna bobina compatibile trovata"}),(0,t.jsxs)("div",{className:"text-xs text-gray-400",children:["Cercando bobine con tipologia ",(0,t.jsx)("strong",{children:l.tipologia})," e formazione ",(0,t.jsx)("strong",{children:l.sezione})]})]}):(0,t.jsx)("div",{className:"space-y-2",children:O.map(e=>(0,t.jsx)("div",{onClick:()=>j(e.id_bobina),className:"p-3 rounded-lg cursor-pointer transition-all duration-200 ".concat(v===e.id_bobina?"bg-blue-100 border-2 border-blue-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),e.stato_bobina&&(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat("Disponibile"===e.stato_bobina?"bg-green-100 text-green-800":"In uso"===e.stato_bobina?"bg-blue-100 text-blue-800":"bg-yellow-100 text-yellow-800"),children:e.stato_bobina})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,t.jsx)("span",{children:e.sezione})]})]}),(0,t.jsxs)("div",{className:"text-right ml-3",children:[(0,t.jsxs)("div",{className:"text-sm font-medium ".concat(e.metri_residui>0?"text-green-600":"text-gray-500"),children:[e.metri_residui,"m"]}),(0,t.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))}):0===F.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500 text-sm",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"p-3 bg-orange-50 border border-orange-200 rounded-lg",children:(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)(G.A,{className:"h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0"}),(0,t.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,t.jsx)("div",{className:"font-medium mb-1",children:"Bobine Incompatibili"}),(0,t.jsx)("div",{className:"text-xs",children:"Selezionando una bobina incompatibile, le caratteristiche del cavo verranno aggiornate."})]})]})}),F.map(e=>(0,t.jsx)("div",{onClick:()=>j(e.id_bobina),className:"p-3 rounded-lg cursor-pointer transition-all duration-200 ".concat(v===e.id_bobina?"bg-orange-100 border-2 border-orange-300 shadow-md":"hover:bg-gray-50 border border-gray-200 hover:border-gray-300"),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,t.jsx)("div",{className:"font-medium text-sm text-gray-900",children:e.id_bobina}),(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800",children:"INCOMPATIBILE"})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{className:"font-medium",children:e.tipologia})," • ",(0,t.jsx)("span",{children:e.sezione})]})]}),(0,t.jsxs)("div",{className:"text-right ml-3",children:[(0,t.jsxs)("div",{className:"text-sm font-medium ".concat(e.metri_residui>0?"text-orange-600":"text-gray-500"),children:[e.metri_residui,"m"]}),(0,t.jsx)("div",{className:"text-xs text-gray-400",children:e.metri_residui>0?"disponibili":"esaurita"})]})]})},e.id_bobina))]})})})]})]}),A&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:A})]}),(0,t.jsxs)(es.Es,{className:"flex justify-end space-x-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:L,disabled:w,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:B,disabled:w||"assegna_nuova"===h&&!v,children:[w&&(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})})}):null}function ez(e){let{open:a,onClose:i,cavo:l,cantiere:n,onSuccess:c,onError:d}=e,{cantiere:p}=(0,o.A)(),h=n||p,[b,v]=(0,s.useState)({metri_posati:"",id_bobina:""});(0,s.useEffect)(()=>{console.log("\uD83D\uDCCA InserisciMetriDialog: FormData aggiornato:",{hasMetri:!!b.metri_posati,hasBobina:!!b.id_bobina,metri_posati:b.metri_posati,id_bobina:b.id_bobina})},[b]);let[j,f]=(0,s.useState)({}),[N,y]=(0,s.useState)({}),[C,w]=(0,s.useState)(!1),[_,A]=(0,s.useState)([]),[S,k]=(0,s.useState)(!1),[E,I]=(0,s.useState)(""),[T,O]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a&&l&&(h&&L(),v({metri_posati:"0",id_bobina:""}),f({}),y({}),I(""))},[a,l,h]),(0,s.useEffect)(()=>{b.metri_posati&&l?F(parseFloat(b.metri_posati)):(f(e=>({...e,metri_posati:void 0})),y(e=>({...e,metri_posati:void 0})))},[b.metri_posati,l]);let F=e=>{if(!l)return;let a={...j},i={...N};delete a.metri_posati,delete i.metri_posati,e>1.1*(l.metri_teorici||0)?i.metri_posati="Attenzione: i metri posati superano del 10% i metri teorici (".concat(l.metri_teorici,"m)"):e>(l.metri_teorici||0)&&(i.metri_posati="Metratura superiore ai metri teorici"),f(a),y(i)},L=async()=>{if(console.log("\uD83C\uDFAF InserisciMetriDialog: Caricamento bobine:",{cavo:!!l,cantiere:!!h,cavoId:null==l?void 0:l.id_cavo,cantiereId:null==h?void 0:h.id_cantiere}),l&&h)try{k(!0);let e=await m.Fw.getBobine(h.id_cantiere),a=[];if(Array.isArray(e))a=e;else if(e&&Array.isArray(e.data))a=e.data;else if(e&&e.bobine&&Array.isArray(e.bobine))a=e.bobine;else throw Error("Formato risposta API non valido");let i=a.filter(e=>"Terminata"!==e.stato_bobina&&"Over"!==e.stato_bobina&&e.metri_residui>0);if(l){console.log("\uD83D\uDD0D InserisciMetriDialog: Filtro per cavo:",{tipologia:l.tipologia,sezione:l.sezione});let e=i.filter(e=>e.tipologia===l.tipologia&&e.sezione===l.sezione),a=i.filter(e=>e.tipologia!==l.tipologia||e.sezione!==l.sezione);e.sort((e,a)=>a.metri_residui-e.metri_residui),a.sort((e,a)=>a.metri_residui-e.metri_residui);let t=[...e,...a];A(t)}else i.sort((e,a)=>a.metri_residui-e.metri_residui),A(i)}catch(t){var e,a,i;console.error("❌ InserisciMetriDialog: Errore caricamento bobine:",{message:t.message,response:t.response,status:null==(e=t.response)?void 0:e.status,data:null==(a=t.response)?void 0:a.data}),(null==(i=t.response)?void 0:i.status)!==404&&d("Errore nel caricamento delle bobine. Puoi comunque usare BOBINA VUOTA."),A([])}finally{k(!1)}},B=e=>{if(!e||"BOBINA_VUOTA"===e)return"VUOTA";if(e&&e.includes("_B"))return e.split("_B")[1];let a=_.find(a=>a.id_bobina===e);return a&&a.numero_bobina||e},M=l?_.filter(e=>{let a=e.tipologia===l.tipologia&&e.sezione===l.sezione,i=""===E||e.id_bobina.toLowerCase().includes(E.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(E.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(E.toLowerCase());return a&&i&&e.metri_residui>0}):[],R=l?_.filter(e=>{let a=e.tipologia!==l.tipologia||e.sezione!==l.sezione,i=""===E||e.id_bobina.toLowerCase().includes(E.toLowerCase())||e.tipologia&&e.tipologia.toLowerCase().includes(E.toLowerCase())||e.numero_bobina&&e.numero_bobina.toLowerCase().includes(E.toLowerCase());return a&&i&&e.metri_residui>0}):[],P=e=>{v(a=>({...a,id_bobina:e.id_bobina})),f(e=>{let a={...e};return delete a.id_bobina,a})},$=async()=>{if(console.log("\uD83D\uDCBE InserisciMetriDialog: Salvataggio metri:",{cavo:null==l?void 0:l.id_cavo,metri_posati:b.metri_posati,id_bobina:b.id_bobina}),!l)return;if(!b.metri_posati||0>parseFloat(b.metri_posati))return void d("Inserire metri posati validi (≥ 0)");if(!b.id_bobina)return void d("Selezionare una bobina o BOBINA VUOTA");let e=parseFloat(b.metri_posati);if("BOBINA_VUOTA"!==b.id_bobina){let e=_.find(e=>e.id_bobina===b.id_bobina);e&&e.metri_residui}try{if(w(!0),!h)throw Error("Cantiere non selezionato");console.log("\uD83D\uDE80 InserisciMetriDialog: Chiamata API updateMetriPosati:",{cantiere:h.id_cantiere,cavo:l.id_cavo,metri:e,bobina:b.id_bobina,isBobinaVuota:"BOBINA_VUOTA"===b.id_bobina}),await m.At.updateMetriPosati(h.id_cantiere,l.id_cavo,e,b.id_bobina,!0),c("Metri posati aggiornati con successo per il cavo ".concat(l.id_cavo,": ").concat(e,"m")),i()}catch(e){var a,t;d((null==(t=e.response)||null==(a=t.data)?void 0:a.detail)||e.message||"Errore durante il salvataggio dei metri posati")}finally{w(!1)}},U=()=>{C||(v({metri_posati:"",id_bobina:""}),f({}),y({}),I(""),i())};return l?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(es.lG,{open:a,onOpenChange:U,children:(0,t.jsxs)(es.Cf,{className:"max-w-7xl h-[90vh] flex flex-col",children:[(0,t.jsxs)(es.c7,{className:"flex-shrink-0",children:[(0,t.jsxs)(es.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(e_.A,{className:"h-5 w-5"}),"Inserisci Metri Posati - ",l.id_cavo]}),(0,t.jsx)(es.rr,{children:"Inserisci i metri effettivamente posati per il cavo e seleziona una bobina o usa BOBINA VUOTA"})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-y-auto space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4",children:[(0,t.jsx)("div",{className:"lg:col-span-2",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-200",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Informazioni Cavo"}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipologia:"})," ",l.tipologia||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Da:"})," ",l.ubicazione_partenza||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Formazione:"})," ",l.sezione||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"A:"})," ",l.ubicazione_arrivo||"N/A"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Metri teorici:"})," ",l.metri_teorici||"N/A"," m"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Gi\xe0 posati:"})," ",l.metratura_reale||0," m"]})]})]})}),(0,t.jsx)("div",{className:"lg:col-span-1",children:(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg border-2 border-blue-300 h-full",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"Metri da Installare"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"metri",className:"text-sm font-medium",children:"Metri Posati"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g.p,{id:"metri",type:"number",value:b.metri_posati,onChange:e=>v(a=>({...a,metri_posati:e.target.value})),placeholder:"Inserisci metri posati",disabled:C,step:"0.1",min:"0",className:"text-lg font-bold text-center border-2 border-blue-400 focus:border-blue-600",autoFocus:!0}),(0,t.jsx)("span",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-lg font-bold text-blue-600",children:"m"})]}),j.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:j.metri_posati}),N.metri_posati&&(0,t.jsx)("p",{className:"text-sm text-amber-600",children:N.metri_posati})]})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 text-lg",children:"Selezione Bobina"}),(0,t.jsx)("div",{className:"p-4 bg-gray-50 rounded-lg",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-12 gap-3 items-center",children:[(0,t.jsx)("div",{className:"sm:col-span-5",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(D.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(g.p,{placeholder:"ID, tipologia, formazione...",value:E,onChange:e=>I(e.target.value),className:"pl-10",disabled:C}),E&&(0,t.jsx)(x.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0",onClick:()=>I(""),children:(0,t.jsx)(z.A,{className:"h-4 w-4"})})]})}),(0,t.jsx)("div",{className:"sm:col-span-7",children:(0,t.jsxs)(x.$,{type:"button",variant:"BOBINA_VUOTA"===b.id_bobina?"default":"outline",className:"w-full h-10 font-bold flex items-center justify-center gap-2 ".concat("BOBINA_VUOTA"===b.id_bobina?"bg-green-600 hover:bg-green-700 text-white":"border-blue-400 text-blue-700 hover:bg-blue-50"),onClick:()=>{v(e=>({...e,id_bobina:"BOBINA_VUOTA"})),f(e=>{let a={...e};return delete a.id_bobina,a}),console.log("\uD83D\uDD04 InserisciMetriDialog: Usando BOBINA_VUOTA:",{saving:!1,metri_posati:b.metri_posati,id_bobina:"BOBINA_VUOTA",errorsCount:Object.keys(j).length})},disabled:C,children:["BOBINA_VUOTA"===b.id_bobina&&(0,t.jsx)(V.A,{className:"h-5 w-5"}),"BOBINA VUOTA"]})})]})}),S?(0,t.jsxs)("div",{className:"flex items-center justify-center p-8",children:[(0,t.jsx)(en.A,{className:"h-6 w-6 animate-spin mr-2"}),(0,t.jsx)("span",{children:"Caricamento bobine..."})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-green-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),"Bobine Compatibili (",M.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===M.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina compatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:M.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(b.id_bobina===e.id_bobina?"bg-green-100 border-green-500 shadow-md":"border-gray-200 hover:bg-green-50 hover:border-green-300"),onClick:()=>P(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[b.id_bobina===e.id_bobina&&(0,t.jsx)(V.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:B(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(u.E,{variant:"outline",className:"bg-green-100 text-green-800 border-green-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h4",{className:"font-medium text-amber-700 mb-2 flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),"Bobine Incompatibili (",R.length,")"]}),(0,t.jsx)("div",{className:"max-h-72 overflow-y-auto border rounded-lg",children:0===R.length?(0,t.jsx)("div",{className:"p-4 text-center text-gray-500",children:"Nessuna bobina incompatibile trovata"}):(0,t.jsx)("div",{className:"divide-y",children:R.map(e=>(0,t.jsx)("div",{className:"p-3 cursor-pointer transition-all border-2 rounded-lg mb-2 ".concat(b.id_bobina===e.id_bobina?"bg-amber-100 border-amber-500 shadow-md":"border-gray-200 hover:bg-amber-50 hover:border-amber-300"),onClick:()=>P(e),children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 flex-1 min-w-0",children:[b.id_bobina===e.id_bobina&&(0,t.jsx)(V.A,{className:"h-5 w-5 text-amber-600 flex-shrink-0"}),(0,t.jsx)("div",{className:"font-bold text-base min-w-fit",children:B(e.id_bobina)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 truncate",children:[e.tipologia," - ",e.sezione]})]}),(0,t.jsxs)(u.E,{variant:"outline",className:"bg-amber-100 text-amber-800 border-amber-300 font-medium min-w-fit",children:[e.metri_residui,"m"]})]})},e.id_bobina))})})]})]}),0===_.length&&!S&&(0,t.jsxs)(r.Fc,{className:"border-amber-200 bg-amber-50",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4 text-amber-600"}),(0,t.jsx)(r.TN,{className:"text-amber-800",children:"Non ci sono bobine disponibili. Puoi procedere con BOBINA VUOTA o aggiungere prima una nuova bobina."})]}),j.id_bobina&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:j.id_bobina})]})]})]}),(0,t.jsxs)(es.Es,{className:"flex-shrink-0 border-t pt-4 mt-4 flex justify-between items-center",children:[(0,t.jsx)("div",{children:"installato"===l.stato_installazione&&l.id_bobina&&(0,t.jsx)(x.$,{variant:"outline",onClick:()=>{O(!0)},disabled:C,className:"text-blue-600 border-blue-300 hover:bg-blue-50",children:"Modifica Bobina"})}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.$,{variant:"outline",onClick:U,disabled:C,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:$,disabled:C||!b.metri_posati||0>parseFloat(b.metri_posati)||!b.id_bobina,className:"bg-mariner-600 hover:bg-mariner-700 text-white disabled:bg-gray-400 disabled:text-gray-200",children:[C&&(0,t.jsx)(en.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Salva"]})]})]})]})}),(0,t.jsx)(eA,{open:T,onClose:()=>O(!1),cavo:l,onSuccess:e=>{c(e),O(!1),i()},onError:d})]}):null}var eS=i(88539),ek=i(25273),eE=i(17580);function eI(e){let{open:a,onClose:i,caviSelezionati:l,tipoComanda:n,onSuccess:c,onError:d}=e,{cantiere:u}=(0,o.A)(),[p,h]=(0,s.useState)({tipo_comanda:n||"POSA",responsabile:"",note:""}),[b,g]=(0,s.useState)([]),[j,f]=(0,s.useState)(!1),[N,y]=(0,s.useState)(!1),[C,w]=(0,s.useState)("");(0,s.useEffect)(()=>{a&&(h({tipo_comanda:n||"POSA",responsabile:"",note:""}),w(""),_())},[a,n]);let _=async()=>{if(u)try{y(!0);let e=await m.AR.getResponsabili(u.id_cantiere);g(e.data)}catch(e){g([])}finally{y(!1)}},A=async()=>{if(u){if(!p.responsabile)return void w("Seleziona un responsabile per la comanda");if(0===l.length)return void w("Seleziona almeno un cavo per la comanda");try{f(!0),w("");let e={tipo_comanda:p.tipo_comanda,responsabile:p.responsabile,note:p.note||null},a=await m.CV.createComandaWithCavi(u.id_cantiere,e,l);c("Comanda ".concat(a.data.codice_comanda," creata con successo per ").concat(l.length," cavi")),i()}catch(i){var e,a;d((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante la creazione della comanda")}finally{f(!1)}}};return(0,t.jsx)(es.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(es.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(es.c7,{children:[(0,t.jsxs)(es.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ek.A,{className:"h-5 w-5"}),"Crea Nuova Comanda"]}),(0,t.jsxs)(es.rr,{children:["Crea una nuova comanda per ",l.length," cavi selezionati"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)(el.J,{className:"text-sm font-medium",children:["Cavi Selezionati (",l.length,")"]}),(0,t.jsx)("div",{className:"mt-2 max-h-32 overflow-y-auto",children:(0,t.jsxs)("div",{className:"flex flex-wrap gap-1",children:[l.slice(0,10).map(e=>(0,t.jsx)("span",{className:"inline-block px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded",children:e},e)),l.length>10&&(0,t.jsxs)("span",{className:"inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded",children:["+",l.length-10," altri..."]})]})})]}),C&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:C})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"tipo",children:"Tipo Comanda *"}),(0,t.jsxs)(v.l6,{value:p.tipo_comanda,onValueChange:e=>h(a=>({...a,tipo_comanda:e})),children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{})}),(0,t.jsxs)(v.gC,{children:[(0,t.jsx)(v.eb,{value:"POSA",children:"\uD83D\uDD27 Posa Cavi"}),(0,t.jsx)(v.eb,{value:"COLLEGAMENTO_PARTENZA",children:"\uD83D\uDD0C Collegamento Partenza"}),(0,t.jsx)(v.eb,{value:"COLLEGAMENTO_ARRIVO",children:"⚡ Collegamento Arrivo"}),(0,t.jsx)(v.eb,{value:"CERTIFICAZIONE",children:"\uD83D\uDCCB Certificazione"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"responsabile",children:"Responsabile *"}),(0,t.jsxs)(v.l6,{value:p.responsabile,onValueChange:e=>h(a=>({...a,responsabile:e})),disabled:N,children:[(0,t.jsx)(v.bq,{children:(0,t.jsx)(v.yv,{placeholder:"Seleziona responsabile..."})}),(0,t.jsx)(v.gC,{children:b.map(e=>(0,t.jsx)(v.eb,{value:e.nome_responsabile,children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(eE.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:e.nome_responsabile}),e.numero_telefono&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["- ",e.numero_telefono]})]})},e.id))})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"note",children:"Note (opzionale)"}),(0,t.jsx)(eS.T,{id:"note",placeholder:"Inserisci eventuali note per la comanda...",value:p.note,onChange:e=>h(a=>({...a,note:e.target.value})),rows:3})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(el.J,{className:"text-sm font-medium",children:"Riepilogo Comanda"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",(e=>{switch(e){case"POSA":return"Posa Cavi";case"COLLEGAMENTO_PARTENZA":return"Collegamento Partenza";case"COLLEGAMENTO_ARRIVO":return"Collegamento Arrivo";case"CERTIFICAZIONE":return"Certificazione";default:return e}})(p.tipo_comanda)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Responsabile:"})," ",p.responsabile||"Non selezionato"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cavi:"})," ",l.length," selezionati"]}),p.note&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Note:"})," ",p.note]})]})]})]}),(0,t.jsxs)(es.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:j,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:A,disabled:j||!p.responsabile||0===l.length,children:[j?(0,t.jsx)(en.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ek.A,{className:"h-4 w-4 mr-2"}),"Crea Comanda"]})]})]})})}var eT=i(29869),eO=i(64261);function eF(e){let{open:a,onClose:i,tipo:l,onSuccess:n,onError:c}=e,{cantiere:d}=(0,o.A)(),[u,p]=(0,s.useState)(null),[h,b]=(0,s.useState)(""),[v,j]=(0,s.useState)(!1),[f,N]=(0,s.useState)(""),[y,C]=(0,s.useState)(0),w=(0,s.useRef)(null),_=async()=>{if(u&&d){if("cavi"===l&&!h.trim())return void N("Inserisci il codice revisione per l'importazione cavi");try{let e;if(j(!0),N(""),C(0),e="cavi"===l?await m.mg.importCavi(d.id_cantiere,u,h.trim()):await m.mg.importBobine(d.id_cantiere,u),C(100),e.data.success){let a=e.data.details,t=e.data.message;"cavi"===l&&(null==a?void 0:a.cavi_importati)?t+=" (".concat(a.cavi_importati," cavi importati)"):"bobine"===l&&(null==a?void 0:a.bobine_importate)&&(t+=" (".concat(a.bobine_importate," bobine importate)")),n(t),i()}else c(e.data.message||"Errore durante l'importazione")}catch(i){var e,a;c((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'importazione del file")}finally{j(!1),C(0)}}},A=()=>{v||(p(null),b(""),N(""),C(0),w.current&&(w.current.value=""),i())},z=()=>"cavi"===l?"Cavi":"Bobine";return(0,t.jsx)(es.lG,{open:a,onOpenChange:A,children:(0,t.jsxs)(es.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(es.c7,{children:[(0,t.jsxs)(es.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(eT.A,{className:"h-5 w-5"}),"Importa ",z()," da Excel"]}),(0,t.jsxs)(es.rr,{children:["Carica un file Excel per importare ",z().toLowerCase()," nel cantiere"]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(el.J,{className:"text-sm font-medium",children:"Requisiti File"}),(0,t.jsx)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:("cavi"===l?["File Excel (.xlsx o .xls)","Colonne richieste: ID_CAVO, SISTEMA, UTILITY, TIPOLOGIA, ecc.","Prima riga deve contenere le intestazioni","Codice revisione obbligatorio per tracciabilit\xe0"]:["File Excel (.xlsx o .xls)","Colonne richieste: NUMERO_BOBINA, UTILITY, TIPOLOGIA, METRI_TOTALI, ecc.","Prima riga deve contenere le intestazioni","I metri residui saranno impostati uguali ai metri totali"]).map((e,a)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)("span",{className:"text-blue-500 mt-0.5",children:"•"}),(0,t.jsx)("span",{children:e})]},a))})]}),f&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:f})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"file",children:"File Excel *"}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(g.p,{ref:w,id:"file",type:"file",accept:".xlsx,.xls",onChange:e=>{var a;let i=null==(a=e.target.files)?void 0:a[0];if(i){if(!["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.ms-excel"].includes(i.type)&&!i.name.toLowerCase().endsWith(".xlsx")&&!i.name.toLowerCase().endsWith(".xls"))return void N("Seleziona un file Excel valido (.xlsx o .xls)");p(i),N("")}},disabled:v,className:"flex-1"}),u&&(0,t.jsxs)("div",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(V.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"text-sm",children:"File selezionato"})]})]}),u&&(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[(0,t.jsx)(eO.A,{className:"h-4 w-4 inline mr-1"}),u.name," (",(u.size/1024/1024).toFixed(2)," MB)"]})]}),"cavi"===l&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(el.J,{htmlFor:"revisione",children:"Codice Revisione *"}),(0,t.jsx)(g.p,{id:"revisione",value:h,onChange:e=>b(e.target.value),placeholder:"es. REV001, V1.0, 2024-01",disabled:v}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Codice identificativo della revisione per tracciabilit\xe0 delle modifiche"})]}),v&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(en.A,{className:"h-4 w-4 animate-spin"}),(0,t.jsx)("span",{className:"text-sm",children:"Caricamento in corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(y,"%")}})})]}),u&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(el.J,{className:"text-sm font-medium",children:"Riepilogo Importazione"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Tipo:"})," ",z()]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File:"})," ",u.name]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Dimensione:"})," ",(u.size/1024/1024).toFixed(2)," MB"]}),"cavi"===l&&h&&(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Revisione:"})," ",h]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==d?void 0:d.nome_cantiere]})]})]})]}),(0,t.jsxs)(es.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:A,disabled:v,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:_,disabled:v||!u||"cavi"===l&&!h.trim(),children:[v?(0,t.jsx)(en.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(eT.A,{className:"h-4 w-4 mr-2"}),"Importa ",z()]})]})]})})}var eD=i(54213);function eL(e){let{open:a,onClose:i,onSuccess:l,onError:n}=e,{cantiere:c}=(0,o.A)(),[d,u]=(0,s.useState)({cavi:!0,bobine:!0,comande:!1,certificazioni:!1,responsabili:!1}),[h,b]=(0,s.useState)(!1),[g,v]=(0,s.useState)(""),j=(e,a)=>{u(i=>({...i,[e]:a}))},f=async()=>{if(c)try{b(!0);let e=await m.mg.exportCavi(c.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","cavi_".concat(c.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("Export cavi completato con successo")}catch(i){var e,a;n((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei cavi")}finally{b(!1)}},N=async()=>{if(c)try{b(!0);let e=await m.mg.exportBobine(c.id_cantiere),a=window.URL.createObjectURL(new Blob([e.data])),i=document.createElement("a");i.href=a,i.setAttribute("download","bobine_".concat(c.nome_cantiere,"_").concat(new Date().toISOString().split("T")[0],".xlsx")),document.body.appendChild(i),i.click(),i.remove(),window.URL.revokeObjectURL(a),l("Export bobine completato con successo")}catch(i){var e,a;n((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export delle bobine")}finally{b(!1)}},y=async()=>{if(c)try{b(!0),v("");let e=[];d.cavi&&e.push(f()),d.bobine&&e.push(N()),d.comande,d.certificazioni,d.responsabili,await Promise.all(e);let a=Object.values(d).filter(Boolean).length;l("Export completato: ".concat(a," file scaricati")),i()}catch(i){var e,a;n((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message||"Errore durante l'export dei dati")}finally{b(!1)}},C=[{key:"cavi",label:"Cavi",description:"Esporta tutti i cavi del cantiere con stato, collegamenti e certificazioni",icon:(0,t.jsx)(eD.A,{className:"h-4 w-4"}),available:!0},{key:"bobine",label:"Bobine",description:"Esporta tutte le bobine del parco cavi con metri residui e assegnazioni",icon:(0,t.jsx)(eO.A,{className:"h-4 w-4"}),available:!0},{key:"comande",label:"Comande",description:"Esporta tutte le comande con cavi assegnati e responsabili",icon:(0,t.jsx)(eO.A,{className:"h-4 w-4"}),available:!1},{key:"certificazioni",label:"Certificazioni",description:"Esporta tutte le certificazioni con esiti e responsabili",icon:(0,t.jsx)(eO.A,{className:"h-4 w-4"}),available:!1},{key:"responsabili",label:"Responsabili",description:"Esporta tutti i responsabili con contatti e ruoli",icon:(0,t.jsx)(eO.A,{className:"h-4 w-4"}),available:!1}];return(0,t.jsx)(es.lG,{open:a,onOpenChange:i,children:(0,t.jsxs)(es.Cf,{className:"sm:max-w-[600px]",children:[(0,t.jsxs)(es.c7,{children:[(0,t.jsxs)(es.L3,{className:"flex items-center gap-2",children:[(0,t.jsx)(ex.A,{className:"h-5 w-5"}),"Esporta Dati Cantiere"]}),(0,t.jsxs)(es.rr,{children:["Seleziona i dati da esportare dal cantiere ",null==c?void 0:c.nome_cantiere]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[g&&(0,t.jsxs)(r.Fc,{variant:"destructive",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:g})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsx)(el.J,{className:"text-sm font-medium",children:"Seleziona Dati da Esportare"}),C.map(e=>(0,t.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border ".concat(e.available?"bg-white":"bg-gray-50"),children:[(0,t.jsx)(p.S,{id:e.key,checked:d[e.key],onCheckedChange:a=>j(e.key,a),disabled:!e.available||h}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[e.icon,(0,t.jsxs)(el.J,{htmlFor:e.key,className:"font-medium ".concat(e.available?"":"text-gray-500"),children:[e.label,!e.available&&(0,t.jsx)("span",{className:"ml-2 text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded",children:"In sviluppo"})]})]}),(0,t.jsx)("p",{className:"text-sm mt-1 ".concat(e.available?"text-gray-600":"text-gray-400"),children:e.description})]})]},e.key))]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)(el.J,{className:"text-sm font-medium",children:"Informazioni Export"}),(0,t.jsxs)("ul",{className:"mt-2 space-y-1 text-sm text-gray-600",children:[(0,t.jsx)("li",{children:"• I file saranno scaricati in formato Excel (.xlsx)"}),(0,t.jsx)("li",{children:"• I nomi file includeranno data e nome cantiere"}),(0,t.jsx)("li",{children:"• I dati esportati riflettono lo stato attuale del database"}),(0,t.jsx)("li",{children:"• L'export non modifica i dati originali"})]})]}),Object.values(d).filter(Boolean).length>0&&(0,t.jsxs)("div",{className:"p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)(el.J,{className:"text-sm font-medium",children:"Riepilogo Export"}),(0,t.jsxs)("div",{className:"mt-2 space-y-1 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Cantiere:"})," ",null==c?void 0:c.nome_cantiere]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"File da scaricare:"})," ",Object.values(d).filter(Boolean).length]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("strong",{children:"Data export:"})," ",new Date().toLocaleDateString("it-IT")]})]})]})]}),(0,t.jsxs)(es.Es,{children:[(0,t.jsx)(x.$,{variant:"outline",onClick:i,disabled:h,children:"Annulla"}),(0,t.jsxs)(x.$,{onClick:y,disabled:h||0===Object.values(d).filter(Boolean).length,children:[h?(0,t.jsx)(en.A,{className:"h-4 w-4 animate-spin mr-2"}):(0,t.jsx)(ex.A,{className:"h-4 w-4 mr-2"}),"Esporta ",Object.values(d).filter(Boolean).length>0?"(".concat(Object.values(d).filter(Boolean).length,")"):""]})]})]})})}function eB(){let{user:e,isAuthenticated:a,isLoading:i}=(0,o.A)(),{cantiereId:x,cantiere:u,isValidCantiere:p,isLoading:h,error:b}=(0,c.jV)(),g=(0,l.useRouter)(),v=e=>{let{title:a,description:i,variant:t}=e},[j,f]=(0,s.useState)([]),[N,y]=(0,s.useState)([]),[C,w]=(0,s.useState)(!0),[_,A]=(0,s.useState)(""),[z,S]=(0,s.useState)([]),[k,E]=(0,s.useState)(!0),[I,T]=(0,s.useState)([]),[O,F]=(0,s.useState)("");(0,s.useEffect)(()=>{T(j)},[j]);let[D,L]=(0,s.useState)({open:!1,cavo:null}),[B,M]=(0,s.useState)({open:!1,cavo:null}),[R,P]=(0,s.useState)({open:!1,cavo:null}),[$,V]=(0,s.useState)({open:!1,cavo:null}),[U,J]=(0,s.useState)({open:!1,cavo:null}),[q,Z]=(0,s.useState)({open:!1,cavo:null}),[K,H]=(0,s.useState)({open:!1,cavo:null,error:""}),[Q,Y]=(0,s.useState)({visible:!1,message:""}),[ee,ea]=(0,s.useState)({open:!1}),[ei,es]=(0,s.useState)({open:!1}),[el,er]=(0,s.useState)(!1),[eo,ed]=(0,s.useState)({totali:0,installati:0,collegati:0,certificati:0,percentualeInstallazione:0,percentualeCollegamento:0,percentualeCertificazione:0,metriTotali:0,metriInstallati:0,metriCollegati:0,metriCertificati:0});(0,s.useEffect)(()=>{i||a||g.push("/login")},[a,i,g]);let em=u||(x&&x>0?{id_cantiere:x,commessa:"Cantiere ".concat(x)}:null);(0,s.useEffect)(()=>{p&&x&&x>0&&!h?(eu(),ex()):h||p||(f([]),y([]),A(b||"Nessun cantiere selezionato"))},[x,p,h,b]);let ex=async()=>{try{let e=await fetch("http://localhost:8001/api/cavi/".concat(x,"/revisione-corrente"),{headers:{Authorization:"Bearer ".concat(localStorage.getItem("token")),"Content-Type":"application/json"}});if(e.ok){let a=await e.json();F(a.revisione_corrente||"00")}else F("00")}catch(e){F("00")}},eu=async()=>{try{w(!0),A("");try{let e=await m.At.getCavi(x),a=e.filter(e=>!e.spare),i=e.filter(e=>e.spare);f(a),y(i),ep(a)}catch(e){throw e}}catch(i){var e,a;A("Errore nel caricamento dei cavi: ".concat((null==(a=i.response)||null==(e=a.data)?void 0:e.detail)||i.message))}finally{w(!1)}},ep=e=>{let a=e.length,i=e.filter(e=>(e.metri_posati||e.metratura_reale||0)>0).length,t=e.filter(e=>3===(e.collegamento||e.collegamenti)).length,s=e.filter(e=>e.certificato).length,l=e.reduce((e,a)=>e+(a.metri_teorici||0),0),n=e.reduce((e,a)=>e+(a.metri_posati||0),0);ed({totali:a,installati:i,collegati:t,certificati:s,percentualeInstallazione:a>0?Math.round(i/a*100):0,percentualeCollegamento:a>0?Math.round(t/a*100):0,percentualeCertificazione:a>0?Math.round(s/a*100):0,metriTotali:l,metriInstallati:n,metriCollegati:e.filter(e=>3===e.collegamento).reduce((e,a)=>e+(a.metri_posati||0),0),metriCertificati:e.filter(e=>e.certificato).reduce((e,a)=>e+(a.metri_posati||0),0)})},eh=(e,a,i)=>{switch(a){case"insert_meters":L({open:!0,cavo:e});break;case"modify_reel":M({open:!0,cavo:e});break;case"view_command":v({title:"Visualizza Comanda",description:"Apertura comanda ".concat(i," per cavo ").concat(e.id_cavo)});break;case"connect_cable":case"connect_arrival":case"connect_departure":case"manage_connections":case"disconnect_cable":P({open:!0,cavo:e});break;case"create_certificate":Z({open:!0,cavo:e});break;case"generate_pdf":J({open:!0,cavo:e})}},eN=(e,a)=>{switch(a){case"view_details":v({title:"Visualizza Dettagli",description:"Apertura dettagli per cavo ".concat(e.id_cavo)});break;case"edit":v({title:"Modifica Cavo",description:"Funzione modifica cavo in sviluppo"});break;case"delete":v({title:"Elimina Cavo",description:"Funzione eliminazione cavo in sviluppo",variant:"destructive"});break;case"add_new":v({title:"Aggiungi Nuovo Cavo",description:"Funzione aggiunta nuovo cavo in sviluppo"});break;case"select":z.includes(e.id_cavo)?(S(z.filter(a=>a!==e.id_cavo)),v({title:"Cavo Deselezionato",description:"Cavo ".concat(e.id_cavo," deselezionato")})):(S([...z,e.id_cavo]),v({title:"Cavo Selezionato",description:"Cavo ".concat(e.id_cavo," selezionato")}));break;case"copy_id":navigator.clipboard.writeText(e.id_cavo),v({title:"ID Copiato",description:"ID cavo ".concat(e.id_cavo," copiato negli appunti")});break;case"copy_details":let i="ID: ".concat(e.id_cavo,", Tipologia: ").concat(e.tipologia,", Formazione: ").concat(e.formazione||e.sezione,", Metri: ").concat(e.metri_teorici);navigator.clipboard.writeText(i),v({title:"Dettagli Copiati",description:"Dettagli cavo copiati negli appunti"});break;case"add_to_command":v({title:"Aggiungi a Comanda",description:"Funzione aggiunta a comanda in sviluppo"});break;case"remove_from_command":v({title:"Rimuovi da Comanda",description:"Funzione rimozione da comanda in sviluppo"});break;case"create_command_posa":ea({open:!0,tipoComanda:"POSA"});break;case"create_command_collegamento_partenza":ea({open:!0,tipoComanda:"COLLEGAMENTO_PARTENZA"});break;case"create_command_collegamento_arrivo":ea({open:!0,tipoComanda:"COLLEGAMENTO_ARRIVO"});break;case"create_command_certificazione":ea({open:!0,tipoComanda:"CERTIFICAZIONE"});break;case"add_multiple_to_command":v({title:"Aggiungi Tutti a Comanda",description:"Funzione aggiunta multipla a comanda in sviluppo"});break;case"remove_multiple_from_commands":v({title:"Rimuovi Tutti dalle Comande",description:"Funzione rimozione multipla dalle comande in sviluppo"});break;default:v({title:"Azione non implementata",description:"Azione ".concat(a," non ancora implementata")})}},ey=e=>{v({title:"Operazione completata",description:e}),eu()},eC=e=>{v({title:"Errore",description:e,variant:"destructive"})},e_=async(e,a,i)=>{try{if(!u)throw Error("Cantiere non selezionato");let t="";switch(console.log("\uD83D\uDE80 ModificaBobina: Operazione:",{cantiere:u.id_cantiere,cavo:e,option:i,bobinaId:a}),i){case"cambia-bobina":await m.At.updateBobina(u.id_cantiere,e,a,!0),t="Bobina ".concat(a," assegnata al cavo ").concat(e);break;case"bobina-vuota":await m.At.updateBobina(u.id_cantiere,e,"BOBINA_VUOTA",!1),t="Bobina rimossa dal cavo ".concat(e);break;case"annulla-posa":await m.At.cancelInstallation(u.id_cantiere,e),t="Installazione annullata per il cavo ".concat(e," - metri restituiti alla bobina");break;default:throw Error("Operazione non riconosciuta")}ey(t),eu()}catch(e){var t,s;eC((null==(s=e.response)||null==(t=s.data)?void 0:t.detail)||e.message||"Errore durante la modifica della bobina")}};return C&&p?(0,t.jsxs)("div",{className:"flex items-center justify-center min-h-screen",children:[(0,t.jsx)(en.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Caricamento cavi..."})]}):(0,t.jsx)(d.u,{children:(0,t.jsxs)("div",{className:"max-w-[90%] mx-auto p-6",children:[_&&(0,t.jsxs)(r.Fc,{variant:"destructive",className:"mb-6",children:[(0,t.jsx)(G.A,{className:"h-4 w-4"}),(0,t.jsx)(r.TN,{children:_})]}),(0,t.jsx)(et,{cavi:j,filteredCavi:I,revisioneCorrente:O,className:"mb-2"}),(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsx)(W,{cavi:j,loading:C,selectionEnabled:k,selectedCavi:z,onSelectionChange:S,onStatusAction:eh,onContextMenuAction:eN})}),N.length>0&&(0,t.jsx)("div",{className:"mb-8",children:(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsxs)(n.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(X.A,{className:"h-5 w-5"}),(0,t.jsxs)("span",{children:["Cavi Spare (",N.length,")"]})]})}),(0,t.jsx)(n.Wu,{children:(0,t.jsx)(W,{cavi:N,loading:C,selectionEnabled:!1,onStatusAction:eh,onContextMenuAction:eN})})]})}),(0,t.jsx)(ez,{open:D.open,onClose:()=>L({open:!1,cavo:null}),cavo:D.cavo,cantiere:em,onSuccess:ey,onError:eC}),(0,t.jsx)(ew,{open:B.open,onClose:()=>M({open:!1,cavo:null}),cavo:B.cavo,onSave:e_}),(0,t.jsx)(ec,{open:R.open,onClose:()=>P({open:!1,cavo:null}),cavo:R.cavo,onSuccess:e=>{v({title:"Successo",description:e}),eu()},onError:e=>{v({title:"Errore",description:e,variant:"destructive"})}}),(0,t.jsx)(eb,{open:$.open,onClose:()=>V({open:!1,cavo:null}),cavo:$.cavo,onConfirm:()=>{Y({visible:!0,message:"Cavo scollegato con successo"}),V({open:!1,cavo:null}),eu()},onError:e=>{H({open:!0,cavo:$.cavo,error:e}),V({open:!1,cavo:null})}}),(0,t.jsx)(eg,{open:U.open,onClose:()=>J({open:!1,cavo:null}),cavo:U.cavo,onSuccess:()=>{Y({visible:!0,message:"PDF generato con successo"}),J({open:!1,cavo:null})},onError:e=>{H({open:!0,cavo:U.cavo,error:e}),J({open:!1,cavo:null})}}),(0,t.jsx)(ej,{open:q.open,onClose:()=>Z({open:!1,cavo:null}),cavo:q.cavo,onSuccess:()=>{Y({visible:!0,message:"Certificazione completata con successo"}),Z({open:!1,cavo:null}),eu()},onError:e=>{H({open:!0,cavo:q.cavo,error:e}),Z({open:!1,cavo:null})}}),(0,t.jsx)(ev,{open:K.open,onClose:()=>H({open:!1,cavo:null,error:""}),cavo:K.cavo,error:K.error,onRetry:()=>{H({open:!1,cavo:null,error:""}),K.cavo&&Z({open:!0,cavo:K.cavo})}}),(0,t.jsx)(ef,{visible:Q.visible,message:Q.message,onClose:()=>Y({visible:!1,message:""})}),(0,t.jsx)(eI,{open:ee.open,onClose:()=>ea({open:!1}),caviSelezionati:z,tipoComanda:ee.tipoComanda,onSuccess:ey,onError:eC}),(0,t.jsx)(eF,{open:ei.open,onClose:()=>es({open:!1}),tipo:ei.tipo||"cavi",onSuccess:ey,onError:eC}),(0,t.jsx)(eL,{open:el,onClose:()=>er(!1),onSuccess:ey,onError:eC})]})})}},47262:(e,a,i)=>{"use strict";i.d(a,{S:()=>r});var t=i(95155);i(12115);var s=i(76981),l=i(5196),n=i(59434);function r(e){let{className:a,...i}=e;return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...i,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(l.A,{className:"size-3.5"})})})}},54165:(e,a,i)=>{"use strict";i.d(a,{Cf:()=>m,Es:()=>u,L3:()=>p,c7:()=>x,lG:()=>r,rr:()=>h,zM:()=>o});var t=i(95155);i(12115);var s=i(15452),l=i(54416),n=i(59434);function r(e){let{...a}=e;return(0,t.jsx)(s.bL,{"data-slot":"dialog",...a})}function o(e){let{...a}=e;return(0,t.jsx)(s.l9,{"data-slot":"dialog-trigger",...a})}function c(e){let{...a}=e;return(0,t.jsx)(s.ZL,{"data-slot":"dialog-portal",...a})}function d(e){let{className:a,...i}=e;return(0,t.jsx)(s.hJ,{"data-slot":"dialog-overlay",className:(0,n.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...i})}function m(e){let{className:a,children:i,showCloseButton:r=!0,...o}=e;return(0,t.jsxs)(c,{"data-slot":"dialog-portal",children:[(0,t.jsx)(d,{}),(0,t.jsxs)(s.UC,{"data-slot":"dialog-content",className:(0,n.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",a),...o,children:[i,r&&(0,t.jsxs)(s.bm,{"data-slot":"dialog-close",className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[(0,t.jsx)(l.A,{}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function x(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"dialog-header",className:(0,n.cn)("flex flex-col gap-2 text-center sm:text-left",a),...i})}function u(e){let{className:a,...i}=e;return(0,t.jsx)("div",{"data-slot":"dialog-footer",className:(0,n.cn)("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",a),...i})}function p(e){let{className:a,...i}=e;return(0,t.jsx)(s.hE,{"data-slot":"dialog-title",className:(0,n.cn)("text-lg leading-none font-semibold",a),...i})}function h(e){let{className:a,...i}=e;return(0,t.jsx)(s.VY,{"data-slot":"dialog-description",className:(0,n.cn)("text-muted-foreground text-sm",a),...i})}},87481:(e,a,i)=>{"use strict";i.d(a,{dj:()=>x});var t=i(12115);let s=0,l=new Map,n=e=>{if(l.has(e))return;let a=setTimeout(()=>{l.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);l.set(e,a)},r=(e,a)=>{switch(a.type){case"ADD_TOAST":return{...e,toasts:[a.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===a.toast.id?{...e,...a.toast}:e)};case"DISMISS_TOAST":{let{toastId:i}=a;return i?n(i):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===i||void 0===i?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===a.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==a.toastId)}}},o=[],c={toasts:[]};function d(e){c=r(c,e),o.forEach(e=>{e(c)})}function m(e){let{...a}=e,i=(s=(s+1)%Number.MAX_VALUE).toString(),t=()=>d({type:"DISMISS_TOAST",toastId:i});return d({type:"ADD_TOAST",toast:{...a,id:i,open:!0,onOpenChange:e=>{e||t()}}}),{id:i,dismiss:t,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:i}})}}function x(){let[e,a]=(0,t.useState)(c);return(0,t.useEffect)(()=>(o.push(a),()=>{let e=o.indexOf(a);e>-1&&o.splice(e,1)}),[]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},88539:(e,a,i)=>{"use strict";i.d(a,{T:()=>n});var t=i(95155),s=i(12115),l=i(59434);let n=s.forwardRef((e,a)=>{let{className:i,...s}=e;return(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",i),ref:a,...s})});n.displayName="Textarea"},93304:()=>{}},e=>{var a=a=>e(e.s=a);e.O(0,[1161,3455,3464,4295,1587,1807,861,6818,283,1642,3587,8441,1684,7358],()=>a(3469)),_N_E=e.O()}]);