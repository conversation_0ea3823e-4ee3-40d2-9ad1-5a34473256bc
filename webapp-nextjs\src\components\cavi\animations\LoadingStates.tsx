'use client'

import React from 'react'
import { Loader2, Cable, Zap, Award, Download } from 'lucide-react'

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  return (
    <Loader2 
      className={`animate-spin ${sizeClasses[size]} ${className}`}
      aria-label="Caricamento in corso"
    />
  )
}

interface ActionLoadingProps {
  action: 'connect' | 'disconnect' | 'certify' | 'generate_pdf' | 'install' | 'loading'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export const ActionLoading: React.FC<ActionLoadingProps> = ({
  action,
  size = 'md',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  const getIcon = () => {
    switch (action) {
      case 'connect':
        return <Zap className={`animate-pulse ${sizeClasses[size]} text-blue-500`} />
      case 'disconnect':
        return <Zap className={`animate-pulse ${sizeClasses[size]} text-orange-500`} />
      case 'certify':
        return <Award className={`animate-pulse ${sizeClasses[size]} text-purple-500`} />
      case 'generate_pdf':
        return <Download className={`animate-bounce ${sizeClasses[size]} text-green-500`} />
      case 'install':
        return <Cable className={`animate-pulse ${sizeClasses[size]} text-yellow-500`} />
      default:
        return <Loader2 className={`animate-spin ${sizeClasses[size]} text-gray-500`} />
    }
  }

  return (
    <div className={`flex items-center justify-center ${className}`}>
      {getIcon()}
    </div>
  )
}

interface TableLoadingProps {
  rows?: number
  columns?: number
}

export const TableLoading: React.FC<TableLoadingProps> = ({
  rows = 5,
  columns = 6
}) => {
  return (
    <div className="space-y-3 p-4">
      {/* Header skeleton */}
      <div className="flex space-x-4">
        {Array.from({ length: columns }).map((_, i) => (
          <div
            key={i}
            className="h-4 bg-gray-200 rounded animate-pulse flex-1"
          />
        ))}
      </div>
      
      {/* Row skeletons */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div
              key={colIndex}
              className="h-8 bg-gray-100 rounded animate-pulse flex-1"
              style={{
                animationDelay: `${(rowIndex * columns + colIndex) * 50}ms`
              }}
            />
          ))}
        </div>
      ))}
    </div>
  )
}

interface KpiLoadingProps {
  count?: number
}

export const KpiLoading: React.FC<KpiLoadingProps> = ({ count = 7 }) => {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2 p-4">
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className="flex items-center space-x-1.5 bg-gray-50 px-1.5 py-1 rounded-lg animate-pulse"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <div className="h-3.5 w-3.5 bg-gray-200 rounded" />
          <div className="flex-1">
            <div className="h-4 bg-gray-200 rounded mb-1" />
            <div className="h-3 bg-gray-100 rounded w-3/4" />
          </div>
        </div>
      ))}
    </div>
  )
}

interface ProgressBarProps {
  progress: number
  animated?: boolean
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple'
  size?: 'sm' | 'md' | 'lg'
  showPercentage?: boolean
  className?: string
}

export const ProgressBar: React.FC<ProgressBarProps> = ({
  progress,
  animated = true,
  color = 'blue',
  size = 'md',
  showPercentage = true,
  className = ''
}) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500',
    purple: 'bg-purple-500'
  }

  const sizeClasses = {
    sm: 'h-1',
    md: 'h-2',
    lg: 'h-3'
  }

  const clampedProgress = Math.min(Math.max(progress, 0), 100)

  return (
    <div className={`w-full ${className}`}>
      {showPercentage && (
        <div className="flex justify-between text-xs text-gray-600 mb-1">
          <span>Progresso</span>
          <span>{clampedProgress.toFixed(1)}%</span>
        </div>
      )}
      <div className={`w-full bg-gray-200 rounded-full ${sizeClasses[size]}`}>
        <div
          className={`${sizeClasses[size]} rounded-full ${colorClasses[color]} ${
            animated ? 'transition-all duration-500 ease-out' : ''
          }`}
          style={{ width: `${clampedProgress}%` }}
        />
      </div>
    </div>
  )
}

interface PulseLoadingProps {
  children: React.ReactNode
  loading?: boolean
  className?: string
}

export const PulseLoading: React.FC<PulseLoadingProps> = ({
  children,
  loading = false,
  className = ''
}) => {
  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        {children}
      </div>
    )
  }

  return <>{children}</>
}

interface FadeInProps {
  children: React.ReactNode
  delay?: number
  duration?: number
  className?: string
}

export const FadeIn: React.FC<FadeInProps> = ({
  children,
  delay = 0,
  duration = 300,
  className = ''
}) => {
  return (
    <div
      className={`animate-in fade-in ${className}`}
      style={{
        animationDelay: `${delay}ms`,
        animationDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  )
}

interface SlideInProps {
  children: React.ReactNode
  direction?: 'up' | 'down' | 'left' | 'right'
  delay?: number
  duration?: number
  className?: string
}

export const SlideIn: React.FC<SlideInProps> = ({
  children,
  direction = 'up',
  delay = 0,
  duration = 300,
  className = ''
}) => {
  const directionClasses = {
    up: 'animate-in slide-in-from-bottom-2',
    down: 'animate-in slide-in-from-top-2',
    left: 'animate-in slide-in-from-right-2',
    right: 'animate-in slide-in-from-left-2'
  }

  return (
    <div
      className={`${directionClasses[direction]} ${className}`}
      style={{
        animationDelay: `${delay}ms`,
        animationDuration: `${duration}ms`
      }}
    >
      {children}
    </div>
  )
}

// Staggered animation for lists
interface StaggeredListProps {
  children: React.ReactNode[]
  staggerDelay?: number
  className?: string
}

export const StaggeredList: React.FC<StaggeredListProps> = ({
  children,
  staggerDelay = 50,
  className = ''
}) => {
  return (
    <div className={className}>
      {children.map((child, index) => (
        <FadeIn key={index} delay={index * staggerDelay}>
          {child}
        </FadeIn>
      ))}
    </div>
  )
}
