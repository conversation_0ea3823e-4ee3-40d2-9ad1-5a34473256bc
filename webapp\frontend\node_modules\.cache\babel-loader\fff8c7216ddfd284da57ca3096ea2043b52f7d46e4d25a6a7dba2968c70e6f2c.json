{"ast": null, "code": "var _jsxFileName = \"C:\\\\CMS\\\\webapp\\\\frontend\\\\src\\\\pages\\\\cavi\\\\CertificazioneCaviPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box, Alert, Button } from '@mui/material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CertificazioneCaviPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const certificazioneRef = useRef();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Con la nuova interfaccia unificata, non abbiamo più bisogno di gestire route specifiche\n  useEffect(() => {\n    console.log('CertificazioneCaviPage caricata per cantiere:', cantiereId);\n  }, [location.pathname, cantiereId]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n  // Gestisce le notifiche\n  const handleSuccess = message => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n  const handleError = message => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n  if (!cantiereId || isNaN(cantiereId)) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: [/*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mb: 2\n        },\n        children: \"Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        onClick: handleBackToCantieri,\n        children: \"Torna ai Cantieri\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 3,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'flex-end'\n      },\n      children: /*#__PURE__*/_jsxDEV(AdminHomeButton, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CertificazioneCaviImproved, {\n      ref: certificazioneRef,\n      cantiereId: cantiereId,\n      onSuccess: handleSuccess,\n      onError: handleError\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(CertificazioneCaviPage, \"lpfA7BBZH3bnEfZyTqsUUuscvbY=\", false, function () {\n  return [useNavigate, useLocation];\n});\n_c = CertificazioneCaviPage;\nexport default CertificazioneCaviPage;\nvar _c;\n$RefreshReg$(_c, \"CertificazioneCaviPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "<PERSON><PERSON>", "<PERSON><PERSON>", "useNavigate", "useLocation", "AdminHomeButton", "CertificazioneCaviImproved", "jsxDEV", "_jsxDEV", "CertificazioneCaviPage", "_s", "navigate", "location", "certificazioneRef", "cantiereId", "parseInt", "localStorage", "getItem", "console", "log", "pathname", "handleBackToCantieri", "handleSuccess", "message", "handleError", "error", "isNaN", "children", "severity", "sx", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "onClick", "display", "alignItems", "justifyContent", "ref", "onSuccess", "onError", "_c", "$RefreshReg$"], "sources": ["C:/CMS/webapp/frontend/src/pages/cavi/CertificazioneCaviPage.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport {\n  Box,\n  Alert,\n  Button\n} from '@mui/material';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport AdminHomeButton from '../../components/common/AdminHomeButton';\nimport CertificazioneCaviImproved from '../../components/cavi/CertificazioneCaviImproved';\n\nconst CertificazioneCaviPage = () => {\n  const navigate = useNavigate();\n  const location = useLocation();\n  const certificazioneRef = useRef();\n\n  // Recupera l'ID del cantiere dal localStorage\n  const cantiereId = parseInt(localStorage.getItem('selectedCantiereId'), 10);\n\n  // Con la nuova interfaccia unificata, non abbiamo più bisogno di gestire route specifiche\n  useEffect(() => {\n    console.log('CertificazioneCaviPage caricata per cantiere:', cantiereId);\n  }, [location.pathname, cantiereId]);\n\n  // Torna alla lista dei cantieri\n  const handleBackToCantieri = () => {\n    navigate('/dashboard/cantieri');\n  };\n\n\n\n  // Gestisce le notifiche\n  const handleSuccess = (message) => {\n    // Qui puoi implementare una notifica di successo se necessario\n    console.log('Successo:', message);\n  };\n\n  const handleError = (message) => {\n    // Qui puoi implementare una notifica di errore se necessario\n    console.error('Errore:', message);\n  };\n\n  if (!cantiereId || isNaN(cantiereId)) {\n    return (\n      <Box>\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          Nessun cantiere selezionato o ID cantiere non valido. Torna alla pagina dei cantieri.\n        </Alert>\n        <Button\n          variant=\"contained\"\n          onClick={handleBackToCantieri}\n        >\n          Torna ai Cantieri\n        </Button>\n      </Box>\n    );\n  }\n\n  return (\n    <Box>\n      <Box sx={{ mb: 3, display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>\n        <AdminHomeButton />\n      </Box>\n\n\n\n      <CertificazioneCaviImproved\n        ref={certificazioneRef}\n        cantiereId={cantiereId}\n        onSuccess={handleSuccess}\n        onError={handleError}\n      />\n    </Box>\n  );\n};\n\nexport default CertificazioneCaviPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SACEC,GAAG,EACHC,KAAK,EACLC,MAAM,QACD,eAAe;AACtB,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,OAAOC,eAAe,MAAM,yCAAyC;AACrE,OAAOC,0BAA0B,MAAM,kDAAkD;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1F,MAAMC,sBAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAMS,iBAAiB,GAAGd,MAAM,CAAC,CAAC;;EAElC;EACA,MAAMe,UAAU,GAAGC,QAAQ,CAACC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC,EAAE,EAAE,CAAC;;EAE3E;EACAnB,SAAS,CAAC,MAAM;IACdoB,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAEL,UAAU,CAAC;EAC1E,CAAC,EAAE,CAACF,QAAQ,CAACQ,QAAQ,EAAEN,UAAU,CAAC,CAAC;;EAEnC;EACA,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IACjCV,QAAQ,CAAC,qBAAqB,CAAC;EACjC,CAAC;;EAID;EACA,MAAMW,aAAa,GAAIC,OAAO,IAAK;IACjC;IACAL,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEI,OAAO,CAAC;EACnC,CAAC;EAED,MAAMC,WAAW,GAAID,OAAO,IAAK;IAC/B;IACAL,OAAO,CAACO,KAAK,CAAC,SAAS,EAAEF,OAAO,CAAC;EACnC,CAAC;EAED,IAAI,CAACT,UAAU,IAAIY,KAAK,CAACZ,UAAU,CAAC,EAAE;IACpC,oBACEN,OAAA,CAACR,GAAG;MAAA2B,QAAA,gBACFnB,OAAA,CAACP,KAAK;QAAC2B,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC,eACR1B,OAAA,CAACN,MAAM;QACLiC,OAAO,EAAC,WAAW;QACnBC,OAAO,EAAEf,oBAAqB;QAAAM,QAAA,EAC/B;MAED;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAEV;EAEA,oBACE1B,OAAA,CAACR,GAAG;IAAA2B,QAAA,gBACFnB,OAAA,CAACR,GAAG;MAAC6B,EAAE,EAAE;QAAEC,EAAE,EAAE,CAAC;QAAEO,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAW,CAAE;MAAAZ,QAAA,eACpFnB,OAAA,CAACH,eAAe;QAAA0B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAIN1B,OAAA,CAACF,0BAA0B;MACzBkC,GAAG,EAAE3B,iBAAkB;MACvBC,UAAU,EAAEA,UAAW;MACvB2B,SAAS,EAAEnB,aAAc;MACzBoB,OAAO,EAAElB;IAAY;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxB,EAAA,CA/DID,sBAAsB;EAAA,QACTN,WAAW,EACXC,WAAW;AAAA;AAAAuC,EAAA,GAFxBlC,sBAAsB;AAiE5B,eAAeA,sBAAsB;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}