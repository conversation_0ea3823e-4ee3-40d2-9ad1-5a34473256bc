"""
Test di Sicurezza per il Sistema di Gestione Password
Testa vulnerabilità comuni e protezioni implementate.
"""

import pytest
import time
import hashlib
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from backend.main import app
from backend.database import get_db
from backend.models.user import User
from backend.models.cantiere import Cantiere
from backend.models.security_models import PasswordResetToken, RateLimiting
from backend.core.password_security import (
    password_validator, token_manager, rate_limiter, password_hasher
)

client = TestClient(app)

class TestPasswordValidation:
    """Test per la validazione delle password."""
    
    def test_weak_passwords_rejected(self):
        """Test che le password deboli vengano rifiutate."""
        weak_passwords = [
            "123456",
            "password",
            "admin",
            "qwerty",
            "abc123",
            "12345678",
            "password123"
        ]
        
        for password in weak_passwords:
            is_valid, error, score = password_validator.validate_password(password)
            assert not is_valid, f"Password debole accettata: {password}"
            assert score < 3, f"Score troppo alto per password debole: {password}"
    
    def test_strong_passwords_accepted(self):
        """Test che le password forti vengano accettate."""
        strong_passwords = [
            "MyStr0ng!P@ssw0rd",
            "C0mpl3x#Passw0rd!",
            "S3cur3$P@ssw0rd2024",
            "Unbreakable!P@ss123"
        ]
        
        for password in strong_passwords:
            is_valid, error, score = password_validator.validate_password(password)
            assert is_valid, f"Password forte rifiutata: {password} - {error}"
            assert score >= 3, f"Score troppo basso per password forte: {password}"
    
    def test_password_length_limits(self):
        """Test dei limiti di lunghezza password."""
        # Password troppo corta
        short_password = "Ab1!"
        is_valid, error, _ = password_validator.validate_password(short_password)
        assert not is_valid
        assert "almeno" in error.lower()
        
        # Password troppo lunga
        long_password = "A" * 200 + "1!"
        is_valid, error, _ = password_validator.validate_password(long_password)
        assert not is_valid
        assert "superare" in error.lower()
    
    def test_password_complexity_requirements(self):
        """Test dei requisiti di complessità."""
        # Manca maiuscola
        no_upper = "mypassword123!"
        is_valid, error, _ = password_validator.validate_password(no_upper)
        assert not is_valid
        assert "maiuscola" in error.lower()
        
        # Manca minuscola
        no_lower = "MYPASSWORD123!"
        is_valid, error, _ = password_validator.validate_password(no_lower)
        assert not is_valid
        assert "minuscola" in error.lower()
        
        # Manca numero
        no_number = "MyPassword!"
        is_valid, error, _ = password_validator.validate_password(no_number)
        assert not is_valid
        assert "numero" in error.lower()
        
        # Manca carattere speciale
        no_special = "MyPassword123"
        is_valid, error, _ = password_validator.validate_password(no_special)
        assert not is_valid
        assert "speciale" in error.lower()

class TestPasswordHashing:
    """Test per l'hashing delle password."""
    
    def test_password_hashing_consistency(self):
        """Test che l'hashing sia consistente."""
        password = "TestPassword123!"
        
        # Hash della stessa password dovrebbe essere diverso ogni volta (salt casuale)
        hash1 = password_hasher.hash_password(password)
        hash2 = password_hasher.hash_password(password)
        
        assert hash1 != hash2, "Gli hash dovrebbero essere diversi (salt casuale)"
        assert hash1.startswith("$2b$"), "Hash dovrebbe essere in formato bcrypt"
        assert hash2.startswith("$2b$"), "Hash dovrebbe essere in formato bcrypt"
    
    def test_password_verification(self):
        """Test della verifica password."""
        password = "TestPassword123!"
        wrong_password = "WrongPassword123!"
        
        hashed = password_hasher.hash_password(password)
        
        # Password corretta dovrebbe essere verificata
        assert password_hasher.verify_password(password, hashed)
        
        # Password sbagliata dovrebbe fallire
        assert not password_hasher.verify_password(wrong_password, hashed)
    
    def test_hash_format_security(self):
        """Test del formato di sicurezza dell'hash."""
        password = "TestPassword123!"
        hashed = password_hasher.hash_password(password)
        
        # Verifica formato bcrypt
        parts = hashed.split("$")
        assert len(parts) >= 4, "Hash bcrypt dovrebbe avere almeno 4 parti"
        assert parts[1] == "2b", "Dovrebbe usare bcrypt 2b"
        assert int(parts[2]) >= 12, "Dovrebbe usare almeno 12 rounds"

class TestTokenSecurity:
    """Test per la sicurezza dei token."""
    
    def test_token_generation_uniqueness(self):
        """Test che i token generati siano unici."""
        tokens = set()
        
        for i in range(100):
            token = token_manager.generate_reset_token(i, f"test{i}@example.com")
            assert token not in tokens, "Token duplicato generato"
            tokens.add(token)
    
    def test_token_validation_timing(self):
        """Test della validazione token con scadenza."""
        user_id = 1
        email = "<EMAIL>"
        
        # Genera token
        token = token_manager.generate_reset_token(user_id, email)
        
        # Token dovrebbe essere valido immediatamente
        is_valid, data = token_manager.validate_reset_token(token)
        assert is_valid
        assert data['user_id'] == user_id
        assert data['email'] == email
    
    def test_token_tampering_detection(self):
        """Test della rilevazione di manomissioni del token."""
        user_id = 1
        email = "<EMAIL>"
        
        token = token_manager.generate_reset_token(user_id, email)
        
        # Modifica il token
        tampered_token = token[:-5] + "XXXXX"
        
        # Token manomesso dovrebbe essere rifiutato
        is_valid, data = token_manager.validate_reset_token(tampered_token)
        assert not is_valid
        assert data is None

class TestRateLimiting:
    """Test per il rate limiting."""
    
    def test_rate_limit_enforcement(self):
        """Test dell'applicazione del rate limiting."""
        identifier = "test_user_123"
        
        # Primi tentativi dovrebbero essere permessi
        for i in range(5):
            is_limited, _ = rate_limiter.is_rate_limited(identifier)
            assert not is_limited, f"Tentativo {i+1} dovrebbe essere permesso"
            rate_limiter.record_attempt(identifier)
        
        # Tentativi successivi dovrebbero essere bloccati
        for i in range(5):
            is_limited, seconds = rate_limiter.is_rate_limited(identifier)
            assert is_limited, f"Tentativo {i+6} dovrebbe essere bloccato"
            assert seconds > 0, "Dovrebbe restituire tempo di attesa"
    
    def test_rate_limit_window_reset(self):
        """Test del reset della finestra di rate limiting."""
        identifier = "test_user_window"
        
        # Riempi il rate limit
        for i in range(10):
            rate_limiter.record_attempt(identifier)
        
        # Dovrebbe essere limitato
        is_limited, _ = rate_limiter.is_rate_limited(identifier)
        assert is_limited
        
        # Simula il passaggio del tempo (in un test reale useresti mock)
        # Per ora testiamo solo la logica base

class TestAPISecurityEndpoints:
    """Test di sicurezza per gli endpoint API."""
    
    def test_password_change_requires_auth(self):
        """Test che il cambio password richieda autenticazione."""
        response = client.post("/api/password/change-password", json={
            "current_password": "old_pass",
            "new_password": "NewPass123!",
            "confirm_password": "NewPass123!"
        })
        
        assert response.status_code == 401, "Dovrebbe richiedere autenticazione"
    
    def test_password_reset_rate_limiting(self):
        """Test del rate limiting per reset password."""
        email = "<EMAIL>"
        
        # Primi tentativi dovrebbero funzionare
        for i in range(3):
            response = client.post("/api/password/request-password-reset", json={
                "email": email,
                "user_type": "user"
            })
            # Anche se l'email non esiste, dovrebbe restituire 200 per sicurezza
            assert response.status_code in [200, 429]
    
    def test_sql_injection_protection(self):
        """Test della protezione da SQL injection."""
        malicious_inputs = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "admin'--",
            "' UNION SELECT * FROM users --"
        ]
        
        for malicious_input in malicious_inputs:
            response = client.post("/api/password/request-password-reset", json={
                "email": malicious_input,
                "user_type": "user"
            })
            
            # Non dovrebbe causare errori del server
            assert response.status_code != 500, f"SQL injection potenziale: {malicious_input}"
    
    def test_xss_protection(self):
        """Test della protezione da XSS."""
        xss_payloads = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "<img src=x onerror=alert('xss')>",
            "';alert('xss');//"
        ]
        
        for payload in xss_payloads:
            response = client.post("/api/password/request-password-reset", json={
                "email": payload,
                "user_type": "user"
            })
            
            # Verifica che il payload non sia riflesso nella risposta
            response_text = response.text.lower()
            assert "<script>" not in response_text
            assert "javascript:" not in response_text
            assert "onerror=" not in response_text
    
    def test_csrf_protection(self):
        """Test della protezione CSRF."""
        # Test che le richieste senza token CSRF appropriato vengano rifiutate
        # (Implementazione dipende dal framework CSRF utilizzato)
        pass
    
    def test_input_validation(self):
        """Test della validazione degli input."""
        # Email non valida
        response = client.post("/api/password/request-password-reset", json={
            "email": "not-an-email",
            "user_type": "user"
        })
        assert response.status_code == 422, "Dovrebbe rifiutare email non valide"
        
        # Tipo utente non valido
        response = client.post("/api/password/request-password-reset", json={
            "email": "<EMAIL>",
            "user_type": "invalid_type"
        })
        assert response.status_code == 422, "Dovrebbe rifiutare tipi utente non validi"
    
    def test_password_validation_endpoint(self):
        """Test dell'endpoint di validazione password."""
        # Password debole
        response = client.post("/api/password/validate-password", 
                             params={"password": "123456"})
        assert response.status_code == 200
        data = response.json()
        assert not data["is_valid"]
        assert data["strength_score"] < 3
        
        # Password forte
        response = client.post("/api/password/validate-password", 
                             params={"password": "StrongP@ssw0rd123!"})
        assert response.status_code == 200
        data = response.json()
        assert data["is_valid"]
        assert data["strength_score"] >= 3

class TestSecurityHeaders:
    """Test per gli header di sicurezza."""
    
    def test_security_headers_present(self):
        """Test che gli header di sicurezza siano presenti."""
        response = client.get("/api/password/validate-password")
        
        # Verifica header di sicurezza comuni
        headers = response.headers
        
        # Content-Type dovrebbe essere impostato correttamente
        assert "application/json" in headers.get("content-type", "")
        
        # Altri header di sicurezza potrebbero essere aggiunti qui
        # come X-Content-Type-Options, X-Frame-Options, etc.

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
