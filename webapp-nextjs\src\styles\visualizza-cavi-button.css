/* Stile personalizzato per i pulsanti "Visualizza Cavi" */
.visualizza-cavi-button {
  position: relative;
  height: 50px;
  padding: 0 30px;
  border: 2px solid #000;
  background: #e8e8e8;
  user-select: none;
  white-space: nowrap;
  transition: all .05s linear;
  font-family: inherit;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  text-decoration: none;
  color: inherit;
}

.visualizza-cavi-button:before,
.visualizza-cavi-button:after {
  content: "";
  position: absolute;
  background: #e8e8e8;
  transition: all .2s linear;
}

.visualizza-cavi-button:before {
  width: calc(100% + 6px);
  height: calc(100% - 16px);
  top: 8px;
  left: -3px;
}

.visualizza-cavi-button:after {
  width: calc(100% - 16px);
  height: calc(100% + 6px);
  top: -3px;
  left: 8px;
}

.visualizza-cavi-button:hover {
  cursor: crosshair;
}

.visualizza-cavi-button:active {
  transform: scale(0.95);
}

.visualizza-cavi-button:hover:before {
  height: calc(100% - 32px);
  top: 16px;
}

.visualizza-cavi-button:hover:after {
  width: calc(100% - 32px);
  left: 16px;
}

.visualizza-cavi-button span {
  font-size: 15px;
  z-index: 3;
  position: relative;
  font-weight: 600;
}

/* Variante per stato attivo */
.visualizza-cavi-button.active {
  background: #d4edda;
  border-color: #28a745;
}

.visualizza-cavi-button.active:before,
.visualizza-cavi-button.active:after {
  background: #d4edda;
}

/* Variante per icone */
.visualizza-cavi-button .icon {
  z-index: 3;
  position: relative;
  margin-right: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .visualizza-cavi-button {
    height: 40px;
    padding: 0 20px;
  }
  
  .visualizza-cavi-button span {
    font-size: 14px;
  }
}
