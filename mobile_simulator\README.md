# CMS Mobile App Simulator

Simulatore dell'app mobile per il sistema CMS di gestione cantieri.

## 🚀 Avvio Rapido

### 1. Avvia il Backend CMS
```bash
cd webapp
python backend/main.py --port 8001
```

### 2. Avvia il Simulatore Mobile
```bash
cd mobile_simulator
python server.py
```

### 3. Apri il Browser
Il simulatore si aprirà automaticamente su: http://localhost:3001

## 📱 Come Usare il Simulatore

### Step 1: Crea una Comanda
1. Accedi al sistema CMS (http://localhost:3000)
2. Crea una nuova comanda con:
   - Responsabile con email/telefono
   - Tipo comanda (POSA, COLLEGAMENTO, CERTIFICAZIONE)
   - Assegna alcuni cavi

### Step 2: Accedi al Simulatore Mobile
1. Inserisci il **codice comanda** generato
2. Inserisci l'**email o telefono** del responsabile
3. <PERSON><PERSON><PERSON> "Accedi"

### Step 3: Esplora le Funzionalità
- **Dettagli Comanda**: Visualizza informazioni e progresso
- **Visualizza Cavi**: Lista dei cavi assegnati
- **Le Mie Comande**: Tutte le comande del responsabile

## 🔧 Funzionalità Implementate

### ✅ Autenticazione
- Login tramite codice comanda + email/telefono
- Validazione responsabile per cantiere specifico
- Token di sessione per sicurezza

### ✅ Gestione Comande
- Visualizzazione dettagli comanda
- Calcolo progresso automatico
- Stati: CREATA, ASSEGNATA, COMPLETATA

### ✅ Gestione Cavi
- Lista cavi assegnati alla comanda
- Stato installazione e collegamenti
- Metrature teoriche e reali

### ✅ Dashboard Responsabile
- Lista di tutte le comande assegnate
- Progresso visuale per ogni comanda
- Navigazione tra comande

## 🎨 Interfaccia

### Design Mobile-First
- Layout ottimizzato per smartphone
- Interfaccia touch-friendly
- Colori e tipografia professionali

### Componenti UI
- **Cards**: Per comande e cavi
- **Progress Bar**: Visualizzazione progresso
- **Status Badge**: Stati colorati
- **Navigation**: Navigazione intuitiva

## 🔌 API Utilizzate

### Endpoint Mobile
- `POST /mobile/login` - Autenticazione
- `GET /mobile/comanda/{codice}` - Dettagli comanda
- `GET /mobile/comanda/{codice}/cavi` - Lista cavi
- `GET /mobile/responsabile/{id}/comande` - Comande responsabile
- `POST /mobile/comanda/{codice}/aggiorna-posa` - Aggiorna posa
- `POST /mobile/comanda/{codice}/aggiorna-collegamento` - Aggiorna collegamento

### Autenticazione
- Token di sessione generato al login
- Validazione token per ogni richiesta
- Logout automatico in caso di errore

## 🧪 Test e Debug

### Console Browser
Apri gli strumenti sviluppatore (F12) per vedere:
- Log delle chiamate API
- Errori JavaScript
- Stato dell'applicazione

### Test API Diretti
```bash
# Test ping
curl http://localhost:8001/api/mobile/ping

# Test login
curl -X POST http://localhost:8001/api/mobile/login \
  -H "Content-Type: application/json" \
  -d '{"codice_comanda":"POS_1_...", "contatto":"<EMAIL>"}'
```

## 🔧 Configurazione

### Porta del Simulatore
Modifica `PORT = 3001` in `server.py` se necessario.

### URL API Backend
Modifica `API_BASE_URL` in `app.js` se il backend è su porta diversa.

### CORS
Il server include automaticamente gli header CORS necessari.

## 📋 Workflow Completo

### 1. Creazione Comanda (Sistema CMS)
```
Utente CMS → Crea Comanda → Auto-inserimento Responsabile → Notifica Email/SMS
```

### 2. Accesso Mobile (App Simulator)
```
Responsabile → Riceve Codice → Login Mobile → Gestione Lavori
```

### 3. Aggiornamento Stato (App Mobile)
```
Responsabile → Aggiorna Progresso → Sincronizzazione Database → Notifiche
```

## 🚨 Troubleshooting

### Errore "API Mobile non disponibile"
- Verifica che il backend sia in esecuzione su porta 8001
- Controlla la console per errori CORS

### Errore "Codice comanda non trovato"
- Verifica che la comanda esista nel database
- Controlla che il codice sia stato copiato correttamente

### Errore "Responsabile non autorizzato"
- Verifica che email/telefono corrispondano al responsabile
- Controlla che il responsabile sia collegato al cantiere corretto

### Errore "Porta già in uso"
- Cambia la porta in `server.py`
- Oppure ferma il processo esistente

## 🔮 Sviluppi Futuri

### App Mobile Reale
- React Native o Flutter
- Notifiche push
- Modalità offline
- Geolocalizzazione

### Funzionalità Avanzate
- Foto dei lavori
- Firma digitale
- Scansione QR code
- Sincronizzazione real-time

### Integrazione IoT
- Sensori di installazione
- Monitoraggio automatico
- Alerting intelligente

## 📞 Supporto

Per problemi o domande:
1. Controlla la console del browser
2. Verifica i log del backend
3. Consulta la documentazione API
4. Testa con dati di esempio

---

**Nota**: Questo è un simulatore per test e sviluppo. L'app mobile reale richiederà implementazione nativa per iOS/Android.
