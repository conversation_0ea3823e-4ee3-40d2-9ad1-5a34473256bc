# -*- coding: utf-8 -*-
import psycopg2
import logging
from datetime import datetime
from typing import Dict, Optional, List
from modules.utils import ValidazioneCampi
from modules.utils import StatoInstallazione, validatore_campo
from modules.utils import StatoBobina
from modules.database_pg import Database, database_connection
from psycopg2.extras import RealDictCursor

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')


def carica_cavi(id_cantiere: int, tipo_cavo: Optional[int] = None):
    """
    Carica tutti i cavi di un cantiere dal database, filtrando per tipo_cavo.

    :param id_cantiere: ID del cantiere.
    :param tipo_cavo: Tipo di cavo da filtrare (0 per attivi, 3 per spare).
    :return: Lista di cavi come dizionari.
    """
    try:
        # Utilizziamo la connessione con RealDictCursor per ottenere dizionari
        db = Database()
        conn = db.get_dict_cursor_connection()
        try:
            with conn.cursor() as c:
                query = '''
                      SELECT id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo, tipologia,
                             n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza, descrizione_utenza_partenza,
                             ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo, metri_teorici, metratura_reale,
                             responsabile_posa, id_bobina, stato_installazione, modificato_manualmente, timestamp
                      FROM Cavi
                      WHERE id_cantiere = %s
                '''

                params = [id_cantiere]

                if tipo_cavo is not None:
                    if tipo_cavo == 0:
                        # Cavi attivi: tutti tranne quelli marcati come SPARE (3)
                        query += " AND (modificato_manualmente != 3 OR modificato_manualmente IS NULL)"
                    elif tipo_cavo == 3:
                        # Cavi SPARE
                        query += " AND modificato_manualmente = 3"

                query += " ORDER BY id_cavo"
                c.execute(query, params)
                return c.fetchall()
        finally:
            conn.close()
    except psycopg2.Error as e:
        logging.error(f"❌ Errore durante il caricamento dei cavi: {e}")
        return []


def visualizza_cavi(id_cantiere):
    """Visualizza la lista dei cavi in formato tabellare, suddivisi in attivi e spare."""
    try:
        cavi_attivi = carica_cavi(id_cantiere, tipo_cavo=0)
        print("\n=== CAVI ATTIVI ===")
        if not cavi_attivi:
            print("Nessun cavo attivo trovato.")
        else:
            _stampa_tabella_cavi(cavi_attivi)

        cavi_spare = carica_cavi(id_cantiere, tipo_cavo=3)
        print("\n=== CAVI SPARE ===")
        if not cavi_spare:
            print("Nessun cavo spare trovato.")
        else:
            _stampa_tabella_cavi(cavi_spare)
    except Exception as e:
        logging.error(f"❌ Errore durante la visualizzazione dei cavi: {e}")


def _stampa_tabella_cavi(cavi):
    """Stampa la tabella dei cavi in formato leggibile."""
    # Intestazione tabella
    print(
        "\nID Cavo         Utility         Tipologia       N.Cond    Sezione         Ubicaz.Part.    Ubicaz.Arr.    Metri T.    Stato")
    print("-" * 140)

    # Stampa i dati
    for cavo in cavi:
        try:
            # Formatta i valori per la visualizzazione
            id_cavo = str(cavo['id_cavo']).replace('$', '')[:15].ljust(15)  # Rimuovi i simboli $ dagli ID
            utility = str(cavo['utility'])[:15].ljust(15)
            tipologia = str(cavo['tipologia'])[:15].ljust(15)

            # Gestisci n_conduttori come stringa o numero
            try:
                n_cond_val = int(cavo['n_conduttori']) if cavo['n_conduttori'] else 0
                n_conduttori = str(n_cond_val) if n_cond_val > 0 else '-'
            except (ValueError, TypeError):
                n_conduttori = str(cavo['n_conduttori']) if cavo['n_conduttori'] else '-'
            n_conduttori = n_conduttori[:8].ljust(10)

            # Gestisci sezione come stringa
            sezione_val = cavo['sezione']
            if isinstance(sezione_val, (int, float)) and sezione_val == 0:
                sezione = '-'  # Se è 0, mostra '-'
            else:
                sezione = str(sezione_val) if sezione_val else '-'
            sezione = sezione[:15].ljust(15)  # Aumentato a 15 caratteri per visualizzare sezioni più lunghe

            ubicazione_partenza = str(cavo['ubicazione_partenza'])[:15].ljust(15)
            ubicazione_arrivo = str(cavo['ubicazione_arrivo'])[:15].ljust(15)
            metri_teorici = f"{float(cavo['metri_teorici']):.2f}".ljust(12)
            stato = str(cavo['stato_installazione'])[:15].ljust(15)

            # Stampa la riga
            print(
                f"{id_cavo}{utility}{tipologia}{n_conduttori}{sezione}{ubicazione_partenza}{ubicazione_arrivo}{metri_teorici}{stato}")

        except Exception as e:
            print(f"❌ Errore durante la stampa del cavo: {e}")


def get_revisione_corrente(id_cantiere: int) -> str:
    """
    Recupera la revisione ufficiale corrente del cantiere.
    Ritorna la revisione più recente o '00' se non ce ne sono.
    """
    try:
        with database_connection() as (conn, cursor):
            cursor.execute("""
                SELECT revisione_ufficiale
                FROM Cavi
                WHERE id_cantiere = %s
                AND revisione_ufficiale IS NOT NULL
                AND revisione_ufficiale != ''
                AND revisione_ufficiale != '00'
                AND revisione_ufficiale != 'TBD'
                ORDER BY revisione_ufficiale DESC
                LIMIT 1
            """, (id_cantiere,))
            result = cursor.fetchone()
            return result[0] if result else '00'
    except Exception as e:
        logging.error(f"Errore nel recupero della revisione corrente: {e}")
        return '00'


def aggiungi_cavo(id_cantiere: int) -> None:
    """Aggiunge un nuovo cavo al database con gestione migliorata delle transazioni e degli errori.

    Args:
        id_cantiere: ID del cantiere a cui aggiungere il cavo
    """
    db = Database()
    id_cavo = None
    campi = {}
    revisione_corrente = None

    try:
        print("\n📝 AGGIUNTA NUOVO CAVO")

        # Recupera la revisione corrente
        revisione_corrente = get_revisione_corrente(id_cantiere)

        # Validazione della revisione: se è TBD o vuota, usa REV1 come default
        if not revisione_corrente or revisione_corrente in ['TBD', '00', '']:
            revisione_corrente = "REV1"
            logging.warning(f"Revisione corrente non valida, usando REV1 come default per cantiere {id_cantiere}")

        logging.info(f"Revisione corrente per cantiere {id_cantiere}: {revisione_corrente}")

        # Fase 1: Verifica esistenza cavo e input ID
        with database_connection(autocommit=True, operation_name="verifica_esistenza_cavo") as (conn, c):
            while True:
                id_cavo = input("ID cavo: ").strip().upper()
                if not id_cavo:
                    print("❌ L'ID cavo è obbligatorio!")
                    continue

                # Verifica esistenza cavo
                c.execute("""
                    SELECT id_cavo, stato_installazione, metratura_reale,
                           modificato_manualmente
                    FROM Cavi
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (id_cavo, id_cantiere))

                cavo_esistente = c.fetchone()

                if cavo_esistente:
                    # Verifica se il cavo è SPARE (modificato_manualmente = 3)
                    if cavo_esistente['modificato_manualmente'] == 3:
                        print("\n⚠️ Esiste già un cavo con questo ID marcato come SPARE.")

                        # Mostra info sullo stato attuale
                        print(f"ℹ️ Stato attuale: {cavo_esistente['stato_installazione']}")
                        if cavo_esistente['metratura_reale'] > 0:
                            print(f"ℹ️ Metratura attuale: {cavo_esistente['metratura_reale']} metri")

                        reintegra = input("Vuoi reintegrare il cavo esistente? (s/n): ").strip().lower()
                        if reintegra == 's':
                            # Determina lo stato di installazione in base alla metratura reale
                            nuovo_stato = (StatoInstallazione.INSTALLATO.value
                                           if cavo_esistente['metratura_reale'] > 0
                                           else StatoInstallazione.DA_INSTALLARE.value)

                            # Usa una transazione separata per il reintegro
                            with database_connection(autocommit=False, operation_name="reintegro_cavo_spare") as (conn_reintegro, c_reintegro):
                                c_reintegro.execute("""
                                    UPDATE Cavi
                                    SET modificato_manualmente = 1,
                                        stato_installazione = %s,
                                        timestamp = CURRENT_TIMESTAMP
                                    WHERE id_cavo = %s AND id_cantiere = %s
                                """, (nuovo_stato, id_cavo, id_cantiere))
                                # Il commit viene fatto automaticamente dal context manager

                            print("\n✅ Cavo reintegrato con successo!")
                            logging.info(f"Cavo {id_cavo} reintegrato nel cantiere {id_cantiere} con stato '{nuovo_stato}'")
                            return
                        else:
                            print("\nInserisci un nuovo ID cavo.")
                            continue
                    else:
                        print("\n❌ Esiste già un cavo attivo con questo ID!")
                        continue

                break

        # Fase 2: Input e validazione campi
        campi_da_validare = [
            ("sistema", "Sistema"),
            ("utility", "Utility"),
            ("colore_cavo", "Colore cavo"),
            ("tipologia", "Tipologia"),
            ("numero_conduttori", "Numero conduttori"),
            ("sezione", "Sezione"),
            ("sh", "SH (S/N)"),  # Nota: la chiave nel dizionario sarà "SH"
            ("partenza", "Ubicazione partenza"),
            ("utenza_partenza", "Utenza partenza"),
            ("descrizione_utenza_partenza", "Descrizione utenza partenza"),
            ("arrivo", "Ubicazione arrivo"),
            ("utenza_arrivo", "Utenza arrivo"),
            ("descrizione_utenza_arrivo", "Descrizione utenza arrivo"),
            ("metri_teorici", "Metri teorici")
            # Nota: responsabile_posa, metratura_reale, id_bobina e stato_installazione sono gestiti dal sistema
        ]

        for campo_validazione, nome_campo in campi_da_validare:
            while True:
                valore = input(f"{nome_campo}: ").strip()

                risultato = validatore_campo(campo_validazione, valore)
                if not risultato[0]:
                    print(f"❌ {risultato[1]}")
                    continue
                campi[nome_campo] = risultato[2]
                break

        # Fase 3: Inserimento nel database con transazione
        with database_connection(autocommit=False, operation_name="inserimento_cavo") as (conn, c):
            # Verifica nuovamente che il cavo non esista (per evitare race condition)
            c.execute("""
                SELECT COUNT(*) FROM Cavi
                WHERE id_cavo = %s AND id_cantiere = %s
            """, (id_cavo, id_cantiere))

            if c.fetchone()[0] > 0:
                print("\n❌ Il cavo esiste già nel database! (Potrebbe essere stato inserito da un altro utente)")
                return

            # Inserisci il nuovo cavo
            c.execute("""
                INSERT INTO Cavi (
                    id_cavo, id_cantiere, revisione_ufficiale, sistema, utility, colore_cavo,
                    tipologia, n_conduttori, sezione, SH, ubicazione_partenza, utenza_partenza,
                    descrizione_utenza_partenza, ubicazione_arrivo, utenza_arrivo, descrizione_utenza_arrivo,
                    metri_teorici, metratura_reale, responsabile_posa, id_bobina,
                    stato_installazione, modificato_manualmente, timestamp
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, 0, NULL, NULL, %s, 0, CURRENT_TIMESTAMP
                )
            """, (
                id_cavo, id_cantiere, revisione_corrente, campi["Sistema"], campi["Utility"], campi["Colore cavo"],
                campi["Tipologia"], campi["Numero conduttori"], campi["Sezione"], campi.get("SH", "-"),
                campi["Ubicazione partenza"], campi["Utenza partenza"], campi["Descrizione utenza partenza"],
                campi["Ubicazione arrivo"], campi["Utenza arrivo"], campi["Descrizione utenza arrivo"],
                campi["Metri teorici"], StatoInstallazione.DA_INSTALLARE.value
            ))
            # Il commit viene fatto automaticamente dal context manager

        print(f"\n✅ Cavo aggiunto con successo! (Revisione: {revisione_corrente})")
        logging.info(f"Nuovo cavo {id_cavo} aggiunto al cantiere {id_cantiere} con revisione {revisione_corrente}")

    except psycopg2.Error as e:
        # Verifica se il cavo è stato comunque inserito nonostante l'errore
        if id_cavo and id_cantiere:
            risultato = db.verifica_stato_dopo_errore(
                "Cavi",
                {"id_cavo": id_cavo, "id_cantiere": id_cantiere},
                None,
                f"inserimento cavo {id_cavo}"
            )

            if risultato['esiste']:
                print(f"\n✅ Nonostante l'errore, il cavo {id_cavo} risulta inserito correttamente nel database.")
                logging.info(f"Cavo {id_cavo} inserito con successo nonostante errore di comunicazione")
                return

        # Se arriviamo qui, l'inserimento è fallito
        logging.error(f"Errore database durante l'aggiunta del cavo: {e}")
        print("\n❌ Errore durante l'inserimento del cavo nel database")

    except Exception as e:
        logging.error(f"Errore durante l'aggiunta del cavo: {e}")
        print("\n❌ Si è verificato un errore durante l'operazione")

        # Verifica se il cavo è stato comunque inserito nonostante l'errore
        if id_cavo and id_cantiere:
            try:
                risultato = db.verifica_stato_dopo_errore(
                    "Cavi",
                    {"id_cavo": id_cavo, "id_cantiere": id_cantiere},
                    None,
                    f"inserimento cavo {id_cavo} dopo errore generico"
                )

                if risultato['esiste']:
                    print(f"\n✅ Nonostante l'errore, il cavo {id_cavo} risulta inserito correttamente nel database.")
                    logging.info(f"Cavo {id_cavo} inserito con successo nonostante errore generico")
            except Exception as verify_error:
                logging.error(f"Errore durante la verifica post-errore: {verify_error}")


def modifica_cavo_manualmente(id_cantiere: int) -> None:
    """
    Permette la modifica manuale di un cavo con gestione migliorata delle transazioni e degli errori.
    Si possono modificare solo i dati fisici del cavo.
    I metri reali (metratura_reale) non possono essere modificati qui.
    Per modificare la bobina di un cavo posato, usare modifica_bobina_cavo_posato().

    Args:
        id_cantiere: ID del cantiere a cui appartiene il cavo.
    """
    db = Database()
    id_cavo = None
    campo_db = None
    valore_validato = None

    try:
        print("\n🔄 MODIFICA CAVO")

        # Fase 1: Recupero dati del cavo
        with database_connection(autocommit=True, operation_name="recupero_dati_cavo") as (conn, c):
            id_cavo = input("\nInserisci l'ID del cavo da modificare: ").strip()
            if not id_cavo:
                print("\n❌ ID cavo non valido!")
                return

            # Recupera i dati del cavo
            c.execute("""
                SELECT *,
                       COALESCE(metratura_reale, 0) as metri_posati,
                       TRIM(UPPER(stato_installazione::text)) as stato_norm
                FROM Cavi
                WHERE id_cavo = %s AND id_cantiere = %s
            """, (id_cavo, id_cantiere))

            cavo = c.fetchone()
            if not cavo:
                print("\n❌ Cavo non trovato!")
                return

            # Controllo più robusto e anticipato
            metratura_reale = float(cavo['metratura_reale']) if 'metratura_reale' in cavo else 0
            stato_installazione = cavo['stato_installazione'] if 'stato_installazione' in cavo else ''
            if metratura_reale > 0 or stato_installazione == StatoInstallazione.INSTALLATO.value:
                print("\n❌ Questo cavo risulta già posato!")
                print(f"ℹ️ Stato attuale del cavo: '{stato_installazione}'")
                print(f"ℹ️ Metratura reale: {metratura_reale}")
                print("\n⚠️ I cavi posati non possono essere modificati manualmente.")

                scelta = input("\nVuoi:\n1. Passare alla modifica bobina cavo posato\n2. Annullare l'operazione\nScelta (1/2): ").strip()

                if scelta == "1":
                    # Richiama direttamente la funzione per modificare bobina cavo posato
                    return modifica_bobina_cavo_posato(id_cantiere, id_cavo)
                else:
                    print("\n❌ Operazione annullata.")
                    return

            # Visualizza i dettagli del cavo
            _visualizza_dettagli_cavo(cavo)

        # Fase 2: Selezione e validazione del campo da modificare
        print("\nCampi modificabili:")
        print("1. Sistema")
        print("2. Utility")
        print("3. Colore cavo")
        print("4. Tipologia")
        print("5. N° Conduttori")
        print("6. Sezione")
        print("7. SH")
        print("8. Ubicazione partenza")
        print("9. Utenza partenza")
        print("10. Descrizione utenza partenza")
        print("11. Ubicazione arrivo")
        print("12. Utenza arrivo")
        print("13. Descrizione utenza arrivo")
        print("14. Metri teorici")

        campo = input("\nSeleziona il campo da modificare: ").strip()

        campi_validi = ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14']

        if campo not in campi_validi:
            print("\n❌ Opzione non valida!")
            return

        # Input del nuovo valore
        nuovo_valore = input("Inserisci il nuovo valore: ").strip()

        # Mapping dei campi e validazione
        campi = {
            '1': ('sistema', nuovo_valore, lambda v: validatore_campo('sistema', v)),
            '2': ('utility', nuovo_valore, lambda v: validatore_campo('utility', v)),
            '3': ('colore_cavo', nuovo_valore, lambda v: validatore_campo('colore_cavo', v)),
            '4': ('tipologia', nuovo_valore, lambda v: validatore_campo('tipologia', v)),
            '5': ('n_conduttori', nuovo_valore, lambda v: validatore_campo('numero_conduttori', v)),
            '6': ('sezione', nuovo_valore, lambda v: validatore_campo('sezione', v)),
            '7': ('SH', nuovo_valore, lambda v: validatore_campo('sh', v)),
            '8': ('ubicazione_partenza', nuovo_valore, lambda v: validatore_campo('partenza', v)),
            '9': ('utenza_partenza', nuovo_valore, lambda v: validatore_campo('utenza', v)),
            '10': ('descrizione_utenza_partenza', nuovo_valore, lambda v: validatore_campo('descrizione', v)),
            '11': ('ubicazione_arrivo', nuovo_valore, lambda v: validatore_campo('arrivo', v)),
            '12': ('utenza_arrivo', nuovo_valore, lambda v: validatore_campo('utenza', v)),
            '13': ('descrizione_utenza_arrivo', nuovo_valore, lambda v: validatore_campo('descrizione', v)),
            '14': ('metri_teorici', nuovo_valore, ValidazioneCampi.valida_metri_teorici),
        }

        campo_db, valore, validatore = campi[campo]
        risultato = validatore(valore)

        # Se il risultato è una tupla, estrai i valori
        if isinstance(risultato, tuple):
            valido, messaggio, valore_validato = risultato
        else:
            # Se il risultato è un oggetto con attributi, accedi direttamente
            valido = risultato.valido
            messaggio = risultato.messaggio
            valore_validato = risultato.valore

        if not valido:
            print(f"\n❌ {messaggio}")
            return

        # Fase 3: Verifica finale e modifica nel database
        with database_connection(autocommit=True, operation_name="verifica_stato_cavo") as (conn, c):
            # Verifica finale prima della modifica
            c.execute("""
                SELECT metratura_reale, stato_installazione
                FROM Cavi
                WHERE id_cavo = %s AND id_cantiere = %s
                FOR UPDATE
            """, (id_cavo, id_cantiere))

            verifica = c.fetchone()
            if not verifica:
                print("\n❌ Cavo non trovato!")
                return

            metratura_reale = float(verifica['metratura_reale']) if verifica['metratura_reale'] else 0
            stato_installazione = verifica['stato_installazione']

            if metratura_reale > 0 or stato_installazione == StatoInstallazione.INSTALLATO.value:
                print("\n❌ Questo cavo risulta già posato e non può essere modificato manualmente!")
                return

        # Conferma modifica
        conferma = input(
            f"\nConfermi la modifica del campo '{campo_db}' a '{valore_validato}'? (s/n): ").strip().lower()
        if conferma != 's':
            print("\n❌ Modifica annullata.")
            return

        # Fase 4: Esecuzione della modifica con transazione
        with database_connection(autocommit=False, operation_name=f"modifica_campo_{campo_db}") as (conn, c):
            # Utilizziamo psycopg2.sql per costruire la query in modo sicuro
            from psycopg2 import sql
            query = sql.SQL("""
                UPDATE Cavi
                SET {} = %s,
                    modificato_manualmente = 1,
                    timestamp = CURRENT_TIMESTAMP
                WHERE id_cantiere = %s AND id_cavo = %s
            """).format(sql.Identifier(campo_db))

            c.execute(query, (valore_validato, id_cantiere, id_cavo))
            # Il commit viene fatto automaticamente dal context manager

        print("\n✅ Cavo aggiornato con successo!")
        logging.info(f"✅ Cavo {id_cavo} aggiornato: campo '{campo_db}' modificato a '{valore_validato}'")

    except psycopg2.Error as e:
        # Verifica se la modifica è stata comunque applicata nonostante l'errore
        if id_cavo and id_cantiere and campo_db and valore_validato:
            try:
                # Verifica se il campo è stato aggiornato al valore atteso
                risultato = db.verifica_stato_dopo_errore(
                    "Cavi",
                    {"id_cavo": id_cavo, "id_cantiere": id_cantiere},
                    {campo_db: valore_validato},
                    f"modifica campo {campo_db} del cavo {id_cavo}"
                )

                if risultato['esiste'] and risultato['valori_corretti']:
                    print(f"\n✅ Nonostante l'errore, il cavo {id_cavo} risulta aggiornato correttamente.")
                    logging.info(f"Cavo {id_cavo} aggiornato con successo nonostante errore di comunicazione")
                    return
            except Exception as verify_error:
                logging.error(f"Errore durante la verifica post-errore: {verify_error}")

        # Se arriviamo qui, la modifica è fallita
        print("\n❌ Errore: inserire un numero valido per la metratura!")
        logging.error(f"❌ Errore database durante la modifica del cavo: {e}")

    except ValueError:
        print("\n❌ Errore: inserire un numero valido per la metratura!")
        logging.error("❌ Errore: inserire un numero valido per la metratura!")

    except Exception as e:
        print(f"\n❌ Errore durante la modifica del cavo: {e}")
        logging.error(f"❌ Errore durante la modifica del cavo: {e}")

        # Verifica se la modifica è stata comunque applicata nonostante l'errore
        if id_cavo and id_cantiere and campo_db and valore_validato:
            try:
                # Verifica se il campo è stato aggiornato al valore atteso
                risultato = db.verifica_stato_dopo_errore(
                    "Cavi",
                    {"id_cavo": id_cavo, "id_cantiere": id_cantiere},
                    {campo_db: valore_validato},
                    f"modifica campo {campo_db} del cavo {id_cavo} dopo errore generico"
                )

                if risultato['esiste'] and risultato['valori_corretti']:
                    print(f"\n✅ Nonostante l'errore, il cavo {id_cavo} risulta aggiornato correttamente.")
                    logging.info(f"Cavo {id_cavo} aggiornato con successo nonostante errore generico")
                    return
            except Exception as verify_error:
                logging.error(f"Errore durante la verifica post-errore generico: {verify_error}")


def _modifica_campo_cavo(conn, id_cavo: str, id_cantiere: int, campo_db: str, valore_validato: str):
    """
    Modifica un campo specifico di un cavo nel database con gestione delle transazioni.
    """
    cursor = conn.cursor()
    try:
        # Utilizziamo psycopg2.sql per costruire la query in modo sicuro
        from psycopg2 import sql
        query = sql.SQL("""
            UPDATE Cavi
            SET {} = %s,
                modificato_manualmente = 1,
                timestamp = CURRENT_TIMESTAMP
            WHERE id_cantiere = %s AND id_cavo = %s
        """).format(sql.Identifier(campo_db))

        cursor.execute(query, (valore_validato, id_cantiere, id_cavo))
        conn.commit()  # Commit della transazione

    except Exception as e:
        conn.rollback()  # Rollback in caso di errore
        raise e


def aggiorna_metri_posati(id_cantiere: int, id_cavo: str = None) -> bool:
    """
    Aggiorna i metri posati per un cavo specifico.
    Se il cavo è già posato, chiede all'utente se vuole cambiare la bobina o il cavo.
    """
    if not isinstance(id_cantiere, int) or id_cantiere <= 0:
        logging.error("❌ ID cantiere non valido")
        return False

    conn = None
    try:
        # Mostra la lista dei cavi prima di chiedere l'ID
        visualizza_cavi(id_cantiere)

        conn = database_connection()
        cursor = conn.cursor()

        if not id_cavo:
            id_cavo = input("\nInserisci ID cavo: ").strip()

        # Ottieni i dettagli del cavo
        cursor.execute('''
            SELECT c.*, pc.metri_residui as bobina_metri_residui, pc.stato_bobina
            FROM Cavi c
            LEFT JOIN parco_cavi pc ON c.ID_BOBINA = pc.ID_BOBINA AND pc.id_cantiere = c.id_cantiere
            WHERE c.id_cavo = %s AND c.id_cantiere = %s
        ''', (id_cavo, id_cantiere))

        cavo = cursor.fetchone()
        if not cavo:
            print("\n❌ Cavo non trovato!")
            return False

        # Gestione cavo spare
        if cavo['modificato_manualmente'] == 3:
            print("\n⚠️ ATTENZIONE: Questo cavo è contrassegnato come SPARE.")
            if input("Vuoi riattivare il cavo? (s/n): ").strip().lower() != 's':
                conn.rollback()
                print("❌ Operazione annullata.")
                return False

            cursor.execute('''
                UPDATE Cavi
                SET modificato_manualmente = 1,
                    stato_installazione = ?,
                    timestamp = CURRENT_TIMESTAMP
                WHERE id_cavo = ? AND id_cantiere = ?
            ''', (StatoInstallazione.DA_INSTALLARE.value, id_cavo, id_cantiere))
            print("\n✅ Cavo riattivato con successo!")

        # Converti l'oggetto Row in un dizionario
        cavo_dict = dict(cavo)

        # Se il cavo è già posato, chiedi all'utente come procedere
        if cavo_dict['metratura_reale'] > 0:
            print("\n⚠️ ATTENZIONE: Questo cavo risulta già posato!")
            print("Opzioni disponibili:")
            print("1. Cambiare la bobina associata")
            print("2. Cambiare il cavo")
            print("3. Terminare operazione")
            scelta = input("\nScegli un'opzione (1/2/3): ").strip()

            if scelta == "1":
                # Chiama la funzione di modifica bobina
                return modifica_bobina_cavo_posato(id_cantiere, id_cavo)
            elif scelta == "2":
                # Richiedi un nuovo ID cavo
                print("\nInserisci un nuovo ID cavo da elaborare.")
                nuovo_id_cavo = input("Nuovo ID cavo: ").strip()
                if nuovo_id_cavo:
                    return aggiorna_metri_posati(id_cantiere, nuovo_id_cavo)
                else:
                    print("\n❌ ID cavo non valido. Operazione annullata.")
                    return False
            elif scelta == "3":
                print("\n✅ Operazione terminata.")
                return False
            else:
                print("\n❌ Opzione non valida. Operazione annullata.")
                return False

        # Visualizza i dettagli del cavo
        _visualizza_dettagli_cavo(cavo_dict)

        # Reset automatico della bobina per cavi da installare
        id_bobina = None
        bobina = None

        # Se il cavo aveva una bobina reale (non BOBINA_VUOTA), resettala a BOBINA_VUOTA
        if cavo_dict.get('id_bobina') and cavo_dict.get('id_bobina') != "BOBINA_VUOTA":
            cursor.execute('''
                UPDATE Cavi
                SET id_bobina = "BOBINA_VUOTA"
                WHERE id_cavo = ? AND id_cantiere = ?
            ''', (id_cavo, id_cantiere))
            cavo_dict['id_bobina'] = "BOBINA_VUOTA"

        # Se l'utente vuole associare una bobina
        if input("\nVuoi associare una bobina? (s/n): ").strip().lower() == 's':
            # Input della bobina
            id_bobina = input_bobina(cavo_dict, id_cantiere)

        # Se l'utente ha annullato l'operazione
        if id_bobina is None:
            print("\n❌ Operazione annullata.")
            return False

        # Se non è BOBINA_VUOTA, ottieni i dettagli della bobina
        if id_bobina != "BOBINA_VUOTA":
            # Cerca la bobina nel database
            cursor.execute('SELECT * FROM parco_cavi WHERE ID_BOBINA = %s AND id_cantiere = %s',
                           (id_bobina, id_cantiere))
            bobina_row = cursor.fetchone()
            if not bobina_row:
                print(f"\n❌ Bobina {id_bobina} non trovata!")
                conn.rollback()
                return False
            bobina = dict(bobina_row)

            # Verifica compatibilità tra cavo e bobina
            if not verifica_compatibilita(conn, cavo_dict, bobina, id_cavo, id_cantiere):
                conn.rollback()
                return False

        # Input dei metri posati
        metri_reali = input_metri_posati(cavo_dict, bobina, id_bobina, id_cavo, id_cantiere, conn)
        if metri_reali is None:
            conn.rollback()
            return False

        # Il cavo è già stato aggiornato in input_metri_posati
        conn.commit()
        return True

    except Exception as e:
        if conn:
            conn.rollback()
        logging.error(f"❌ Errore in aggiorna_metri_posati: {str(e)}")
        print(f"\n❌ Errore durante l'aggiornamento: {str(e)}")
        return False
    finally:
        if conn:
            conn.close()  # Chiudi la connessione per liberare il lock

def get_bobine_disponibili(id_cantiere: int, tipologia: Optional[str] = None, n_conduttori: Optional[int] = None, sezione: Optional[float] = None, visualizza: bool = False) -> List[Dict]:
    """
    Recupera le bobine disponibili per un cantiere, filtrando per caratteristiche e stato.
    Esclude bobine in stato OVER/TERMINATE.
    Nota: n_conduttori non è più utilizzato per la compatibilità.

    Args:
        id_cantiere: ID del cantiere
        tipologia: Filtro per tipologia di cavo
        n_conduttori: Filtro per numero di conduttori (non utilizzato per la compatibilità)
        sezione: Filtro per sezione del cavo
        visualizza: Se True, visualizza le bobine disponibili

    Returns:
        Lista di dizionari con le bobine disponibili
    """
    try:
        with database_connection() as conn:
            cursor = conn.cursor()

            # Filtri per caratteristiche
            filtri = []
            parametri = []

            if tipologia:
                filtri.append("tipologia = %s")
                parametri.append(tipologia)
            # Nota: n_conduttori non è più utilizzato per la compatibilità
            if sezione:
                filtri.append("sezione = %s")
                parametri.append(sezione)

            # Escludi bobine OVER/TERMINATE
            filtri.append("stato_bobina NOT IN (%s, %s)")
            parametri.extend([StatoBobina.OVER.value, StatoBobina.TERMINATA.value])

            parametri.append(id_cantiere)

            where_clause = " AND ".join(filtri) + (" AND " if filtri else "")

            query = f'''
                SELECT ID_BOBINA, tipologia, n_conduttori, sezione,
                       metri_residui, stato_bobina
                FROM parco_cavi
                WHERE {where_clause} id_cantiere = %s AND metri_residui > 0
                ORDER BY stato_bobina, metri_residui DESC, ID_BOBINA ASC
            '''

            cursor.execute(query, parametri)
            bobine = cursor.fetchall()

            # Converti le righe in dizionari
            bobine_list = [dict(bobina) for bobina in bobine]

            # Visualizza le bobine se richiesto
            if visualizza:
                print("\nBobine disponibili:")
                if not bobine_list:
                    print("❌ Nessuna bobina disponibile con le caratteristiche richieste.")
                else:
                    # Intestazione tabella
                    print("\n" + "-" * 80)
                    print(f"{'ID':<5} {'Tipologia':<20} {'Cond.':<6} {'Sezione':<10} {'Residui':<10} {'Stato':<12}")
                    print("-" * 80)

                    # Visualizza le bobine
                    for bobina in bobine_list:
                        # Estrai solo la parte Y dell'ID_BOBINA
                        id_parts = bobina['ID_BOBINA'].split('_B')
                        y_part = id_parts[1] if len(id_parts) > 1 else bobina['ID_BOBINA']

                        # Formatta i valori per la visualizzazione
                        tipologia = str(bobina['tipologia'])[:18].ljust(20)
                        n_conduttori = str(bobina['n_conduttori'])[:4].ljust(6)
                        sezione = str(bobina['sezione'])[:8].ljust(10)
                        metri_residui = f"{float(bobina['metri_residui']):.2f}".ljust(10)
                        stato = str(bobina['stato_bobina'])[:10].ljust(12)

                        print(f"{y_part:<5} {tipologia} {n_conduttori} {sezione} {metri_residui} {stato}")

            return bobine_list

    except Exception as e:
        logging.error(f"Errore durante il recupero delle bobine disponibili: {e}")
        return []


def verifica_stato_bobina(id_bobina: str, id_cantiere: int) -> Optional[str]:
    """
    Verifica se una bobina esiste e se è in stato OVER/TERMINATE.
    Restituisce:
    - None se la bobina non esiste.
    - Lo stato della bobina se esiste.
    """
    try:
        with database_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT stato_bobina
                FROM parco_cavi
                WHERE ID_BOBINA = %s AND id_cantiere = %s
            ''', (id_bobina, id_cantiere))
            result = cursor.fetchone()
            return result[0] if result else None
    except Exception as e:
        logging.error(f"Errore durante la verifica dello stato della bobina: {e}")
        return None


def input_bobina(cavo: dict, id_cantiere: int) -> Optional[str]:
    """
    Gestisce l'input della bobina, ricostruendo l'ID completo nel formato Cx_By.
    L'utente inserisce solo la parte Y dell'ID bobina.
    Restituisce:
    - L'ID bobina completo (es. "C1_B3") se l'utente seleziona una bobina.
    - "BOBINA_VUOTA" se l'utente sceglie di non associare una bobina.
    - None se l'utente annulla l'operazione.
    """
    # Mostra le bobine disponibili utilizzando il parametro visualizza=True
    get_bobine_disponibili(
        id_cantiere,
        tipologia=cavo.get('tipologia'),
        n_conduttori=cavo.get('n_conduttori'),
        sezione=cavo.get('sezione'),
        visualizza=True
    )

    while True:
        y_input = input(
            "\nInserisci l'ID della bobina (solo la parte numerica o testuale): "
            "\n('v' per bobina vuota, 'q' per annullare): "
        ).strip().upper()

        # Opzione annulla
        if y_input == 'Q':
            return None  # Annulla l'operazione

        # Opzione bobina vuota
        if y_input == 'V':
            print("\nℹ️ Procedi con inserimento metri senza associare bobina")
            return "BOBINA_VUOTA"

        # Converti l'input in stringa
        y_input = str(y_input)

        # Ricostruisci l'ID completo della bobina
        id_bobina = f"C{id_cantiere}_B{y_input}"

        # Verifica se la bobina esiste e non è in stato OVER/TERMINATA
        stato_bobina = verifica_stato_bobina(id_bobina, id_cantiere)
        if stato_bobina is None:
            print(f"❌ Bobina {y_input} non trovata!")
            continue
        if stato_bobina in [StatoBobina.OVER.value, StatoBobina.TERMINATA.value]:
            print(f"❌ La bobina {y_input} esiste ma è in stato {stato_bobina} e non può essere utilizzata.")
            continue

        # Restituisci l'ID completo della bobina
        return id_bobina


def input_metri_posati(cavo: dict, bobina: dict, id_bobina: str, id_cavo: str, id_cantiere: int, conn) -> Optional[    float]:
    """Input e validazione dei metri posati."""
    while True:
        try:
            # Input metri posati
            metri_input = input("\nInserire metri posati (o 'q' per annullare): ").strip()

            if metri_input.lower() == 'q':
                print("❌ Operazione annullata dall'utente")
                return None

            if not metri_input:
                print("❌ Devi inserire un valore numerico per i metri posati")
                continue

            risultato = ValidazioneCampi.valida_numero(metri_input, "Metri posati")
            if not risultato.valido:
                print(f"❌ {risultato.messaggio}")
                continue

            metri_reali = float(risultato.valore)

            # Controllo metri residui bobina (solo se bobina reale)
            if id_bobina is not None:
                id_bobina = id_bobina.strip()

            if id_bobina != "BOBINA_VUOTA" and bobina:
                if metri_reali > bobina['metri_residui']:
                    print(
                        f"\n⚠️ ATTENZIONE: Stai cercando di posare {metri_reali} metri, ma la bobina ha solo {bobina['metri_residui']} metri disponibili!")
                    print("Questo porterà la bobina in stato OVER.")
                    if input("Vuoi continuare? (s/n): ").lower() != 's':
                        continue

            # Controllo metri teorici cavo
            try:
                metri_teorici = float(cavo['metri_teorici']) if cavo['metri_teorici'] is not None else 0.0
            except (ValueError, TypeError):
                metri_teorici = 0.0

            if metri_teorici > 0 and metri_reali > metri_teorici:
                print(f"⚠️ Attenzione: i metri da posare ({metri_reali}) superano i metri teorici ({metri_teorici})")
                if input("Vuoi continuare comunque? (s/n): ").lower() != 's':
                    continue

            # AGGIORNAMENTO DATABASE (comune a tutti i casi)
            try:
                cursor = conn.cursor()

                print(
                    f"DEBUG: Aggiornamento cavo {id_cavo} con metri_reali={metri_reali}, stato={StatoInstallazione.INSTALLATO.value}")

                # Aggiorna cavo
                cursor.execute('''
                    UPDATE Cavi
                    SET metratura_reale = %s,
                        stato_installazione = %s,
                        timestamp = CURRENT_TIMESTAMP
                    WHERE id_cavo = %s AND id_cantiere = %s
                ''', (metri_reali, StatoInstallazione.INSTALLATO.value, id_cavo, id_cantiere))

                if cursor.rowcount == 0:
                    print(f"\n⚠️ Attenzione: nessuna riga aggiornata per il cavo {id_cavo}")

                # Registra la data di posa direttamente nella tabella Cavi
                from datetime import date
                data_oggi = date.today()
                cursor.execute('''
                    UPDATE Cavi
                    SET data_posa = %s
                    WHERE id_cavo = %s AND id_cantiere = %s
                ''', (data_oggi, id_cavo, id_cantiere))

                print(f"\nℹ️ Registrata posa di {metri_reali} metri in data {data_oggi}")

                # Aggiorna bobina se presente o imposta a BOBINA_VUOTA
                if id_bobina == "BOBINA_VUOTA":
                    cursor.execute('''
                        UPDATE Cavi
                        SET id_bobina = 'BOBINA_VUOTA'
                        WHERE id_cavo = %s AND id_cantiere = %s
                    ''', (id_cavo, id_cantiere))
                else:
                    success = update_cavo_bobina(conn, cavo, bobina, id_cavo, id_cantiere, id_bobina, metri_reali)
                    if not success:
                        conn.rollback()
                        print("\n❌ Errore durante l'aggiornamento della bobina")
                        return None

                conn.commit()
                print("\n✅ Cavo aggiornato con successo!")
                return metri_reali

            except Exception as e:
                conn.rollback()
                print(f"\n❌ Errore durante l'aggiornamento: {str(e)}")
                return None

        except ValueError:
            print("❌ Inserire un numero valido!")
        except Exception as e:
            print(f"❌ Errore imprevisto: {e}")
            return None

def update_cavo_bobina(conn, cavo_dict, bobina_dict, id_cavo, id_cantiere, nuova_bobina_id, metri_posati, vecchia_bobina_id=None):
    """
    Aggiorna la bobina associata a un cavo posato.

    :param conn: Connessione al database
    :param cavo_dict: Dizionario con i dati del cavo
    :param bobina_dict: Dizionario con i dati della bobina (può essere None)
    :param id_cavo: ID del cavo da aggiornare
    :param id_cantiere: ID del cantiere
    :param nuova_bobina_id: ID della nuova bobina (stringa)
    :param metri_posati: Metri di cavo posati
    :param vecchia_bobina_id: ID della vecchia bobina (opzionale)
    :return: True se l'operazione è riuscita, False altrimenti
    """
    try:
        cursor = conn.cursor()

        # Verifica che nuova_bobina_id sia una stringa
        if nuova_bobina_id is not None and not isinstance(nuova_bobina_id, str):
            raise ValueError("L'ID bobina deve essere una stringa.")

        # Se la nuova bobina è "BOBINA_VUOTA", imposta l'ID a "BOBINA_VUOTA" nel database
        if nuova_bobina_id == "BOBINA_VUOTA":
            cursor.execute('''
                UPDATE Cavi
                SET id_bobina = "BOBINA_VUOTA"
                WHERE id_cavo = ? AND id_cantiere = ?
            ''', (id_cavo, id_cantiere))
            conn.commit()
            print("\n✅ Cavo aggiornato: bobina impostata a BOBINA_VUOTA.")
            return True

        # Altrimenti, aggiorna con la nuova bobina
        cursor.execute('''
            UPDATE Cavi
            SET id_bobina = ?
            WHERE id_cavo = ? AND id_cantiere = ?
        ''', (nuova_bobina_id, id_cavo, id_cantiere))

        # Se c'è una vecchia bobina, restituisci i metri
        if vecchia_bobina_id and vecchia_bobina_id != "BOBINA_VUOTA" and vecchia_bobina_id != "TBD":
            cursor.execute('''
                UPDATE parco_cavi
                SET metri_residui = metri_residui + ?
                WHERE ID_BOBINA = ? AND id_cantiere = ?
            ''', (metri_posati, vecchia_bobina_id, id_cantiere))

            # Aggiorna lo stato della vecchia bobina
            update_stato_bobina(conn, vecchia_bobina_id, id_cantiere)

        # Sottrai i metri dalla nuova bobina
        if nuova_bobina_id != "BOBINA_VUOTA" and nuova_bobina_id != "TBD":
            cursor.execute('''
                UPDATE parco_cavi
                SET metri_residui = metri_residui - ?
                WHERE ID_BOBINA = ? AND id_cantiere = ?
            ''', (metri_posati, nuova_bobina_id, id_cantiere))

            # Aggiorna lo stato della nuova bobina
            update_stato_bobina(conn, nuova_bobina_id, id_cantiere)

        conn.commit()
        print("\n✅ Cavo aggiornato con la nuova bobina.")
        return True

    except Exception as e:
        conn.rollback()
        logging.error(f"Errore in update_cavo_bobina: {str(e)}")
        print(f"\n❌ Errore durante l'aggiornamento: {str(e)}")
        return False


def _marca_cavo_come_spare(conn, id_cavo: str, id_cantiere: int, stato_precedente: str, metri_reali: float):
    """
    Marca un cavo come spare/consumato nel database con gestione delle transazioni.

    :param conn: Connessione al database.
    :param id_cavo: ID del cavo da marcare come spare.
    :param id_cantiere: ID del cantiere.
    :param stato_precedente: Stato precedente del cavo.
    :param metri_reali: Metri reali posati del cavo.
    """
    try:
        c = conn.cursor()
        c.execute('BEGIN')  # Inizia la transazione

        # Aggiorna il cavo impostando solo il flag SPARE e la nota
        c.execute('''
            UPDATE Cavi
            SET stato_installazione = %s,
                timestamp = %s,
                modificato_manualmente = 3
            WHERE id_cavo = %s AND id_cantiere = %s
        ''', (
            "SPARE",
            datetime.now(),
            id_cavo,
            id_cantiere
        ))

        c.execute('COMMIT')  # Conferma la transazione
    except Exception as e:
        c.execute('ROLLBACK')  # Annulla la transazione in caso di errore
        raise e


def elimina_cavo(id_cantiere: int):
    """
    Marca un cavo come spare/consumato con flag 3 e aggiorna lo stato a SPARE,
    o lo elimina definitivamente se è da posare.

    :param id_cantiere: ID del cantiere a cui appartiene il cavo.
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()

            print("\n🗑️ SPARE/CONSUMATI")
            id_cavo = input("Inserisci l'ID del cavo da segnare come spare/consumato: ").strip()

            # Verifica se il cavo esiste e controlla il suo stato
            c.execute('''
                SELECT id_cavo, stato_installazione, metratura_reale
                FROM Cavi
                WHERE id_cavo = %s AND id_cantiere = %s
            ''', (id_cavo, id_cantiere))

            cavo = c.fetchone()
            if not cavo:
                print("❌ Cavo non trovato!")
                return

            # Controllo se il cavo è installato o ha metri reali posati
            is_installed = cavo[1] == "Installato" or (cavo[2] and cavo[2] > 0)

            if is_installed:
                print("\n⚠️ ATTENZIONE: Questo cavo risulta installato o parzialmente posato!")
                print(f"Stato: {cavo[1]}")
                print(f"Metri reali: {cavo[2]}")

                conferma_speciale = input(
                    "\n⚠️ Sei ASSOLUTAMENTE sicuro di voler procedere? (scrivi 'CONFERMA' per procedere): ").strip()

                if conferma_speciale != "CONFERMA":
                    print("Operazione annullata.")
                    return

                logging.warning(f"Cavo marcato come spare/consumato: ID={id_cavo}, "
                                f"Cantiere={id_cantiere}, Stato={cavo[1]}, Metri_reali={cavo[2]}")

                # Per cavi installati, procedi sempre con la marcatura SPARE
                try:
                    _marca_cavo_come_spare(conn, id_cavo, id_cantiere, cavo[1], cavo[2])
                    print("✅ Cavo marcato come spare/consumato con successo!")
                    logging.info(f"✅ Cavo marcato come spare/consumato: ID={id_cavo}, Cantiere={id_cantiere}")
                except Exception as e:
                    print(f"❌ Errore durante l'operazione: {str(e)}")
                    conn.rollback()
                    logging.error(f"❌ Errore durante la marcatura del cavo come spare/consumato: {str(e)}")
            else:
                # Per cavi da posare, offri l'opzione di eliminazione definitiva
                print("\n📋 Il cavo risulta da posare. Puoi:")
                print("1. Marcarlo come SPARE (mantenendolo nel database)")
                print("2. Eliminarlo definitivamente dal database")

                scelta = input("\nScegli l'opzione (1/2): ").strip()

                if scelta == "1":
                    conferma = input("⚠️ Confermi di voler marcare il cavo come SPARE? (s/n): ").lower()
                    if conferma != 's':
                        print("Operazione annullata.")
                        return

                    try:
                        _marca_cavo_come_spare(conn, id_cavo, id_cantiere, cavo[1], cavo[2])
                        print("✅ Cavo marcato come spare/consumato con successo!")
                        logging.info(f"✅ Cavo marcato come spare/consumato: ID={id_cavo}, Cantiere={id_cantiere}")
                    except Exception as e:
                        print(f"❌ Errore durante l'operazione: {str(e)}")
                        conn.rollback()
                        logging.error(f"❌ Errore durante la marcatura del cavo come spare/consumato: {str(e)}")

                elif scelta == "2":
                    conferma = input("⚠️ Confermi di voler ELIMINARE DEFINITIVAMENTE il cavo? (s/n): ").lower()
                    if conferma != 's':
                        print("Operazione annullata.")
                        return

                    try:
                        c.execute('DELETE FROM Cavi WHERE id_cavo = %s AND id_cantiere = %s',
                                  (id_cavo, id_cantiere))
                        conn.commit()
                        print("✅ Cavo eliminato definitivamente dal database!")
                        logging.info(f"✅ Cavo eliminato definitivamente: ID={id_cavo}, Cantiere={id_cantiere}")
                    except Exception as e:
                        print(f"❌ Errore durante l'eliminazione: {str(e)}")
                        conn.rollback()
                        logging.error(f"❌ Errore durante l'eliminazione definitiva del cavo: {str(e)}")

                else:
                    print("❌ Opzione non valida. Operazione annullata.")
                    return

    except Exception as e:
        logging.error(f"❌ Errore generico: {str(e)}")


def _visualizza_dettagli_cavo(cavo):
    """
    Visualizza i dettagli di un cavo.

    :param cavo: Record del cavo dal database (sqlite3.Row)
    """
    print("\nDettagli del cavo:")
    print("1. Sistema:", cavo['sistema'] if 'sistema' in cavo else '-')
    print("2. Utility:", cavo['utility'])
    print("3. Colore Cavo:", cavo['colore_cavo'] if 'colore_cavo' in cavo else '-')
    print("4. Tipologia:", cavo['tipologia'])
    print("5. N° Conduttori:", cavo['n_conduttori'])
    print("6. Sezione:", cavo['sezione'])
    print("7. SH:", cavo['SH'] if 'SH' in cavo else '-')
    print("8. Ubicazione Partenza:", cavo['ubicazione_partenza'] if 'ubicazione_partenza' in cavo else (cavo['partenza'] if 'partenza' in cavo else '-'))
    print("9. Utenza Partenza:", cavo['utenza_partenza'] if 'utenza_partenza' in cavo else '-')
    print("10. Descrizione Utenza Partenza:", cavo['descrizione_utenza_partenza'] if 'descrizione_utenza_partenza' in cavo else '-')
    print("11. Ubicazione Arrivo:", cavo['ubicazione_arrivo'] if 'ubicazione_arrivo' in cavo else (cavo['arrivo'] if 'arrivo' in cavo else '-'))
    print("12. Utenza Arrivo:", cavo['utenza_arrivo'] if 'utenza_arrivo' in cavo else '-')
    print("13. Descrizione Utenza Arrivo:", cavo['descrizione_utenza_arrivo'] if 'descrizione_utenza_arrivo' in cavo else '-')
    print("14. Metri Teorici:", cavo['metri_teorici'])
    print("15. Stato Installazione:", cavo['stato_installazione'] if 'stato_installazione' in cavo else '-')

    # Mostra la metratura reale solo se è maggiore di zero
    if cavo.get('metratura_reale', 0) > 0:
        print("16. Metratura Reale:", cavo['metratura_reale'])


def modifica_bobina_cavo_posato(id_cantiere: int, id_cavo: str = None) -> bool:
    """
    Modifica la bobina associata a un cavo già posato.
    L'utente inserisce solo la parte Y dell'ID bobina (es. "3" invece di "C1_B3")
    """
    try:
        with database_connection() as conn:
            cursor = conn.cursor()

            # Se l'ID cavo non è fornito, chiedilo
            if id_cavo is None:
                visualizza_cavi(id_cantiere)
                id_cavo = input("\nInserisci ID cavo da modificare: ").strip()
                if not id_cavo:
                    print("❌ Nessun ID cavo inserito")
                    return False

            # Recupera i dettagli del cavo
            cursor.execute('''
                SELECT c.*, pc.metri_residui as bobina_residui, pc.stato_bobina as bobina_stato
                FROM Cavi c
                LEFT JOIN parco_cavi pc ON c.id_bobina = pc.id_bobina AND pc.id_cantiere = c.id_cantiere
                WHERE c.id_cavo = %s AND c.id_cantiere = %s
            ''', (id_cavo, id_cantiere))

            cavo = cursor.fetchone()
            if not cavo:
                print("❌ Cavo non trovato!")
                return False

            # Verifica che il cavo sia posato
            if not cavo['metratura_reale'] or float(cavo['metratura_reale']) <= 0:
                print("❌ Cavo non risulta posato! Usa 'Aggiorna metri posati' prima")
                return False

            cavo_dict = dict(cavo)
            _visualizza_dettagli_cavo(cavo_dict)

            print("\n" + "=" * 40)
            print("🔄 MODIFICA BOBINA")
            print("=" * 40)
            print("1. Assegna nuova bobina")
            print("2. Rimuovi bobina attuale [v]")
            print("3. Annulla installazione [a]")
            print("4. Annulla operazione [q]")

            scelta = input("\nScelta (1/2/3/4): ").strip().lower()

            # Opzione 2 - Rimuovi bobina
            if scelta in ['2', 'v']:
                if not cavo['id_bobina']:
                    print("ℹ️ Il cavo non ha bobine associate")
                    return True

                success = update_cavo_bobina(
                    conn, cavo_dict, None, id_cavo, id_cantiere,
                    "BOBINA_VUOTA", cavo['metratura_reale'],
                    cavo['id_bobina']
                )

                if success:
                    print(f"\n✅ Rimossa bobina {cavo['id_bobina']}")
                    print(f"   › Restituiti {cavo['metratura_reale']} m")
                    return True
                return False

            # Opzione 3 - Annulla installazione
            elif scelta in ['3', 'a']:
                conferma = input("\n⚠️ ATTENZIONE: Questa operazione annullerà l'installazione del cavo.\n"
                                "Confermi? (s/n): ").strip().lower()
                if conferma == 's':
                    return annulla_installazione_cavo(id_cantiere, id_cavo, cavo_dict)
                else:
                    print("❌ Operazione annullata")
                    return False

            # Opzione 1 - Assegna nuova bobina
            elif scelta == '1':
                # Mostra bobine compatibili
                bobine = get_bobine_disponibili(
                    id_cantiere,
                    tipologia=cavo['tipologia'],
                    n_conduttori=cavo['n_conduttori'],
                    sezione=cavo['sezione'],
                    visualizza=True
                )

                if not bobine:
                    print("❌ Nessuna bobina compatibile disponibile!")
                    return False

                # Chiedi all'utente di inserire solo la parte Y dell'ID
                y_input = input(
                    "\nInserisci l'ID della bobina (solo la parte numerica): "
                    "\n('v' per bobina vuota, 'q' per annullare): "
                ).strip().upper()

                if y_input == 'Q':
                    print("❌ Operazione annullata")
                    return False
                if y_input == 'V':
                    y_input = "BOBINA_VUOTA"

                # Ricostruisci l'ID completo
                if y_input != "BOBINA_VUOTA":
                    nuova_bobina_id = f"C{id_cantiere}_B{y_input}"

                    # Verifica che la bobina esista
                    cursor.execute('''
                        SELECT * FROM parco_cavi
                        WHERE ID_BOBINA = ? AND id_cantiere = ?
                    ''', (nuova_bobina_id, id_cantiere))
                    bobina = cursor.fetchone()

                    if not bobina:
                        print(f"❌ Bobina {y_input} non trovata!")
                        return False

                    bobina_dict = dict(bobina)
                else:
                    nuova_bobina_id = "BOBINA_VUOTA"
                    bobina_dict = None

                # Aggiorna la bobina
                success = update_cavo_bobina(
                    conn, cavo_dict, bobina_dict, id_cavo, id_cantiere,
                    nuova_bobina_id, cavo['metratura_reale'],
                    cavo['id_bobina']
                )

                if success:
                    print(f"\n✅ Assegnata bobina {y_input}")
                    print(f"   › Metri utilizzati: {cavo['metratura_reale']} m")
                    return True
                return False

            # Opzione 4 - Annulla
            elif scelta in ['4', 'q']:
                print("❌ Operazione annullata")
                return False

            else:
                print("❌ Scelta non valida!")
                return False

    except Exception as e:
        print(f"\n❌ Errore durante la modifica: {str(e)}")
        logging.error(f"Errore in modifica_bobina_cavo_posato: {str(e)}")
        return False


def annulla_installazione_cavo(id_cantiere: int, id_cavo: str, cavo_dict: dict) -> bool:
    """
    Annulla l'installazione di un cavo, restituendo i metri alla bobina originale.
    """
    try:
        print("\n↩️ ANNULLAMENTO INSTALLAZIONE")
        print("\nDettagli del cavo:")
        print(f"ID Cavo: {cavo_dict['id_cavo']}")
        print(f"Metri posati: {cavo_dict['metratura_reale']}")
        print(f"ID Bobina: {cavo_dict.get('id_bobina') or 'Nessuna bobina'}")

        with database_connection() as conn:
            cursor = conn.cursor()

            metri_da_restituire = float(cavo_dict['metratura_reale'])
            id_bobina = cavo_dict.get('id_bobina')

            # Inizia una transazione
            cursor.execute('BEGIN')

            # Restituisci i metri alla bobina originale
            if id_bobina and id_bobina != "BOBINA_VUOTA" and id_bobina != "TBD":
                # Verifica se l'ID bobina è già nel formato completo
                if not id_bobina.startswith(f"C{id_cantiere}_B"):
                    id_bobina = f"C{id_cantiere}_B{id_bobina}"

                cursor.execute('SELECT metri_residui, metri_totali FROM parco_cavi WHERE ID_BOBINA = ? AND id_cantiere = ?',
                               (id_bobina, id_cantiere))
                result = cursor.fetchone()
                if result:
                    metri_residui, metri_totali = result
                    nuovi_metri_residui = metri_residui + metri_da_restituire

                    # Aggiorna i metri residui della bobina
                    cursor.execute('''
                        UPDATE parco_cavi
                        SET metri_residui = ?
                        WHERE ID_BOBINA = ? AND id_cantiere = ?
                    ''', (nuovi_metri_residui, id_bobina, id_cantiere))

                    # Aggiorna lo stato della bobina usando la funzione dedicata
                    update_stato_bobina(conn, id_bobina, id_cantiere)

                    print(f"\n✅ Restituiti {metri_da_restituire} metri alla bobina {id_bobina}")
                else:
                    print(f"\n⚠️ Bobina {id_bobina} non trovata nel parco cavi!")
                    # Continuiamo comunque con l'annullamento dell'installazione

            # Aggiorna lo stato del cavo a DA_POSARE, azzera la metratura reale e rimuovi la data di posa
            cursor.execute('''
                UPDATE Cavi
                SET stato_installazione = ?,
                    metratura_reale = 0,
                    id_bobina = NULL,
                    data_posa = NULL
                WHERE id_cavo = ? AND id_cantiere = ?
            ''', (StatoInstallazione.DA_INSTALLARE.value, id_cavo, id_cantiere))

            print(f"\nℹ️ Rimossa la data di posa per il cavo {id_cavo}")

            # Commit della transazione
            conn.commit()
            print("\n✅ Installazione annullata con successo!")
            return True

    except Exception as e:
        if 'conn' in locals() and conn:
            conn.rollback()
        print(f"\n❌ Errore durante l'annullamento dell'installazione: {str(e)}")
        logging.error(f"Errore in annulla_installazione_cavo: {str(e)}")
        return False

def verifica_compatibilita(conn, cavo_dict, bobina, id_cavo, id_cantiere):
    """
    Verifica la compatibilità tra un cavo e una bobina.
    Se non sono compatibili, offre opzioni per aggiornare le caratteristiche del cavo.
    Nota: n_conduttori non è più utilizzato per la compatibilità.

    Args:
        conn: Connessione al database
        cavo_dict: Dizionario con le caratteristiche del cavo
        bobina: Dizionario con le caratteristiche della bobina
        id_cavo: ID del cavo
        id_cantiere: ID del cantiere

    Returns:
        bool: True se compatibili o aggiornate con successo, False se operazione annullata
    """
    # Verifica compatibilità tra cavo e bobina
    # Nota: n_conduttori non è più utilizzato per la compatibilità
    if (cavo_dict['tipologia'] != bobina['tipologia'] or
            cavo_dict['sezione'] != bobina['sezione']):

        print("\n⚠️ ATTENZIONE: Le caratteristiche non corrispondono!")
        print("\nCaratteristiche cavo in lista:")
        print(f"Tipologia: {cavo_dict['tipologia']}")
        print(f"Formazione: {cavo_dict['sezione']}")

        print("\nCaratteristiche bobina selezionata:")
        print(f"Tipologia: {bobina['tipologia']}")
        print(f"Formazione: {bobina['sezione']}")
        print(f"Metri residui: {bobina['metri_residui']}")

        print("\nOpzioni:")
        print("1. Aggiorna caratteristiche cavo in lista con quelle della bobina")
        print("2. Seleziona un'altra bobina")

        scelta = input("\nScegli un'opzione (1/2): ").strip()
        if scelta == "1":
            cursor = conn.cursor()
            # Aggiorna le caratteristiche del cavo
            # Utilizziamo psycopg2.sql per costruire la query in modo sicuro
            from psycopg2 import sql
            query = sql.SQL('''
                UPDATE Cavi
                SET tipologia = %s,
                    sezione = %s,
                    modificato_manualmente = 1,
                    timestamp = CURRENT_TIMESTAMP
                WHERE id_cavo = %s AND id_cantiere = %s
            ''')

            cursor.execute(query, (
                bobina['tipologia'],
                bobina['sezione'],
                id_cavo,
                id_cantiere
            ))

            # Aggiorna il dizionario del cavo
            cavo_dict['tipologia'] = bobina['tipologia']
            cavo_dict['sezione'] = bobina['sezione']

            print("\n✅ Caratteristiche cavo aggiornate con successo!")
            return True
        else:
            print("\n❌ Operazione annullata.")
            return False

    # Se sono compatibili, ritorna True
    return True

def update_stato_bobina(conn, id_bobina: str, id_cantiere: int) -> bool:
    """
    Aggiorna lo stato di una bobina in base ai suoi metri residui e totali.

    Args:
        conn: Connessione al database
        id_bobina: ID della bobina da aggiornare
        id_cantiere: ID del cantiere

    Returns:
        bool: True se l'aggiornamento è avvenuto con successo, False altrimenti
    """
    try:
        cursor = conn.cursor()

        # Ottieni i metri residui e totali della bobina
        cursor.execute('''
            SELECT metri_residui, metri_totali
            FROM parco_cavi
            WHERE ID_BOBINA = ? AND id_cantiere = ?
        ''', (id_bobina, id_cantiere))

        result = cursor.fetchone()
        if not result:
            logging.error(f"Bobina {id_bobina} non trovata nel cantiere {id_cantiere}")
            return False

        metri_residui, metri_totali = result

        # Determina il nuovo stato della bobina
        if metri_residui < 0:
            nuovo_stato = StatoBobina.OVER.value
        elif metri_residui == 0:
            nuovo_stato = StatoBobina.TERMINATA.value
        elif metri_residui < metri_totali:
            nuovo_stato = StatoBobina.IN_USO.value
        else:
            nuovo_stato = StatoBobina.DISPONIBILE.value

        # Aggiorna lo stato della bobina
        cursor.execute('''
            UPDATE parco_cavi
            SET stato_bobina = ?
            WHERE ID_BOBINA = ? AND id_cantiere = ?
        ''', (nuovo_stato, id_bobina, id_cantiere))

        logging.info(f"Stato della bobina {id_bobina} aggiornato a {nuovo_stato}")
        return True

    except Exception as e:
        logging.error(f"Errore durante l'aggiornamento dello stato della bobina {id_bobina}: {e}")
        return False
