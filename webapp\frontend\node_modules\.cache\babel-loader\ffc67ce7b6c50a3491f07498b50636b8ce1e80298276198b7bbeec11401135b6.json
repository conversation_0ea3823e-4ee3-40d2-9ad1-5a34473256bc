{"ast": null, "code": "import { formatDistance } from \"./hr/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./hr/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./hr/_lib/formatRelative.mjs\";\nimport { localize } from \"./hr/_lib/localize.mjs\";\nimport { match } from \"./hr/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Croatian locale.\n * @language Croatian\n * @iso-639-2 hrv\n * <AUTHOR> [@silvenon](https://github.com/silvenon)\n * <AUTHOR> [@manico](https://github.com/manico)\n * <AUTHOR> [@jerzabek](https://github.com/jerzabek)\n */\nexport const hr = {\n  code: \"hr\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1\n  }\n};\n\n// Fallback for modularized imports:\nexport default hr;", "map": {"version": 3, "names": ["formatDistance", "formatLong", "formatRelative", "localize", "match", "hr", "code", "options", "weekStartsOn", "firstWeekContainsDate"], "sources": ["C:/CMS/webapp/frontend/node_modules/date-fns/locale/hr.mjs"], "sourcesContent": ["import { formatDistance } from \"./hr/_lib/formatDistance.mjs\";\nimport { formatLong } from \"./hr/_lib/formatLong.mjs\";\nimport { formatRelative } from \"./hr/_lib/formatRelative.mjs\";\nimport { localize } from \"./hr/_lib/localize.mjs\";\nimport { match } from \"./hr/_lib/match.mjs\";\n\n/**\n * @category Locales\n * @summary Croatian locale.\n * @language Croatian\n * @iso-639-2 hrv\n * <AUTHOR> [@silvenon](https://github.com/silvenon)\n * <AUTHOR> [@manico](https://github.com/manico)\n * <AUTHOR> [@jerzabek](https://github.com/jerzabek)\n */\nexport const hr = {\n  code: \"hr\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 1 /* Monday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default hr;\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,UAAU,QAAQ,0BAA0B;AACrD,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,QAAQ,QAAQ,wBAAwB;AACjD,SAASC,KAAK,QAAQ,qBAAqB;;AAE3C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,EAAE,GAAG;EAChBC,IAAI,EAAE,IAAI;EACVN,cAAc,EAAEA,cAAc;EAC9BC,UAAU,EAAEA,UAAU;EACtBC,cAAc,EAAEA,cAAc;EAC9BC,QAAQ,EAAEA,QAAQ;EAClBC,KAAK,EAAEA,KAAK;EACZG,OAAO,EAAE;IACPC,YAAY,EAAE,CAAC,CAAC;IAChBC,qBAAqB,EAAE;EACzB;AACF,CAAC;;AAED;AACA,eAAeJ,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}