from sqlalchemy import Column, Integer, String, Float, Date, DateTime, ForeignKey, Text, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from backend.database import Base

class CertificazioneCavo(Base):
    """
    Modello SQLAlchemy per la tabella certificazioni_cavi.
    Corrisponde alla tabella CertificazioniCavi nel database esistente.
    Aggiornato per conformità CEI 64-8.
    """
    __tablename__ = "certificazionicavi"

    id_certificazione = Column(Integer, primary_key=True, index=True)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)
    id_cavo = Column(String, nullable=False)
    numero_certificato = Column(String, nullable=False)
    data_certificazione = Column(Date, nullable=False)
    id_operatore = Column(String, nullable=True)
    strumento_utilizzato = Column(String, nullable=True)
    id_strumento = Column(Integer, ForeignKey("strumenticertificati.id_strumento"), nullable=True)
    lunghezza_misurata = Column(Float, nullable=True)
    valore_continuita = Column(String, nullable=True)
    valore_isolamento = Column(String, nullable=True)
    valore_resistenza = Column(String, nullable=True)
    percorso_certificato = Column(String, nullable=True)
    percorso_foto = Column(String, nullable=True)
    note = Column(Text, nullable=True)
    timestamp_creazione = Column(DateTime, default=func.now())
    timestamp_modifica = Column(DateTime, nullable=True)

    # Nuovi campi CEI 64-8
    id_rapporto = Column(Integer, ForeignKey("rapportigeneralicollaudo.id_rapporto"), nullable=True)
    tipo_certificato = Column(String, default='SINGOLO')  # 'SINGOLO', 'GRUPPO'
    stato_certificato = Column(String, default='CONFORME')  # 'CONFORME', 'NON_CONFORME', 'BOZZA'
    designazione_funzionale = Column(Text, nullable=True)
    tensione_nominale = Column(String, nullable=True)
    tensione_prova_isolamento = Column(Integer, nullable=True)  # in Volt
    durata_prova_isolamento = Column(Integer, nullable=True)  # in secondi
    valore_minimo_isolamento = Column(Float, nullable=True)  # in MΩ
    temperatura_prova = Column(Float, nullable=True)
    umidita_prova = Column(Float, nullable=True)
    esito_complessivo = Column(String, nullable=True)  # 'CONFORME', 'NON_CONFORME', 'CONFORME_CON_OSSERVAZIONI'

    # Relazioni
    cantiere = relationship("Cantiere", backref="certificazioni_cavi")
    strumento_certificato = relationship("StrumentoCertificato", back_populates="certificazioni_cavi")
    rapporto_generale = relationship("RapportoGeneraleCollaudo", back_populates="certificazioni")
    prove_dettagliate = relationship("ProvaDettagliata", back_populates="certificazione", cascade="all, delete-orphan")
    non_conformita = relationship("NonConformita", back_populates="certificazione")
