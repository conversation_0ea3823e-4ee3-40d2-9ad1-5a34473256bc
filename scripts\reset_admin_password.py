#!/usr/bin/env python3
"""
Script per Reset Password Amministratore
Uso: python reset_admin_password.py
"""

import sys
import getpass
import psycopg2
from datetime import datetime
import bcrypt

def hash_password(password: str) -> str:
    """Genera hash bcrypt della password."""
    salt = bcrypt.gensalt(rounds=12)
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def get_admin_users(cursor):
    """Recupera tutti gli utenti admin."""
    cursor.execute("""
        SELECT id_utente, username, email, ruolo 
        FROM Utenti 
        WHERE ruolo = 'owner'
        ORDER BY username
    """)
    return cursor.fetchall()

def update_admin_password(cursor, user_id: int, new_password_hash: str):
    """Aggiorna la password dell'admin nel database."""
    cursor.execute("""
        UPDATE Utenti 
        SET password = %s,
            last_password_change = %s,
            failed_login_attempts = 0,
            locked_until = NULL,
            password_plain = NULL
        WHERE id_utente = %s AND ruolo = 'owner'
    """, (new_password_hash, datetime.utcnow(), user_id))
    
    return cursor.rowcount > 0

def main():
    print("🔐 Script Reset Password Amministratore CMS")
    print("=" * 50)
    
    # Configurazione database
    db_config = {
        'host': 'localhost',
        'port': 5432,
        'database': 'cantieri',
        'user': 'postgres',
        'password': 'Taranto'
    }
    
    try:
        # Connessione database
        print("📡 Connessione al database...")
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        print("✅ Connesso al database")
        
        # Recupera utenti admin
        print("\n👥 Utenti amministratori trovati:")
        admin_users = get_admin_users(cursor)
        
        if not admin_users:
            print("❌ Nessun utente amministratore trovato!")
            return
        
        for i, (user_id, username, email, ruolo) in enumerate(admin_users, 1):
            print(f"{i}. ID: {user_id} | Username: {username} | Email: {email}")
        
        # Selezione utente
        if len(admin_users) == 1:
            selected_user = admin_users[0]
            print(f"\n🎯 Selezionato automaticamente: {selected_user[1]}")
        else:
            while True:
                try:
                    choice = int(input(f"\nSeleziona utente (1-{len(admin_users)}): "))
                    if 1 <= choice <= len(admin_users):
                        selected_user = admin_users[choice - 1]
                        break
                    else:
                        print("❌ Selezione non valida")
                except ValueError:
                    print("❌ Inserisci un numero valido")
        
        user_id, username, email, ruolo = selected_user
        print(f"\n🔧 Reset password per: {username} (ID: {user_id})")
        
        # Conferma operazione
        confirm = input("\n⚠️  ATTENZIONE: Questa operazione resetterà la password dell'amministratore.\nContinuare? (sì/no): ")
        if confirm.lower() not in ['sì', 'si', 'yes', 'y']:
            print("❌ Operazione annullata")
            return
        
        # Input nuova password
        print("\n🔑 Inserisci la nuova password:")
        print("Requisiti: min 8 caratteri, maiuscole, minuscole, numeri, simboli")
        
        while True:
            new_password = getpass.getpass("Nuova password: ")
            confirm_password = getpass.getpass("Conferma password: ")
            
            if not new_password:
                print("❌ Password non può essere vuota")
                continue
                
            if new_password != confirm_password:
                print("❌ Le password non corrispondono")
                continue
                
            if len(new_password) < 8:
                print("❌ Password troppo corta (minimo 8 caratteri)")
                continue
                
            # Validazione base
            has_upper = any(c.isupper() for c in new_password)
            has_lower = any(c.islower() for c in new_password)
            has_digit = any(c.isdigit() for c in new_password)
            has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in new_password)
            
            if not all([has_upper, has_lower, has_digit, has_special]):
                print("❌ Password deve contenere: maiuscole, minuscole, numeri e simboli")
                continue
                
            break
        
        # Genera hash
        print("\n🔐 Generazione hash password...")
        password_hash = hash_password(new_password)
        print("✅ Hash generato")
        
        # Aggiorna database
        print("💾 Aggiornamento database...")
        success = update_admin_password(cursor, user_id, password_hash)
        
        if success:
            conn.commit()
            print("✅ Password aggiornata con successo!")
            
            # Log dell'operazione
            cursor.execute("""
                INSERT INTO security_events (event_type, user_id, username, success, additional_data, created_at)
                VALUES (%s, %s, %s, %s, %s, %s)
            """, (
                'admin_password_reset',
                user_id,
                username,
                True,
                {'reset_method': 'manual_script', 'reset_by': 'database_admin'},
                datetime.utcnow()
            ))
            conn.commit()
            print("✅ Evento loggato nel sistema di audit")
            
            print(f"\n🎉 Reset completato per utente: {username}")
            print("💡 L'amministratore può ora accedere con la nuova password")
            
        else:
            print("❌ Errore durante l'aggiornamento della password")
            conn.rollback()
            
    except psycopg2.Error as e:
        print(f"❌ Errore database: {e}")
    except KeyboardInterrupt:
        print("\n❌ Operazione interrotta dall'utente")
    except Exception as e:
        print(f"❌ Errore: {e}")
    finally:
        if 'conn' in locals():
            conn.close()
            print("📡 Connessione database chiusa")

if __name__ == "__main__":
    main()
