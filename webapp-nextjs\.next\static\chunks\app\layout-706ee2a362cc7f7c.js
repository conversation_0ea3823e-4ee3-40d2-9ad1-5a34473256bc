(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{3493:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("cable",[["path",{d:"M17 21v-2a1 1 0 0 1-1-1v-1a2 2 0 0 1 2-2h2a2 2 0 0 1 2 2v1a1 1 0 0 1-1 1",key:"10bnsj"}],["path",{d:"M19 15V6.5a1 1 0 0 0-7 0v11a1 1 0 0 1-7 0V9",key:"1eqmu1"}],["path",{d:"M21 21v-2h-4",key:"14zm7j"}],["path",{d:"M3 5h4V3",key:"z442eg"}],["path",{d:"M7 5a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6a1 1 0 0 1 1-1V3",key:"ebdjd7"}]])},5302:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},14186:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},17259:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,5302,23)),Promise.resolve().then(a.t.bind(a,75353,23)),Promise.resolve().then(a.t.bind(a,30347,23)),Promise.resolve().then(a.bind(a,43541)),Promise.resolve().then(a.bind(a,97680)),Promise.resolve().then(a.bind(a,48287)),Promise.resolve().then(a.bind(a,40283))},17580:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},23227:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("building-2",[["path",{d:"M6 22V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v18Z",key:"1b4qmf"}],["path",{d:"M6 12H4a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2h2",key:"i71pzd"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v9a2 2 0 0 1-2 2h-2",key:"10jefs"}],["path",{d:"M10 6h4",key:"1itunk"}],["path",{d:"M10 10h4",key:"tcdvrf"}],["path",{d:"M10 14h4",key:"kelpxr"}],["path",{d:"M10 18h4",key:"1ulq68"}]])},25273:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("clipboard-list",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"M12 11h4",key:"1jrz19"}],["path",{d:"M12 16h4",key:"n85exb"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 16h.01",key:"18s6g9"}]])},30285:(e,t,a)=>{"use strict";a.d(t,{$:()=>d});var s=a(95155);a(12115);var r=a(99708),i=a(74466),n=a(59434);let l=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:a,size:i,asChild:d=!1,...c}=e,o=d?r.DX:"button";return(0,s.jsx)(o,{"data-slot":"button",className:(0,n.cn)(l({variant:a,size:i,className:t})),...c})}},30347:()=>{},35695:(e,t,a)=>{"use strict";var s=a(18999);a.o(s,"useParams")&&a.d(t,{useParams:function(){return s.useParams}}),a.o(s,"usePathname")&&a.d(t,{usePathname:function(){return s.usePathname}}),a.o(s,"useRouter")&&a.d(t,{useRouter:function(){return s.useRouter}}),a.o(s,"useSearchParams")&&a.d(t,{useSearchParams:function(){return s.useSearchParams}})},37108:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},43541:(e,t,a)=>{"use strict";a.d(t,{default:()=>m});var s=a(95155);a(12115);var r=a(40283),i=a(55365),n=a(30285),l=a(1243),d=a(14186),c=a(69074),o=a(54416);function h(){let{expirationWarning:e,daysUntilExpiration:t,expirationDate:a,dismissExpirationWarning:h}=(0,r.A)();return e?(0,s.jsx)(i.Fc,{variant:0===t||1===t?"destructive":"default",className:"mb-4 border-l-4",children:(0,s.jsxs)("div",{className:"flex items-start justify-between",children:[(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:[0===t?(0,s.jsx)(l.A,{className:"h-4 w-4"}):1===t?(0,s.jsx)(d.A,{className:"h-4 w-4"}):(0,s.jsx)(c.A,{className:"h-4 w-4"}),(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(i.TN,{className:"font-medium",children:e}),a&&(0,s.jsxs)(i.TN,{className:"text-sm mt-1 opacity-90",children:["Data di scadenza: ",new Date(a).toLocaleDateString("it-IT",{day:"2-digit",month:"2-digit",year:"numeric"})]}),(0,s.jsx)(i.TN,{className:"text-sm mt-2 opacity-80",children:"Contatta l'amministratore per rinnovare il tuo account."})]})]}),(0,s.jsx)(n.$,{variant:"ghost",size:"sm",onClick:h,className:"h-6 w-6 p-0 hover:bg-transparent",children:(0,s.jsx)(o.A,{className:"h-4 w-4"})})]})}):null}function m(e){let{children:t}=e,{isAuthenticated:a}=(0,r.A)();return(0,s.jsxs)("main",{className:"pt-16",children:[a&&(0,s.jsx)("div",{className:"container mx-auto px-4 py-2",children:(0,s.jsx)(h,{})}),t]})}},48287:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>c});var s=a(95155),r=a(87481),i=a(30285),n=a(85339),l=a(40646),d=a(54416);function c(){let{toasts:e,dismiss:t}=(0,r.dj)();return(0,s.jsx)("div",{className:"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",children:e.map(e=>(0,s.jsxs)("div",{className:"group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",style:{backgroundColor:"destructive"===e.variant?"#fef2f2":"#f0f9ff",borderColor:"destructive"===e.variant?"#fecaca":"#bae6fd"},children:[(0,s.jsxs)("div",{className:"flex items-start space-x-2",children:["destructive"===e.variant?(0,s.jsx)(n.A,{className:"h-4 w-4 text-red-600 mt-0.5"}):(0,s.jsx)(l.A,{className:"h-4 w-4 text-green-600 mt-0.5"}),(0,s.jsxs)("div",{className:"grid gap-1",children:[e.title&&(0,s.jsx)("div",{className:"text-sm font-semibold ".concat("destructive"===e.variant?"text-red-900":"text-gray-900"),children:e.title}),e.description&&(0,s.jsx)("div",{className:"text-sm ".concat("destructive"===e.variant?"text-red-700":"text-gray-700"),children:e.description})]})]}),(0,s.jsx)(i.$,{variant:"ghost",size:"sm",className:"absolute right-1 top-1 h-6 w-6 p-0 hover:bg-transparent",onClick:()=>t(e.id),children:(0,s.jsx)(d.A,{className:"h-3 w-3"})})]},e.id))})}},54416:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},55365:(e,t,a)=>{"use strict";a.d(t,{Fc:()=>d,TN:()=>c});var s=a(95155),r=a(12115),i=a(74466),n=a(59434);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef((e,t)=>{let{className:a,variant:r,...i}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(l({variant:r}),a),...i})});d.displayName="Alert",r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",a),...r})}).displayName="AlertTitle";let c=r.forwardRef((e,t)=>{let{className:a,...r}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",a),...r})});c.displayName="AlertDescription"},57434:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},59434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var s=a(52596),r=a(39688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}},69074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},71007:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},72713:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},75353:e=>{e.exports={style:{fontFamily:"'JetBrains Mono', 'JetBrains Mono Fallback'",fontStyle:"normal"},className:"__className_3c557b",variable:"__variable_3c557b"}},79397:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]])},85339:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(19946).A)("circle-alert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},87481:(e,t,a)=>{"use strict";a.d(t,{dj:()=>m});var s=a(12115);let r=0,i=new Map,n=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),o({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],c={toasts:[]};function o(e){c=l(c,e),d.forEach(e=>{e(c)})}function h(e){let{...t}=e,a=(r=(r+1)%Number.MAX_VALUE).toString(),s=()=>o({type:"DISMISS_TOAST",toastId:a});return o({type:"ADD_TOAST",toast:{...t,id:a,open:!0,onOpenChange:e=>{e||s()}}}),{id:a,dismiss:s,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function m(){let[e,t]=(0,s.useState)(c);return(0,s.useEffect)(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[]),{...e,toast:h,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}},97680:(e,t,a)=>{"use strict";a.d(t,{Navbar:()=>w});var s=a(95155),r=a(12115),i=a(6874),n=a.n(i),l=a(35695),d=a(30285),c=a(40283),o=a(19946);let h=(0,o.A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]]);var m=a(23227),u=a(3493),x=a(37108),v=a(57434),p=a(72713),f=a(25273),b=a(79397),y=a(71007);let g=(0,o.A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]]);var j=a(54416);let k=(0,o.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]);var N=a(17580);let A=(e,t,a,s)=>{let r={name:"owner"===e?"Menu Admin":"user"===e?"Lista Cantieri":"cantieri_user"===e?"Gestione Cavi":"Home",href:"owner"===e?"/admin":"user"===e?"/cantieri":"cantieri_user"===e?"/cavi":"/",icon:h};if("owner"===e&&!t)return[r];if("user"===e||t&&(null==a?void 0:a.role)==="user"){let e=[r];return t&&e.push({name:"Cantieri",href:"/cantieri",icon:m.A}),s&&e.push({name:"Visualizza Cavi",href:"/cavi",icon:u.A},{name:"Parco Cavi",href:"/parco-cavi",icon:x.A},{name:"Gestione Excel",href:"/excel",icon:v.A},{name:"Report",href:"/reports",icon:p.A},{name:"Gestione Comande",href:"/comande",icon:f.A},{name:"Produttivit\xe0",href:"/productivity",icon:b.A}),e}if("cantieri_user"===e||t&&(null==a?void 0:a.role)==="cantieri_user"){let t=[r];return s&&("cantieri_user"!==e&&t.push({name:"Visualizza Cavi",href:"/cavi",icon:u.A}),t.push({name:"Parco Cavi",href:"/parco-cavi",icon:x.A},{name:"Gestione Excel",href:"/excel",icon:v.A},{name:"Report",href:"/reports",icon:p.A},{name:"Gestione Comande",href:"/comande",icon:f.A},{name:"Produttivit\xe0",href:"/productivity",icon:b.A})),t}return[r]};function w(){let[e,t]=(0,r.useState)(!1),a=(0,l.usePathname)(),{user:i,cantiere:o,isAuthenticated:h,isImpersonating:x,impersonatedUser:v,logout:p}=(0,c.A)(),f=(null==o?void 0:o.id_cantiere)||parseInt(localStorage.getItem("selectedCantiereId")||"0"),b=(null==o?void 0:o.commessa)||localStorage.getItem("selectedCantiereName")||"Cantiere ".concat(f),w=A(null==i?void 0:i.ruolo,x,v,f);return"/login"!==a&&h?(0,s.jsxs)("nav",{className:"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm",children:[(0,s.jsx)("div",{className:"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"flex justify-between h-16",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 cursor-default",children:[(0,s.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center",children:(0,s.jsx)(u.A,{className:"w-5 h-5 text-white"})}),(0,s.jsxs)("div",{className:"hidden sm:block",children:[(0,s.jsx)("h1",{className:"text-xl font-bold text-slate-900",children:"CABLYS"}),(0,s.jsx)("p",{className:"text-xs text-slate-500 -mt-1",children:"Cable Installation System"})]})]}),(0,s.jsx)("div",{className:"hidden md:flex items-center space-x-1",children:w.map(e=>{let t=a===e.href||"/"!==e.href&&a.startsWith(e.href),r=e.icon;return(0,s.jsx)(n(),{href:e.href,children:(0,s.jsxs)("div",{className:"flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ".concat(t?"text-blue-700 bg-blue-50 border border-blue-200 font-medium":"text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 border border-transparent"),children:[(0,s.jsx)(r,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"hidden lg:inline",children:e.name})]})},e.name)})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4 ml-8",children:[f&&f>0&&(0,s.jsxs)("div",{className:"hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md",children:[(0,s.jsx)(m.A,{className:"w-3 h-3 text-blue-600"}),(0,s.jsx)("div",{className:"text-xs",children:(0,s.jsx)("span",{className:"text-blue-900 font-medium",children:b})})]}),(0,s.jsxs)("div",{className:"hidden sm:flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"text-right",children:(0,s.jsx)("p",{className:"text-sm font-medium text-slate-900",children:x&&v?v.username:null==i?void 0:i.username})}),(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:(0,s.jsx)(y.A,{className:"w-3 h-3 text-white"})}),(0,s.jsxs)(d.$,{variant:"ghost",size:"sm",className:"flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent text-red-600 hover:text-red-700 hover:bg-red-50 hover:border-red-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2",onClick:p,title:"Logout",children:[(0,s.jsx)(g,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"hidden lg:inline",children:"Logout"})]})]}),(0,s.jsx)("div",{className:"md:hidden",children:(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:()=>t(!e),className:"text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md",children:e?(0,s.jsx)(j.A,{className:"w-5 h-5"}):(0,s.jsx)(k,{className:"w-5 h-5"})})})]})]})}),e&&(0,s.jsxs)("div",{className:"md:hidden border-t border-slate-200 bg-white",children:[(0,s.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1",children:w.map(e=>{let r=a===e.href||"/"!==e.href&&a.startsWith(e.href),i=e.icon;return(0,s.jsx)(n(),{href:e.href,children:(0,s.jsxs)("div",{className:"w-full flex items-center justify-start space-x-3 px-3 py-2 transition-all duration-200 ease-in-out rounded-md ".concat(r?"text-blue-700 bg-blue-50 border border-blue-200 font-medium":"text-slate-600 hover:text-slate-900 hover:bg-blue-50 border border-transparent"),onClick:()=>t(!1),children:[(0,s.jsx)(i,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:e.name})]})},e.name)})}),(0,s.jsxs)("div",{className:"border-t border-slate-200 px-4 py-3",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center",children:i?(0,s.jsx)(y.A,{className:"w-3 h-3 text-white"}):(0,s.jsx)(m.A,{className:"w-3 h-3 text-white"})}),(0,s.jsx)("div",{children:(0,s.jsx)("p",{className:"text-sm font-medium text-slate-900",children:x&&v?v.username:i?i.username:null==o?void 0:o.commessa})})]}),(0,s.jsx)(d.$,{variant:"ghost",size:"sm",onClick:p,title:"Logout",className:"hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md",children:(0,s.jsx)(g,{className:"w-4 h-4"})})]}),(null==i?void 0:i.ruolo)==="owner"&&!x&&(0,s.jsxs)("div",{className:"mt-3 pt-3 border-t border-slate-200",children:[(0,s.jsx)("p",{className:"text-xs font-medium text-slate-500 mb-2",children:"AMMINISTRAZIONE"}),(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsx)(n(),{href:"/admin",className:"block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150",onClick:()=>t(!1),children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(N.A,{className:"w-4 h-4 text-slate-500"}),(0,s.jsx)("span",{children:"Pannello Admin"})]})}),(0,s.jsx)(n(),{href:"/admin?tab=users",className:"block px-3 py-2 text-sm text-slate-700 hover:bg-slate-100 rounded-md transition-colors duration-150",onClick:()=>t(!1),children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)(N.A,{className:"w-4 h-4 text-slate-500"}),(0,s.jsx)("span",{children:"Gestione Utenti"})]})})]})]})]})]})]}):null}}},e=>{var t=t=>e(e.s=t);e.O(0,[5822,7690,3455,3464,2513,283,8441,1684,7358],()=>t(17259)),_N_E=e.O()}]);