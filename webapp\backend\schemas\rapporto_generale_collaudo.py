from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import date, datetime

class RapportoGeneraleCollaudoBase(BaseModel):
    """Schema base per i rapporti generali di collaudo."""
    numero_rapporto: str = Field(..., description="Numero univoco del rapporto")
    data_rapporto: date = Field(..., description="Data del rapporto")
    
    # Dati Progetto/Commessa
    nome_progetto: Optional[str] = Field(None, description="Nome del progetto")
    codice_progetto: Optional[str] = Field(None, description="Codice del progetto")
    cliente_finale: Optional[str] = Field(None, description="Cliente finale")
    localita_impianto: Optional[str] = Field(None, description="Località dell'impianto")
    societa_installatrice: Optional[str] = Field(None, description="Società installatrice")
    societa_responsabile_prove: Optional[str] = Field(None, description="Società responsabile delle prove")
    data_inizio_collaudo: Optional[date] = Field(None, description="Data inizio collaudo")
    data_fine_collaudo: Optional[date] = Field(None, description="Data fine collaudo")
    
    # Riferimenti Normativi
    normative_applicate: Optional[str] = Field(None, description="Normative applicate (JSON)")
    documentazione_progetto: Optional[str] = Field(None, description="Documentazione di progetto (JSON)")
    
    # Scopo e Ambito
    scopo_rapporto: Optional[str] = Field(None, description="Scopo del rapporto")
    ambito_collaudo: Optional[str] = Field(None, description="Ambito del collaudo")
    
    # Condizioni Ambientali
    temperatura_ambiente: Optional[float] = Field(None, description="Temperatura ambiente (°C)")
    umidita_ambiente: Optional[float] = Field(None, description="Umidità ambiente (%)")
    
    # Personale
    responsabile_tecnico: Optional[str] = Field(None, description="Responsabile tecnico")
    rappresentante_cliente: Optional[str] = Field(None, description="Rappresentante del cliente")
    
    # Stato e Conclusioni
    stato_rapporto: Optional[str] = Field('BOZZA', description="Stato del rapporto")
    conclusioni: Optional[str] = Field(None, description="Conclusioni")
    dichiarazione_conformita: Optional[bool] = Field(False, description="Dichiarazione di conformità")

class RapportoGeneraleCollaudoCreate(RapportoGeneraleCollaudoBase):
    """Schema per la creazione di un nuovo rapporto generale."""
    pass

class RapportoGeneraleCollaudoUpdate(BaseModel):
    """Schema per l'aggiornamento di un rapporto generale."""
    numero_rapporto: Optional[str] = None
    data_rapporto: Optional[date] = None
    nome_progetto: Optional[str] = None
    codice_progetto: Optional[str] = None
    cliente_finale: Optional[str] = None
    localita_impianto: Optional[str] = None
    societa_installatrice: Optional[str] = None
    societa_responsabile_prove: Optional[str] = None
    data_inizio_collaudo: Optional[date] = None
    data_fine_collaudo: Optional[date] = None
    normative_applicate: Optional[str] = None
    documentazione_progetto: Optional[str] = None
    scopo_rapporto: Optional[str] = None
    ambito_collaudo: Optional[str] = None
    temperatura_ambiente: Optional[float] = None
    umidita_ambiente: Optional[float] = None
    responsabile_tecnico: Optional[str] = None
    rappresentante_cliente: Optional[str] = None
    stato_rapporto: Optional[str] = None
    conclusioni: Optional[str] = None
    dichiarazione_conformita: Optional[bool] = None

class RapportoGeneraleCollaudoInDB(RapportoGeneraleCollaudoBase):
    """Schema per il rapporto generale nel database."""
    id_rapporto: int
    id_cantiere: int
    numero_cavi_totali: int = 0
    numero_cavi_conformi: int = 0
    numero_cavi_non_conformi: int = 0
    timestamp_creazione: datetime
    timestamp_modifica: Optional[datetime] = None

    class Config:
        from_attributes = True

class RapportoGeneraleCollaudoResponse(RapportoGeneraleCollaudoInDB):
    """Schema per la risposta API del rapporto generale."""
    pass

class RapportoGeneraleCollaudoListResponse(BaseModel):
    """Schema per la lista dei rapporti generali."""
    id_rapporto: int
    numero_rapporto: str
    data_rapporto: date
    nome_progetto: Optional[str] = None
    stato_rapporto: str
    numero_cavi_totali: int = 0
    numero_cavi_conformi: int = 0
    numero_cavi_non_conformi: int = 0
    timestamp_creazione: datetime

    class Config:
        from_attributes = True
