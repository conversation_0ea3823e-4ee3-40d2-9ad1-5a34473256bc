import os
from dotenv import load_dotenv
from pathlib import Path

# Carica le variabili d'ambiente dal file .env se presente
load_dotenv()

class Settings:
    # Configurazione dell'applicazione
    APP_NAME = "CMS API"
    API_PREFIX = "/api"

    # Configurazione dei file statici
    BASE_DIR = Path(__file__).resolve().parent.parent  # webapp directory
    STATIC_DIR = BASE_DIR / "static"  # webapp/static directory
    STATIC_URL = "/static"

    # Configurazione del database PostgreSQL
    DB_HOST = os.getenv("DB_HOST", "localhost")
    DB_PORT = os.getenv("DB_PORT", "5432")
    DB_NAME = os.getenv("DB_NAME", "cantieri")
    DB_USER = os.getenv("DB_USER", "postgres")
    DB_PASSWORD = os.getenv("DB_PASSWORD", "Taranto")

    # Stringa di connessione per SQLAlchemy
    DATABASE_URL = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}"
    print(f"Stringa di connessione al database: {DATABASE_URL}")

    # Configurazione JWT
    SECRET_KEY = os.getenv("SECRET_KEY", "chiave_segreta_temporanea_da_cambiare_in_produzione")
    ALGORITHM = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES = 1440  # 24 ore

    # Configurazione CORS
    CORS_ORIGINS = [
        "http://localhost:3000",  # Frontend React in sviluppo
        "http://localhost:3001",  # Frontend React in sviluppo (porta alternativa)
        "http://localhost:8080",  # Possibile altro frontend
        "http://localhost",       # Frontend senza porta specificata
        "http://localhost:5500", # Live Server di VS Code
        "http://127.0.0.1:5500", # Live Server di VS Code (IP alternativo)
        "*",                     # Qualsiasi origine (solo per sviluppo)
    ]

settings = Settings()
