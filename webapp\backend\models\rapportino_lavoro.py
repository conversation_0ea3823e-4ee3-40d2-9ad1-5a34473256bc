from sqlalchemy import Column, Integer, String, Date, DateTime, ForeignKey, Text, Float, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from backend.database import Base


class RapportinoLavoro(Base):
    """
    Modello SQLAlchemy per la tabella rapportini_lavoro.
    Gestisce i rapportini di lavoro inviati dalla mobile app con sistema di revisione.
    """
    __tablename__ = "rapportini_lavoro"

    id_rapportino = Column(Integer, primary_key=True, index=True)
    codice_comanda = Column(String, ForeignKey("comande.codice_comanda"), nullable=False)
    id_cavo = Column(String, nullable=False)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)
    
    # Dati del lavoro svolto
    attivita_svolta = Column(String, nullable=False)  # POSATO, COLLEGATO, TESTATO
    bobina_utilizzata = Column(String, nullable=True)
    metri_posati = Column(Float, nullable=True)
    ore_lavoro = Column(Float, nullable=True)
    numero_componenti_squadra = Column(Integer, nullable=True)
    
    # Note e problemi
    note_lavoro = Column(Text, nullable=True)
    problemi_riscontrati = Column(Text, nullable=True)
    
    # Dati temporali
    data_lavoro = Column(Date, nullable=False)
    data_inserimento = Column(DateTime, nullable=False, default=func.now())
    
    # Sistema di revisione
    stato_revisione = Column(String, nullable=False, default="IN_ATTESA")  # IN_ATTESA, APPROVATO, RIFIUTATO
    data_revisione = Column(DateTime, nullable=True)
    note_revisione = Column(Text, nullable=True)
    id_utente_revisore = Column(Integer, ForeignKey("utenti.id_utente"), nullable=True)
    
    # Dati corretti dal revisore (se necessario)
    metri_posati_corretti = Column(Float, nullable=True)
    bobina_utilizzata_corretta = Column(String, nullable=True)
    
    # Responsabile che ha inserito i dati
    responsabile = Column(String, nullable=False)
    
    # Flag per indicare se i dati sono stati applicati al sistema
    dati_applicati = Column(Boolean, default=False)
    data_applicazione = Column(DateTime, nullable=True)

    # Relazioni
    comanda = relationship("Comanda", backref="rapportini_lavoro")
    cantiere = relationship("Cantiere", backref="rapportini_lavoro")
    utente_revisore = relationship("User", backref="rapportini_revisionati")
