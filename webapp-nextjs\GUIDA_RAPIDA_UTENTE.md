# 📖 Guida Rapida Utente - CABLYS Next.js

## 🚀 Avvio del Sistema

### **<PERSON>od<PERSON>o (Raccomandato)**
```cmd
# Windows - Do<PERSON><PERSON> click o da terminale
START_CABLYS.bat

# Linux/macOS
./run_system.sh
```

### **Accesso al Sistema**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8001

## 🔐 Tipi di Login

### **1. 👑 Amministratore**
- **Quando usare**: Gestione completa del sistema
- **Credenziali**: Username/Password admin
- **Accesso a**: Tutti i moduli + gestione utenti

### **2. 👤 Utente Standard**
- **Quando usare**: Gestione dei propri cantieri
- **Credenziali**: Username/Password utente
- **Accesso a**: Cantieri, cavi, comande (solo propri)

### **3. 🏗️ Utente Cantiere**
- **Quando usare**: Lavoro sul campo per cantiere specifico
- **Credenziali**: Codice univoco + Password cantiere
- **Accesso a**: Solo cavi del cantiere specifico

## 🏗️ Gestione Cantieri

### **Creazione Nuovo Cantiere**
1. Login come Admin o Utente Standard
2. Vai su "Cantieri" nel menu
3. Click "Nuovo Cantiere"
4. Compila i campi obbligatori:
   - **Commessa**: Codice identificativo
   - **Descrizione**: Descrizione del progetto
   - **Cliente**: Nome del cliente
5. Click "Crea Cantiere"

### **Gestione Password Cantiere**
1. Nella lista cantieri, click sull'icona **👁️** nella colonna "Password Cantiere"
2. Per modificare: Click "Modifica Password" nel dialog di modifica cantiere
3. Inserisci nuova password e conferma

### **Ricerca Cantieri**
- Usa la barra di ricerca in alto
- Cerca per: commessa, descrizione, cliente
- La ricerca è in tempo reale

## 🔌 Gestione Cavi

### **Visualizzazione Cavi**
- **Da cantiere**: Click "Accedi" nella lista cantieri
- **Login diretto**: Usa codice univoco + password cantiere

### **Stati Cavi**
- 🔴 **Da Installare**: Cavo non ancora posato
- 🟡 **Installato**: Cavo posato ma non collegato
- 🔵 **Collegato**: Cavo collegato ma non certificato
- 🟢 **Certificato**: Cavo completamente terminato
- ⚪ **Spare**: Cavo di riserva

### **Azioni Rapide (Tasto Destro)**
- Click destro su un cavo per menu contestuale
- Azioni disponibili: Modifica, Elimina, Cambia Stato

## 📊 Monitoraggio Progresso

### **Colonna Avanzamento**
- Mostra percentuale di completamento cantiere
- Basata su calcolo **IAP** (Indice di Avanzamento Ponderato)
- Colori progressivi:
  - 🔴 0-30%: Rosso (inizio progetto)
  - 🟡 31-70%: Giallo (in corso)
  - 🟢 71-100%: Verde (quasi completo/completo)

### **Calcolo IAP**
- **Posa**: Peso 2.0
- **Collegamento**: Peso 1.5
- **Certificazione**: Peso 0.5
- Aggiornamento automatico ad ogni modifica

## 🎨 Interfaccia Utente

### **Colori e Stati**
Il sistema usa colori morbidi e professionali:
- **Verde smeraldo**: Stati positivi/completati
- **Giallo ambra**: Attenzione/in corso
- **Rosa morbido**: Errori (non aggressivi)
- **Blu cielo**: Informazioni
- **Grigio ardesia**: Stati neutri

### **Menu Contestuali**
- **Tasto destro** su elementi per azioni rapide
- Icone intuitive per ogni azione
- Menu si adatta ai bordi dello schermo

### **Badge di Stato**
- Ogni stato ha icona e colore specifico
- Leggibilità ottimizzata per accessibilità
- Coerenza visiva in tutto il sistema

## 🔒 Sicurezza e Password

### **Cambio Password**
1. Login nel sistema
2. Menu utente → "Cambia Password"
3. Inserisci password attuale e nuova
4. Conferma modifiche

### **Visualizzazione Password Cantieri**
- **Utenti Standard**: Icona 👁️ nella lista cantieri
- **Amministratori**: Accesso completo a tutte le password
- Visualizzazione sicura con conferma

### **Scadenza Account**
- Notifiche automatiche 5 giorni prima della scadenza
- Alert visibili nel dashboard
- Solo per amministratori: gestione scadenze

## 📱 Accessibilità e Mobile

### **Design Responsive**
- Interfaccia ottimizzata per mobile
- Touch-friendly su tablet e smartphone
- Menu adattivi per schermi piccoli

### **PWA (Progressive Web App)**
- Installabile come app nativa
- Funziona offline (funzionalità limitate)
- Icona sul desktop/home screen

### **Accessibilità**
- Conformità WCAG per contrasto colori
- Supporto navigazione da tastiera
- Compatibilità screen reader

## 🆘 Risoluzione Problemi

### **Problemi di Login**
- Verifica credenziali (maiuscole/minuscole)
- Per cantieri: controlla codice univoco e password
- Contatta amministratore se account scaduto

### **Problemi di Connessione**
- Verifica che backend sia avviato (porta 8001)
- Riavvia il sistema con `START_CABLYS.bat`
- Controlla connessione internet

### **Problemi di Performance**
- Chiudi tab browser non necessarie
- Riavvia browser se lento
- Verifica risorse sistema (RAM/CPU)

### **Errori di Salvataggio**
- Verifica connessione database
- Controlla permessi utente
- Riprova dopo alcuni secondi

## 📞 Supporto

### **Documentazione Completa**
- `README.md`: Panoramica generale
- `AGGIORNAMENTI_SISTEMA.md`: Ultime novità
- `RUN_SYSTEM_README.md`: Dettagli tecnici

### **Log e Debug**
- Log sistema in console browser (F12)
- File log backend in directory webapp
- Usa `monitor_system.py` per diagnostica

### **Contatti**
- Amministratore sistema per problemi account
- Supporto tecnico per problemi funzionali
- Documentazione online per guide dettagliate

---

**💡 Suggerimento**: Tieni sempre aperta questa guida durante i primi utilizzi del sistema!
