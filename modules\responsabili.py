"""
Modulo per la gestione dei responsabili delle comande.
Gestisce l'auto-inserimento e la ricerca dei responsabili.
"""

import logging
from typing import Optional, Dict, Any
from datetime import datetime
from modules.database_pg import Database

def trova_responsabile_per_contatto(id_cantiere: int, email: Optional[str] = None, telefono: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Trova un responsabile esistente per email o telefono in un cantiere specifico.

    Args:
        id_cantiere: ID del cantiere
        email: Email del responsabile
        telefono: Telefono del responsabile

    Returns:
        Dict con i dati del responsabile se trovato, None altrimenti
    """
    if not email and not telefono:
        return None
        
    try:
        db = Database()
        with db.get_connection() as conn:
            c = conn.cursor()
            
            # Cerca per email se fornita
            if email:
                c.execute("""
                    SELECT id_responsabile, nome_responsabile, telefono, email, id_cantiere, data_creazione, attivo
                    FROM Responsabili
                    WHERE email = %s AND id_cantiere = %s AND attivo = TRUE
                """, (email, id_cantiere))
                result = c.fetchone()
                if result:
                    return {
                        'id_responsabile': result[0],
                        'nome_responsabile': result[1],
                        'telefono': result[2],
                        'email': result[3],
                        'id_cantiere': result[4],
                        'data_creazione': result[5],
                        'attivo': result[6]
                    }

            # Cerca per telefono se fornito e non trovato per email
            if telefono:
                c.execute("""
                    SELECT id_responsabile, nome_responsabile, telefono, email, id_cantiere, data_creazione, attivo
                    FROM Responsabili
                    WHERE telefono = %s AND id_cantiere = %s AND attivo = TRUE
                """, (telefono, id_cantiere))
                result = c.fetchone()
                if result:
                    return {
                        'id_responsabile': result[0],
                        'nome_responsabile': result[1],
                        'telefono': result[2],
                        'email': result[3],
                        'id_cantiere': result[4],
                        'data_creazione': result[5],
                        'attivo': result[6]
                    }
                    
            return None
            
    except Exception as e:
        logging.error(f"❌ Errore nella ricerca responsabile: {str(e)}")
        return None

def crea_responsabile(nome_responsabile: str, id_cantiere: int, email: Optional[str] = None, telefono: Optional[str] = None) -> Optional[int]:
    """
    Crea un nuovo responsabile nel database per un cantiere specifico.

    Args:
        nome_responsabile: Nome del responsabile
        id_cantiere: ID del cantiere
        email: Email del responsabile (opzionale)
        telefono: Telefono del responsabile (opzionale)

    Returns:
        ID del responsabile creato o None in caso di errore
    """
    if not email and not telefono:
        logging.error("❌ Almeno uno tra email e telefono deve essere specificato")
        return None
        
    try:
        db = Database()
        with db.get_connection() as conn:
            c = conn.cursor()

            # Inserisci il nuovo responsabile
            c.execute("""
                INSERT INTO Responsabili (nome_responsabile, email, telefono, id_cantiere, data_creazione, attivo)
                VALUES (%s, %s, %s, %s, %s, %s)
                RETURNING id_responsabile
            """, (nome_responsabile, email, telefono, id_cantiere, datetime.now(), True))
            
            result = c.fetchone()
            if result:
                id_responsabile = result[0]
                conn.commit()
                logging.info(f"✅ Responsabile {nome_responsabile} creato con ID {id_responsabile}")
                return id_responsabile
            else:
                logging.error("❌ Errore nella creazione del responsabile")
                return None
                
    except Exception as e:
        logging.error(f"❌ Errore nella creazione responsabile: {str(e)}")
        return None

def ottieni_o_crea_responsabile(nome_responsabile: str, id_cantiere: int, email: Optional[str] = None, telefono: Optional[str] = None) -> Optional[Dict[str, Any]]:
    """
    Ottiene un responsabile esistente o ne crea uno nuovo se non esiste.
    Questa è la funzione principale per l'auto-inserimento per un cantiere specifico.

    Args:
        nome_responsabile: Nome del responsabile
        id_cantiere: ID del cantiere
        email: Email del responsabile (opzionale)
        telefono: Telefono del responsabile (opzionale)

    Returns:
        Dict con i dati del responsabile (esistente o nuovo creato)
    """
    # Prima cerca se esiste già
    responsabile_esistente = trova_responsabile_per_contatto(id_cantiere, email, telefono)

    if responsabile_esistente:
        logging.info(f"✅ Responsabile esistente trovato: {responsabile_esistente['nome_responsabile']}")
        return responsabile_esistente

    # Se non esiste, creane uno nuovo
    logging.info(f"📝 Creazione nuovo responsabile: {nome_responsabile}")
    id_responsabile = crea_responsabile(nome_responsabile, id_cantiere, email, telefono)

    if id_responsabile:
        # Recupera i dati del responsabile appena creato
        return {
            'id_responsabile': id_responsabile,
            'nome_responsabile': nome_responsabile,
            'telefono': telefono,
            'email': email,
            'id_cantiere': id_cantiere,
            'data_creazione': datetime.now(),
            'attivo': True
        }
    
    return None

def lista_responsabili(id_cantiere: Optional[int] = None, attivi_solo: bool = True) -> list:
    """
    Ottiene la lista di tutti i responsabili, opzionalmente filtrati per cantiere.

    Args:
        id_cantiere: ID del cantiere per filtrare (opzionale)
        attivi_solo: Se True, restituisce solo i responsabili attivi

    Returns:
        Lista dei responsabili
    """
    try:
        db = Database()
        with db.get_connection() as conn:
            c = conn.cursor()

            query = """
                SELECT id_responsabile, nome_responsabile, telefono, email, id_cantiere, data_creazione, attivo
                FROM Responsabili
            """

            conditions = []
            params = []

            if id_cantiere is not None:
                conditions.append("id_cantiere = %s")
                params.append(id_cantiere)

            if attivi_solo:
                conditions.append("attivo = TRUE")

            if conditions:
                query += " WHERE " + " AND ".join(conditions)

            query += " ORDER BY nome_responsabile"

            c.execute(query, params)
            results = c.fetchall()
            
            responsabili = []
            for result in results:
                responsabili.append({
                    'id_responsabile': result[0],
                    'nome_responsabile': result[1],
                    'telefono': result[2],
                    'email': result[3],
                    'id_cantiere': result[4],
                    'data_creazione': result[5],
                    'attivo': result[6]
                })
                
            return responsabili
            
    except Exception as e:
        logging.error(f"❌ Errore nel recupero lista responsabili: {str(e)}")
        return []

def aggiorna_responsabile(id_responsabile: int, nome_responsabile: Optional[str] = None, 
                         email: Optional[str] = None, telefono: Optional[str] = None, 
                         attivo: Optional[bool] = None) -> bool:
    """
    Aggiorna i dati di un responsabile esistente.
    
    Args:
        id_responsabile: ID del responsabile da aggiornare
        nome_responsabile: Nuovo nome (opzionale)
        email: Nuova email (opzionale)
        telefono: Nuovo telefono (opzionale)
        attivo: Nuovo stato attivo (opzionale)
        
    Returns:
        True se l'aggiornamento è riuscito, False altrimenti
    """
    try:
        db = Database()
        with db.get_connection() as conn:
            c = conn.cursor()

            # Costruisci la query di aggiornamento dinamicamente
            updates = []
            params = []
            
            if nome_responsabile is not None:
                updates.append("nome_responsabile = %s")
                params.append(nome_responsabile)
                
            if email is not None:
                updates.append("email = %s")
                params.append(email)
                
            if telefono is not None:
                updates.append("telefono = %s")
                params.append(telefono)
                
            if attivo is not None:
                updates.append("attivo = %s")
                params.append(attivo)
                
            if not updates:
                logging.warning("⚠️ Nessun campo da aggiornare specificato")
                return False
                
            params.append(id_responsabile)
            
            query = f"""
                UPDATE Responsabili 
                SET {', '.join(updates)}
                WHERE id_responsabile = %s
            """
            
            c.execute(query, params)
            rows_affected = c.rowcount
            conn.commit()
            
            if rows_affected > 0:
                logging.info(f"✅ Responsabile {id_responsabile} aggiornato con successo")
                return True
            else:
                logging.warning(f"⚠️ Nessun responsabile trovato con ID {id_responsabile}")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nell'aggiornamento responsabile: {str(e)}")
        return False
