import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Estrai il token di autorizzazione dall'header
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { 
          success: false, 
          detail: 'Token di autorizzazione mancante' 
        }, 
        { status: 401 }
      )
    }

    // Proxy la richiesta al backend FastAPI
    const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'
    
    const response = await fetch(`${backendUrl}/api/password/change-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader
      },
      body: JSON.stringify(body)
    })

    const data = await response.json()

    return NextResponse.json(data, { 
      status: response.status,
      headers: {
        'Content-Type': 'application/json'
      }
    })

  } catch (error) {
    console.error('Password change error:', error)
    return NextResponse.json(
      { 
        success: false, 
        detail: 'Errore interno del server' 
      }, 
      { status: 500 }
    )
  }
}
