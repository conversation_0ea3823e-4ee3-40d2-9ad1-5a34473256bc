"""
Sistema di Audit Logging per Sicurezza
Registra tutte le operazioni di sicurezza per compliance e debugging.
"""

import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional
from enum import Enum
import os
from dataclasses import dataclass, asdict

class SecurityEventType(Enum):
    """Tipi di eventi di sicurezza."""
    LOGIN_SUCCESS = "login_success"
    LOGIN_FAILED = "login_failed"
    PASSWORD_CHANGE_SUCCESS = "password_change_success"
    PASSWORD_CHANGE_FAILED = "password_change_failed"
    PASSWORD_RESET_REQUEST = "password_reset_request"
    PASSWORD_RESET_SUCCESS = "password_reset_success"
    PASSWORD_RESET_FAILED = "password_reset_failed"
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INVALID_TOKEN = "invalid_token"
    ACCOUNT_LOCKED = "account_locked"
    SUSPICIOUS_ACTIVITY = "suspicious_activity"

@dataclass
class SecurityEvent:
    """Evento di sicurezza da loggare."""
    event_type: SecurityEventType
    user_id: Optional[int] = None
    username: Optional[str] = None
    email: Optional[str] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    success: bool = True
    error_message: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow()

class SecurityAuditLogger:
    """Logger specializzato per eventi di sicurezza."""
    
    def __init__(self, log_file: str = None):
        self.log_file = log_file or os.getenv('SECURITY_LOG_FILE', 'logs/security.log')
        
        # Configura logger specifico per sicurezza
        self.logger = logging.getLogger('security_audit')
        self.logger.setLevel(logging.INFO)
        
        # Evita duplicazione se già configurato
        if not self.logger.handlers:
            # Handler per file
            file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
            file_handler.setLevel(logging.INFO)
            
            # Formato JSON per parsing automatico
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(formatter)
            
            self.logger.addHandler(file_handler)
            
            # Handler per console in development
            if os.getenv('ENVIRONMENT', 'development') == 'development':
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.INFO)
                console_handler.setFormatter(formatter)
                self.logger.addHandler(console_handler)
    
    def log_security_event(self, event: SecurityEvent):
        """
        Logga un evento di sicurezza.
        
        Args:
            event: Evento da loggare
        """
        try:
            # Converte l'evento in dizionario
            event_dict = asdict(event)
            
            # Converte enum e datetime in stringhe
            event_dict['event_type'] = event.event_type.value
            event_dict['timestamp'] = event.timestamp.isoformat()
            
            # Rimuove dati sensibili
            event_dict = self._sanitize_event_data(event_dict)
            
            # Logga come JSON
            log_message = json.dumps(event_dict, ensure_ascii=False, indent=None)
            
            if event.success:
                self.logger.info(log_message)
            else:
                self.logger.warning(log_message)
                
        except Exception as e:
            # Fallback logging se il sistema di audit fallisce
            self.logger.error(f"Failed to log security event: {str(e)}")
    
    def _sanitize_event_data(self, event_dict: Dict[str, Any]) -> Dict[str, Any]:
        """
        Rimuove o maschera dati sensibili dall'evento.
        
        Args:
            event_dict: Dizionario dell'evento
            
        Returns:
            Dict: Evento sanitizzato
        """
        # Lista di campi da rimuovere completamente
        sensitive_fields = ['password', 'token', 'secret']
        
        # Lista di campi da mascherare parzialmente
        partial_mask_fields = ['email', 'ip_address']
        
        sanitized = event_dict.copy()
        
        # Rimuove campi sensibili
        for field in sensitive_fields:
            if field in sanitized:
                del sanitized[field]
        
        # Maschera campi parzialmente
        for field in partial_mask_fields:
            if field in sanitized and sanitized[field]:
                sanitized[field] = self._mask_sensitive_data(sanitized[field])
        
        # Sanitizza additional_data se presente
        if 'additional_data' in sanitized and sanitized['additional_data']:
            sanitized['additional_data'] = self._sanitize_additional_data(
                sanitized['additional_data']
            )
        
        return sanitized
    
    def _mask_sensitive_data(self, data: str) -> str:
        """
        Maschera parzialmente dati sensibili.
        
        Args:
            data: Dato da mascherare
            
        Returns:
            str: Dato mascherato
        """
        if '@' in data:  # Email
            parts = data.split('@')
            if len(parts) == 2:
                username = parts[0]
                domain = parts[1]
                masked_username = username[:2] + '*' * (len(username) - 2)
                return f"{masked_username}@{domain}"
        
        # IP address o altri dati
        if len(data) > 4:
            return data[:2] + '*' * (len(data) - 4) + data[-2:]
        
        return '*' * len(data)
    
    def _sanitize_additional_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitizza dati aggiuntivi.
        
        Args:
            data: Dati aggiuntivi
            
        Returns:
            Dict: Dati sanitizzati
        """
        sanitized = {}
        
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in ['password', 'token', 'secret']):
                sanitized[key] = '[REDACTED]'
            elif isinstance(value, str) and len(value) > 100:
                sanitized[key] = value[:100] + '...[TRUNCATED]'
            else:
                sanitized[key] = value
        
        return sanitized

# Funzioni di convenienza per logging eventi comuni
def log_login_attempt(username: str, success: bool, ip_address: str = None, 
                     error_message: str = None, user_id: int = None):
    """Logga tentativo di login."""
    event = SecurityEvent(
        event_type=SecurityEventType.LOGIN_SUCCESS if success else SecurityEventType.LOGIN_FAILED,
        user_id=user_id,
        username=username,
        ip_address=ip_address,
        success=success,
        error_message=error_message
    )
    audit_logger.log_security_event(event)

def log_password_change(user_id: int, username: str, success: bool, 
                       ip_address: str = None, error_message: str = None):
    """Logga cambio password."""
    event = SecurityEvent(
        event_type=SecurityEventType.PASSWORD_CHANGE_SUCCESS if success else SecurityEventType.PASSWORD_CHANGE_FAILED,
        user_id=user_id,
        username=username,
        ip_address=ip_address,
        success=success,
        error_message=error_message
    )
    audit_logger.log_security_event(event)

def log_password_reset_request(email: str, ip_address: str = None, 
                              success: bool = True, error_message: str = None):
    """Logga richiesta reset password."""
    event = SecurityEvent(
        event_type=SecurityEventType.PASSWORD_RESET_REQUEST,
        email=email,
        ip_address=ip_address,
        success=success,
        error_message=error_message
    )
    audit_logger.log_security_event(event)

def log_password_reset_completion(user_id: int, email: str, success: bool,
                                 ip_address: str = None, error_message: str = None):
    """Logga completamento reset password."""
    event = SecurityEvent(
        event_type=SecurityEventType.PASSWORD_RESET_SUCCESS if success else SecurityEventType.PASSWORD_RESET_FAILED,
        user_id=user_id,
        email=email,
        ip_address=ip_address,
        success=success,
        error_message=error_message
    )
    audit_logger.log_security_event(event)

def log_rate_limit_exceeded(identifier: str, ip_address: str = None):
    """Logga superamento rate limit."""
    event = SecurityEvent(
        event_type=SecurityEventType.RATE_LIMIT_EXCEEDED,
        ip_address=ip_address,
        success=False,
        additional_data={'identifier': identifier}
    )
    audit_logger.log_security_event(event)

def log_suspicious_activity(description: str, user_id: int = None, 
                           ip_address: str = None, additional_data: Dict = None):
    """Logga attività sospetta."""
    event = SecurityEvent(
        event_type=SecurityEventType.SUSPICIOUS_ACTIVITY,
        user_id=user_id,
        ip_address=ip_address,
        success=False,
        error_message=description,
        additional_data=additional_data
    )
    audit_logger.log_security_event(event)

# Istanza globale
audit_logger = SecurityAuditLogger()
