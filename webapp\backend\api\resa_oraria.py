"""
API endpoints per il sistema di calcolo resa oraria.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field
from datetime import datetime, date

from ..core.security import get_current_active_user
from ..database import get_db
from ..models.user import User

# Import delle funzioni del modulo resa oraria
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from modules.resa_oraria import (
    calcola_resa_comanda, calcola_statistiche_resa_cantiere, 
    genera_report_resa_oraria, registra_tempo_lavoro_cavo
)

router = APIRouter()

# Schemi Pydantic per le richieste e risposte

class RegistraTempoLavoroRequest(BaseModel):
    """Schema per registrare tempo di lavoro per un cavo."""
    id_cavo: str = Field(..., description="ID del cavo")
    tipo_attivita: str = Field(..., description="Tipo attività: POSA, COLLEGAMENTO_PARTENZA, COLLEGAMENTO_ARRIVO, CERTIFICAZIONE")
    ore_lavoro: float = Field(..., gt=0, description="Ore di lavoro effettuate")
    numero_persone: int = Field(..., gt=0, description="Numero di persone coinvolte")
    data_lavoro: Optional[datetime] = Field(None, description="Data e ora del lavoro (default: ora corrente)")

class ResaComandaResponse(BaseModel):
    """Schema di risposta per la resa di una comanda."""
    tipo_attivita: str
    codice_comanda: str
    responsabile: Optional[str] = None
    totale_cavi: int
    totale_ore: float
    resa_media: float
    dettagli_specifici: Dict[str, Any]
    raccomandazioni: Optional[List[str]] = None

class StatisticheResaCantiereResponse(BaseModel):
    """Schema di risposta per le statistiche resa cantiere."""
    id_cantiere: int
    periodo_analisi: str
    data_analisi: str
    statistiche_per_tipo: Dict[str, Any]
    riepilogo_generale: Dict[str, Any]
    raccomandazioni: Optional[List[str]] = None

@router.post("/cavi/{id_cantiere}/{id_cavo}/registra-tempo")
async def registra_tempo_lavoro_endpoint(
    id_cantiere: int,
    id_cavo: str,
    request: RegistraTempoLavoroRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Registra il tempo di lavoro per un cavo specifico.
    """
    try:
        success = registra_tempo_lavoro_cavo(
            id_cavo=id_cavo,
            id_cantiere=id_cantiere,
            tipo_attivita=request.tipo_attivita,
            ore_lavoro=request.ore_lavoro,
            numero_persone=request.numero_persone,
            data_lavoro=request.data_lavoro
        )
        
        if success:
            return {
                "message": f"Tempo di lavoro registrato con successo per cavo {id_cavo}",
                "id_cavo": id_cavo,
                "tipo_attivita": request.tipo_attivita,
                "ore_lavoro": request.ore_lavoro,
                "numero_persone": request.numero_persone
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nella registrazione del tempo di lavoro"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore interno: {str(e)}"
        )

@router.get("/comande/{codice_comanda}/resa", response_model=ResaComandaResponse)
async def ottieni_resa_comanda(
    codice_comanda: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Calcola e restituisce la resa oraria di una comanda specifica.
    """
    try:
        resa_data = calcola_resa_comanda(codice_comanda)
        
        if not resa_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Comanda {codice_comanda} non trovata o senza dati di resa"
            )
        
        # Adatta i dati al formato di risposta
        tipo_attivita = resa_data.get('tipo_attivita', '')
        
        if tipo_attivita == 'POSA':
            resa_media = resa_data.get('resa_media_metri_ora', 0)
            dettagli_specifici = {
                'totale_metri': resa_data.get('totale_metri', 0),
                'resa_media_metri_ora': resa_media,
                'resa_media_per_persona': resa_data.get('resa_media_per_persona', 0),
                'numero_medio_persone': resa_data.get('numero_medio_persone', 0)
            }
        elif tipo_attivita in ['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO']:
            resa_media = resa_data.get('resa_media_collegamenti_ora', 0)
            dettagli_specifici = {
                'totale_collegamenti': resa_data.get('totale_collegamenti', 0),
                'resa_per_squadra': resa_data.get('resa_per_squadra', 0),
                'numero_componenti_squadra': resa_data.get('numero_componenti_squadra', 1)
            }
        elif tipo_attivita == 'CERTIFICAZIONE':
            resa_media = resa_data.get('resa_media_test_ora', 0)
            dettagli_specifici = {
                'totale_test': resa_data.get('totale_test', 0),
                'resa_per_squadra': resa_data.get('resa_per_squadra', 0),
                'cavi_conformi': resa_data.get('cavi_conformi', 0),
                'cavi_non_conformi': resa_data.get('cavi_non_conformi', 0),
                'percentuale_conformita': resa_data.get('percentuale_conformita', 0)
            }
        else:
            resa_media = 0
            dettagli_specifici = {}
        
        # Aggiungi dettagli cavi se presenti
        if 'dettagli_cavi' in resa_data:
            dettagli_specifici['dettagli_cavi'] = resa_data['dettagli_cavi']
        
        return ResaComandaResponse(
            tipo_attivita=tipo_attivita,
            codice_comanda=codice_comanda,
            responsabile=resa_data.get('responsabile'),
            totale_cavi=resa_data.get('totale_cavi', 0),
            totale_ore=resa_data.get('totale_ore', 0),
            resa_media=resa_media,
            dettagli_specifici=dettagli_specifici
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel calcolo della resa: {str(e)}"
        )

@router.get("/cantieri/{id_cantiere}/statistiche-resa", response_model=StatisticheResaCantiereResponse)
async def ottieni_statistiche_resa_cantiere(
    id_cantiere: int,
    tipo_attivita: Optional[str] = Query(None, description="Filtra per tipo attività"),
    giorni_analisi: int = Query(30, ge=1, le=365, description="Giorni da analizzare"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Calcola e restituisce le statistiche di resa oraria per un cantiere.
    """
    try:
        statistiche = calcola_statistiche_resa_cantiere(
            id_cantiere=id_cantiere,
            tipo_attivita=tipo_attivita,
            giorni_analisi=giorni_analisi
        )
        
        if not statistiche:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Nessuna statistica trovata per cantiere {id_cantiere}"
            )
        
        return StatisticheResaCantiereResponse(
            id_cantiere=statistiche['id_cantiere'],
            periodo_analisi=statistiche['periodo_analisi'],
            data_analisi=statistiche['data_analisi'],
            statistiche_per_tipo=statistiche['statistiche_per_tipo'],
            riepilogo_generale=statistiche['riepilogo_generale']
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel calcolo delle statistiche: {str(e)}"
        )

@router.get("/cantieri/{id_cantiere}/report-resa")
async def genera_report_resa_cantiere(
    id_cantiere: int,
    codice_comanda: Optional[str] = Query(None, description="Comanda specifica (opzionale)"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Genera un report completo della resa oraria per cantiere o comanda specifica.
    """
    try:
        report = genera_report_resa_oraria(
            id_cantiere=id_cantiere,
            codice_comanda=codice_comanda
        )
        
        if 'errore' in report:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=report['errore']
            )
        
        return report
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella generazione del report: {str(e)}"
        )

@router.get("/cantieri/{id_cantiere}/benchmark")
async def ottieni_benchmark_resa(
    id_cantiere: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene benchmark e confronti di resa per il cantiere.
    """
    try:
        # Calcola statistiche per diversi periodi
        statistiche_30_giorni = calcola_statistiche_resa_cantiere(id_cantiere, giorni_analisi=30)
        statistiche_90_giorni = calcola_statistiche_resa_cantiere(id_cantiere, giorni_analisi=90)
        
        benchmark = {
            'id_cantiere': id_cantiere,
            'data_analisi': datetime.now().isoformat(),
            'confronto_temporale': {
                'ultimi_30_giorni': statistiche_30_giorni.get('riepilogo_generale', {}),
                'ultimi_90_giorni': statistiche_90_giorni.get('riepilogo_generale', {})
            },
            'trend_analisi': _calcola_trend_resa(statistiche_30_giorni, statistiche_90_giorni),
            'obiettivi_suggeriti': _genera_obiettivi_resa()
        }
        
        return benchmark
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel calcolo dei benchmark: {str(e)}"
        )

def _calcola_trend_resa(stats_30: Dict, stats_90: Dict) -> Dict[str, str]:
    """Calcola il trend della resa confrontando due periodi."""
    trend = {}
    
    # Confronta resa posa se disponibile
    posa_30 = stats_30.get('statistiche_per_tipo', {}).get('POSA', {})
    posa_90 = stats_90.get('statistiche_per_tipo', {}).get('POSA', {})
    
    if posa_30 and posa_90:
        resa_30 = posa_30.get('resa_media_metri_ora', 0)
        resa_90 = posa_90.get('resa_media_metri_ora', 0)
        
        if resa_30 > resa_90 * 1.1:
            trend['posa'] = 'miglioramento'
        elif resa_30 < resa_90 * 0.9:
            trend['posa'] = 'peggioramento'
        else:
            trend['posa'] = 'stabile'
    
    return trend

def _genera_obiettivi_resa() -> Dict[str, float]:
    """Genera obiettivi di resa suggeriti."""
    return {
        'posa_metri_ora_obiettivo': 80.0,
        'collegamenti_ora_obiettivo': 1.5,
        'test_ora_obiettivo': 1.0,
        'conformita_percentuale_obiettivo': 95.0
    }
