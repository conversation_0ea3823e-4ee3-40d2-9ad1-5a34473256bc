# Sistema Meteorologico - Autocompilazione Temperatura e Umidità

## Panoramica

Implementazione completa del sistema meteorologico per il recupero automatico di temperatura e umidità durante la certificazione dei cavi, basato sulla localizzazione geografica del cantiere.

## Funzionalità Implementate

### 🌤️ **Recupero Automatico Dati Meteorologici**
- **API Integration**: OpenWeatherMap API per dati meteorologici reali
- **Geocoding**: Conversione automatica città/nazione → coordinate GPS
- **Cache intelligente**: 30 minuti di cache per evitare chiamate eccessive
- **Fallback robusto**: Dati demo in caso di errore API

### 🔄 **Autocompilazione Form Certificazione**
- **Eliminazione campi manuali**: Rimossi campi temperatura e umidità dai form
- **Caricamento automatico**: Dati meteorologici caricati all'apertura del dialog
- **Nota informativa**: Sezione dedicata con stato dei dati meteorologici
- **Aggiornamento manuale**: Pulsante per ricaricare i dati

### 📍 **Integrazione con Localizzazione Cantieri**
- **Utilizzo dati cantiere**: Città e nazione del cantiere per geolocalizzazione
- **Supporto internazionale**: Gestione cantieri in tutto il mondo
- **Mapping paesi**: Conversione automatica nomi nazioni → codici ISO

## Architettura Implementata

### Backend Services

#### `weather_service.py`
```python
class WeatherService:
    - get_weather_for_cantiere(db, cantiere_id)
    - get_weather_by_city(city, country)
    - get_coordinates(city, country)
    - get_demo_weather_data()
```

#### API Endpoint
```python
GET /cantieri/{cantiere_id}/weather
- Recupera dati meteorologici per cantiere specifico
- Gestione errori con fallback automatico
- Autorizzazione utente integrata
```

### Frontend Services

#### `weatherService.js`
```javascript
const weatherService = {
    getWeatherForCantiere(cantiereId)
    formatWeatherData(weatherData)
    getFormattedWeatherForCantiere(cantiereId)
}
```

### Componenti Aggiornati

#### `CertificazioneCaviImproved.js`
- ✅ **Autocaricamento**: Dati meteorologici caricati automaticamente
- ✅ **UI migliorata**: Sezione dedicata con indicatori di stato
- ✅ **Integrazione**: Dati inclusi automaticamente nella certificazione

#### `CertificazioneForm.jsx`
- ✅ **Eliminazione campi**: Rimossi input manuali temperatura/umidità
- ✅ **Nota informativa**: Sezione con stato dati meteorologici
- ✅ **Autocompilazione**: Dati inclusi automaticamente nel submit

## Configurazione API

### OpenWeatherMap Setup
```bash
# Variabile d'ambiente richiesta
export OPENWEATHER_API_KEY="your_api_key_here"

# API gratuita: 1000 chiamate/giorno
# Registrazione: https://openweathermap.org/api
```

### Fallback Demo
```javascript
// Dati demo utilizzati quando API non disponibile
{
    temperature: 22.5,
    humidity: 65,
    pressure: 1013,
    description: 'Dati demo',
    is_demo: true
}
```

## Interfaccia Utente

### Sezione Condizioni Ambientali
```jsx
📦 Paper con colori dinamici:
   🟢 Verde: Dati reali recuperati con successo
   🟡 Giallo: Dati demo (API non disponibile)

🌤️ Icona meteo + Testo informativo
✅/⚠️/❌ Indicatori di stato
🔄 Pulsante aggiornamento manuale
```

### Messaggi di Stato
- ✅ **Successo**: "Dati rilevati automaticamente dalla localizzazione del cantiere"
- ⚠️ **Demo**: "Dati demo - Verifica configurazione API o localizzazione cantiere"
- ❌ **Errore**: "Impossibile recuperare dati meteorologici reali"

## Flusso di Lavoro

### 1. Apertura Dialog Certificazione
```
1. User clicca "Certifica Cavo"
2. Sistema carica automaticamente dati meteorologici
3. Mostra sezione informativa con stato
4. Precompila temperatura/umidità nel form
```

### 2. Invio Certificazione
```
1. User compila form certificazione
2. Sistema include automaticamente:
   - temperatura_prova: weatherData.temperature
   - umidita_prova: weatherData.humidity
3. Salvataggio in database con dati meteorologici
```

### 3. Gestione Errori
```
1. API non disponibile → Dati demo
2. Cantiere senza localizzazione → Dati demo
3. Errore rete → Dati demo
4. Cache valida → Dati cached
```

## Benefici Implementati

### 🎯 **Automazione Completa**
- **Zero input manuale**: Temperatura e umidità automatiche
- **Dati accurati**: Condizioni reali del cantiere
- **Conformità normativa**: CEI 64-8 compliance automatica

### 🌍 **Supporto Globale**
- **Cantieri internazionali**: Supporto per tutti i paesi
- **Localizzazione precisa**: Basata su città del cantiere
- **Fuso orario**: Dati meteorologici locali

### 🔒 **Affidabilità**
- **Fallback robusto**: Sempre funzionante anche senza API
- **Cache intelligente**: Riduce chiamate API
- **Gestione errori**: Graceful degradation

### 📊 **Tracciabilità**
- **Timestamp**: Quando sono stati rilevati i dati
- **Fonte**: Indicazione se dati reali o demo
- **Storico**: Dati salvati per ogni certificazione

## File Modificati/Creati

### Backend
- ✅ `webapp/backend/services/weather_service.py` (NUOVO)
- ✅ `webapp/backend/api/cantieri.py` (endpoint weather)

### Frontend
- ✅ `webapp/frontend/src/services/weatherService.js` (NUOVO)
- ✅ `webapp/frontend/src/components/cavi/CertificazioneCaviImproved.js`
- ✅ `webapp/frontend/src/components/certificazioni/CertificazioneForm.jsx`

### Documentazione
- ✅ `docs/SISTEMA_METEOROLOGICO.md` (QUESTO FILE)

## Testing

### Test Manuale
1. **Cantiere con localizzazione**: Verifica recupero dati reali
2. **Cantiere senza città**: Verifica fallback demo
3. **API non configurata**: Verifica dati demo
4. **Certificazione**: Verifica inclusione automatica dati

### Test API
```bash
# Test endpoint weather
curl -X GET "http://localhost:8001/cantieri/1/weather" \
     -H "Authorization: Bearer YOUR_TOKEN"
```

## Prossimi Sviluppi

### 🔮 **Funzionalità Future**
- **Previsioni meteo**: Dati previsionali per pianificazione
- **Storico meteorologico**: Analisi condizioni passate
- **Alert meteo**: Notifiche condizioni avverse
- **Integrazione mappe**: Visualizzazione cantieri su mappa

### 🛠️ **Miglioramenti Tecnici**
- **Multiple API**: Fallback tra diverse API meteo
- **Coordinate precise**: GPS invece di città
- **Cache avanzata**: Redis per cache distribuita
- **Monitoring**: Metriche utilizzo API

## Note Tecniche

- **Rate limiting**: 1000 chiamate/giorno con API gratuita
- **Cache duration**: 30 minuti per bilanciare accuratezza/performance
- **Error handling**: Graceful degradation sempre garantita
- **Data validation**: Validazione dati meteorologici ricevuti
