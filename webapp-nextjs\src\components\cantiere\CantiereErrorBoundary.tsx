'use client'

import React from 'react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertCircle, Construction, RefreshCw, ArrowLeft } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useCantiere } from '@/hooks/useCantiere'

interface CantiereErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  showBackButton?: boolean
  backUrl?: string
}

/**
 * Componente per gestire errori relativi alla selezione del cantiere
 * Mostra messaggi di errore appropriati e opzioni di recupero
 */
export function CantiereErrorBoundary({ 
  children, 
  fallback, 
  showBackButton = true, 
  backUrl = '/cantieri' 
}: CantiereErrorBoundaryProps) {
  const router = useRouter()
  const { cantiereId, cantiere, isValidCantiere, isLoading, error, clearError } = useCantiere()

  // Se stiamo caricando, mostra un loader
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardContent className="flex items-center justify-center p-8">
              <div className="text-center">
                <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
                <p className="text-lg font-medium text-gray-700">Caricamento cantiere...</p>
                <p className="text-sm text-gray-500 mt-2">Verifica della selezione cantiere in corso</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Se c'è un errore o il cantiere non è valido, mostra l'errore
  if (error || !isValidCantiere) {
    const errorMessage = error || 'Nessun cantiere selezionato'
    
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6">
        <div className="max-w-4xl mx-auto space-y-6">
          
          {/* Header con errore */}
          <Card className="border-red-200 bg-red-50">
            <CardHeader>
              <CardTitle className="flex items-center text-red-800">
                <AlertCircle className="h-6 w-6 mr-2" />
                Problema con la selezione del cantiere
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Errore:</strong> {errorMessage}
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>

          {/* Informazioni di debug */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-gray-700">
                <Construction className="h-5 w-5 mr-2" />
                Informazioni cantiere
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-600">ID Cantiere:</span>
                  <span className="ml-2 text-gray-800">{cantiereId || 'Non disponibile'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Nome Cantiere:</span>
                  <span className="ml-2 text-gray-800">{cantiere?.commessa || 'Non disponibile'}</span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">Cantiere Valido:</span>
                  <span className={`ml-2 ${isValidCantiere ? 'text-green-600' : 'text-red-600'}`}>
                    {isValidCantiere ? 'Sì' : 'No'}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-600">localStorage ID:</span>
                  <span className="ml-2 text-gray-800">
                    {typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereId') || 'Non presente' : 'N/A'}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Azioni di recupero */}
          <Card>
            <CardHeader>
              <CardTitle className="text-gray-700">Azioni disponibili</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex flex-wrap gap-3">
                
                {/* Pulsante per tornare ai cantieri */}
                {showBackButton && (
                  <Button 
                    onClick={() => router.push(backUrl)}
                    variant="default"
                    className="flex items-center"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Seleziona Cantiere
                  </Button>
                )}

                {/* Pulsante per pulire errore */}
                {error && (
                  <Button 
                    onClick={clearError}
                    variant="outline"
                    className="flex items-center"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Riprova
                  </Button>
                )}

                {/* Pulsante per pulire localStorage */}
                <Button 
                  onClick={() => {
                    localStorage.removeItem('selectedCantiereId')
                    localStorage.removeItem('selectedCantiereName')
                    localStorage.removeItem('cantiere_data')
                    window.location.reload()
                  }}
                  variant="outline"
                  className="flex items-center text-orange-600 border-orange-300 hover:bg-orange-50"
                >
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Reset Dati Cantiere
                </Button>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Suggerimento:</strong> Se il problema persiste, prova a selezionare nuovamente un cantiere 
                  dalla pagina principale o contatta l'amministratore del sistema.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  // Se tutto è ok, renderizza i children
  return <>{children}</>
}

/**
 * Hook per utilizzare il CantiereErrorBoundary in modo condizionale
 */
export function useCantiereErrorBoundary() {
  const { isValidCantiere, isLoading, error } = useCantiere()
  
  return {
    shouldShowError: !isLoading && (!isValidCantiere || error),
    isLoading,
    error
  }
}
