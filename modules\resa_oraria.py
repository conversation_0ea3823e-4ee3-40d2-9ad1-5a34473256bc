"""
Modulo per il calcolo intelligente della resa oraria per comande.
Calcola la produttività per posa, collegamenti e certificazioni.
"""

import logging
import psycopg2
from psycopg2.extras import RealDictCursor
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Any, Optional, Tuple
from contextlib import contextmanager
from dataclasses import dataclass
from enum import Enum

# Configura il logging
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")

# Configurazione del database
DB_HOST = 'localhost'
DB_PORT = '5432'
DB_NAME = 'cantieri'
DB_USER = 'postgres'
DB_PASSWORD = 'Taranto'

@contextmanager
def database_connection():
    """Context manager per gestire connessioni al database in modo sicuro."""
    conn = None
    try:
        conn = psycopg2.connect(
            host=DB_HOST,
            port=DB_PORT,
            dbname=DB_NAME,
            user=DB_USER,
            password=DB_PASSWORD
        )
        conn.cursor_factory = RealDictCursor
        yield conn
    except psycopg2.Error as e:
        logging.error(f"❌ Errore database: {str(e)}")
        raise
    finally:
        if conn:
            conn.close()

class TipoAttivita(Enum):
    """Tipi di attività per il calcolo della resa."""
    POSA = "POSA"
    COLLEGAMENTO_PARTENZA = "COLLEGAMENTO_PARTENZA"
    COLLEGAMENTO_ARRIVO = "COLLEGAMENTO_ARRIVO"
    CERTIFICAZIONE = "CERTIFICAZIONE"

@dataclass
class RendimentoLavoro:
    """Classe per rappresentare il rendimento di un lavoro."""
    tipo_attivita: str
    unita_lavorate: float  # metri per posa, collegamenti per collegamenti, test per certificazioni
    ore_totali: float
    numero_persone: int
    resa_oraria_totale: float  # unità/ora totali
    resa_oraria_per_persona: float  # unità/ora per persona
    data_lavoro: datetime
    codice_comanda: str
    responsabile: str

@dataclass
class StatisticheResa:
    """Statistiche aggregate della resa oraria."""
    tipo_attivita: str
    resa_media_oraria: float
    resa_media_per_persona: float
    migliore_resa: float
    peggiore_resa: float
    totale_lavori: int
    periodo_analisi: str
    responsabili_coinvolti: List[str]

def calcola_resa_posa(metri_posati: float, ore_lavoro: float, numero_persone: int) -> Tuple[float, float]:
    """
    Calcola la resa oraria per la posa di cavi.
    
    Args:
        metri_posati: Metri di cavo posati
        ore_lavoro: Ore totali di lavoro della squadra
        numero_persone: Numero di persone nella squadra
    
    Returns:
        Tuple[float, float]: (resa_totale_metri_ora, resa_per_persona_metri_ora)
    """
    if ore_lavoro <= 0:
        return 0.0, 0.0
    
    resa_totale = metri_posati / ore_lavoro
    resa_per_persona = resa_totale / numero_persone if numero_persone > 0 else 0.0
    
    return resa_totale, resa_per_persona

def calcola_resa_collegamento(numero_collegamenti: int, ore_lavoro: float, numero_persone: int) -> Tuple[float, float]:
    """
    Calcola la resa oraria per i collegamenti.
    Per i collegamenti si considera il lavoro di squadra.
    
    Args:
        numero_collegamenti: Numero di collegamenti effettuati
        ore_lavoro: Ore totali di lavoro della squadra
        numero_persone: Numero di persone nella squadra
    
    Returns:
        Tuple[float, float]: (resa_totale_collegamenti_ora, resa_per_squadra)
    """
    if ore_lavoro <= 0:
        return 0.0, 0.0
    
    resa_totale = numero_collegamenti / ore_lavoro
    # Per i collegamenti, la resa per squadra è più significativa della resa per persona
    resa_per_squadra = resa_totale  # Collegamenti/ora per squadra
    
    return resa_totale, resa_per_squadra

def calcola_resa_certificazione(numero_test: int, ore_lavoro: float, numero_persone: int) -> Tuple[float, float]:
    """
    Calcola la resa oraria per le certificazioni/test.
    Per le certificazioni si considera il lavoro di squadra specializzata.
    
    Args:
        numero_test: Numero di test/certificazioni effettuate
        ore_lavoro: Ore totali di lavoro della squadra
        numero_persone: Numero di persone nella squadra
    
    Returns:
        Tuple[float, float]: (resa_totale_test_ora, resa_per_squadra)
    """
    if ore_lavoro <= 0:
        return 0.0, 0.0
    
    resa_totale = numero_test / ore_lavoro
    # Per le certificazioni, la resa per squadra è più significativa
    resa_per_squadra = resa_totale  # Test/ora per squadra
    
    return resa_totale, resa_per_squadra

def registra_tempo_lavoro_cavo(id_cavo: str, id_cantiere: int, tipo_attivita: str, 
                              ore_lavoro: float, numero_persone: int, 
                              data_lavoro: Optional[datetime] = None) -> bool:
    """
    Registra i tempi di lavoro per un cavo specifico.
    
    Args:
        id_cavo: ID del cavo
        id_cantiere: ID del cantiere
        tipo_attivita: Tipo di attività (POSA, COLLEGAMENTO_PARTENZA, etc.)
        ore_lavoro: Ore di lavoro effettuate
        numero_persone: Numero di persone coinvolte
        data_lavoro: Data del lavoro (default: oggi)
    
    Returns:
        bool: True se registrazione riuscita
    """
    try:
        if not data_lavoro:
            data_lavoro = datetime.now()
            
        with database_connection() as conn:
            c = conn.cursor()
            
            # Aggiorna i campi specifici per tipo di attività
            if tipo_attivita == TipoAttivita.POSA.value:
                c.execute("""
                    UPDATE Cavi 
                    SET ore_lavoro_posa = %s,
                        numero_persone_impiegate = %s,
                        data_fine_lavoro = %s
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (ore_lavoro, numero_persone, data_lavoro, id_cavo, id_cantiere))
                
            elif tipo_attivita == TipoAttivita.COLLEGAMENTO_PARTENZA.value:
                c.execute("""
                    UPDATE Cavi 
                    SET ore_lavoro_collegamento_partenza = %s
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (ore_lavoro, id_cavo, id_cantiere))
                
            elif tipo_attivita == TipoAttivita.COLLEGAMENTO_ARRIVO.value:
                c.execute("""
                    UPDATE Cavi 
                    SET ore_lavoro_collegamento_arrivo = %s
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (ore_lavoro, id_cavo, id_cantiere))
                
            elif tipo_attivita == TipoAttivita.CERTIFICAZIONE.value:
                c.execute("""
                    UPDATE Cavi 
                    SET ore_lavoro_certificazione = %s
                    WHERE id_cavo = %s AND id_cantiere = %s
                """, (ore_lavoro, id_cavo, id_cantiere))
            
            if c.rowcount > 0:
                conn.commit()
                logging.info(f"✅ Registrato tempo lavoro per cavo {id_cavo}: {ore_lavoro}h, {numero_persone} persone")
                return True
            else:
                logging.warning(f"⚠️ Cavo {id_cavo} non trovato per registrazione tempo")
                return False
                
    except Exception as e:
        logging.error(f"❌ Errore nella registrazione tempo lavoro: {str(e)}")
        return False

def calcola_resa_comanda(codice_comanda: str) -> Optional[Dict[str, Any]]:
    """
    Calcola la resa oraria complessiva di una comanda.
    
    Args:
        codice_comanda: Codice della comanda
    
    Returns:
        Dict con statistiche di resa o None se errore
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            
            # Ottieni informazioni comanda
            c.execute("""
                SELECT tipo_comanda, responsabile, numero_componenti_squadra, 
                       data_creazione, data_completamento
                FROM Comande 
                WHERE codice_comanda = %s
            """, (codice_comanda,))
            
            comanda = c.fetchone()
            if not comanda:
                logging.error(f"❌ Comanda {codice_comanda} non trovata")
                return None
            
            tipo_comanda = comanda['tipo_comanda']
            
            # Calcola resa in base al tipo di comanda
            if tipo_comanda == "POSA":
                return _calcola_resa_posa_comanda(c, codice_comanda, comanda)
            elif tipo_comanda in ["COLLEGAMENTO_PARTENZA", "COLLEGAMENTO_ARRIVO"]:
                return _calcola_resa_collegamento_comanda(c, codice_comanda, comanda)
            elif tipo_comanda == "CERTIFICAZIONE":
                return _calcola_resa_certificazione_comanda(c, codice_comanda, comanda)
            else:
                logging.error(f"❌ Tipo comanda non riconosciuto: {tipo_comanda}")
                return None
                
    except Exception as e:
        logging.error(f"❌ Errore nel calcolo resa comanda {codice_comanda}: {str(e)}")
        return None

def _calcola_resa_posa_comanda(cursor, codice_comanda: str, comanda: dict) -> Dict[str, Any]:
    """Calcola resa specifica per comande di posa."""
    # Ottieni tutti i cavi posati nella comanda
    cursor.execute("""
        SELECT id_cavo, metratura_reale, ore_lavoro_posa, numero_persone_impiegate,
               data_fine_lavoro, responsabile_posa
        FROM Cavi 
        WHERE comanda_posa = %s 
        AND stato_installazione = 'Installato'
        AND metratura_reale > 0
    """, (codice_comanda,))
    
    cavi_posati = cursor.fetchall()
    
    if not cavi_posati:
        return {
            'tipo_attivita': 'POSA',
            'codice_comanda': codice_comanda,
            'totale_cavi': 0,
            'totale_metri': 0,
            'totale_ore': 0,
            'resa_media_metri_ora': 0,
            'resa_media_per_persona': 0,
            'errore': 'Nessun cavo posato trovato'
        }
    
    totale_metri = sum(float(cavo['metratura_reale'] or 0) for cavo in cavi_posati)
    totale_ore = sum(float(cavo['ore_lavoro_posa'] or 0) for cavo in cavi_posati)
    
    # Calcola resa media
    if totale_ore > 0:
        resa_totale = totale_metri / totale_ore
        numero_medio_persone = sum(int(cavo['numero_persone_impiegate'] or 1) for cavo in cavi_posati) / len(cavi_posati)
        resa_per_persona = resa_totale / numero_medio_persone if numero_medio_persone > 0 else 0
    else:
        resa_totale = 0
        resa_per_persona = 0
    
    return {
        'tipo_attivita': 'POSA',
        'codice_comanda': codice_comanda,
        'responsabile': comanda['responsabile'],
        'totale_cavi': len(cavi_posati),
        'totale_metri': round(totale_metri, 2),
        'totale_ore': round(totale_ore, 2),
        'resa_media_metri_ora': round(resa_totale, 2),
        'resa_media_per_persona': round(resa_per_persona, 2),
        'numero_medio_persone': round(numero_medio_persone, 1) if 'numero_medio_persone' in locals() else 0,
        'dettagli_cavi': [
            {
                'id_cavo': cavo['id_cavo'],
                'metri': float(cavo['metratura_reale'] or 0),
                'ore': float(cavo['ore_lavoro_posa'] or 0),
                'persone': int(cavo['numero_persone_impiegate'] or 1),
                'resa_cavo': round(float(cavo['metratura_reale'] or 0) / float(cavo['ore_lavoro_posa'] or 1), 2) if cavo['ore_lavoro_posa'] else 0
            }
            for cavo in cavi_posati
        ]
    }

def _calcola_resa_collegamento_comanda(cursor, codice_comanda: str, comanda: dict) -> Dict[str, Any]:
    """Calcola resa specifica per comande di collegamento."""
    tipo_comanda = comanda['tipo_comanda']

    # Determina il campo da controllare
    if tipo_comanda == "COLLEGAMENTO_PARTENZA":
        campo_comanda = "comanda_partenza"
        campo_ore = "ore_lavoro_collegamento_partenza"
        campo_responsabile = "responsabile_partenza"
        # Per partenza: collegamenti = 1 o 3 (partenza collegata)
        condizione_collegamento = "AND (collegamenti = 1 OR collegamenti = 3)"
    else:  # COLLEGAMENTO_ARRIVO
        campo_comanda = "comanda_arrivo"
        campo_ore = "ore_lavoro_collegamento_arrivo"
        campo_responsabile = "responsabile_arrivo"
        # Per arrivo: collegamenti = 2 o 3 (arrivo collegato)
        condizione_collegamento = "AND (collegamenti = 2 OR collegamenti = 3)"

    # Ottieni tutti i cavi collegati nella comanda
    query = f"""
        SELECT id_cavo, collegamenti, {campo_ore}, {campo_responsabile}
        FROM Cavi
        WHERE {campo_comanda} = %s
        AND stato_installazione = 'Installato'
        {condizione_collegamento}
    """

    cursor.execute(query, (codice_comanda,))
    cavi_collegati = cursor.fetchall()

    if not cavi_collegati:
        return {
            'tipo_attivita': tipo_comanda,
            'codice_comanda': codice_comanda,
            'totale_cavi': 0,
            'totale_collegamenti': 0,
            'totale_ore': 0,
            'resa_media_collegamenti_ora': 0,
            'resa_per_squadra': 0,
            'errore': 'Nessun collegamento trovato'
        }

    totale_collegamenti = len(cavi_collegati)
    totale_ore = sum(float(cavo[campo_ore] or 0) for cavo in cavi_collegati)

    # Calcola resa per squadra (collegamenti/ora)
    if totale_ore > 0:
        resa_collegamenti_ora = totale_collegamenti / totale_ore
        resa_per_squadra = resa_collegamenti_ora  # Per collegamenti, resa per squadra
    else:
        resa_collegamenti_ora = 0
        resa_per_squadra = 0

    return {
        'tipo_attivita': tipo_comanda,
        'codice_comanda': codice_comanda,
        'responsabile': comanda['responsabile'],
        'totale_cavi': len(cavi_collegati),
        'totale_collegamenti': totale_collegamenti,
        'totale_ore': round(totale_ore, 2),
        'resa_media_collegamenti_ora': round(resa_collegamenti_ora, 2),
        'resa_per_squadra': round(resa_per_squadra, 2),
        'numero_componenti_squadra': comanda.get('numero_componenti_squadra', 1),
        'dettagli_cavi': [
            {
                'id_cavo': cavo['id_cavo'],
                'collegamenti': cavo['collegamenti'],
                'ore': float(cavo[campo_ore] or 0),
                'resa_cavo': round(1 / float(cavo[campo_ore] or 1), 2) if cavo[campo_ore] else 0  # collegamenti/ora per questo cavo
            }
            for cavo in cavi_collegati
        ]
    }

def _calcola_resa_certificazione_comanda(cursor, codice_comanda: str, comanda: dict) -> Dict[str, Any]:
    """Calcola resa specifica per comande di certificazione."""
    # Ottieni tutti i cavi certificati nella comanda
    cursor.execute("""
        SELECT id_cavo, stato_certificazione, ore_lavoro_certificazione,
               data_certificazione_cavo
        FROM Cavi
        WHERE comanda_certificazione = %s
        AND stato_installazione = 'Installato'
        AND stato_certificazione IN ('Certificato', 'Non Conforme')
    """, (codice_comanda,))

    cavi_certificati = cursor.fetchall()

    if not cavi_certificati:
        return {
            'tipo_attivita': 'CERTIFICAZIONE',
            'codice_comanda': codice_comanda,
            'totale_cavi': 0,
            'totale_test': 0,
            'totale_ore': 0,
            'resa_media_test_ora': 0,
            'resa_per_squadra': 0,
            'errore': 'Nessuna certificazione trovata'
        }

    totale_test = len(cavi_certificati)
    totale_ore = sum(float(cavo['ore_lavoro_certificazione'] or 0) for cavo in cavi_certificati)

    # Calcola resa per squadra (test/ora)
    if totale_ore > 0:
        resa_test_ora = totale_test / totale_ore
        resa_per_squadra = resa_test_ora  # Per certificazioni, resa per squadra
    else:
        resa_test_ora = 0
        resa_per_squadra = 0

    # Statistiche aggiuntive per certificazioni
    cavi_conformi = sum(1 for cavo in cavi_certificati if cavo['stato_certificazione'] == 'Certificato')
    cavi_non_conformi = totale_test - cavi_conformi
    percentuale_conformita = (cavi_conformi / totale_test * 100) if totale_test > 0 else 0

    return {
        'tipo_attivita': 'CERTIFICAZIONE',
        'codice_comanda': codice_comanda,
        'responsabile': comanda['responsabile'],
        'totale_cavi': len(cavi_certificati),
        'totale_test': totale_test,
        'totale_ore': round(totale_ore, 2),
        'resa_media_test_ora': round(resa_test_ora, 2),
        'resa_per_squadra': round(resa_per_squadra, 2),
        'numero_componenti_squadra': comanda.get('numero_componenti_squadra', 1),
        'cavi_conformi': cavi_conformi,
        'cavi_non_conformi': cavi_non_conformi,
        'percentuale_conformita': round(percentuale_conformita, 2),
        'dettagli_cavi': [
            {
                'id_cavo': cavo['id_cavo'],
                'stato_certificazione': cavo['stato_certificazione'],
                'ore': float(cavo['ore_lavoro_certificazione'] or 0),
                'resa_cavo': round(1 / float(cavo['ore_lavoro_certificazione'] or 1), 2) if cavo['ore_lavoro_certificazione'] else 0
            }
            for cavo in cavi_certificati
        ]
    }

def calcola_statistiche_resa_cantiere(id_cantiere: int, tipo_attivita: Optional[str] = None,
                                     giorni_analisi: int = 30) -> Dict[str, Any]:
    """
    Calcola statistiche aggregate della resa oraria per un cantiere.

    Args:
        id_cantiere: ID del cantiere
        tipo_attivita: Tipo di attività da analizzare (None per tutte)
        giorni_analisi: Numero di giorni da analizzare (default: 30)

    Returns:
        Dict con statistiche aggregate
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()

            # Data limite per l'analisi
            data_limite = datetime.now() - timedelta(days=giorni_analisi)

            statistiche = {}

            # Analizza ogni tipo di attività
            tipi_da_analizzare = [tipo_attivita] if tipo_attivita else ['POSA', 'COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO', 'CERTIFICAZIONE']

            for tipo in tipi_da_analizzare:
                if tipo == 'POSA':
                    stats = _analizza_resa_posa_cantiere(c, id_cantiere, data_limite)
                elif tipo in ['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO']:
                    stats = _analizza_resa_collegamento_cantiere(c, id_cantiere, tipo, data_limite)
                elif tipo == 'CERTIFICAZIONE':
                    stats = _analizza_resa_certificazione_cantiere(c, id_cantiere, data_limite)
                else:
                    continue

                if stats:
                    statistiche[tipo] = stats

            return {
                'id_cantiere': id_cantiere,
                'periodo_analisi': f"Ultimi {giorni_analisi} giorni",
                'data_analisi': datetime.now().isoformat(),
                'statistiche_per_tipo': statistiche,
                'riepilogo_generale': _calcola_riepilogo_generale(statistiche)
            }

    except Exception as e:
        logging.error(f"❌ Errore nel calcolo statistiche resa cantiere {id_cantiere}: {str(e)}")
        return {}

def _analizza_resa_posa_cantiere(cursor, id_cantiere: int, data_limite: datetime) -> Dict[str, Any]:
    """Analizza la resa di posa per un cantiere."""
    cursor.execute("""
        SELECT
            c.id_cavo,
            c.metratura_reale,
            c.ore_lavoro_posa,
            c.numero_persone_impiegate,
            c.responsabile_posa,
            c.data_fine_lavoro,
            cmd.codice_comanda
        FROM Cavi c
        JOIN Comande cmd ON c.comanda_posa = cmd.codice_comanda
        WHERE c.id_cantiere = %s
        AND c.stato_installazione = 'Installato'
        AND c.metratura_reale > 0
        AND c.ore_lavoro_posa > 0
        AND (c.data_fine_lavoro >= %s OR c.data_fine_lavoro IS NULL)
        ORDER BY c.data_fine_lavoro DESC
    """, (id_cantiere, data_limite))

    lavori_posa = cursor.fetchall()

    if not lavori_posa:
        return None

    # Calcola statistiche
    rese_orarie = []
    rese_per_persona = []
    responsabili = set()

    for lavoro in lavori_posa:
        metri = float(lavoro['metratura_reale'])
        ore = float(lavoro['ore_lavoro_posa'])
        persone = int(lavoro['numero_persone_impiegate'] or 1)

        resa_oraria = metri / ore
        resa_per_persona = resa_oraria / persone

        rese_orarie.append(resa_oraria)
        rese_per_persona.append(resa_per_persona)

        if lavoro['responsabile_posa']:
            responsabili.add(lavoro['responsabile_posa'])

    return {
        'tipo_attivita': 'POSA',
        'totale_lavori': len(lavori_posa),
        'totale_metri': sum(float(l['metratura_reale']) for l in lavori_posa),
        'totale_ore': sum(float(l['ore_lavoro_posa']) for l in lavori_posa),
        'resa_media_metri_ora': round(sum(rese_orarie) / len(rese_orarie), 2),
        'resa_media_per_persona': round(sum(rese_per_persona) / len(rese_per_persona), 2),
        'resa_migliore': round(max(rese_orarie), 2),
        'resa_peggiore': round(min(rese_orarie), 2),
        'responsabili_coinvolti': list(responsabili),
        'numero_responsabili': len(responsabili)
    }

def _analizza_resa_collegamento_cantiere(cursor, id_cantiere: int, tipo_collegamento: str, data_limite: datetime) -> Dict[str, Any]:
    """Analizza la resa di collegamento per un cantiere."""
    if tipo_collegamento == "COLLEGAMENTO_PARTENZA":
        campo_comanda = "comanda_partenza"
        campo_ore = "ore_lavoro_collegamento_partenza"
        campo_responsabile = "responsabile_partenza"
        condizione_collegamento = "AND (c.collegamenti = 1 OR c.collegamenti = 3)"
    else:  # COLLEGAMENTO_ARRIVO
        campo_comanda = "comanda_arrivo"
        campo_ore = "ore_lavoro_collegamento_arrivo"
        campo_responsabile = "responsabile_arrivo"
        condizione_collegamento = "AND (c.collegamenti = 2 OR c.collegamenti = 3)"

    query = f"""
        SELECT
            c.id_cavo,
            c.{campo_ore},
            c.{campo_responsabile},
            cmd.codice_comanda,
            cmd.numero_componenti_squadra
        FROM Cavi c
        JOIN Comande cmd ON c.{campo_comanda} = cmd.codice_comanda
        WHERE c.id_cantiere = %s
        AND c.stato_installazione = 'Installato'
        AND c.{campo_ore} > 0
        {condizione_collegamento}
        AND cmd.data_completamento >= %s
        ORDER BY cmd.data_completamento DESC
    """

    cursor.execute(query, (id_cantiere, data_limite))
    lavori_collegamento = cursor.fetchall()

    if not lavori_collegamento:
        return None

    # Calcola statistiche per squadra
    rese_per_squadra = []
    responsabili = set()

    for lavoro in lavori_collegamento:
        ore = float(lavoro[campo_ore])
        resa_squadra = 1 / ore  # collegamenti/ora per squadra

        rese_per_squadra.append(resa_squadra)

        if lavoro[campo_responsabile]:
            responsabili.add(lavoro[campo_responsabile])

    return {
        'tipo_attivita': tipo_collegamento,
        'totale_lavori': len(lavori_collegamento),
        'totale_collegamenti': len(lavori_collegamento),
        'totale_ore': sum(float(l[campo_ore]) for l in lavori_collegamento),
        'resa_media_per_squadra': round(sum(rese_per_squadra) / len(rese_per_squadra), 2),
        'resa_migliore': round(max(rese_per_squadra), 2),
        'resa_peggiore': round(min(rese_per_squadra), 2),
        'responsabili_coinvolti': list(responsabili),
        'numero_responsabili': len(responsabili)
    }

def _analizza_resa_certificazione_cantiere(cursor, id_cantiere: int, data_limite: datetime) -> Dict[str, Any]:
    """Analizza la resa di certificazione per un cantiere."""
    cursor.execute("""
        SELECT
            c.id_cavo,
            c.ore_lavoro_certificazione,
            c.stato_certificazione,
            c.data_certificazione_cavo,
            cmd.codice_comanda,
            cmd.numero_componenti_squadra,
            cmd.responsabile
        FROM Cavi c
        JOIN Comande cmd ON c.comanda_certificazione = cmd.codice_comanda
        WHERE c.id_cantiere = %s
        AND c.stato_installazione = 'Installato'
        AND c.ore_lavoro_certificazione > 0
        AND c.stato_certificazione IN ('Certificato', 'Non Conforme')
        AND (c.data_certificazione_cavo >= %s OR c.data_certificazione_cavo IS NULL)
        ORDER BY c.data_certificazione_cavo DESC
    """, (id_cantiere, data_limite))

    lavori_certificazione = cursor.fetchall()

    if not lavori_certificazione:
        return None

    # Calcola statistiche per squadra
    rese_per_squadra = []
    responsabili = set()
    cavi_conformi = 0

    for lavoro in lavori_certificazione:
        ore = float(lavoro['ore_lavoro_certificazione'])
        resa_squadra = 1 / ore  # test/ora per squadra

        rese_per_squadra.append(resa_squadra)

        if lavoro['responsabile']:
            responsabili.add(lavoro['responsabile'])

        if lavoro['stato_certificazione'] == 'Certificato':
            cavi_conformi += 1

    percentuale_conformita = (cavi_conformi / len(lavori_certificazione) * 100) if lavori_certificazione else 0

    return {
        'tipo_attivita': 'CERTIFICAZIONE',
        'totale_lavori': len(lavori_certificazione),
        'totale_test': len(lavori_certificazione),
        'totale_ore': sum(float(l['ore_lavoro_certificazione']) for l in lavori_certificazione),
        'resa_media_per_squadra': round(sum(rese_per_squadra) / len(rese_per_squadra), 2),
        'resa_migliore': round(max(rese_per_squadra), 2),
        'resa_peggiore': round(min(rese_per_squadra), 2),
        'cavi_conformi': cavi_conformi,
        'cavi_non_conformi': len(lavori_certificazione) - cavi_conformi,
        'percentuale_conformita': round(percentuale_conformita, 2),
        'responsabili_coinvolti': list(responsabili),
        'numero_responsabili': len(responsabili)
    }

def _calcola_riepilogo_generale(statistiche: Dict[str, Any]) -> Dict[str, Any]:
    """Calcola un riepilogo generale delle statistiche."""
    if not statistiche:
        return {}

    totale_lavori = sum(stats.get('totale_lavori', 0) for stats in statistiche.values())
    totale_ore = sum(stats.get('totale_ore', 0) for stats in statistiche.values())

    # Raccoglie tutti i responsabili
    tutti_responsabili = set()
    for stats in statistiche.values():
        tutti_responsabili.update(stats.get('responsabili_coinvolti', []))

    # Calcola medie ponderate dove possibile
    riepilogo = {
        'totale_attivita_analizzate': len(statistiche),
        'totale_lavori_completati': totale_lavori,
        'totale_ore_lavorate': round(totale_ore, 2),
        'responsabili_totali': len(tutti_responsabili),
        'lista_responsabili': list(tutti_responsabili)
    }

    # Aggiungi dettagli specifici per tipo
    if 'POSA' in statistiche:
        posa_stats = statistiche['POSA']
        riepilogo['totale_metri_posati'] = posa_stats.get('totale_metri', 0)
        riepilogo['resa_media_posa'] = posa_stats.get('resa_media_metri_ora', 0)

    if any(tipo.startswith('COLLEGAMENTO') for tipo in statistiche.keys()):
        collegamenti_totali = sum(
            stats.get('totale_collegamenti', 0)
            for tipo, stats in statistiche.items()
            if tipo.startswith('COLLEGAMENTO')
        )
        riepilogo['totale_collegamenti_effettuati'] = collegamenti_totali

    if 'CERTIFICAZIONE' in statistiche:
        cert_stats = statistiche['CERTIFICAZIONE']
        riepilogo['totale_test_effettuati'] = cert_stats.get('totale_test', 0)
        riepilogo['percentuale_conformita_media'] = cert_stats.get('percentuale_conformita', 0)

    return riepilogo

def genera_report_resa_oraria(id_cantiere: int, codice_comanda: Optional[str] = None) -> Dict[str, Any]:
    """
    Genera un report completo della resa oraria.

    Args:
        id_cantiere: ID del cantiere
        codice_comanda: Codice comanda specifica (opzionale)

    Returns:
        Dict con report completo
    """
    try:
        report = {
            'id_cantiere': id_cantiere,
            'data_generazione': datetime.now().isoformat(),
            'tipo_report': 'comanda_specifica' if codice_comanda else 'cantiere_completo'
        }

        if codice_comanda:
            # Report per comanda specifica
            resa_comanda = calcola_resa_comanda(codice_comanda)
            if resa_comanda:
                report['resa_comanda'] = resa_comanda
                report['raccomandazioni'] = _genera_raccomandazioni_comanda(resa_comanda)
            else:
                report['errore'] = f"Impossibile calcolare resa per comanda {codice_comanda}"
        else:
            # Report per cantiere completo
            statistiche_cantiere = calcola_statistiche_resa_cantiere(id_cantiere)
            if statistiche_cantiere:
                report['statistiche_cantiere'] = statistiche_cantiere
                report['raccomandazioni'] = _genera_raccomandazioni_cantiere(statistiche_cantiere)
            else:
                report['errore'] = f"Impossibile calcolare statistiche per cantiere {id_cantiere}"

        return report

    except Exception as e:
        logging.error(f"❌ Errore nella generazione report resa oraria: {str(e)}")
        return {
            'errore': f"Errore nella generazione del report: {str(e)}",
            'data_errore': datetime.now().isoformat()
        }

def _genera_raccomandazioni_comanda(resa_comanda: Dict[str, Any]) -> List[str]:
    """Genera raccomandazioni basate sulla resa di una comanda."""
    raccomandazioni = []
    tipo_attivita = resa_comanda.get('tipo_attivita', '')

    if tipo_attivita == 'POSA':
        resa_metri_ora = resa_comanda.get('resa_media_metri_ora', 0)
        resa_per_persona = resa_comanda.get('resa_media_per_persona', 0)

        if resa_metri_ora < 50:  # Soglia bassa per posa
            raccomandazioni.append("⚠️ Resa di posa sotto la media. Verificare eventuali difficoltà tecniche o logistiche.")
        elif resa_metri_ora > 150:  # Soglia alta per posa
            raccomandazioni.append("✅ Ottima resa di posa! Considerare questo team come riferimento.")

        if resa_per_persona < 20:
            raccomandazioni.append("💡 Considerare l'aumento del numero di persone nella squadra per migliorare l'efficienza.")

    elif tipo_attivita in ['COLLEGAMENTO_PARTENZA', 'COLLEGAMENTO_ARRIVO']:
        resa_per_squadra = resa_comanda.get('resa_per_squadra', 0)

        if resa_per_squadra < 0.5:  # Meno di 0.5 collegamenti/ora
            raccomandazioni.append("⚠️ Resa di collegamento bassa. Verificare complessità dei collegamenti o formazione del team.")
        elif resa_per_squadra > 2:  # Più di 2 collegamenti/ora
            raccomandazioni.append("✅ Eccellente resa di collegamento! Team molto efficiente.")

    elif tipo_attivita == 'CERTIFICAZIONE':
        resa_per_squadra = resa_comanda.get('resa_per_squadra', 0)
        percentuale_conformita = resa_comanda.get('percentuale_conformita', 0)

        if resa_per_squadra < 0.3:  # Meno di 0.3 test/ora
            raccomandazioni.append("⚠️ Resa di certificazione bassa. Verificare procedure di test o strumentazione.")

        if percentuale_conformita < 90:
            raccomandazioni.append("🔍 Percentuale di conformità sotto il 90%. Analizzare cause di non conformità.")
        elif percentuale_conformita == 100:
            raccomandazioni.append("✅ Perfetta conformità! Ottimo lavoro di installazione e test.")

    if not raccomandazioni:
        raccomandazioni.append("✅ Prestazioni nella norma. Continuare con le procedure attuali.")

    return raccomandazioni

def _genera_raccomandazioni_cantiere(statistiche_cantiere: Dict[str, Any]) -> List[str]:
    """Genera raccomandazioni basate sulle statistiche del cantiere."""
    raccomandazioni = []
    statistiche = statistiche_cantiere.get('statistiche_per_tipo', {})

    # Analizza ogni tipo di attività
    for tipo, stats in statistiche.items():
        if tipo == 'POSA':
            resa_media = stats.get('resa_media_metri_ora', 0)
            if resa_media < 60:
                raccomandazioni.append(f"📉 Resa posa media bassa ({resa_media:.1f} m/h). Considerare ottimizzazioni logistiche.")

        elif tipo.startswith('COLLEGAMENTO'):
            resa_media = stats.get('resa_media_per_squadra', 0)
            if resa_media < 0.8:
                raccomandazioni.append(f"🔧 Resa collegamenti bassa ({resa_media:.2f} coll/h). Verificare procedure e formazione.")

        elif tipo == 'CERTIFICAZIONE':
            conformita = stats.get('percentuale_conformita', 0)
            if conformita < 85:
                raccomandazioni.append(f"⚠️ Conformità certificazioni bassa ({conformita:.1f}%). Rivedere qualità installazioni.")

    # Raccomandazioni generali
    riepilogo = statistiche_cantiere.get('riepilogo_generale', {})
    responsabili_totali = riepilogo.get('responsabili_totali', 0)

    if responsabili_totali < 3:
        raccomandazioni.append("👥 Pochi responsabili attivi. Considerare formazione di nuovi team leader.")

    if not raccomandazioni:
        raccomandazioni.append("✅ Prestazioni generali buone. Mantenere gli standard attuali.")

    return raccomandazioni
