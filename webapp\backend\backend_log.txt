INFO:backend.database:Tentativo di connessione al database: postgresql://postgres:Taranto@localhost:5432/cantieri
INFO:backend.database:Connessione al database riuscita
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\_internal\_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pydantic\_internal\_config.py:373: UserWarning: Valid config keys have changed in V2:
* 'orm_mode' has been renamed to 'from_attributes'
  warnings.warn(message, UserWarning)
INFO:     Started server process [13244]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8001 (Press CTRL+C to quit)
INFO:backend.database:Nuova sessione del database creata
Stringa di connessione al database: postgresql://postgres:Taranto@localhost:5432/cantieri
Directory temporanea creata: C:\CMS\webapp\static\temp
Configurazione CORS con origini: ['http://localhost:3000', 'http://localhost:3001', 'http://localhost:8080', 'http://localhost', 'http://localhost:5500', 'http://127.0.0.1:5500', '*']
2025-06-16 20:44:14,084 INFO sqlalchemy.engine.Engine select pg_catalog.version()
INFO:sqlalchemy.engine.Engine:select pg_catalog.version()
2025-06-16 20:44:14,084 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-06-16 20:44:14,085 INFO sqlalchemy.engine.Engine select current_schema()
INFO:sqlalchemy.engine.Engine:select current_schema()
2025-06-16 20:44:14,086 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-06-16 20:44:14,086 INFO sqlalchemy.engine.Engine show standard_conforming_strings
INFO:sqlalchemy.engine.Engine:show standard_conforming_strings
2025-06-16 20:44:14,086 INFO sqlalchemy.engine.Engine [raw sql] {}
INFO:sqlalchemy.engine.Engine:[raw sql] {}
2025-06-16 20:44:14,087 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-16 20:44:14,090 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.username = %(username_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.username = %(username_1)s 
 LIMIT %(param_1)s
2025-06-16 20:44:14,090 INFO sqlalchemy.engine.Engine [generated in 0.00018s] {'username_1': 'a', 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[generated in 0.00018s] {'username_1': 'a', 'param_1': 1}
ERROR:backend.database:Errore durante l'utilizzo della sessione del database: 401: Username o password non corretti
INFO:backend.database:Sessione del database chiusa
DEBUG - Verifica password per utente: a
DEBUG - Password fornita: 'a'
DEBUG - Password hash nel DB: '$2b$12$6jJ2kk6/oAoNn4CN14H4l.LpPg03ciKSJhCUKWOqoA.wY31ZeOd7i'
DEBUG - Verifica password - Password inserita: 'a'
DEBUG - Hash nel DB (troncato): '$2b$12$6jJ2kk6/oAoNn4CN14H4l.L...'
Errore bcrypt: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
DEBUG - Autenticazione fallita: password non corretta
2025-06-16 20:44:14,297 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53717 - "POST /api/auth/login HTTP/1.1" 401 Unauthorized
INFO:backend.database:Nuova sessione del database creata
2025-06-16 20:44:20,593 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-16 20:44:20,594 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.username = %(username_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.username = %(username_1)s 
 LIMIT %(param_1)s
2025-06-16 20:44:20,594 INFO sqlalchemy.engine.Engine [cached since 6.504s ago] {'username_1': 'a', 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 6.504s ago] {'username_1': 'a', 'param_1': 1}
ERROR:backend.database:Errore durante l'utilizzo della sessione del database: 401: Username o password non corretti
INFO:backend.database:Sessione del database chiusa
DEBUG - Verifica password per utente: a
DEBUG - Password fornita: 'a'
DEBUG - Password hash nel DB: '$2b$12$6jJ2kk6/oAoNn4CN14H4l.LpPg03ciKSJhCUKWOqoA.wY31ZeOd7i'
DEBUG - Verifica password - Password inserita: 'a'
DEBUG - Hash nel DB (troncato): '$2b$12$6jJ2kk6/oAoNn4CN14H4l.L...'
Errore bcrypt: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
DEBUG - Autenticazione fallita: password non corretta
2025-06-16 20:44:20,817 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53720 - "POST /api/auth/login HTTP/1.1" 401 Unauthorized
INFO:backend.database:Nuova sessione del database creata
2025-06-16 20:44:26,720 INFO sqlalchemy.engine.Engine BEGIN (implicit)
INFO:sqlalchemy.engine.Engine:BEGIN (implicit)
2025-06-16 20:44:26,720 INFO sqlalchemy.engine.Engine SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.username = %(username_1)s 
 LIMIT %(param_1)s
INFO:sqlalchemy.engine.Engine:SELECT utenti.id_utente AS utenti_id_utente, utenti.username AS utenti_username, utenti.password AS utenti_password, utenti.password_plain AS utenti_password_plain, utenti.ruolo AS utenti_ruolo, utenti.data_scadenza AS utenti_data_scadenza, utenti.abilitato AS utenti_abilitato, utenti.created_by AS utenti_created_by, utenti.ragione_sociale AS utenti_ragione_sociale, utenti.indirizzo AS utenti_indirizzo, utenti.nazione AS utenti_nazione, utenti.email AS utenti_email, utenti.vat AS utenti_vat, utenti.referente_aziendale AS utenti_referente_aziendale, utenti.telefono AS utenti_telefono, utenti.cellulare AS utenti_cellulare 
FROM utenti 
WHERE utenti.username = %(username_1)s 
 LIMIT %(param_1)s
2025-06-16 20:44:26,720 INFO sqlalchemy.engine.Engine [cached since 12.63s ago] {'username_1': 'a', 'param_1': 1}
INFO:sqlalchemy.engine.Engine:[cached since 12.63s ago] {'username_1': 'a', 'param_1': 1}
ERROR:backend.database:Errore durante l'utilizzo della sessione del database: 401: Username o password non corretti
INFO:backend.database:Sessione del database chiusa
DEBUG - Verifica password per utente: a
DEBUG - Password fornita: 'a'
DEBUG - Password hash nel DB: '$2b$12$6jJ2kk6/oAoNn4CN14H4l.LpPg03ciKSJhCUKWOqoA.wY31ZeOd7i'
DEBUG - Verifica password - Password inserita: 'a'
DEBUG - Hash nel DB (troncato): '$2b$12$6jJ2kk6/oAoNn4CN14H4l.L...'
Errore bcrypt: 'charmap' codec can't encode character '\u2705' in position 0: character maps to <undefined>
DEBUG - Autenticazione fallita: password non corretta
2025-06-16 20:44:26,918 INFO sqlalchemy.engine.Engine ROLLBACK
INFO:sqlalchemy.engine.Engine:ROLLBACK
INFO:     127.0.0.1:53725 - "POST /api/auth/login HTTP/1.1" 401 Unauthorized
