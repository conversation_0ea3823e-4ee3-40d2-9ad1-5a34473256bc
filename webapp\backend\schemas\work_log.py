from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any
from datetime import datetime
from enum import Enum


class ActivityTypeEnum(str, Enum):
    """Enum per i tipi di attività."""
    POSA = "Posa"
    COLLEGAMENTO = "Collegamento"
    CERTIFICAZIONE = "Certificazione"


class EnvironmentalConditionsEnum(str, Enum):
    """Enum per le condizioni ambientali."""
    NORMALE = "Normale"
    SPAZI_RISTRETTI = "Spazi Ristretti"
    IN_ALTEZZA = "In Altezza"
    ESTERNO = "Esterno"


class ToolsUsedEnum(str, Enum):
    """Enum per gli strumenti utilizzati."""
    MANUALE = "Manuale"
    AUTOMATICO = "Automatico"


class WorkLogBase(BaseModel):
    """Schema base per i work logs."""
    operator_id: int = Field(..., description="ID dell'operatore")
    cable_type_id: Optional[int] = Field(None, description="ID del tipo di cavo")
    activity_type: ActivityTypeEnum = Field(..., description="Tipo di attività")
    sub_activity_detail: Optional[str] = Field(None, max_length=255, description="Dettaglio sub-attività")
    environmental_conditions: EnvironmentalConditionsEnum = Field(
        EnvironmentalConditionsEnum.NORMALE, 
        description="Condizioni ambientali"
    )
    tools_used: ToolsUsedEnum = Field(
        ToolsUsedEnum.MANUALE, 
        description="Strumenti utilizzati"
    )
    quantity: float = Field(..., gt=0, description="Quantità (metri per Posa, unità per Collegamento/Certificazione)")
    start_timestamp: datetime = Field(..., description="Timestamp di inizio")
    end_timestamp: datetime = Field(..., description="Timestamp di fine")
    duration_minutes: Optional[int] = Field(None, description="Durata in minuti (calcolata automaticamente)")
    number_of_operators_on_task: int = Field(1, ge=1, description="Numero di operatori sul task")
    notes: Optional[str] = Field(None, description="Note aggiuntive")
    id_cantiere: int = Field(..., description="ID del cantiere")

    @validator('end_timestamp')
    def validate_end_after_start(cls, v, values):
        """Valida che end_timestamp sia dopo start_timestamp."""
        if 'start_timestamp' in values and v <= values['start_timestamp']:
            raise ValueError('end_timestamp deve essere dopo start_timestamp')
        return v

    @validator('duration_minutes', always=True)
    def calculate_duration(cls, v, values):
        """Calcola automaticamente la durata se non fornita."""
        if v is None and 'start_timestamp' in values and 'end_timestamp' in values:
            start = values['start_timestamp']
            end = values['end_timestamp']
            if end > start:
                delta = end - start
                return int(delta.total_seconds() / 60)
        return v


class WorkLogCreate(WorkLogBase):
    """Schema per la creazione di un work log."""
    pass


class WorkLogUpdate(BaseModel):
    """Schema per l'aggiornamento di un work log."""
    operator_id: Optional[int] = None
    cable_type_id: Optional[int] = None
    activity_type: Optional[ActivityTypeEnum] = None
    sub_activity_detail: Optional[str] = None
    environmental_conditions: Optional[EnvironmentalConditionsEnum] = None
    tools_used: Optional[ToolsUsedEnum] = None
    quantity: Optional[float] = Field(None, gt=0)
    start_timestamp: Optional[datetime] = None
    end_timestamp: Optional[datetime] = None
    duration_minutes: Optional[int] = None
    number_of_operators_on_task: Optional[int] = Field(None, ge=1)
    notes: Optional[str] = None


class WorkLogInDB(WorkLogBase):
    """Schema per un work log nel database."""
    id: int
    created_at: datetime

    class Config:
        from_attributes = True


class WorkLogResponse(WorkLogInDB):
    """Schema per la risposta di un work log con dati calcolati."""
    productivity_per_hour: Optional[float] = Field(None, description="Produttività per ora")
    productivity_per_person_per_hour: Optional[float] = Field(None, description="Produttività per persona per ora")
    total_man_hours: Optional[float] = Field(None, description="Ore-uomo totali")


class ProductivityHistoricalRequest(BaseModel):
    """Schema per la richiesta di produttività storica."""
    cable_type_id: Optional[int] = None
    activity_type: Optional[ActivityTypeEnum] = None
    experience_level: Optional[str] = None
    environmental_conditions: Optional[EnvironmentalConditionsEnum] = None
    tools_used: Optional[ToolsUsedEnum] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    id_cantiere: Optional[int] = None


class ProductivityHistoricalResponse(BaseModel):
    """Schema per la risposta di produttività storica."""
    calculated_productivity: float = Field(..., description="Produttività calcolata")
    unit: str = Field(..., description="Unità di misura")
    total_quantity: float = Field(..., description="Quantità totale")
    total_man_hours: float = Field(..., description="Ore-uomo totali")
    number_of_logs: int = Field(..., description="Numero di log analizzati")
    filters_applied: Dict[str, Any] = Field(..., description="Filtri applicati")


class PredictionEstimationRequest(BaseModel):
    """Schema per la richiesta di stima predittiva."""
    cable_type_id: Optional[int] = None
    activity_type: ActivityTypeEnum = Field(..., description="Tipo di attività")
    quantity_required: float = Field(..., gt=0, description="Quantità richiesta")
    environmental_conditions: EnvironmentalConditionsEnum = Field(
        EnvironmentalConditionsEnum.NORMALE,
        description="Condizioni ambientali"
    )
    tools_used: ToolsUsedEnum = Field(
        ToolsUsedEnum.MANUALE,
        description="Strumenti utilizzati"
    )
    number_of_operators: int = Field(1, ge=1, description="Numero di operatori")
    experience_level: Optional[str] = Field(None, description="Livello di esperienza")


class PredictionEstimationResponse(BaseModel):
    """Schema per la risposta di stima predittiva."""
    inputs: PredictionEstimationRequest = Field(..., description="Input forniti")
    base_productivity: float = Field(..., description="Produttività base")
    expected_productivity_per_operator: float = Field(..., description="Produttività attesa per operatore")
    estimated_total_man_hours: float = Field(..., description="Ore-uomo stimate totali")
    estimated_time_for_team_hours: float = Field(..., description="Tempo stimato per il team in ore")
    correction_factors: Dict[str, float] = Field(..., description="Fattori di correzione applicati")


class WorkLogListResponse(BaseModel):
    """Schema per la lista di work logs."""
    work_logs: list[WorkLogResponse]
    total_count: int
    page: int
    per_page: int
