(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4722],{5588:(e,t,o)=>{"use strict";o.r(t),o.d(t,{default:()=>s});var n=o(95155),r=o(12115),a=o(35695);function s(){let[e,t]=(0,r.useState)(""),[o,s]=(0,r.useState)(!1),i=(0,a.useRouter)(),c=async()=>{s(!0),t("Starting login test...\n");try{t(e=>e+"Step 1: Testing basic fetch...\n");let e=new FormData;e.append("username","admin"),e.append("password","admin"),t(e=>e+"Step 2: Sending login request...\n");let o=await fetch("http://localhost:8001/api/auth/login",{method:"POST",body:e});if(t(e=>e+"Step 3: Response status: ".concat(o.status,"\n")),!o.ok)throw Error("HTTP error! status: ".concat(o.status));let n=await o.json();t(e=>e+"Step 4: Response data received\n"),t(e=>e+"Data: ".concat(JSON.stringify(n,null,2),"\n")),t(e=>e+"Step 5: Saving token to localStorage...\n"),localStorage.setItem("token",n.access_token);let r=localStorage.getItem("token");t(e=>e+"Step 6: Token saved: ".concat(r?"YES":"NO","\n")),t(e=>e+"Step 7: Testing router...\n"),"owner"===n.role&&(t(e=>e+"Step 8: Redirecting to /admin...\n"),setTimeout(()=>{t(e=>e+"Step 9: Using router.push...\n"),i.push("/admin")},1e3),setTimeout(()=>{t(e=>e+"Step 10: Using window.location...\n"),window.location.href="/admin"},3e3))}catch(e){t(t=>t+"ERROR: ".concat(e.message,"\n")),console.error("Login test error:",e)}finally{s(!1)}};return(0,n.jsxs)("div",{style:{padding:"20px",fontFamily:"monospace"},children:[(0,n.jsx)("h1",{children:"Debug Login Page"}),(0,n.jsxs)("div",{style:{marginBottom:"20px"},children:[(0,n.jsx)("button",{onClick:c,disabled:o,style:{padding:"10px 20px",marginRight:"10px",backgroundColor:"#007bff",color:"white",border:"none",borderRadius:"5px",cursor:o?"not-allowed":"pointer"},children:o?"Testing...":"Full Login Test"}),(0,n.jsx)("button",{onClick:()=>{t("Testing router directly...\n");try{i.push("/admin"),t(e=>e+"Router.push called successfully\n")}catch(e){t(t=>t+"Router error: ".concat(e.message,"\n"))}},style:{padding:"10px 20px",marginRight:"10px",backgroundColor:"#28a745",color:"white",border:"none",borderRadius:"5px",cursor:"pointer"},children:"Test Router Only"}),(0,n.jsx)("button",{onClick:()=>{t("Testing window.location...\n"),window.location.href="/admin"},style:{padding:"10px 20px",marginRight:"10px",backgroundColor:"#ffc107",color:"black",border:"none",borderRadius:"5px",cursor:"pointer"},children:"Test Window.location"}),(0,n.jsx)("button",{onClick:()=>{localStorage.clear(),t("LocalStorage cleared\n")},style:{padding:"10px 20px",backgroundColor:"#dc3545",color:"white",border:"none",borderRadius:"5px",cursor:"pointer"},children:"Clear Storage"})]}),(0,n.jsx)("div",{style:{backgroundColor:"#f8f9fa",padding:"15px",borderRadius:"5px",whiteSpace:"pre-wrap",minHeight:"400px",fontSize:"12px",fontFamily:"monospace",border:"1px solid #ddd"},children:e||"Click a button to start testing..."}),(0,n.jsxs)("div",{style:{marginTop:"20px"},children:[(0,n.jsx)("h3",{children:"Current localStorage:"}),(0,n.jsx)("div",{style:{backgroundColor:"#f8f9fa",padding:"10px",borderRadius:"5px",fontSize:"12px",fontFamily:"monospace"},children:Object.keys(localStorage).map(e=>{var t;return"".concat(e,": ").concat(null==(t=localStorage.getItem(e))?void 0:t.substring(0,50),"...")}).join("\n")||"Empty"})]})]})}},35695:(e,t,o)=>{"use strict";var n=o(18999);o.o(n,"useParams")&&o.d(t,{useParams:function(){return n.useParams}}),o.o(n,"usePathname")&&o.d(t,{usePathname:function(){return n.usePathname}}),o.o(n,"useRouter")&&o.d(t,{useRouter:function(){return n.useRouter}}),o.o(n,"useSearchParams")&&o.d(t,{useSearchParams:function(){return n.useSearchParams}})},63885:(e,t,o)=>{Promise.resolve().then(o.bind(o,5588))}},e=>{var t=t=>e(e.s=t);e.O(0,[8441,1684,7358],()=>t(63885)),_N_E=e.O()}]);