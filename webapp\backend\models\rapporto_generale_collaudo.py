from sqlalchemy import Column, Integer, String, Date, Text, Boolean, Float, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from backend.database import Base

class RapportoGeneraleCollaudo(Base):
    """
    Modello SQLAlchemy per la tabella RapportiGeneraliCollaudo.
    Gestisce i rapporti generali di collaudo per conformità CEI 64-8.
    """
    __tablename__ = "rapportigeneralicollaudo"
    
    id_rapporto = Column(Integer, primary_key=True, index=True)
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)
    numero_rapporto = Column(String, nullable=False, unique=True)
    data_rapporto = Column(Date, nullable=False)
    
    # Dati Progetto/Commessa
    nome_progetto = Column(String, nullable=True)
    codice_progetto = Column(String, nullable=True)
    cliente_finale = Column(String, nullable=True)
    localita_impianto = Column(String, nullable=True)
    societa_installatrice = Column(String, nullable=True)
    societa_responsabile_prove = Column(String, nullable=True)
    data_inizio_collaudo = Column(Date, nullable=True)
    data_fine_collaudo = Column(Date, nullable=True)
    
    # Riferimenti Normativi (JSON per flessibilità)
    normative_applicate = Column(Text, nullable=True)  # JSON string
    documentazione_progetto = Column(Text, nullable=True)  # JSON string
    
    # Scopo e Ambito
    scopo_rapporto = Column(Text, nullable=True)
    ambito_collaudo = Column(Text, nullable=True)
    
    # Condizioni Ambientali
    temperatura_ambiente = Column(Float, nullable=True)
    umidita_ambiente = Column(Float, nullable=True)
    
    # Riepilogo
    numero_cavi_totali = Column(Integer, default=0)
    numero_cavi_conformi = Column(Integer, default=0)
    numero_cavi_non_conformi = Column(Integer, default=0)
    
    # Personale
    responsabile_tecnico = Column(String, nullable=True)
    rappresentante_cliente = Column(String, nullable=True)
    
    # Stato e Conclusioni
    stato_rapporto = Column(String, default='BOZZA')  # 'BOZZA', 'COMPLETATO', 'APPROVATO'
    conclusioni = Column(Text, nullable=True)
    dichiarazione_conformita = Column(Boolean, default=False)
    
    # Metadati
    timestamp_creazione = Column(DateTime, default=func.now())
    timestamp_modifica = Column(DateTime, nullable=True)
    
    # Relazioni
    cantiere = relationship("Cantiere", backref="rapporti_collaudo")
    certificazioni = relationship("CertificazioneCavo", back_populates="rapporto_generale")
    non_conformita = relationship("NonConformita", back_populates="rapporto")
