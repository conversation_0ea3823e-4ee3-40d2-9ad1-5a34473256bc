# =====================================================
# CONFIGURAZIONE EMAIL PER GESTIONE PASSWORD
# =====================================================

# Provider email (gmail, outlook, smtp_custom)
EMAIL_PROVIDER=gmail

# === CONFIGURAZIONE GMAIL ===
# Usa App Password, non la password normale dell'account
# Guida: https://support.google.com/accounts/answer/185833
GMAIL_USERNAME=<EMAIL>
GMAIL_APP_PASSWORD=your-16-char-app-password
GMAIL_FROM_EMAIL=<EMAIL>

# === CONFIGURAZIONE OUTLOOK ===
OUTLOOK_USERNAME=<EMAIL>
OUTLOOK_PASSWORD=your-password
OUTLOOK_FROM_EMAIL=<EMAIL>

# === CONFIGURAZIONE SMTP PERSONALIZZATO ===
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USE_TLS=true
SMTP_USE_SSL=false
SMTP_USERNAME=your-username
SMTP_PASSWORD=your-password
SMTP_FROM_EMAIL=<EMAIL>

# === CONFIGURAZIONI GENERALI ===
EMAIL_FROM_NAME=CMS Sistema
EMAIL_TESTING_MODE=false  # Se true, stampa email invece di inviarle

# =====================================================
# CONFIGURAZIONE DATABASE
# =====================================================

# Database PostgreSQL
DATABASE_URL=postgresql://username:password@localhost:5432/cms_db

# =====================================================
# CONFIGURAZIONE SICUREZZA
# =====================================================

# Chiave segreta per JWT e token (genera una chiave sicura)
SECRET_KEY=your-super-secret-key-change-this-in-production

# Configurazione CORS
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# =====================================================
# CONFIGURAZIONE API
# =====================================================

# URL base dell'API
API_PREFIX=/api

# Directory per file statici
STATIC_DIR=./static

# =====================================================
# CONFIGURAZIONE FRONTEND
# =====================================================

# URL del backend per il frontend Next.js
NEXT_PUBLIC_API_URL=http://localhost:8001

# =====================================================
# CONFIGURAZIONE LOGGING
# =====================================================

# Livello di log (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Directory per i log
LOG_DIR=./logs

# =====================================================
# CONFIGURAZIONE SICUREZZA PASSWORD
# =====================================================

# Durata token reset password (minuti)
PASSWORD_RESET_TOKEN_EXPIRY=30

# Numero massimo tentativi login
MAX_LOGIN_ATTEMPTS=5

# Durata blocco account (minuti)
ACCOUNT_LOCKOUT_DURATION=15

# Rate limiting - finestra temporale (secondi)
RATE_LIMIT_WINDOW=300

# Rate limiting - max richieste per finestra
RATE_LIMIT_MAX_REQUESTS=10
