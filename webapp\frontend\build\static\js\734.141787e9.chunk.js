"use strict";(self.webpackChunkcms_frontend=self.webpackChunkcms_frontend||[]).push([[734,894],{1894:(e,i,r)=>{r.d(i,{apiService:()=>d});var n=r(8816),t=r(173),o=r(561),a=r(7079),s=r(9678),l=r(2043),c=r(184);const d={login:n.A.login,logout:n.A.logout,getCurrentUser:n.A.getCurrentUser,getCantieri:t.A.getMyCantieri,getCantiere:t.A.getCantiere,createCantiere:t.A.createCantiere,deleteCantiere:t.A.deleteCantiere,getCavi:o.A.getCavi,getCavo:o.A.getCavo,createCavo:o.A.createCavo,updateCavo:o.A.updateCavo,deleteCavo:o.A.deleteCavo,aggiornaCavo:o.A.aggiornaCavo,getCertificazioni:a.A.getCertificazioni,getCertificazione:a.A.getCertificazione,createCertificazione:a.A.createCertificazione,updateCertificazione:a.A.updateCertificazione,deleteCertificazione:a.A.deleteCertificazione,getStrumenti:a.A.getStrumenti,createStrumento:a.A.createStrumento,updateStrumento:a.A.updateStrumento,deleteStrumento:a.A.deleteStrumento,getParcoCavi:s.A.getParcoCavi,createBobina:s.A.createBobina,updateBobina:s.A.updateBobina,deleteBobina:s.A.deleteBobina,generateTemplate:l.default.generateTemplate,importExcel:l.default.importExcel,getReports:c.A.getReports}},4734:(e,i,r)=>{r.r(i),r.d(i,{default:()=>b});var n=r(5043),t=r(3336),o=r(5865),a=r(9650),s=r(1806),l=r(4882),c=r(8076),d=r(39),x=r(3460),h=r(3845),A=r(6446),j=r(7392),u=r(35),g=r(6600),m=r(5316),z=r(8903),v=r(9347),C=r(1906),f=r(3632),p=r(3560),_=r(3768),y=r(1894),S=r(579);const b=function(e){let{certificazioni:i,onEdit:r,onDelete:b,cantiereId:k}=e;const[D,E]=(0,n.useState)(null),[I,M]=(0,n.useState)(!1),[N,T]=(0,n.useState)(!1),[w,B]=(0,n.useState)(null),[F,L]=(0,n.useState)(!1),P=e=>e?new Date(e).toLocaleDateString("it-IT"):"-",R=e=>{if(!e)return"default";const i=parseFloat(e);return i>=500?"success":i>=100?"warning":"error"};return 0===i.length?(0,S.jsxs)(t.A,{sx:{p:3,textAlign:"center"},children:[(0,S.jsx)(o.A,{variant:"h6",color:"text.secondary",children:"Nessuna certificazione trovata"}),(0,S.jsx)(o.A,{variant:"body2",color:"text.secondary",sx:{mt:1},children:'Clicca su "Nuova Certificazione" per aggiungere la prima certificazione'})]}):(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(a.A,{component:t.A,children:(0,S.jsxs)(s.A,{children:[(0,S.jsx)(l.A,{children:(0,S.jsxs)(c.A,{children:[(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"N\xb0 Certificato"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"ID Cavo"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Tipologia"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Sezione"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Data"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Operatore"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Isolamento (M\u03a9)"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Lunghezza (m)"})}),(0,S.jsx)(d.A,{children:(0,S.jsx)("strong",{children:"Azioni"})})]})}),(0,S.jsx)(x.A,{children:i.map((e=>(0,S.jsxs)(c.A,{hover:!0,children:[(0,S.jsx)(d.A,{children:(0,S.jsx)(o.A,{variant:"body2",fontWeight:"bold",children:e.numero_certificato})}),(0,S.jsx)(d.A,{children:(0,S.jsx)(o.A,{variant:"body2",fontFamily:"monospace",children:e.id_cavo})}),(0,S.jsx)(d.A,{children:e.cavo_tipologia||"-"}),(0,S.jsx)(d.A,{children:e.cavo_sezione||"-"}),(0,S.jsx)(d.A,{children:P(e.data_certificazione)}),(0,S.jsx)(d.A,{children:e.id_operatore||"-"}),(0,S.jsx)(d.A,{children:(0,S.jsx)(h.A,{label:e.valore_isolamento||"-",color:R(e.valore_isolamento),size:"small"})}),(0,S.jsx)(d.A,{children:e.lunghezza_misurata?e.lunghezza_misurata.toFixed(2):"-"}),(0,S.jsx)(d.A,{children:(0,S.jsxs)(A.A,{sx:{display:"flex",gap:.5},children:[(0,S.jsx)(j.A,{size:"small",onClick:()=>(async e=>{try{L(!0);const i=await y.apiService.getCertificazione(k,e.id_certificazione);E(i),M(!0)}catch(i){console.error("Errore nel caricamento dei dettagli:",i)}finally{L(!1)}})(e),title:"Visualizza dettagli",children:(0,S.jsx)(f.A,{fontSize:"small"})}),(0,S.jsx)(j.A,{size:"small",onClick:()=>r(e),title:"Modifica",children:(0,S.jsx)(p.A,{fontSize:"small"})}),(0,S.jsx)(j.A,{size:"small",onClick:()=>(B(e),void T(!0)),title:"Elimina",color:"error",children:(0,S.jsx)(_.A,{fontSize:"small"})})]})})]},e.id_certificazione)))})]})}),(0,S.jsxs)(u.A,{open:I,onClose:()=>M(!1),maxWidth:"md",fullWidth:!0,children:[(0,S.jsxs)(g.A,{children:["Dettagli Certificazione ",null===D||void 0===D?void 0:D.numero_certificato]}),(0,S.jsx)(m.A,{children:D&&(0,S.jsxs)(z.Ay,{container:!0,spacing:2,sx:{mt:1},children:[(0,S.jsxs)(z.Ay,{item:!0,xs:12,md:6,children:[(0,S.jsx)(o.A,{variant:"subtitle2",color:"text.secondary",children:"Informazioni Cavo"}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"ID Cavo:"})," ",D.id_cavo]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Tipologia:"})," ",D.cavo_tipologia||"-"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Sezione:"})," ",D.cavo_sezione||"-"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Partenza:"})," ",D.cavo_ubicazione_partenza||"-"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Arrivo:"})," ",D.cavo_ubicazione_arrivo||"-"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Metri Teorici:"})," ",D.cavo_metri_teorici||"-"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Stato:"})," ",D.cavo_stato_installazione||"-"]})]}),(0,S.jsxs)(z.Ay,{item:!0,xs:12,md:6,children:[(0,S.jsx)(o.A,{variant:"subtitle2",color:"text.secondary",children:"Informazioni Certificazione"}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"N\xb0 Certificato:"})," ",D.numero_certificato]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Data:"})," ",P(D.data_certificazione)]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Operatore:"})," ",D.id_operatore||"-"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Lunghezza Misurata:"})," ",D.lunghezza_misurata?`${D.lunghezza_misurata.toFixed(2)} m`:"-"]})]}),(0,S.jsxs)(z.Ay,{item:!0,xs:12,md:6,children:[(0,S.jsx)(o.A,{variant:"subtitle2",color:"text.secondary",children:"Valori di Test"}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Continuit\xe0:"})," ",D.valore_continuita||"-"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Isolamento:"})," ",D.valore_isolamento||"-"," M\u03a9"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Resistenza:"})," ",D.valore_resistenza||"-"]})]}),(0,S.jsxs)(z.Ay,{item:!0,xs:12,md:6,children:[(0,S.jsx)(o.A,{variant:"subtitle2",color:"text.secondary",children:"Strumento Utilizzato"}),D.strumento_nome?(0,S.jsxs)(S.Fragment,{children:[(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Nome:"})," ",D.strumento_nome]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Marca:"})," ",D.strumento_marca||"-"]}),(0,S.jsxs)(o.A,{children:[(0,S.jsx)("strong",{children:"Modello:"})," ",D.strumento_modello||"-"]})]}):(0,S.jsx)(o.A,{children:D.strumento_utilizzato||"Non specificato"})]}),D.note&&(0,S.jsxs)(z.Ay,{item:!0,xs:12,children:[(0,S.jsx)(o.A,{variant:"subtitle2",color:"text.secondary",children:"Note"}),(0,S.jsx)(o.A,{children:D.note})]})]})}),(0,S.jsx)(v.A,{children:(0,S.jsx)(C.A,{onClick:()=>M(!1),children:"Chiudi"})})]}),(0,S.jsxs)(u.A,{open:N,onClose:()=>T(!1),children:[(0,S.jsx)(g.A,{children:"Conferma Eliminazione"}),(0,S.jsxs)(m.A,{children:[(0,S.jsxs)(o.A,{children:["Sei sicuro di voler eliminare la certificazione ",null===w||void 0===w?void 0:w.numero_certificato,"?"]}),(0,S.jsx)(o.A,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Questa operazione non pu\xf2 essere annullata."})]}),(0,S.jsxs)(v.A,{children:[(0,S.jsx)(C.A,{onClick:()=>T(!1),children:"Annulla"}),(0,S.jsx)(C.A,{onClick:async()=>{try{L(!0),await y.apiService.deleteCertificazione(k,w.id_certificazione),T(!1),B(null),b()}catch(e){console.error("Errore nell'eliminazione:",e)}finally{L(!1)}},color:"error",disabled:F,children:"Elimina"})]})]})]})}}}]);
//# sourceMappingURL=734.141787e9.chunk.js.map