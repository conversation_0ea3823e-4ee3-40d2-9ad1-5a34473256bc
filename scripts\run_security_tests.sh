#!/bin/bash

# 🔒 Script per Eseguire Test di Sicurezza Completi
# Esegue tutti i test di sicurezza e penetration testing

set -e  # Exit on any error

# Colori per output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configurazione
BACKEND_URL=${BACKEND_URL:-"http://localhost:8001"}
FRONTEND_URL=${FRONTEND_URL:-"http://localhost:3000"}
TEST_EMAIL=${TEST_EMAIL:-"<EMAIL>"}
REPORT_DIR="security_reports"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

echo -e "${BLUE}🔒 CMS Security Testing Suite${NC}"
echo -e "${BLUE}================================${NC}"
echo "Backend URL: $BACKEND_URL"
echo "Frontend URL: $FRONTEND_URL"
echo "Timestamp: $TIMESTAMP"
echo ""

# Crea directory per i report
mkdir -p "$REPORT_DIR"

# Funzione per logging
log() {
    echo -e "${GREEN}[$(date +'%H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%H:%M:%S')] ERROR: $1${NC}"
}

# Funzione per verificare se il servizio è attivo
check_service() {
    local url=$1
    local name=$2
    
    log "Verificando $name su $url..."
    
    if curl -s --max-time 10 "$url/health" > /dev/null 2>&1 || \
       curl -s --max-time 10 "$url" > /dev/null 2>&1; then
        log "✅ $name è attivo"
        return 0
    else
        error "❌ $name non è raggiungibile su $url"
        return 1
    fi
}

# Funzione per eseguire test Python
run_python_tests() {
    log "🧪 Eseguendo test di sicurezza Python..."
    
    cd webapp/backend
    
    # Test unitari di sicurezza
    if python -m pytest tests/test_password_security.py -v --tb=short > "$REPORT_DIR/unit_tests_$TIMESTAMP.log" 2>&1; then
        log "✅ Test unitari di sicurezza completati"
    else
        warn "⚠️ Alcuni test unitari sono falliti - controlla il log"
    fi
    
    cd ../..
}

# Funzione per eseguire penetration testing
run_penetration_tests() {
    log "🎯 Eseguendo penetration testing..."
    
    cd webapp/backend
    
    # Penetration testing
    if python tests/security_penetration_test.py "$BACKEND_URL" > "$REPORT_DIR/penetration_test_$TIMESTAMP.log" 2>&1; then
        log "✅ Penetration testing completato"
    else
        warn "⚠️ Penetration testing ha rilevato problemi - controlla il log"
    fi
    
    cd ../..
}

# Funzione per test di carico
run_load_tests() {
    log "⚡ Eseguendo test di carico..."
    
    # Test di carico con curl
    local endpoint="$BACKEND_URL/api/password/validate-password"
    local concurrent_requests=50
    local total_requests=500
    
    log "Testing endpoint: $endpoint"
    log "Richieste concorrenti: $concurrent_requests"
    log "Richieste totali: $total_requests"
    
    # Crea script temporaneo per test di carico
    cat > /tmp/load_test.sh << 'EOF'
#!/bin/bash
endpoint=$1
for i in {1..10}; do
    curl -s -X POST "$endpoint" \
         -H "Content-Type: application/json" \
         -d '{"password":"TestPassword123!"}' \
         -w "%{http_code},%{time_total}\n" \
         -o /dev/null &
done
wait
EOF
    
    chmod +x /tmp/load_test.sh
    
    # Esegui test di carico
    log "Avvio test di carico..."
    start_time=$(date +%s)
    
    for batch in {1..5}; do
        /tmp/load_test.sh "$endpoint" >> "$REPORT_DIR/load_test_$TIMESTAMP.log" &
    done
    
    wait
    
    end_time=$(date +%s)
    duration=$((end_time - start_time))
    
    log "✅ Test di carico completato in ${duration}s"
    
    # Analizza risultati
    if [ -f "$REPORT_DIR/load_test_$TIMESTAMP.log" ]; then
        local success_count=$(grep -c "200," "$REPORT_DIR/load_test_$TIMESTAMP.log" || echo "0")
        local total_count=$(wc -l < "$REPORT_DIR/load_test_$TIMESTAMP.log")
        local success_rate=$((success_count * 100 / total_count))
        
        log "Tasso di successo: $success_rate% ($success_count/$total_count)"
        
        if [ $success_rate -lt 95 ]; then
            warn "Tasso di successo basso: $success_rate%"
        fi
    fi
    
    rm -f /tmp/load_test.sh
}

# Funzione per test di sicurezza database
run_database_security_tests() {
    log "🗄️ Eseguendo test di sicurezza database..."
    
    # Test connessioni e permessi
    cat > /tmp/db_security_test.sql << 'EOF'
-- Test sicurezza database
\echo 'Testing database security...'

-- Verifica tabelle di sicurezza esistano
SELECT 'security_events table exists' as test, 
       CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'security_events') 
            THEN 'PASS' ELSE 'FAIL' END as result;

SELECT 'rate_limiting table exists' as test,
       CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'rate_limiting') 
            THEN 'PASS' ELSE 'FAIL' END as result;

SELECT 'password_reset_tokens table exists' as test,
       CASE WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'password_reset_tokens') 
            THEN 'PASS' ELSE 'FAIL' END as result;

-- Verifica indici di sicurezza
SELECT 'security indexes exist' as test,
       CASE WHEN COUNT(*) >= 3 
            THEN 'PASS' ELSE 'FAIL' END as result
FROM pg_indexes 
WHERE tablename IN ('security_events', 'rate_limiting', 'password_reset_tokens');

-- Verifica trigger di audit
SELECT 'audit triggers exist' as test,
       CASE WHEN COUNT(*) >= 2 
            THEN 'PASS' ELSE 'FAIL' END as result
FROM pg_trigger t
JOIN pg_class c ON t.tgrelid = c.oid
WHERE c.relname IN ('utenti', 'cantieri') 
  AND t.tgname LIKE '%updated_at%';

-- Test funzioni di cleanup
SELECT 'cleanup functions exist' as test,
       CASE WHEN COUNT(*) >= 3 
            THEN 'PASS' ELSE 'FAIL' END as result
FROM pg_proc 
WHERE proname IN ('cleanup_expired_tokens', 'cleanup_old_rate_limits', 'cleanup_old_security_events');

\echo 'Database security tests completed.'
EOF
    
    # Esegui test database
    if psql -d cantieri -f /tmp/db_security_test.sql > "$REPORT_DIR/db_security_$TIMESTAMP.log" 2>&1; then
        log "✅ Test sicurezza database completati"
        
        # Conta i test passati
        local passed=$(grep -c "PASS" "$REPORT_DIR/db_security_$TIMESTAMP.log" || echo "0")
        local failed=$(grep -c "FAIL" "$REPORT_DIR/db_security_$TIMESTAMP.log" || echo "0")
        
        log "Database tests: $passed passed, $failed failed"
        
        if [ $failed -gt 0 ]; then
            warn "Alcuni test database sono falliti"
        fi
    else
        error "❌ Errore nei test database"
    fi
    
    rm -f /tmp/db_security_test.sql
}

# Funzione per test configurazione email
test_email_configuration() {
    log "📧 Testando configurazione email..."
    
    cd webapp/backend
    
    # Test configurazione email
    python << 'EOF' > "$REPORT_DIR/email_test_$TIMESTAMP.log" 2>&1
try:
    from core.email_config import email_config_manager
    
    print("Testing email configuration...")
    
    # Test configurazione
    config = email_config_manager.get_current_config()
    print(f"✅ Email provider: {email_config_manager.current_provider}")
    print(f"✅ SMTP host: {config.host}")
    print(f"✅ SMTP port: {config.port}")
    print(f"✅ From email: {config.from_email}")
    
    # Test connessione
    success, message = email_config_manager.test_connection()
    if success:
        print(f"✅ SMTP connection: {message}")
    else:
        print(f"❌ SMTP connection failed: {message}")
    
    print("Email configuration test completed.")
    
except Exception as e:
    print(f"❌ Email test error: {e}")
EOF
    
    if grep -q "✅ SMTP connection:" "$REPORT_DIR/email_test_$TIMESTAMP.log"; then
        log "✅ Configurazione email OK"
    else
        warn "⚠️ Problemi con configurazione email"
    fi
    
    cd ../..
}

# Funzione per generare report finale
generate_final_report() {
    log "📊 Generando report finale..."
    
    local report_file="$REPORT_DIR/security_report_$TIMESTAMP.md"
    
    cat > "$report_file" << EOF
# 🔒 Security Test Report

**Data**: $(date)
**Timestamp**: $TIMESTAMP
**Backend URL**: $BACKEND_URL
**Frontend URL**: $FRONTEND_URL

## 📋 Test Eseguiti

### ✅ Test Completati
EOF
    
    # Aggiungi risultati dei test
    for log_file in "$REPORT_DIR"/*_"$TIMESTAMP".log; do
        if [ -f "$log_file" ]; then
            local test_name=$(basename "$log_file" | sed "s/_$TIMESTAMP.log//")
            echo "- $test_name" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

## 📊 Riassunto

### Database Security
$(grep -h "PASS\|FAIL" "$REPORT_DIR/db_security_$TIMESTAMP.log" 2>/dev/null | head -10 || echo "No database tests found")

### Load Testing
$(tail -5 "$REPORT_DIR/load_test_$TIMESTAMP.log" 2>/dev/null || echo "No load test results found")

### Email Configuration
$(grep -h "✅\|❌" "$REPORT_DIR/email_test_$TIMESTAMP.log" 2>/dev/null | head -5 || echo "No email test results found")

## 🔗 File di Log

EOF
    
    # Lista tutti i file di log generati
    for log_file in "$REPORT_DIR"/*_"$TIMESTAMP".log; do
        if [ -f "$log_file" ]; then
            echo "- $(basename "$log_file")" >> "$report_file"
        fi
    done
    
    cat >> "$report_file" << EOF

## 🎯 Raccomandazioni

1. Controlla tutti i log per dettagli specifici
2. Risolvi eventuali test falliti prima del deployment
3. Monitora le performance dopo il deployment
4. Esegui questi test regolarmente (settimanalmente)

---
*Report generato automaticamente da CMS Security Testing Suite*
EOF
    
    log "✅ Report finale generato: $report_file"
}

# Main execution
main() {
    log "🚀 Avvio test di sicurezza completi..."
    
    # Verifica servizi
    if ! check_service "$BACKEND_URL" "Backend"; then
        error "Backend non raggiungibile. Avvia il servizio e riprova."
        exit 1
    fi
    
    # Esegui tutti i test
    run_python_tests
    run_penetration_tests
    run_load_tests
    run_database_security_tests
    test_email_configuration
    
    # Genera report finale
    generate_final_report
    
    log "🎉 Test di sicurezza completati!"
    log "📁 Report disponibili in: $REPORT_DIR/"
    
    # Mostra riassunto
    echo ""
    echo -e "${BLUE}📊 RIASSUNTO RAPIDO${NC}"
    echo -e "${BLUE}===================${NC}"
    
    local total_logs=$(ls "$REPORT_DIR"/*_"$TIMESTAMP".log 2>/dev/null | wc -l)
    echo "Test eseguiti: $total_logs"
    
    if grep -q "FAIL" "$REPORT_DIR"/*_"$TIMESTAMP".log 2>/dev/null; then
        warn "⚠️ Alcuni test sono falliti - controlla i log"
    else
        log "✅ Tutti i test sono passati"
    fi
    
    echo ""
    echo -e "${YELLOW}Prossimi passi:${NC}"
    echo "1. Controlla il report finale: $REPORT_DIR/security_report_$TIMESTAMP.md"
    echo "2. Risolvi eventuali problemi identificati"
    echo "3. Esegui nuovamente i test se necessario"
    echo "4. Procedi con il deployment se tutto è OK"
}

# Gestione segnali
trap 'error "Test interrotti"; exit 1' INT TERM

# Esegui main
main "$@"
