from sqlalchemy import Column, Integer, String, <PERSON>olean, DateTime, ForeignKey
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from backend.database import Base

class Responsabile(Base):
    """
    Modello SQLAlchemy per la tabella responsabili.
    Gestisce i responsabili delle comande con dati di contatto per cantiere specifico.
    """
    __tablename__ = "responsabili"

    id_responsabile = Column(Integer, primary_key=True, index=True)
    nome_responsabile = Column(String(255), nullable=False)
    telefono = Column(String(20), nullable=True)
    email = Column(String(255), nullable=True)
    experience_level = Column(String(20), nullable=False, default='Senior')  # 'Junior', 'Senior', 'Apprentice'
    id_cantiere = Column(Integer, ForeignKey("cantieri.id_cantiere"), nullable=False)
    data_creazione = Column(DateTime, nullable=False, default=func.now())
    attivo = Column(Boolean, default=True)

    # Relazioni
    cantiere = relationship("Cantiere", backref="responsabili")
