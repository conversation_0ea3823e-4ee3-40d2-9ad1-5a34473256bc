#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Modulo per la gestione degli strumenti certificati utilizzati nei cantieri.
"""

import logging
import os
from datetime import datetime, date, timedelta
from typing import List, Dict, Any, Optional, Tuple
from modules.database_pg import database_connection

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def crea_strumento(id_cantiere: int) -> bool:
    """
    Crea un nuovo strumento certificato nel database.

    Args:
        id_cantiere: ID del cantiere

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        print("\n🔧 CREAZIONE NUOVO STRUMENTO CERTIFICATO")

        # Richiedi i dati dello strumento
        nome = input("Nome dello strumento: ").strip()
        if not nome:
            print("❌ Il nome dello strumento è obbligatorio!")
            return False

        marca = input("Marca dello strumento: ").strip()
        if not marca:
            print("❌ La marca dello strumento è obbligatoria!")
            return False

        modello = input("Modello dello strumento: ").strip()
        if not modello:
            print("❌ Il modello dello strumento è obbligatorio!")
            return False

        numero_serie = input("Numero di serie dello strumento: ").strip()
        if not numero_serie:
            print("❌ Il numero di serie dello strumento è obbligatorio!")
            return False

        # Verifica se lo strumento esiste già
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT id_strumento FROM StrumentiCertificati
                WHERE id_cantiere = ? AND numero_serie = ?
            """, (id_cantiere, numero_serie))

            if c.fetchone():
                print(f"❌ Uno strumento con numero di serie {numero_serie} esiste già nel cantiere!")
                return False

        # Richiedi la data di calibrazione
        data_calibrazione_str = input("Data di calibrazione (YYYY-MM-DD): ").strip()
        try:
            data_calibrazione = datetime.strptime(data_calibrazione_str, "%Y-%m-%d").date()
        except ValueError:
            print("❌ Formato data non valido! Usa YYYY-MM-DD")
            return False

        # Richiedi la data di scadenza della calibrazione
        data_scadenza_str = input("Data di scadenza della calibrazione (YYYY-MM-DD): ").strip()
        try:
            data_scadenza = datetime.strptime(data_scadenza_str, "%Y-%m-%d").date()
        except ValueError:
            print("❌ Formato data non valido! Usa YYYY-MM-DD")
            return False

        # Verifica che la data di scadenza sia successiva alla data di calibrazione
        if data_scadenza <= data_calibrazione:
            print("❌ La data di scadenza deve essere successiva alla data di calibrazione!")
            return False

        # Richiedi il percorso del certificato di calibrazione
        certificato_calibrazione = input("Percorso del certificato di calibrazione (opzionale): ").strip()

        # Richiedi le note
        note = input("Note (opzionale): ").strip()

        # Inserisci lo strumento nel database
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                INSERT INTO StrumentiCertificati (
                    id_cantiere, nome, marca, modello, numero_serie,
                    data_calibrazione, data_scadenza_calibrazione,
                    certificato_calibrazione, note, timestamp_modifica
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                id_cantiere, nome, marca, modello, numero_serie,
                data_calibrazione.isoformat(), data_scadenza.isoformat(),
                certificato_calibrazione, note, datetime.now().isoformat()
            ))

            print(f"\n✅ Strumento {nome} {marca} {modello} creato con successo!")
            return True

    except Exception as e:
        logging.error(f"Errore durante la creazione dello strumento: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False

def visualizza_strumenti(id_cantiere: int) -> bool:
    """
    Visualizza tutti gli strumenti certificati del cantiere.

    Args:
        id_cantiere: ID del cantiere

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        print("\n" + "=" * 80)
        print("📋 STRUMENTI CERTIFICATI")
        print("=" * 80)

        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT
                    id_strumento, nome, marca, modello, numero_serie,
                    data_calibrazione, data_scadenza_calibrazione
                FROM StrumentiCertificati
                WHERE id_cantiere = ?
                ORDER BY nome, marca, modello
            """, (id_cantiere,))

            strumenti = c.fetchall()

            if not strumenti:
                print("\nNessuno strumento certificato trovato.")
                return True

            # Intestazione della tabella
            print(f"\n{'ID':<5} {'Nome':<15} {'Marca':<15} {'Modello':<15} {'Numero Serie':<15} {'Calibrazione':<12} {'Scadenza':<12} {'Stato':<10}")
            print("-" * 95)

            # Righe della tabella
            oggi = date.today()
            for strumento in strumenti:
                # Converti la data in oggetto date se è una stringa, altrimenti usala direttamente
                if isinstance(strumento['data_calibrazione'], str):
                    data_calibrazione = datetime.strptime(strumento['data_calibrazione'], "%Y-%m-%d").date()
                else:
                    data_calibrazione = strumento['data_calibrazione']

                if isinstance(strumento['data_scadenza_calibrazione'], str):
                    data_scadenza = datetime.strptime(strumento['data_scadenza_calibrazione'], "%Y-%m-%d").date()
                else:
                    data_scadenza = strumento['data_scadenza_calibrazione']

                # Determina lo stato della calibrazione
                if data_scadenza < oggi:
                    stato = "SCADUTO"
                elif data_scadenza < oggi + timedelta(days=30):
                    stato = "IN SCADENZA"
                else:
                    stato = "VALIDO"

                print(f"{strumento['id_strumento']:<5} {strumento['nome'][:15]:<15} {strumento['marca'][:15]:<15} {strumento['modello'][:15]:<15} {strumento['numero_serie'][:15]:<15} {data_calibrazione.strftime('%Y-%m-%d'):<12} {data_scadenza.strftime('%Y-%m-%d'):<12} {stato:<10}")

            return True

    except Exception as e:
        logging.error(f"Errore durante la visualizzazione degli strumenti: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False

def dettagli_strumento(id_cantiere: int, id_strumento: int) -> bool:
    """
    Visualizza i dettagli di uno strumento certificato.

    Args:
        id_cantiere: ID del cantiere
        id_strumento: ID dello strumento

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT *
                FROM StrumentiCertificati
                WHERE id_cantiere = ? AND id_strumento = ?
            """, (id_cantiere, id_strumento))

            strumento = c.fetchone()
            if not strumento:
                print(f"\n❌ Strumento con ID {id_strumento} non trovato!")
                return False

            print("\n" + "=" * 60)
            print(f"📋 DETTAGLI STRUMENTO: {strumento['nome']} {strumento['marca']} {strumento['modello']}")
            print("=" * 60)
            print(f"ID: {strumento['id_strumento']}")
            print(f"Nome: {strumento['nome']}")
            print(f"Marca: {strumento['marca']}")
            print(f"Modello: {strumento['modello']}")
            print(f"Numero di serie: {strumento['numero_serie']}")
            print(f"Data calibrazione: {strumento['data_calibrazione']}")
            print(f"Data scadenza calibrazione: {strumento['data_scadenza_calibrazione']}")

            # Calcola i giorni rimanenti alla scadenza
            if isinstance(strumento['data_scadenza_calibrazione'], str):
                data_scadenza = datetime.strptime(strumento['data_scadenza_calibrazione'], "%Y-%m-%d").date()
            else:
                data_scadenza = strumento['data_scadenza_calibrazione']

            oggi = date.today()
            giorni_rimanenti = (data_scadenza - oggi).days

            if giorni_rimanenti < 0:
                print(f"Stato calibrazione: SCADUTO da {abs(giorni_rimanenti)} giorni")
            elif giorni_rimanenti == 0:
                print("Stato calibrazione: SCADE OGGI")
            else:
                print(f"Stato calibrazione: VALIDO (scade tra {giorni_rimanenti} giorni)")

            if strumento['certificato_calibrazione']:
                print(f"Certificato di calibrazione: {strumento['certificato_calibrazione']}")
            else:
                print("Certificato di calibrazione: Non disponibile")

            if strumento['note']:
                print(f"Note: {strumento['note']}")

            return True

    except Exception as e:
        logging.error(f"Errore durante la visualizzazione dei dettagli dello strumento: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False

def modifica_strumento(id_cantiere: int, id_strumento: int) -> bool:
    """
    Modifica uno strumento certificato esistente.

    Args:
        id_cantiere: ID del cantiere
        id_strumento: ID dello strumento

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        # Verifica che lo strumento esista
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT *
                FROM StrumentiCertificati
                WHERE id_cantiere = ? AND id_strumento = ?
            """, (id_cantiere, id_strumento))

            strumento = c.fetchone()
            if not strumento:
                print(f"\n❌ Strumento con ID {id_strumento} non trovato!")
                return False

            print("\n" + "=" * 60)
            print(f"✏️ MODIFICA STRUMENTO: {strumento['nome']} {strumento['marca']} {strumento['modello']}")
            print("=" * 60)

            # Richiedi i nuovi dati (lascia vuoto per mantenere il valore attuale)
            nome = input(f"Nome [{strumento['nome']}]: ").strip() or strumento['nome']
            marca = input(f"Marca [{strumento['marca']}]: ").strip() or strumento['marca']
            modello = input(f"Modello [{strumento['modello']}]: ").strip() or strumento['modello']
            numero_serie = input(f"Numero di serie [{strumento['numero_serie']}]: ").strip() or strumento['numero_serie']

            # Richiedi la data di calibrazione
            data_calibrazione_default = strumento['data_calibrazione']
            data_calibrazione_str = input(f"Data di calibrazione [{data_calibrazione_default}]: ").strip() or data_calibrazione_default
            try:
                data_calibrazione = datetime.strptime(data_calibrazione_str, "%Y-%m-%d").date()
            except ValueError:
                print("❌ Formato data non valido! Usa YYYY-MM-DD")
                return False

            # Richiedi la data di scadenza della calibrazione
            data_scadenza_default = strumento['data_scadenza_calibrazione']
            data_scadenza_str = input(f"Data di scadenza della calibrazione [{data_scadenza_default}]: ").strip() or data_scadenza_default
            try:
                data_scadenza = datetime.strptime(data_scadenza_str, "%Y-%m-%d").date()
            except ValueError:
                print("❌ Formato data non valido! Usa YYYY-MM-DD")
                return False

            # Verifica che la data di scadenza sia successiva alla data di calibrazione
            if data_scadenza <= data_calibrazione:
                print("❌ La data di scadenza deve essere successiva alla data di calibrazione!")
                return False

            # Richiedi il percorso del certificato di calibrazione
            certificato_default = strumento['certificato_calibrazione'] or "Non disponibile"
            certificato_calibrazione = input(f"Percorso del certificato di calibrazione [{certificato_default}]: ").strip()
            if not certificato_calibrazione and certificato_default != "Non disponibile":
                certificato_calibrazione = strumento['certificato_calibrazione']

            # Richiedi le note
            note_default = strumento['note'] or ""
            note = input(f"Note [{note_default}]: ").strip()
            if not note and note_default:
                note = note_default

            # Aggiorna lo strumento nel database
            c.execute("""
                UPDATE StrumentiCertificati
                SET nome = ?, marca = ?, modello = ?, numero_serie = ?,
                    data_calibrazione = ?, data_scadenza_calibrazione = ?,
                    certificato_calibrazione = ?, note = ?, timestamp_modifica = ?
                WHERE id_cantiere = ? AND id_strumento = ?
            """, (
                nome, marca, modello, numero_serie,
                data_calibrazione.isoformat(), data_scadenza.isoformat(),
                certificato_calibrazione, note, datetime.now().isoformat(),
                id_cantiere, id_strumento
            ))

            print(f"\n✅ Strumento {nome} {marca} {modello} aggiornato con successo!")
            return True

    except Exception as e:
        logging.error(f"Errore durante la modifica dello strumento: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False

def elimina_strumento(id_cantiere: int, id_strumento: int) -> bool:
    """
    Elimina uno strumento certificato.

    Args:
        id_cantiere: ID del cantiere
        id_strumento: ID dello strumento

    Returns:
        bool: True se l'operazione è riuscita, False altrimenti
    """
    try:
        # Verifica che lo strumento esista
        with database_connection() as conn:
            c = conn.cursor()
            c.execute("""
                SELECT nome, marca, modello
                FROM StrumentiCertificati
                WHERE id_cantiere = ? AND id_strumento = ?
            """, (id_cantiere, id_strumento))

            strumento = c.fetchone()
            if not strumento:
                print(f"\n❌ Strumento con ID {id_strumento} non trovato!")
                return False

            # Verifica se lo strumento è utilizzato in qualche certificazione
            c.execute("""
                SELECT COUNT(*) as num_certificazioni
                FROM CertificazioniCavi
                WHERE id_cantiere = ? AND id_strumento = ?
            """, (id_cantiere, id_strumento))

            result = c.fetchone()
            if result and result['num_certificazioni'] > 0:
                print(f"\n❌ Impossibile eliminare lo strumento: è utilizzato in {result['num_certificazioni']} certificazioni!")
                return False

            # Chiedi conferma
            print(f"\n⚠️ Stai per eliminare lo strumento: {strumento['nome']} {strumento['marca']} {strumento['modello']}")
            conferma = input("Sei sicuro? (s/n): ").strip().lower()
            if conferma != 's':
                print("Operazione annullata.")
                return False

            # Elimina lo strumento
            c.execute("""
                DELETE FROM StrumentiCertificati
                WHERE id_cantiere = ? AND id_strumento = ?
            """, (id_cantiere, id_strumento))

            print(f"\n✅ Strumento eliminato con successo!")
            return True

    except Exception as e:
        logging.error(f"Errore durante l'eliminazione dello strumento: {str(e)}")
        print(f"\n❌ Errore: {str(e)}")
        return False

def menu_gestione_strumenti(id_cantiere: int) -> None:
    """
    Menu principale per la gestione degli strumenti certificati.

    Args:
        id_cantiere: ID del cantiere
    """
    while True:
        print("\n" + "=" * 60)
        print("🔧 GESTIONE STRUMENTI CERTIFICATI")
        print("=" * 60)
        print("\nOpzioni disponibili:")
        print(" 1. Visualizza tutti gli strumenti")
        print(" 2. Crea nuovo strumento")
        print(" 3. Dettagli strumento")
        print(" 4. Modifica strumento")
        print(" 5. Elimina strumento")
        print(" 6. Torna al menu precedente")

        scelta = input("\nSeleziona un'opzione (1-6): ").strip()

        if scelta == "1":
            visualizza_strumenti(id_cantiere)
        elif scelta == "2":
            crea_strumento(id_cantiere)
        elif scelta == "3":
            visualizza_strumenti(id_cantiere)
            id_strumento = input("\nInserisci l'ID dello strumento da visualizzare: ").strip()
            if id_strumento.isdigit():
                dettagli_strumento(id_cantiere, int(id_strumento))
            else:
                print("❌ ID strumento non valido!")
        elif scelta == "4":
            visualizza_strumenti(id_cantiere)
            id_strumento = input("\nInserisci l'ID dello strumento da modificare: ").strip()
            if id_strumento.isdigit():
                modifica_strumento(id_cantiere, int(id_strumento))
            else:
                print("❌ ID strumento non valido!")
        elif scelta == "5":
            visualizza_strumenti(id_cantiere)
            id_strumento = input("\nInserisci l'ID dello strumento da eliminare: ").strip()
            if id_strumento.isdigit():
                elimina_strumento(id_cantiere, int(id_strumento))
            else:
                print("❌ ID strumento non valido!")
        elif scelta == "6":
            break
        else:
            print("❌ Opzione non valida!")
