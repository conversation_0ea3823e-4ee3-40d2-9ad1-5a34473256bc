import type { Metada<PERSON> } from "next";
import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import "@/styles/visualizza-cavi-button.css";
import { Navbar } from "@/components/layout/Navbar";
import MainContent from "@/components/layout/MainContent";
import { AuthProvider } from "@/contexts/AuthContext";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "CABLYS - Cable Installation Advance System",
  description: "Sistema avanzato per la gestione dell'installazione cavi",
  manifest: "/manifest.json",
  themeColor: "#2563eb",
  viewport: "width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "CABLYS"
  }
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="it">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased`}
      >
        <AuthProvider>
          <div className="min-h-screen bg-slate-50">
            <Navbar />
            <MainContent>
              {children}
            </MainContent>
          </div>
          <Toaster />
        </AuthProvider>
      </body>
    </html>
  );
}
