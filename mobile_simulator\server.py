#!/usr/bin/env python3
"""
Server semplice per servire il simulatore dell'app mobile.
Avvia il simulatore su http://localhost:3001
"""

import http.server
import socketserver
import os
import webbrowser
from pathlib import Path

# Configurazione
PORT = 3001
DIRECTORY = Path(__file__).parent

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """Handler personalizzato per servire i file del simulatore."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=str(DIRECTORY), **kwargs)
    
    def end_headers(self):
        # Aggiungi header CORS per permettere le chiamate API
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Gestisci le richieste OPTIONS per CORS
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # Log personalizzato
        print(f"📱 Mobile Simulator: {format % args}")

def main():
    """Avvia il server del simulatore mobile."""
    
    print("🚀 Avvio CMS Mobile App Simulator...")
    print(f"📱 Directory: {DIRECTORY}")
    print(f"🌐 URL: http://localhost:{PORT}")
    print("=" * 50)
    
    # Verifica che i file esistano
    index_file = DIRECTORY / "index.html"
    app_file = DIRECTORY / "app.js"
    
    if not index_file.exists():
        print("❌ Errore: index.html non trovato!")
        return
    
    if not app_file.exists():
        print("❌ Errore: app.js non trovato!")
        return
    
    try:
        # Crea il server
        with socketserver.TCPServer(("", PORT), CustomHTTPRequestHandler) as httpd:
            print(f"✅ Server avviato su porta {PORT}")
            print("\n📋 ISTRUZIONI:")
            print("1. Assicurati che il backend CMS sia in esecuzione su porta 8001")
            print("2. Crea una comanda dal sistema CMS")
            print("3. Usa il codice comanda e i dati del responsabile per accedere")
            print("4. Testa le funzionalità dell'app mobile")
            print("\n🔧 Per fermare il server: Ctrl+C")
            print("=" * 50)
            
            # Apri automaticamente il browser
            try:
                webbrowser.open(f'http://localhost:{PORT}')
                print("🌐 Browser aperto automaticamente")
            except:
                print("⚠️ Impossibile aprire il browser automaticamente")
                print(f"   Apri manualmente: http://localhost:{PORT}")
            
            # Avvia il server
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n\n🛑 Server fermato dall'utente")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ Errore: Porta {PORT} già in uso!")
            print("   Prova a cambiare porta o ferma il processo esistente")
        else:
            print(f"❌ Errore del server: {e}")
    except Exception as e:
        print(f"❌ Errore imprevisto: {e}")

if __name__ == "__main__":
    main()
