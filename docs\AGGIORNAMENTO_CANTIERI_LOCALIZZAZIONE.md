# Aggiornamento Sistema Cantieri - Localizzazione Geografica

## Panoramica

Implementazione completa della ristrutturazione del sistema cantieri per supportare la localizzazione geografica, necessaria per il recupero automatico di temperatura e umidità durante la certificazione.

## Modifiche Implementate

### 1. Database - Struttura Cantieri

#### Campi modificati:
- `nome` → `commessa` (rinominato)

#### Nuovi campi aggiunti:
- `nome_cliente` (TEXT) - Nome del cliente del cantiere
- `indirizzo_cantiere` (TEXT) - Indirizzo completo del cantiere  
- `citta_cantiere` (TEXT) - Città del cantiere
- `nazione_cantiere` (TEXT) - Nazione del cantiere

#### Campi rimossi:
- `progetto_commessa` (duplicato con commessa)
- `codice_progetto` (duplicato con codice_univoco)

#### Campi mantenuti:
- `riferimenti_normativi` 
- `documentazione_progetto`

### 2. Backend API

#### File modificati:
- `webapp/backend/models/cantiere.py` - Aggiornato modello SQLAlchemy
- `webapp/backend/schemas/cantiere.py` - Aggiornati schemi Pydantic
- `webapp/backend/api/cantieri.py` - Aggiornati endpoint per nuovi campi

#### Funzionalità aggiornate:
- Creazione cantiere con localizzazione
- Aggiornamento cantiere con nuovi campi
- Validazione dati di localizzazione

### 3. Frontend

#### Nuovi componenti:
- `CreateCantiereDialog.js` - Dialog moderno per creazione cantiere
- Sezioni organizzate: Informazioni Generali, Cliente e Localizzazione, Sicurezza, Normative

#### Componenti aggiornati:
- `EditCantiereDialog.js` - Supporto per nuovi campi di localizzazione
- `CantieriFilterableTable.js` - Colonne aggiornate per mostrare cliente, città, nazione
- `UserPage.js` - Integrazione nuovi componenti

#### Miglioramenti UX:
- Form organizzato in sezioni logiche
- Dropdown per selezione nazione
- Validazione migliorata
- Layout responsive con Grid Material-UI

### 4. Script di Migrazione

#### File creati:
- `scripts/migrate_cantieri_localizzazione.py` - Script di migrazione completo
- `scripts/README_migrazione_cantieri.md` - Documentazione migrazione

#### Funzionalità migrazione:
- Backup automatico dati esistenti
- Rinomina colonne esistenti
- Aggiunta nuovi campi
- Valori di default per cantieri esistenti
- Pulizia campi obsoleti
- Verifica completamento migrazione
- Rollback automatico in caso di errore

## Struttura Finale Database

```sql
CREATE TABLE cantieri (
    id_cantiere SERIAL PRIMARY KEY,
    commessa TEXT NOT NULL,                    -- Rinominato da 'nome'
    descrizione TEXT,
    nome_cliente TEXT,                         -- NUOVO
    indirizzo_cantiere TEXT,                   -- NUOVO  
    citta_cantiere TEXT,                       -- NUOVO
    nazione_cantiere TEXT,                     -- NUOVO
    data_creazione TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    password_cantiere TEXT NOT NULL,
    id_utente INTEGER NOT NULL,
    codice_univoco TEXT UNIQUE NOT NULL,
    riferimenti_normativi TEXT,
    documentazione_progetto TEXT,
    FOREIGN KEY (id_utente) REFERENCES Utenti(id_utente)
);
```

## Benefici Implementati

### 1. Localizzazione Precisa
- Ogni cantiere ha la sua posizione geografica specifica
- Supporto per cantieri internazionali
- Gestione clienti multipli per azienda

### 2. Preparazione Sistema Meteorologico
- Struttura pronta per API meteorologiche
- Campi città/nazione per geocoding
- Base per recupero automatico temperatura/umidità

### 3. Miglioramenti UX
- Form più organizzato e intuitivo
- Visualizzazione dati più chiara
- Workflow di creazione migliorato

### 4. Compatibilità
- Retrocompatibilità con dati esistenti
- Migrazione sicura con backup
- Fallback per campi legacy

## Prossimi Passi

### 1. Servizio Meteorologico
```javascript
// Implementazione futura
const getWeatherData = async (city, country) => {
  // API call a OpenWeatherMap o simile
  return { temperature: 22.5, humidity: 65 };
};
```

### 2. Geocoding
```javascript
// Conversione indirizzo → coordinate
const getCoordinates = async (address, city, country) => {
  // API call per ottenere lat/lng
  return { latitude: 45.4642, longitude: 9.1900 };
};
```

### 3. Integrazione Certificazione
- Auto-popolamento temperatura/umidità
- Cache dati meteorologici
- Override manuale quando necessario

## File Modificati

### Backend:
- `webapp/backend/models/cantiere.py`
- `webapp/backend/schemas/cantiere.py`
- `webapp/backend/api/cantieri.py`

### Frontend:
- `webapp/frontend/src/components/cantieri/CreateCantiereDialog.js` (NUOVO)
- `webapp/frontend/src/components/cantieri/EditCantiereDialog.js`
- `webapp/frontend/src/components/cantieri/CantieriFilterableTable.js`
- `webapp/frontend/src/pages/UserPage.js`

### Scripts:
- `scripts/migrate_cantieri_localizzazione.py` (NUOVO)
- `scripts/README_migrazione_cantieri.md` (NUOVO)

### Documentazione:
- `docs/AGGIORNAMENTO_CANTIERI_LOCALIZZAZIONE.md` (QUESTO FILE)

## Esecuzione Migrazione

```bash
# 1. Backup database
pg_dump -h localhost -U postgres -d cantieri > backup_pre_migrazione.sql

# 2. Esegui migrazione
cd scripts
python migrate_cantieri_localizzazione.py

# 3. Verifica risultati
# Controlla log: migrate_cantieri_localizzazione.log
```

## Test Consigliati

1. **Creazione nuovo cantiere** con tutti i campi di localizzazione
2. **Modifica cantiere esistente** per aggiungere informazioni geografiche
3. **Visualizzazione tabella cantieri** con nuove colonne
4. **Compatibilità** con cantieri creati prima della migrazione
5. **Validazione** campi obbligatori e opzionali

## Note Tecniche

- Tutti i campi di localizzazione sono opzionali per compatibilità
- Nazione ha default "Italia" nel form di creazione
- Supporto per cantieri internazionali
- Retrocompatibilità garantita con campo `nome` → `commessa`
