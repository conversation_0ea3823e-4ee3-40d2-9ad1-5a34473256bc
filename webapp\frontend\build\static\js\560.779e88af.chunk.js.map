{"version": 3, "file": "static/js/560.779e88af.chunk.js", "mappings": "gNASO,MAAMA,EAAa,CAExBC,MAAOC,EAAAA,EAAYD,MACnBE,OAAQD,EAAAA,EAAYC,OACpBC,eAAgBF,EAAAA,EAAYE,eAG5BC,YAAaC,EAAAA,EAAgBC,cAC7BC,YAAaF,EAAAA,EAAgBE,YAC7BC,eAAgBH,EAAAA,EAAgBG,eAChCC,eAAgBJ,EAAAA,EAAgBI,eAGhCC,QAASC,EAAAA,EAAYD,QACrBE,QAASD,EAAAA,EAAYC,QACrBC,WAAYF,EAAAA,EAAYE,WACxBC,WAAYH,EAAAA,EAAYG,WACxBC,WAAYJ,EAAAA,EAAYI,WACxBC,aAAcL,EAAAA,EAAYK,aAG1BC,kBAAmBC,EAAAA,EAAsBD,kBACzCE,kBAAmBD,EAAAA,EAAsBC,kBACzCC,qBAAsBF,EAAAA,EAAsBE,qBAC5CC,qBAAsBH,EAAAA,EAAsBG,qBAC5CC,qBAAsBJ,EAAAA,EAAsBI,qBAG5CC,aAAcL,EAAAA,EAAsBK,aACpCC,gBAAiBN,EAAAA,EAAsBM,gBACvCC,gBAAiBP,EAAAA,EAAsBO,gBACvCC,gBAAiBR,EAAAA,EAAsBQ,gBAGvCC,aAAcC,EAAAA,EAAiBD,aAC/BE,aAAcD,EAAAA,EAAiBC,aAC/BC,aAAcF,EAAAA,EAAiBE,aAC/BC,aAAcH,EAAAA,EAAiBG,aAG/BC,iBAAkBC,EAAAA,QAAaD,iBAC/BE,YAAaD,EAAAA,QAAaC,YAG1BC,WAAYC,EAAAA,EAAcD,W,sOCuQ5B,QAxSA,SAA2BE,GAAkE,IAAjE,WAAEC,EAAU,eAAEC,EAAc,UAAEC,EAAS,UAAEC,EAAS,SAAEC,GAAUL,EACxF,MAAOM,EAAUC,IAAeC,EAAAA,EAAAA,UAAS,CACvCC,QAAS,GACTC,aAAc,GACdC,qBAAsB,GACtBC,aAAc,GACdC,mBAAoB,GACpBC,kBAAmB,KACnBC,kBAAmB,MACnBC,kBAAmB,KACnBC,KAAM,MAGDC,EAAMC,IAAWX,EAAAA,EAAAA,UAAS,KAC1BY,EAAcC,IAAmBb,EAAAA,EAAAA,UAAS,OAC1Cc,EAASC,IAAcf,EAAAA,EAAAA,WAAS,IAChCgB,EAAOC,IAAYjB,EAAAA,EAAAA,UAAS,KAEnCkB,EAAAA,EAAAA,YAAU,KACRC,IACIzB,GACFK,EAAY,CACVE,QAASP,EAAeO,SAAW,GACnCC,aAAcR,EAAeQ,cAAgB,GAC7CC,qBAAsBT,EAAeS,sBAAwB,GAC7DC,aAAcV,EAAeU,cAAgB,GAC7CC,mBAAoBX,EAAeW,oBAAsB,GACzDC,kBAAmBZ,EAAeY,mBAAqB,KACvDC,kBAAmBb,EAAea,mBAAqB,MACvDC,kBAAmBd,EAAec,mBAAqB,KACvDC,KAAMf,EAAee,MAAQ,IAEjC,GACC,CAACf,EAAgBD,IAEpB,MAAM0B,EAAWC,UACf,IAEE,MACMC,SADiBnE,EAAAA,WAAWW,QAAQ4B,IACV6B,QAAOC,GACR,eAA7BA,EAAKC,sBAKP,GAHAb,EAAQU,GAGJ3B,EAAgB,CAClB,MAAM6B,EAAOF,EAAeI,MAAKC,GAAKA,EAAEzB,UAAYP,EAAeO,UACnEY,EAAgBU,EAClB,CACF,CAAE,MAAOI,GACPC,QAAQZ,MAAM,mCAAoCW,GAClDV,EAAS,kCACX,GAGIY,EAAoBA,CAACC,EAAOC,KAChChC,GAAYiC,IAAI,IACXA,EACH,CAACF,GAAQC,KACR,EAmEL,OACEE,EAAAA,EAAAA,MAACC,EAAAA,EAAK,CAACC,GAAI,CAAEC,EAAG,GAAIC,SAAA,EAClBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,KAAKC,cAAY,EAAAJ,SAClC3C,EAAiB,0BAA4B,yBAG/CsB,IACCsB,EAAAA,EAAAA,KAACI,EAAAA,EAAK,CAACC,SAAS,QAAQR,GAAI,CAAES,GAAI,GAAIP,SACnCrB,KAILsB,EAAAA,EAAAA,KAAA,QAAMO,SA7CWzB,UAGnB,GAFA0B,EAAMC,iBAEDjD,EAASG,QAKd,IACEc,GAAW,GACXE,EAAS,IAET,MAAM+B,EAAa,IACdlD,EACHM,aAAcN,EAASM,cAAgB,KACvCC,mBAAoBP,EAASO,mBAAqB4C,WAAWnD,EAASO,oBAAsB,MAG1FX,SACIxC,EAAAA,WAAWsB,qBAAqBiB,EAAYC,EAAewD,kBAAmBF,GACpFpD,EAAU,kDAEJ1C,EAAAA,WAAWqB,qBAAqBkB,EAAYuD,GAClDpD,EAAU,sCAEd,CAAE,MAAO+B,GAAM,IAADwB,EAAAC,EACZxB,QAAQZ,MAAM,0BAA2BW,GACzCV,GAAqB,QAAZkC,EAAAxB,EAAI0B,gBAAQ,IAAAF,GAAM,QAANC,EAAZD,EAAcG,YAAI,IAAAF,OAAN,EAAZA,EAAoBG,SAAU,8CACzC,CAAC,QACCxC,GAAW,EACb,MA1BEE,EAAS,mCA0BX,EAe+BoB,UAC3BJ,EAAAA,EAAAA,MAACuB,EAAAA,GAAI,CAACC,WAAS,EAACC,QAAS,EAAErB,SAAA,EAEzBJ,EAAAA,EAAAA,MAACuB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAGvB,SAAA,EAChBC,EAAAA,EAAAA,KAACC,EAAAA,EAAU,CAACC,QAAQ,YAAYqB,MAAM,iBAAiBpB,cAAY,EAAAJ,SAAC,oBAGpEC,EAAAA,EAAAA,KAACwB,EAAAA,EAAY,CACX/B,MAAOnB,EACPmD,SArFaC,CAAClB,EAAOmB,KAC/BpD,EAAgBoD,GAEdlE,EADEkE,EACUjC,IAAI,IACXA,EACH/B,QAASgE,EAAShE,QAClBI,mBAAoB4D,EAASC,iBAAmB,KAGtClC,IAAI,IACXA,EACH/B,QAAS,GACTI,mBAAoB,KAExB,EAwEU8D,QAASzD,EACT0D,eAAiBC,GAAW,GAAGA,EAAOpE,aAAaoE,EAAOC,WAAa,MAAMD,EAAOE,SAAW,KAC/FC,YAAcC,IACZnC,EAAAA,EAAAA,KAACoC,EAAAA,EAAS,IACJD,EACJE,MAAM,sBACNC,UAAQ,EACRC,WAAS,IAGbC,WAAYpF,IAEbkB,IACC0B,EAAAA,EAAAA,KAACyC,EAAAA,EAAG,CAAC5C,GAAI,CAAE6C,GAAI,EAAG5C,EAAG,EAAG6C,QAAS,UAAWC,aAAc,GAAI7C,UAC5DJ,EAAAA,EAAAA,MAACM,EAAAA,EAAU,CAACC,QAAQ,QAAOH,SAAA,EACzBC,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,cAAkB,IAAEzB,EAAauE,qBAAuB,IAAI,MACpE7C,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,aAAiB,IAAEzB,EAAawE,mBAAqB,IAAI,MACjE9C,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,oBAAwB,IAAEzB,EAAayE,eAAiB,IAAI,MACpE/C,EAAAA,EAAAA,KAAA,UAAAD,SAAQ,kBAAsB,IAAEzB,EAAasD,iBAAmB,aAMxE5B,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAGvB,UAChBC,EAAAA,EAAAA,KAACgD,EAAAA,EAAO,OAIVhD,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAI2B,GAAI,EAAElD,UACvBC,EAAAA,EAAAA,KAACoC,EAAAA,EAAS,CACRC,MAAM,YACN5C,MAAOjC,EAASI,aAChB6D,SAAWyB,GAAM3D,EAAkB,eAAgB2D,EAAEC,OAAO1D,OAC5D8C,WAAS,OAIbvC,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAI2B,GAAI,EAAElD,UACvBJ,EAAAA,EAAAA,MAACyD,EAAAA,EAAW,CAACb,WAAS,EAAAxC,SAAA,EACpBC,EAAAA,EAAAA,KAACqD,EAAAA,EAAU,CAAAtD,SAAC,2BACZJ,EAAAA,EAAAA,MAAC2D,EAAAA,EAAM,CACL7D,MAAOjC,EAASM,aAChB2D,SAhHiBjB,IAC7B,MAAM+C,EAAc/C,EAAM2C,OAAO1D,MAGjC,GAFAF,EAAkB,eAAgBgE,GAE9BA,EAAa,CACf,MAAMC,EAAYnG,EAAU8B,MAAKsE,GAAKA,EAAE3F,eAAiByF,IACrDC,GACFjE,EAAkB,uBAAwB,GAAGiE,EAAUE,QAAQF,EAAUG,SAASH,EAAUI,UAEhG,MACErE,EAAkB,uBAAwB,GAC5C,EAsGY8C,MAAM,wBAAuBtC,SAAA,EAE7BC,EAAAA,EAAAA,KAAC6D,EAAAA,EAAQ,CAACpE,MAAM,GAAEM,SAAC,YAClB1C,EAAUyG,KAAKN,IACd7D,EAAAA,EAAAA,MAACkE,EAAAA,EAAQ,CAA8BpE,MAAO+D,EAAU1F,aAAaiC,SAAA,CAClEyD,EAAUE,KAAK,IAAEF,EAAUG,MAAM,IAAEH,EAAUI,UADjCJ,EAAU1F,yBAQjCkC,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAGvB,UAChBC,EAAAA,EAAAA,KAACoC,EAAAA,EAAS,CACRC,MAAM,sCACN5C,MAAOjC,EAASK,qBAChB4D,SAAWyB,GAAM3D,EAAkB,uBAAwB2D,EAAEC,OAAO1D,OACpE8C,WAAS,EACTwB,WAAW,2EAIf/D,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAGvB,UAChBC,EAAAA,EAAAA,KAACgD,EAAAA,EAAO,OAIVhD,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAI2B,GAAI,EAAElD,UACvBC,EAAAA,EAAAA,KAACoC,EAAAA,EAAS,CACRC,MAAM,yBACN2B,KAAK,SACLvE,MAAOjC,EAASO,mBAChB0D,SAAWyB,GAAM3D,EAAkB,qBAAsB2D,EAAEC,OAAO1D,OAClE8C,WAAS,EACT0B,WAAY,CAAEC,KAAM,IAAMC,IAAK,QAInCnE,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAI2B,GAAI,EAAElD,UACvBC,EAAAA,EAAAA,KAACoC,EAAAA,EAAS,CACRC,MAAM,8BACN5C,MAAOjC,EAASS,kBAChBwD,SAAWyB,GAAM3D,EAAkB,oBAAqB2D,EAAEC,OAAO1D,OACjE8C,WAAS,OAIbvC,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAI2B,GAAI,EAAElD,UACvBC,EAAAA,EAAAA,KAACoC,EAAAA,EAAS,CACRC,MAAM,uBACN5C,MAAOjC,EAASQ,kBAChByD,SAAWyB,GAAM3D,EAAkB,oBAAqB2D,EAAEC,OAAO1D,OACjE8C,WAAS,OAIbvC,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAI2B,GAAI,EAAElD,UACvBC,EAAAA,EAAAA,KAACoC,EAAAA,EAAS,CACRC,MAAM,oBACN5C,MAAOjC,EAASU,kBAChBuD,SAAWyB,GAAM3D,EAAkB,oBAAqB2D,EAAEC,OAAO1D,OACjE8C,WAAS,OAKbvC,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAGvB,UAChBC,EAAAA,EAAAA,KAACoC,EAAAA,EAAS,CACRC,MAAM,OACN5C,MAAOjC,EAASW,KAChBsD,SAAWyB,GAAM3D,EAAkB,OAAQ2D,EAAEC,OAAO1D,OACpD8C,WAAS,EACT6B,WAAS,EACTC,KAAM,OAKVrE,EAAAA,EAAAA,KAACkB,EAAAA,GAAI,CAACG,MAAI,EAACC,GAAI,GAAGvB,UAChBJ,EAAAA,EAAAA,MAAC8C,EAAAA,EAAG,CAAC5C,GAAI,CAAEyE,QAAS,OAAQC,IAAK,EAAGC,eAAgB,YAAazE,SAAA,EAC/DC,EAAAA,EAAAA,KAACyE,EAAAA,EAAM,CACLvE,QAAQ,WACRwE,WAAW1E,EAAAA,EAAAA,KAAC2E,EAAAA,EAAU,IACtBC,QAASrH,EACTiF,SAAUhE,EAAQuB,SACnB,aAGDC,EAAAA,EAAAA,KAACyE,EAAAA,EAAM,CACLT,KAAK,SACL9D,QAAQ,YACRwE,WAAW1E,EAAAA,EAAAA,KAAC6E,EAAAA,EAAQ,IACpBrC,SAAUhE,EAAQuB,SAEjBvB,EAAU,iBAAmB,qCAQ9C,C", "sources": ["services/apiService.js", "components/certificazioni/CertificazioneForm.jsx"], "sourcesContent": ["// Servizio API unificato che espone tutti i servizi\nimport authService from './authService';\nimport cantieriService from './cantieriService';\nimport caviService from './caviService';\nimport certificazioneService from './certificazioneService';\nimport parcoCaviService from './parcoCaviService';\nimport excelService from './excelService';\nimport reportService from './reportService';\n\nexport const apiService = {\n  // Auth\n  login: authService.login,\n  logout: authService.logout,\n  getCurrentUser: authService.getCurrentUser,\n\n  // Cantieri\n  getCantieri: cantieriService.getMyCantieri,\n  getCantiere: cantieriService.getCantiere,\n  createCantiere: cantieriService.createCantiere,\n  deleteCantiere: cantieriService.deleteCantiere,\n\n  // Cavi\n  getCavi: caviService.getCavi,\n  getCavo: caviService.getCavo,\n  createCavo: caviService.createCavo,\n  updateCavo: caviService.updateCavo,\n  deleteCavo: caviService.deleteCavo,\n  aggiornaCavo: caviService.aggiornaCavo,\n\n  // Certificazioni\n  getCertificazioni: certificazioneService.getCertificazioni,\n  getCertificazione: certificazioneService.getCertificazione,\n  createCertificazione: certificazioneService.createCertificazione,\n  updateCertificazione: certificazioneService.updateCertificazione,\n  deleteCertificazione: certificazioneService.deleteCertificazione,\n\n  // Strumenti\n  getStrumenti: certificazioneService.getStrumenti,\n  createStrumento: certificazioneService.createStrumento,\n  updateStrumento: certificazioneService.updateStrumento,\n  deleteStrumento: certificazioneService.deleteStrumento,\n\n  // Parco Cavi\n  getParcoCavi: parcoCaviService.getParcoCavi,\n  createBobina: parcoCaviService.createBobina,\n  updateBobina: parcoCaviService.updateBobina,\n  deleteBobina: parcoCaviService.deleteBobina,\n\n  // Excel\n  generateTemplate: excelService.generateTemplate,\n  importExcel: excelService.importExcel,\n\n  // Reports\n  getReports: reportService.getReports\n};\n\nexport default apiService;\n", "import React, { useState, useEffect } from 'react';\nimport {\n  Paper,\n  Typography,\n  TextField,\n  Button,\n  Box,\n  Grid,\n  FormControl,\n  InputLabel,\n  Select,\n  MenuItem,\n  Autocomplete,\n  Alert,\n  Divider\n} from '@mui/material';\nimport { Save as SaveIcon, Cancel as CancelIcon } from '@mui/icons-material';\n\nimport { apiService } from '../../services/apiService';\n\nfunction CertificazioneForm({ cantiereId, certificazione, strumenti, onSuccess, onCancel }) {\n  const [formData, setFormData] = useState({\n    id_cavo: '',\n    id_operatore: '',\n    strumento_utilizzato: '',\n    id_strumento: '',\n    lunghezza_misurata: '',\n    valore_continuita: 'OK',\n    valore_isolamento: '500',\n    valore_resistenza: 'OK',\n    note: ''\n  });\n\n  const [cavi, setCavi] = useState([]);\n  const [selectedCavo, setSelectedCavo] = useState(null);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n\n  useEffect(() => {\n    loadCavi();\n    if (certificazione) {\n      setFormData({\n        id_cavo: certificazione.id_cavo || '',\n        id_operatore: certificazione.id_operatore || '',\n        strumento_utilizzato: certificazione.strumento_utilizzato || '',\n        id_strumento: certificazione.id_strumento || '',\n        lunghezza_misurata: certificazione.lunghezza_misurata || '',\n        valore_continuita: certificazione.valore_continuita || 'OK',\n        valore_isolamento: certificazione.valore_isolamento || '500',\n        valore_resistenza: certificazione.valore_resistenza || 'OK',\n        note: certificazione.note || ''\n      });\n    }\n  }, [certificazione, cantiereId]);\n\n  const loadCavi = async () => {\n    try {\n      // Carica solo i cavi installati che non hanno già una certificazione\n      const caviData = await apiService.getCavi(cantiereId);\n      const caviInstallati = caviData.filter(cavo => \n        cavo.stato_installazione === 'INSTALLATO'\n      );\n      setCavi(caviInstallati);\n\n      // Se stiamo modificando, trova il cavo selezionato\n      if (certificazione) {\n        const cavo = caviInstallati.find(c => c.id_cavo === certificazione.id_cavo);\n        setSelectedCavo(cavo);\n      }\n    } catch (err) {\n      console.error('Errore nel caricamento dei cavi:', err);\n      setError('Errore nel caricamento dei cavi');\n    }\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  const handleCavoChange = (event, newValue) => {\n    setSelectedCavo(newValue);\n    if (newValue) {\n      setFormData(prev => ({\n        ...prev,\n        id_cavo: newValue.id_cavo,\n        lunghezza_misurata: newValue.metratura_reale || ''\n      }));\n    } else {\n      setFormData(prev => ({\n        ...prev,\n        id_cavo: '',\n        lunghezza_misurata: ''\n      }));\n    }\n  };\n\n  const handleStrumentoChange = (event) => {\n    const strumentoId = event.target.value;\n    handleInputChange('id_strumento', strumentoId);\n    \n    if (strumentoId) {\n      const strumento = strumenti.find(s => s.id_strumento === strumentoId);\n      if (strumento) {\n        handleInputChange('strumento_utilizzato', `${strumento.nome} ${strumento.marca} ${strumento.modello}`);\n      }\n    } else {\n      handleInputChange('strumento_utilizzato', '');\n    }\n  };\n\n  const handleSubmit = async (event) => {\n    event.preventDefault();\n    \n    if (!formData.id_cavo) {\n      setError('Seleziona un cavo da certificare');\n      return;\n    }\n\n    try {\n      setLoading(true);\n      setError('');\n\n      const submitData = {\n        ...formData,\n        id_strumento: formData.id_strumento || null,\n        lunghezza_misurata: formData.lunghezza_misurata ? parseFloat(formData.lunghezza_misurata) : null\n      };\n\n      if (certificazione) {\n        await apiService.updateCertificazione(cantiereId, certificazione.id_certificazione, submitData);\n        onSuccess('Certificazione aggiornata con successo');\n      } else {\n        await apiService.createCertificazione(cantiereId, submitData);\n        onSuccess('Certificazione creata con successo');\n      }\n    } catch (err) {\n      console.error('Errore nel salvataggio:', err);\n      setError(err.response?.data?.detail || 'Errore nel salvataggio della certificazione');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Paper sx={{ p: 3 }}>\n      <Typography variant=\"h6\" gutterBottom>\n        {certificazione ? 'Modifica Certificazione' : 'Nuova Certificazione'}\n      </Typography>\n\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 2 }}>\n          {error}\n        </Alert>\n      )}\n\n      <form onSubmit={handleSubmit}>\n        <Grid container spacing={3}>\n          {/* Selezione Cavo */}\n          <Grid item xs={12}>\n            <Typography variant=\"subtitle2\" color=\"text.secondary\" gutterBottom>\n              Selezione Cavo\n            </Typography>\n            <Autocomplete\n              value={selectedCavo}\n              onChange={handleCavoChange}\n              options={cavi}\n              getOptionLabel={(option) => `${option.id_cavo} - ${option.tipologia || ''} ${option.sezione || ''}`}\n              renderInput={(params) => (\n                <TextField\n                  {...params}\n                  label=\"Cavo da certificare\"\n                  required\n                  fullWidth\n                />\n              )}\n              disabled={!!certificazione} // Non modificabile se stiamo editando\n            />\n            {selectedCavo && (\n              <Box sx={{ mt: 1, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>\n                <Typography variant=\"body2\">\n                  <strong>Partenza:</strong> {selectedCavo.ubicazione_partenza || '-'} | \n                  <strong> Arrivo:</strong> {selectedCavo.ubicazione_arrivo || '-'} | \n                  <strong> Metri Teorici:</strong> {selectedCavo.metri_teorici || '-'} | \n                  <strong> Metri Reali:</strong> {selectedCavo.metratura_reale || '-'}\n                </Typography>\n              </Box>\n            )}\n          </Grid>\n\n          <Grid item xs={12}>\n            <Divider />\n          </Grid>\n\n          {/* Informazioni Operatore e Strumento */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Operatore\"\n              value={formData.id_operatore}\n              onChange={(e) => handleInputChange('id_operatore', e.target.value)}\n              fullWidth\n            />\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <FormControl fullWidth>\n              <InputLabel>Strumento Certificato</InputLabel>\n              <Select\n                value={formData.id_strumento}\n                onChange={handleStrumentoChange}\n                label=\"Strumento Certificato\"\n              >\n                <MenuItem value=\"\">Nessuno</MenuItem>\n                {strumenti.map((strumento) => (\n                  <MenuItem key={strumento.id_strumento} value={strumento.id_strumento}>\n                    {strumento.nome} {strumento.marca} {strumento.modello}\n                  </MenuItem>\n                ))}\n              </Select>\n            </FormControl>\n          </Grid>\n\n          <Grid item xs={12}>\n            <TextField\n              label=\"Descrizione Strumento (alternativa)\"\n              value={formData.strumento_utilizzato}\n              onChange={(e) => handleInputChange('strumento_utilizzato', e.target.value)}\n              fullWidth\n              helperText=\"Utilizzare solo se lo strumento non è presente nell'elenco sopra\"\n            />\n          </Grid>\n\n          <Grid item xs={12}>\n            <Divider />\n          </Grid>\n\n          {/* Misurazioni */}\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Lunghezza Misurata (m)\"\n              type=\"number\"\n              value={formData.lunghezza_misurata}\n              onChange={(e) => handleInputChange('lunghezza_misurata', e.target.value)}\n              fullWidth\n              inputProps={{ step: 0.01, min: 0 }}\n            />\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Valore Isolamento (MΩ)\"\n              value={formData.valore_isolamento}\n              onChange={(e) => handleInputChange('valore_isolamento', e.target.value)}\n              fullWidth\n            />\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Valore Continuità\"\n              value={formData.valore_continuita}\n              onChange={(e) => handleInputChange('valore_continuita', e.target.value)}\n              fullWidth\n            />\n          </Grid>\n\n          <Grid item xs={12} md={6}>\n            <TextField\n              label=\"Valore Resistenza\"\n              value={formData.valore_resistenza}\n              onChange={(e) => handleInputChange('valore_resistenza', e.target.value)}\n              fullWidth\n            />\n          </Grid>\n\n          {/* Note */}\n          <Grid item xs={12}>\n            <TextField\n              label=\"Note\"\n              value={formData.note}\n              onChange={(e) => handleInputChange('note', e.target.value)}\n              fullWidth\n              multiline\n              rows={3}\n            />\n          </Grid>\n\n          {/* Pulsanti */}\n          <Grid item xs={12}>\n            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>\n              <Button\n                variant=\"outlined\"\n                startIcon={<CancelIcon />}\n                onClick={onCancel}\n                disabled={loading}\n              >\n                Annulla\n              </Button>\n              <Button\n                type=\"submit\"\n                variant=\"contained\"\n                startIcon={<SaveIcon />}\n                disabled={loading}\n              >\n                {loading ? 'Salvataggio...' : 'Salva Certificazione'}\n              </Button>\n            </Box>\n          </Grid>\n        </Grid>\n      </form>\n    </Paper>\n  );\n}\n\nexport default CertificazioneForm;\n"], "names": ["apiService", "login", "authService", "logout", "getCurrentUser", "getCantieri", "cantieriService", "getMyCantieri", "getCantiere", "createCantiere", "deleteCantiere", "get<PERSON><PERSON>", "caviService", "getCavo", "createCavo", "updateCavo", "deleteCavo", "aggiornaCavo", "getCertificazioni", "certificazioneService", "getCertificazione", "createCertificazione", "updateCertificazione", "deleteCertificazione", "getStrumenti", "createStrumento", "updateStrumento", "deleteStrumento", "getParcoCavi", "parcoCaviService", "createBobina", "updateBobina", "deleteBobina", "generateTemplate", "excelService", "importExcel", "getReports", "reportService", "_ref", "cantiereId", "certificazione", "strumenti", "onSuccess", "onCancel", "formData", "setFormData", "useState", "id_cavo", "id_operatore", "strumento_utilizzato", "id_strumento", "<PERSON><PERSON><PERSON>_misurata", "valore_continuita", "valore_isolamento", "valore_resistenza", "note", "cavi", "<PERSON><PERSON><PERSON>", "selected<PERSON><PERSON><PERSON>", "setSelectedCavo", "loading", "setLoading", "error", "setError", "useEffect", "loadCavi", "async", "caviInstallati", "filter", "cavo", "stato_installazione", "find", "c", "err", "console", "handleInputChange", "field", "value", "prev", "_jsxs", "Paper", "sx", "p", "children", "_jsx", "Typography", "variant", "gutterBottom", "<PERSON><PERSON>", "severity", "mb", "onSubmit", "event", "preventDefault", "submitData", "parseFloat", "id_certificazione", "_err$response", "_err$response$data", "response", "data", "detail", "Grid", "container", "spacing", "item", "xs", "color", "Autocomplete", "onChange", "handleCavoChange", "newValue", "metratura_reale", "options", "getOptionLabel", "option", "tipologia", "sezione", "renderInput", "params", "TextField", "label", "required", "fullWidth", "disabled", "Box", "mt", "bgcolor", "borderRadius", "ubicazione_partenza", "ubicazione_arrivo", "metri_te<PERSON>ci", "Divider", "md", "e", "target", "FormControl", "InputLabel", "Select", "strumentoId", "strumento", "s", "nome", "marca", "modello", "MenuItem", "map", "helperText", "type", "inputProps", "step", "min", "multiline", "rows", "display", "gap", "justifyContent", "<PERSON><PERSON>", "startIcon", "CancelIcon", "onClick", "SaveIcon"], "sourceRoot": ""}