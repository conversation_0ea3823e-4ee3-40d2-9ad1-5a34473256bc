'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'

export default function DebugLoginPage() {
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)
  const router = useRouter()

  const testLogin = async () => {
    setLoading(true)
    setResult('Starting login test...\n')
    
    try {
      // Test 1: Basic fetch
      setResult(prev => prev + 'Step 1: Testing basic fetch...\n')
      
      const formData = new FormData()
      formData.append('username', 'admin')
      formData.append('password', 'admin')

      setResult(prev => prev + 'Step 2: Sending login request...\n')
      
      const response = await fetch('http://localhost:8001/api/auth/login', {
        method: 'POST',
        body: formData
      })

      setResult(prev => prev + `Step 3: Response status: ${response.status}\n`)
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setResult(prev => prev + `Step 4: Response data received\n`)
      setResult(prev => prev + `Data: ${JSON.stringify(data, null, 2)}\n`)

      // Test 2: Save token
      setResult(prev => prev + 'Step 5: Saving token to localStorage...\n')
      localStorage.setItem('token', data.access_token)
      
      // Test 3: Verify token was saved
      const savedToken = localStorage.getItem('token')
      setResult(prev => prev + `Step 6: Token saved: ${savedToken ? 'YES' : 'NO'}\n`)

      // Test 4: Test router
      setResult(prev => prev + 'Step 7: Testing router...\n')
      
      if (data.role === 'owner') {
        setResult(prev => prev + 'Step 8: Redirecting to /admin...\n')
        
        // Try different redirect methods
        setTimeout(() => {
          setResult(prev => prev + 'Step 9: Using router.push...\n')
          router.push('/admin')
        }, 1000)
        
        setTimeout(() => {
          setResult(prev => prev + 'Step 10: Using window.location...\n')
          window.location.href = '/admin'
        }, 3000)
      }

    } catch (error) {
      setResult(prev => prev + `ERROR: ${error.message}\n`)
      console.error('Login test error:', error)
    } finally {
      setLoading(false)
    }
  }

  const testRouter = () => {
    setResult('Testing router directly...\n')
    try {
      router.push('/admin')
      setResult(prev => prev + 'Router.push called successfully\n')
    } catch (error) {
      setResult(prev => prev + `Router error: ${error.message}\n`)
    }
  }

  const testWindowLocation = () => {
    setResult('Testing window.location...\n')
    window.location.href = '/admin'
  }

  const clearStorage = () => {
    localStorage.clear()
    setResult('LocalStorage cleared\n')
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Debug Login Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testLogin} 
          disabled={loading}
          style={{ 
            padding: '10px 20px', 
            marginRight: '10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Testing...' : 'Full Login Test'}
        </button>
        
        <button 
          onClick={testRouter} 
          style={{ 
            padding: '10px 20px',
            marginRight: '10px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Test Router Only
        </button>
        
        <button 
          onClick={testWindowLocation} 
          style={{ 
            padding: '10px 20px',
            marginRight: '10px',
            backgroundColor: '#ffc107',
            color: 'black',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Test Window.location
        </button>
        
        <button 
          onClick={clearStorage} 
          style={{ 
            padding: '10px 20px',
            backgroundColor: '#dc3545',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: 'pointer'
          }}
        >
          Clear Storage
        </button>
      </div>

      <div style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '15px', 
        borderRadius: '5px',
        whiteSpace: 'pre-wrap',
        minHeight: '400px',
        fontSize: '12px',
        fontFamily: 'monospace',
        border: '1px solid #ddd'
      }}>
        {result || 'Click a button to start testing...'}
      </div>
      
      <div style={{ marginTop: '20px' }}>
        <h3>Current localStorage:</h3>
        <div style={{ 
          backgroundColor: '#f8f9fa', 
          padding: '10px', 
          borderRadius: '5px',
          fontSize: '12px',
          fontFamily: 'monospace'
        }}>
          {typeof window !== 'undefined' ? 
            Object.keys(localStorage).map(key => 
              `${key}: ${localStorage.getItem(key)?.substring(0, 50)}...`
            ).join('\n') || 'Empty'
            : 'Not available (SSR)'
          }
        </div>
      </div>
    </div>
  )
}
