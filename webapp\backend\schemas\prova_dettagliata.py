from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from datetime import datetime

class ProvaDettagliataBase(BaseModel):
    """Schema base per le prove dettagliate."""
    tipo_prova: str = Field(..., description="Tipo di prova")
    data_prova: Optional[datetime] = Field(None, description="Data e ora della prova")
    operatore: Optional[str] = Field(None, description="Operatore che ha eseguito la prova")
    condizioni_ambientali: Optional[Dict[str, Any]] = Field(None, description="Condizioni ambientali")
    risultati: Optional[Dict[str, Any]] = Field(None, description="Risultati della prova")
    valori_misurati: Optional[Dict[str, Any]] = Field(None, description="Valori misurati")
    valori_attesi: Optional[Dict[str, Any]] = Field(None, description="Valori attesi")
    esito: str = Field(..., description="Esito della prova")
    note_prova: Optional[str] = Field(None, description="Note sulla prova")

class ProvaDettagliataCreate(ProvaDettagliataBase):
    """Schema per la creazione di una nuova prova dettagliata."""
    pass

class ProvaDettagliataUpdate(BaseModel):
    """Schema per l'aggiornamento di una prova dettagliata."""
    tipo_prova: Optional[str] = None
    data_prova: Optional[datetime] = None
    operatore: Optional[str] = None
    condizioni_ambientali: Optional[Dict[str, Any]] = None
    risultati: Optional[Dict[str, Any]] = None
    valori_misurati: Optional[Dict[str, Any]] = None
    valori_attesi: Optional[Dict[str, Any]] = None
    esito: Optional[str] = None
    note_prova: Optional[str] = None

class ProvaDettagliataInDB(ProvaDettagliataBase):
    """Schema per la prova dettagliata nel database."""
    id_prova: int
    id_certificazione: int

    class Config:
        from_attributes = True

class ProvaDettagliataResponse(ProvaDettagliataInDB):
    """Schema per la risposta API della prova dettagliata."""
    pass

class ProvaDettagliataListResponse(BaseModel):
    """Schema per la lista delle prove dettagliate."""
    id_prova: int
    id_certificazione: int
    tipo_prova: str
    data_prova: Optional[datetime] = None
    operatore: Optional[str] = None
    esito: str

    class Config:
        from_attributes = True

# Schemi specifici per i tipi di prova

class EsameVistaRisultati(BaseModel):
    """Schema per i risultati dell'esame a vista."""
    conformita_percorso: str = Field(..., description="OK|NON_OK|N_A")
    correttezza_tipo_cavo: str = Field(..., description="OK|NON_OK|N_A")
    integrita_guaina: str = Field(..., description="OK|NON_OK|N_A")
    correttezza_collegamenti: str = Field(..., description="OK|NON_OK|N_A")
    correttezza_siglatura: str = Field(..., description="OK|NON_OK|N_A")
    correttezza_supporti: str = Field(..., description="OK|NON_OK|N_A")
    note_esame: Optional[str] = Field(None, description="Note dell'esame")

class ContinuitaConduttore(BaseModel):
    """Schema per un singolo conduttore nella prova di continuità."""
    id_conduttore: str = Field(..., description="Identificativo del conduttore")
    collegamento_atteso: str = Field(..., description="Collegamento atteso")
    continuita_rilevata: str = Field(..., description="OK|INTERROTTO")
    resistenza_ohm: Optional[float] = Field(None, description="Resistenza misurata in Ohm")
    resistenza_attesa: Optional[float] = Field(None, description="Resistenza attesa in Ohm")

class ContinuitaRisultati(BaseModel):
    """Schema per i risultati della prova di continuità."""
    conduttori: list[ContinuitaConduttore] = Field(..., description="Lista dei conduttori testati")

class IsolamentoMisura(BaseModel):
    """Schema per una singola misura di isolamento."""
    tra: str = Field(..., description="Tra quali conduttori")
    valore_mohm: float = Field(..., description="Valore misurato in MΩ")

class IsolamentoRisultati(BaseModel):
    """Schema per i risultati della prova di isolamento."""
    tensione_prova_v: int = Field(..., description="Tensione di prova in Volt")
    durata_prova_sec: int = Field(..., description="Durata della prova in secondi")
    valore_minimo_mohm: float = Field(..., description="Valore minimo accettabile in MΩ")
    misure: list[IsolamentoMisura] = Field(..., description="Lista delle misure effettuate")

class SequenzaFasiTensione(BaseModel):
    """Schema per una tensione nella sequenza fasi."""
    fase: str = Field(..., description="Identificativo della fase")
    tensione_v: float = Field(..., description="Tensione misurata in Volt")

class SequenzaFasiRisultati(BaseModel):
    """Schema per i risultati della prova di sequenza fasi."""
    sequenza_attesa: str = Field(..., description="Sequenza attesa")
    sequenza_rilevata: str = Field(..., description="Sequenza rilevata")
    rotazione: str = Field(..., description="DESTRORSA|SINISTRORSA")
    tensioni: list[SequenzaFasiTensione] = Field(..., description="Tensioni misurate")
