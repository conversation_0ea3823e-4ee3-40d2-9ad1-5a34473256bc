/**
 * Comprehensive test suite for Cable Visualization UI improvements
 * Tests modal interactions, tooltip functionality, accessibility features,
 * and consistent behavior across different cable states
 */

import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'

// Import components to test
import {
  DisconnectCableModal,
  GeneratePdfModal,
  CertificationErrorModal,
  CertificationModal,
  SuccessToast
} from '../modals/CableActionModals'
import { ModificaBobinaModal, InserisciMetriModal } from '../modals/BobinaManagementModals'
import { CableTooltip, ActionTooltip, KpiTooltip } from '../tooltips/CableTooltips'
import CaviStatistics from '../CaviStatistics'
import SmartCaviFilter from '../SmartCaviFilter'
import { LoadingSpinner, ActionLoading, ProgressBar } from '../animations/LoadingStates'
import { AccessibleButton, LiveRegion, FocusTrap } from '../accessibility/AccessibilityHelpers'

// Mock data
const mockCavo = {
  id_cavo: 'TEST001',
  sistema: 'Test System',
  utility: 'Test Utility',
  tipologia: 'Test Type',
  metri_posati: 100,
  metratura_reale: 100,
  stato_installazione: 'Installato',
  collegamento: 3,
  certificato: true
}

const mockCavi = [
  mockCavo,
  {
    id_cavo: 'TEST002',
    sistema: 'Test System 2',
    utility: 'Test Utility 2',
    tipologia: 'Test Type 2',
    metri_posati: 0,
    metratura_reale: 0,
    stato_installazione: 'Da installare',
    collegamento: 0,
    certificato: false
  }
]

describe('Cable Action Modals', () => {
  test('DisconnectCableModal renders correctly and handles interactions', async () => {
    const mockOnClose = jest.fn()
    const mockOnConfirm = jest.fn()

    render(
      <DisconnectCableModal
        open={true}
        onClose={mockOnClose}
        onConfirm={mockOnConfirm}
        cavo={mockCavo}
      />
    )

    // Check modal content
    expect(screen.getByText('Conferma Scollegamento')).toBeInTheDocument()
    expect(screen.getByText(/TEST001/)).toBeInTheDocument()

    // Test cancel button
    const cancelButton = screen.getByText('Annulla')
    await userEvent.click(cancelButton)
    expect(mockOnClose).toHaveBeenCalled()

    // Test confirm button
    const confirmButton = screen.getByText('Scollega Cavo')
    await userEvent.click(confirmButton)
    expect(mockOnConfirm).toHaveBeenCalled()
  })

  test('GeneratePdfModal handles PDF generation options', async () => {
    const mockOnClose = jest.fn()
    const mockOnGenerate = jest.fn()

    render(
      <GeneratePdfModal
        open={true}
        onClose={mockOnClose}
        onGenerate={mockOnGenerate}
        cavo={mockCavo}
      />
    )

    // Check modal content
    expect(screen.getByText('Genera Certificato PDF')).toBeInTheDocument()

    // Test file name input
    const fileNameInput = screen.getByLabelText(/Nome File/)
    await userEvent.type(fileNameInput, 'test-certificate')

    // Test generate button
    const generateButton = screen.getByText('Genera PDF')
    await userEvent.click(generateButton)
    
    expect(mockOnGenerate).toHaveBeenCalledWith(
      expect.objectContaining({
        fileName: expect.stringContaining('test-certificate')
      })
    )
  })

  test('CertificationErrorModal displays error information', () => {
    const mockOnClose = jest.fn()
    const errorMessage = 'Cable not properly connected'
    const missingRequirements = ['Complete installation', 'Verify connections']

    render(
      <CertificationErrorModal
        open={true}
        onClose={mockOnClose}
        cavo={mockCavo}
        errorMessage={errorMessage}
        missingRequirements={missingRequirements}
      />
    )

    expect(screen.getByText('Impossibile Certificare Cavo')).toBeInTheDocument()
    expect(screen.getByText(errorMessage)).toBeInTheDocument()
    expect(screen.getByText('Complete installation')).toBeInTheDocument()
    expect(screen.getByText('Verify connections')).toBeInTheDocument()
  })

  test('Modal keyboard navigation works correctly', async () => {
    const mockOnClose = jest.fn()

    render(
      <DisconnectCableModal
        open={true}
        onClose={mockOnClose}
        onConfirm={jest.fn()}
        cavo={mockCavo}
      />
    )

    // Test ESC key closes modal
    fireEvent.keyDown(document, { key: 'Escape' })
    expect(mockOnClose).toHaveBeenCalled()
  })
})

describe('Tooltip Components', () => {
  test('CableTooltip shows and hides on hover', async () => {
    render(
      <CableTooltip content="Test tooltip content">
        <button>Hover me</button>
      </CableTooltip>
    )

    const trigger = screen.getByText('Hover me')
    
    // Hover to show tooltip
    await userEvent.hover(trigger)
    await waitFor(() => {
      expect(screen.getByText('Test tooltip content')).toBeInTheDocument()
    })

    // Unhover to hide tooltip
    await userEvent.unhover(trigger)
    await waitFor(() => {
      expect(screen.queryByText('Test tooltip content')).not.toBeInTheDocument()
    })
  })

  test('ActionTooltip provides contextual information', async () => {
    render(
      <ActionTooltip action="connect" cableId="TEST001">
        <button>Connect</button>
      </ActionTooltip>
    )

    const trigger = screen.getByText('Connect')
    await userEvent.hover(trigger)
    
    await waitFor(() => {
      expect(screen.getByText(/Collega il cavo TEST001/)).toBeInTheDocument()
    })
  })

  test('KpiTooltip shows statistics information', async () => {
    render(
      <KpiTooltip type="installed" count={5} percentage={50}>
        <div>5 Installed</div>
      </KpiTooltip>
    )

    const trigger = screen.getByText('5 Installed')
    await userEvent.hover(trigger)
    
    await waitFor(() => {
      expect(screen.getByText(/Cavi fisicamente installati: 5 cavi/)).toBeInTheDocument()
      expect(screen.getByText(/50.0%/)).toBeInTheDocument()
    })
  })
})

describe('Statistics Component', () => {
  test('CaviStatistics renders KPI boxes with click functionality', async () => {
    const mockOnFilterChange = jest.fn()

    render(
      <CaviStatistics
        cavi={mockCavi}
        filteredCavi={mockCavi}
        onFilterChange={mockOnFilterChange}
        activeFilter={null}
      />
    )

    // Check if statistics are displayed
    expect(screen.getByText('Statistiche Cavi')).toBeInTheDocument()
    
    // Test clicking on installed KPI
    const installedKpi = screen.getByText('installati')
    await userEvent.click(installedKpi.closest('div')!)
    
    expect(mockOnFilterChange).toHaveBeenCalledWith('installed')
  })

  test('Filter indicator shows when active', () => {
    render(
      <CaviStatistics
        cavi={mockCavi}
        filteredCavi={mockCavi}
        onFilterChange={jest.fn()}
        activeFilter="installed"
      />
    )

    expect(screen.getByText('Filtro attivo')).toBeInTheDocument()
    expect(screen.getByText('Rimuovi filtro')).toBeInTheDocument()
  })
})

describe('Smart Filter Component', () => {
  test('SmartCaviFilter handles search input', async () => {
    const mockOnFilteredDataChange = jest.fn()

    render(
      <SmartCaviFilter
        cavi={mockCavi}
        onFilteredDataChange={mockOnFilteredDataChange}
        totalCount={2}
      />
    )

    const searchInput = screen.getByPlaceholderText(/Cerca per ID, sistema/)
    await userEvent.type(searchInput, 'TEST001')

    // Should filter the data
    expect(mockOnFilteredDataChange).toHaveBeenCalled()
  })

  test('Selection toggle button appears when items available', () => {
    render(
      <SmartCaviFilter
        cavi={mockCavi}
        onFilteredDataChange={jest.fn()}
        onSelectionToggle={jest.fn()}
        totalCount={2}
      />
    )

    expect(screen.getByText('Abilita Selezione')).toBeInTheDocument()
  })

  test('Deselect all button appears when items selected', () => {
    render(
      <SmartCaviFilter
        cavi={mockCavi}
        onFilteredDataChange={jest.fn()}
        onSelectionToggle={jest.fn()}
        selectionEnabled={true}
        selectedCount={2}
        totalCount={2}
      />
    )

    expect(screen.getByText(/Deseleziona Tutto \(2\)/)).toBeInTheDocument()
  })
})

describe('Loading States', () => {
  test('LoadingSpinner renders with correct size', () => {
    render(<LoadingSpinner size="lg" />)
    
    const spinner = screen.getByLabelText('Caricamento in corso')
    expect(spinner).toHaveClass('h-8', 'w-8')
  })

  test('ActionLoading shows appropriate icon for action', () => {
    render(<ActionLoading action="connect" />)
    
    // Should render with blue color for connect action
    const icon = document.querySelector('.text-blue-500')
    expect(icon).toBeInTheDocument()
  })

  test('ProgressBar displays correct percentage', () => {
    render(<ProgressBar progress={75} showPercentage={true} />)
    
    expect(screen.getByText('75.0%')).toBeInTheDocument()
  })
})

describe('Accessibility Features', () => {
  test('AccessibleButton has proper ARIA attributes', () => {
    render(
      <AccessibleButton
        ariaLabel="Test button"
        ariaDescribedBy="test-description"
      >
        Click me
      </AccessibleButton>
    )

    const button = screen.getByRole('button')
    expect(button).toHaveAttribute('aria-label', 'Test button')
    expect(button).toHaveAttribute('aria-describedby', 'test-description')
  })

  test('LiveRegion announces dynamic content', () => {
    render(<LiveRegion message="Content updated" politeness="assertive" />)
    
    const liveRegion = screen.getByText('Content updated')
    expect(liveRegion).toHaveAttribute('aria-live', 'assertive')
    expect(liveRegion).toHaveAttribute('aria-atomic', 'true')
  })

  test('FocusTrap manages focus correctly', () => {
    render(
      <FocusTrap active={true}>
        <button>First</button>
        <button>Second</button>
        <button>Last</button>
      </FocusTrap>
    )

    const firstButton = screen.getByText('First')
    expect(firstButton).toHaveFocus()
  })
})

describe('Enhanced Modal Features', () => {
  test('CertificationModal renders with enhanced features', () => {
    const mockOnClose = jest.fn()
    const mockOnCertify = jest.fn()

    render(
      <CertificationModal
        open={true}
        onClose={mockOnClose}
        cavo={mockCavo}
        onCertify={mockOnCertify}
      />
    )

    // Check for enhanced certification interface
    expect(screen.getByText(/TEST001/)).toBeInTheDocument()
    expect(screen.getByText(/Gestione Certificazione/)).toBeInTheDocument()
    expect(screen.getByText(/Stato Cavo/)).toBeInTheDocument()
    expect(screen.getByText(/Responsabile Certificazione/)).toBeInTheDocument()
  })

  test('ModificaBobinaModal renders with advanced features', () => {
    const mockOnClose = jest.fn()
    const mockOnSave = jest.fn()

    render(
      <ModificaBobinaModal
        open={true}
        onClose={mockOnClose}
        cavo={mockCavo}
        onSave={mockOnSave}
      />
    )

    // Check for enhanced bobina management
    expect(screen.getByText(/TEST001/)).toBeInTheDocument()
    expect(screen.getByText(/Modifica Bobina Cavo/)).toBeInTheDocument()
    expect(screen.getByText(/Informazioni Cavo Selezionato/)).toBeInTheDocument()
    expect(screen.getByText(/Opzioni di modifica/)).toBeInTheDocument()
  })

  test('InserisciMetriModal includes validation and progress tracking', () => {
    const mockOnClose = jest.fn()
    const mockOnSave = jest.fn()

    render(
      <InserisciMetriModal
        open={true}
        onClose={mockOnClose}
        cavo={mockCavo}
        onSave={mockOnSave}
      />
    )

    // Check for meter insertion features
    expect(screen.getByText(/TEST001/)).toBeInTheDocument()
    expect(screen.getByText(/Inserisci Metri Posati/)).toBeInTheDocument()
    expect(screen.getByText(/Metri da Installare/)).toBeInTheDocument()
    expect(screen.getByText(/Già Installati/)).toBeInTheDocument()
    expect(screen.getByLabelText(/Metri Posati/)).toBeInTheDocument()
  })
})

describe('Integration Tests', () => {
  test('Complete cable workflow with enhanced modals', async () => {
    // Test the complete user workflow with enhanced modal features
    const mockOnClose = jest.fn()
    const mockOnConfirm = jest.fn()

    render(
      <DisconnectCableModal
        open={true}
        onClose={mockOnClose}
        cavo={mockCavo}
        onConfirm={mockOnConfirm}
      />
    )

    // Check for enhanced features
    expect(screen.getByText(/TEST001/)).toBeInTheDocument()
    expect(screen.getByText(/Conferma Scollegamento/)).toBeInTheDocument()

    // Test ESC key functionality
    fireEvent.keyDown(document, { key: 'Escape' })
    expect(mockOnClose).toHaveBeenCalled()
  })

  test('Keyboard navigation across enhanced components', async () => {
    // Test enhanced keyboard navigation
    const mockOnClose = jest.fn()
    const mockOnGenerate = jest.fn()

    render(
      <GeneratePdfModal
        open={true}
        onClose={mockOnClose}
        cavo={mockCavo}
        onGenerate={mockOnGenerate}
      />
    )

    // Test tab navigation and form interaction
    const fileInput = screen.getByLabelText(/Nome File/)
    expect(fileInput).toBeInTheDocument()

    // Test ESC key
    fireEvent.keyDown(document, { key: 'Escape' })
    expect(mockOnClose).toHaveBeenCalled()
  })

  test('Enhanced accessibility features', async () => {
    // Test enhanced accessibility compliance
    const mockOnClose = jest.fn()
    const mockOnCertify = jest.fn()

    render(
      <CertificationModal
        open={true}
        onClose={mockOnClose}
        cavo={mockCavo}
        onCertify={mockOnCertify}
      />
    )

    // Check for ARIA labels and proper structure
    expect(screen.getByText(/Gestione Certificazione/)).toBeInTheDocument()
    expect(screen.getByText(/Stato Cavo/)).toBeInTheDocument()

    // Check for proper form labeling
    expect(screen.getByLabelText(/Responsabile Certificazione/)).toBeInTheDocument()
  })
})
