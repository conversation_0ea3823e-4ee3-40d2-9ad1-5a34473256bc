'use client'

import { useState } from 'react'

export default function TestLoginPage() {
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)

  const testAdminLogin = async () => {
    setLoading(true)
    setResult('Testing...')
    
    try {
      console.log('Starting admin login test...')
      
      const formData = new FormData()
      formData.append('username', 'admin')
      formData.append('password', 'admin')

      console.log('Sending request to:', 'http://localhost:8001/api/auth/login')
      
      const response = await fetch('http://localhost:8001/api/auth/login', {
        method: 'POST',
        body: formData
      })

      console.log('Response status:', response.status)
      console.log('Response headers:', response.headers)

      const data = await response.json()
      console.log('Response data:', data)

      if (response.ok) {
        setResult(`SUCCESS: ${JSON.stringify(data, null, 2)}`)
      } else {
        setResult(`ERROR: ${response.status} - ${JSON.stringify(data, null, 2)}`)
      }
    } catch (error) {
      console.error('Login test error:', error)
      setResult(`EXCEPTION: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  const testCantiereLogin = async () => {
    setLoading(true)
    setResult('Testing...')
    
    try {
      console.log('Starting cantiere login test...')
      
      const response = await fetch('http://localhost:8001/api/auth/login/cantiere', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          codice_univoco: 'TEST123',
          password: 'test123'
        })
      })

      console.log('Response status:', response.status)
      const data = await response.json()
      console.log('Response data:', data)

      if (response.ok) {
        setResult(`SUCCESS: ${JSON.stringify(data, null, 2)}`)
      } else {
        setResult(`ERROR: ${response.status} - ${JSON.stringify(data, null, 2)}`)
      }
    } catch (error) {
      console.error('Cantiere login test error:', error)
      setResult(`EXCEPTION: ${error.message}`)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>Test Login Page</h1>
      
      <div style={{ marginBottom: '20px' }}>
        <button 
          onClick={testAdminLogin} 
          disabled={loading}
          style={{ 
            padding: '10px 20px', 
            marginRight: '10px',
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Testing...' : 'Test Admin Login'}
        </button>
        
        <button 
          onClick={testCantiereLogin} 
          disabled={loading}
          style={{ 
            padding: '10px 20px',
            backgroundColor: '#28a745',
            color: 'white',
            border: 'none',
            borderRadius: '5px',
            cursor: loading ? 'not-allowed' : 'pointer'
          }}
        >
          {loading ? 'Testing...' : 'Test Cantiere Login'}
        </button>
      </div>

      <div style={{ 
        backgroundColor: '#f8f9fa', 
        padding: '15px', 
        borderRadius: '5px',
        whiteSpace: 'pre-wrap',
        minHeight: '200px'
      }}>
        {result || 'Click a button to test login...'}
      </div>
    </div>
  )
}
