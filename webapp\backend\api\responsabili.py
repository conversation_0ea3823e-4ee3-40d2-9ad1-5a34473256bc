from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ..core.security import get_current_active_user
from ..database import get_db
from ..models.user import User
from ..models.responsabile import Responsabile
from ..schemas.responsabile import (
    ResponsabileCreate, ResponsabileUpdate, ResponsabileInDB, ResponsabileResponse
)

# Importa le funzioni dal modulo responsabili esistente
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', '..'))
from modules.responsabili import (
    ottieni_o_crea_responsabile, trova_responsabile_per_contatto, crea_responsabile
)

router = APIRouter()

@router.get("/cantiere/{id_cantiere}", response_model=List[ResponsabileResponse])
async def ottieni_responsabili_cantiere(
    id_cantiere: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Ottiene la lista dei responsabili per un cantiere.
    """
    try:
        responsabili = db.query(Responsabile).filter(
            Responsabile.id_cantiere == id_cantiere,
            Responsabile.attivo == True
        ).all()
        
        return responsabili
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nel recupero dei responsabili: {str(e)}"
        )

@router.post("/cantiere/{id_cantiere}", response_model=ResponsabileResponse, status_code=status.HTTP_201_CREATED)
async def crea_responsabile_cantiere(
    id_cantiere: int,
    responsabile_data: ResponsabileCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Crea un nuovo responsabile per un cantiere.
    """
    try:
        # Usa la funzione del modulo esistente per l'auto-inserimento
        responsabile_result = ottieni_o_crea_responsabile(
            nome_responsabile=responsabile_data.nome_responsabile,
            id_cantiere=id_cantiere,
            email=responsabile_data.email,
            telefono=responsabile_data.telefono
        )
        
        if not responsabile_result:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Errore nella creazione del responsabile"
            )
        
        # Recupera il responsabile dal database per la risposta
        responsabile = db.query(Responsabile).filter(
            Responsabile.id_responsabile == responsabile_result['id_responsabile']
        ).first()
        
        if not responsabile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Responsabile creato ma non trovato"
            )
        
        return responsabile
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella creazione del responsabile: {str(e)}"
        )

@router.get("/cantiere/{id_cantiere}/cerca", response_model=Optional[ResponsabileResponse])
async def cerca_responsabile_per_contatto(
    id_cantiere: int,
    email: Optional[str] = None,
    telefono: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Cerca un responsabile esistente per email o telefono.
    """
    try:
        if not email and not telefono:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Almeno uno tra email e telefono deve essere specificato"
            )
        
        # Usa la funzione del modulo esistente
        responsabile_data = trova_responsabile_per_contatto(
            id_cantiere=id_cantiere,
            email=email,
            telefono=telefono
        )
        
        if not responsabile_data:
            return None
        
        # Recupera il responsabile dal database
        responsabile = db.query(Responsabile).filter(
            Responsabile.id_responsabile == responsabile_data['id_responsabile']
        ).first()
        
        return responsabile
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nella ricerca del responsabile: {str(e)}"
        )

@router.put("/{id_responsabile}", response_model=ResponsabileResponse)
async def aggiorna_responsabile(
    id_responsabile: int,
    responsabile_update: ResponsabileUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Aggiorna un responsabile esistente.
    """
    try:
        responsabile = db.query(Responsabile).filter(
            Responsabile.id_responsabile == id_responsabile
        ).first()
        
        if not responsabile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Responsabile non trovato"
            )
        
        # Aggiorna i campi specificati
        update_data = responsabile_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(responsabile, field, value)
        
        db.commit()
        db.refresh(responsabile)
        
        return responsabile
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'aggiornamento del responsabile: {str(e)}"
        )

@router.delete("/{id_responsabile}", status_code=status.HTTP_204_NO_CONTENT)
async def elimina_responsabile(
    id_responsabile: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Disattiva un responsabile (soft delete).
    """
    try:
        responsabile = db.query(Responsabile).filter(
            Responsabile.id_responsabile == id_responsabile
        ).first()
        
        if not responsabile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Responsabile non trovato"
            )
        
        # Soft delete - imposta attivo a False
        responsabile.attivo = False
        db.commit()
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Errore nell'eliminazione del responsabile: {str(e)}"
        )
