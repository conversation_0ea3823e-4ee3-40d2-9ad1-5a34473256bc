(()=>{var e={};e.id=209,e.ids=[209],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},80486:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>l,routeModule:()=>u,serverHooks:()=>w,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>c});var s={};t.r(s),t.d(s,{POST:()=>i});var a=t(96559),o=t(48088),n=t(37719),p=t(32190);async function i(e){try{let r=await e.json(),t=await fetch("http://localhost:8001/api/password/request-password-reset",{method:"POST",headers:{"Content-Type":"application/json","X-Forwarded-For":e.headers.get("x-forwarded-for")||e.headers.get("x-real-ip")||e.ip||"unknown","User-Agent":e.headers.get("user-agent")||"unknown"},body:JSON.stringify(r)}),s=await t.json();return p.NextResponse.json(s,{status:t.status,headers:{"Content-Type":"application/json"}})}catch(e){return p.NextResponse.json({success:!1,detail:"Errore interno del server"},{status:500})}}let u=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/password/request-password-reset/route",pathname:"/api/password/request-password-reset",filename:"route",bundlePath:"app/api/password/request-password-reset/route"},resolvedPagePath:"C:\\CMS\\webapp-nextjs\\src\\app\\api\\password\\request-password-reset\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:c,serverHooks:w}=u;function l(){return(0,n.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:c})}},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,580],()=>t(80486));module.exports=s})();