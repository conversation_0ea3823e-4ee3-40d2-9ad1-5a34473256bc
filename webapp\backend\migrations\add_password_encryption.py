"""
Migrazione per aggiungere il campo password_cantiere_encrypted alla tabella cantieri.
Questo campo permetterà il recupero delle password dimenticate.
"""

import sys
import os

# Aggiungi il percorso della webapp al PYTHONPATH
webapp_path = os.path.join(os.path.dirname(__file__), '..', '..')
sys.path.insert(0, webapp_path)

import psycopg2
from backend.core.password_encryption import encrypt_password
from backend.config import settings

def get_db_connection():
    """Crea una connessione diretta al database usando psycopg2"""
    return psycopg2.connect(
        host="localhost",
        database="cantieri",
        user="postgres",
        password="Taranto"
    )

def add_password_encryption_field():
    """
    Aggiunge il campo password_cantiere_encrypted alla tabella cantieri
    e cripta le password esistenti.
    """
    try:
        # Connessione al database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🔄 Inizio migrazione: aggiunta campo password_cantiere_encrypted...")
        
        # 1. Aggiungi il nuovo campo
        try:
            cursor.execute("""
                ALTER TABLE cantieri 
                ADD COLUMN password_cantiere_encrypted VARCHAR(500);
            """)
            print("✅ Campo password_cantiere_encrypted aggiunto con successo")
        except psycopg2.errors.DuplicateColumn:
            print("⚠️  Campo password_cantiere_encrypted già esistente")
            conn.rollback()
        
        # 2. Recupera tutti i cantieri esistenti
        cursor.execute("""
            SELECT id_cantiere, password_cantiere 
            FROM cantieri 
            WHERE password_cantiere_encrypted IS NULL
        """)
        
        cantieri = cursor.fetchall()
        print(f"📋 Trovati {len(cantieri)} cantieri da aggiornare")
        
        # 3. Per ogni cantiere, se la password non è hashata, criptala
        updated_count = 0
        for cantiere_id, password_cantiere in cantieri:
            try:
                # Se la password non è hashata (non inizia con $2), criptala
                if not password_cantiere.startswith('$2'):
                    encrypted_password = encrypt_password(password_cantiere)
                    
                    if encrypted_password:
                        cursor.execute("""
                            UPDATE cantieri 
                            SET password_cantiere_encrypted = %s 
                            WHERE id_cantiere = %s
                        """, (encrypted_password, cantiere_id))
                        
                        updated_count += 1
                        print(f"✅ Cantiere {cantiere_id}: password criptata")
                    else:
                        print(f"❌ Cantiere {cantiere_id}: errore nella criptazione")
                else:
                    print(f"⚠️  Cantiere {cantiere_id}: password già hashata, impossibile criptare")
                    
            except Exception as e:
                print(f"❌ Errore nell'aggiornamento del cantiere {cantiere_id}: {e}")
        
        # 4. Commit delle modifiche
        conn.commit()
        print(f"✅ Migrazione completata! {updated_count} cantieri aggiornati")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante la migrazione: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

def rollback_password_encryption_field():
    """
    Rimuove il campo password_cantiere_encrypted dalla tabella cantieri.
    """
    try:
        # Connessione al database
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("🔄 Inizio rollback: rimozione campo password_cantiere_encrypted...")
        
        # Rimuovi il campo
        cursor.execute("""
            ALTER TABLE cantieri 
            DROP COLUMN IF EXISTS password_cantiere_encrypted;
        """)
        
        conn.commit()
        print("✅ Rollback completato! Campo password_cantiere_encrypted rimosso")
        
        return True
        
    except Exception as e:
        print(f"❌ Errore durante il rollback: {e}")
        if conn:
            conn.rollback()
        return False
        
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_password_encryption_field()
    else:
        add_password_encryption_field()
