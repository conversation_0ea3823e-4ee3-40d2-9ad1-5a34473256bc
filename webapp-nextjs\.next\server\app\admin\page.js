(()=>{var e={};e.id=698,e.ids=[698],e.modules={1132:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\CMS\\\\webapp-nextjs\\\\src\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx","default")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8819:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},9245:()=>{},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12412:e=>{"use strict";e.exports=require("assert")},12597:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},13861:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21820:e=>{"use strict";e.exports=require("os")},24883:(e,a,s)=>{Promise.resolve().then(s.bind(s,43795))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},40211:(e,a,s)=>{"use strict";s.d(a,{C1:()=>y,bL:()=>N});var t=s(43210),r=s(98599),i=s(11273),l=s(70569),n=s(65551),d=s(83721),o=s(18853),c=s(46059),m=s(14163),u=s(60687),x="Checkbox",[p,h]=(0,i.A)(x),[g,v]=p(x);function j(e){let{__scopeCheckbox:a,checked:s,children:r,defaultChecked:i,disabled:l,form:d,name:o,onCheckedChange:c,required:m,value:p="on",internal_do_not_use_render:h}=e,[v,j]=(0,n.i)({prop:s,defaultProp:i??!1,onChange:c,caller:x}),[b,f]=t.useState(null),[N,w]=t.useState(null),y=t.useRef(!1),A=!b||!!d||!!b.closest("form"),z={checked:v,disabled:l,setChecked:j,control:b,setControl:f,name:o,form:d,value:p,hasConsumerStoppedPropagationRef:y,required:m,defaultChecked:!k(i)&&i,isFormControl:A,bubbleInput:N,setBubbleInput:w};return(0,u.jsx)(g,{scope:a,...z,children:"function"==typeof h?h(z):r})}var b="CheckboxTrigger",f=t.forwardRef(({__scopeCheckbox:e,onKeyDown:a,onClick:s,...i},n)=>{let{control:d,value:o,disabled:c,checked:x,required:p,setControl:h,setChecked:g,hasConsumerStoppedPropagationRef:j,isFormControl:f,bubbleInput:N}=v(b,e),w=(0,r.s)(n,h),y=t.useRef(x);return t.useEffect(()=>{let e=d?.form;if(e){let a=()=>g(y.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[d,g]),(0,u.jsx)(m.sG.button,{type:"button",role:"checkbox","aria-checked":k(x)?"mixed":x,"aria-required":p,"data-state":C(x),"data-disabled":c?"":void 0,disabled:c,value:o,...i,ref:w,onKeyDown:(0,l.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(s,e=>{g(e=>!!k(e)||!e),N&&f&&(j.current=e.isPropagationStopped(),j.current||e.stopPropagation())})})});f.displayName=b;var N=t.forwardRef((e,a)=>{let{__scopeCheckbox:s,name:t,checked:r,defaultChecked:i,required:l,disabled:n,value:d,onCheckedChange:o,form:c,...m}=e;return(0,u.jsx)(j,{__scopeCheckbox:s,checked:r,defaultChecked:i,disabled:n,required:l,onCheckedChange:o,name:t,form:c,value:d,internal_do_not_use_render:({isFormControl:e})=>(0,u.jsxs)(u.Fragment,{children:[(0,u.jsx)(f,{...m,ref:a,__scopeCheckbox:s}),e&&(0,u.jsx)(z,{__scopeCheckbox:s})]})})});N.displayName=x;var w="CheckboxIndicator",y=t.forwardRef((e,a)=>{let{__scopeCheckbox:s,forceMount:t,...r}=e,i=v(w,s);return(0,u.jsx)(c.C,{present:t||k(i.checked)||!0===i.checked,children:(0,u.jsx)(m.sG.span,{"data-state":C(i.checked),"data-disabled":i.disabled?"":void 0,...r,ref:a,style:{pointerEvents:"none",...e.style}})})});y.displayName=w;var A="CheckboxBubbleInput",z=t.forwardRef(({__scopeCheckbox:e,...a},s)=>{let{control:i,hasConsumerStoppedPropagationRef:l,checked:n,defaultChecked:c,required:x,disabled:p,name:h,value:g,form:j,bubbleInput:b,setBubbleInput:f}=v(A,e),N=(0,r.s)(s,f),w=(0,d.Z)(n),y=(0,o.X)(i);t.useEffect(()=>{if(!b)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,a=!l.current;if(w!==n&&e){let s=new Event("click",{bubbles:a});b.indeterminate=k(n),e.call(b,!k(n)&&n),b.dispatchEvent(s)}},[b,w,n,l]);let z=t.useRef(!k(n)&&n);return(0,u.jsx)(m.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:c??z.current,required:x,disabled:p,name:h,value:g,form:j,...a,tabIndex:-1,ref:N,style:{...a.style,...y,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function k(e){return"indeterminate"===e}function C(e){return k(e)?"indeterminate":e?"checked":"unchecked"}z.displayName=A},41550:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},43795:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>en});var t=s(60687),r=s(43210),i=s.n(r);s(9245);var l=s(44493),n=s(4780),d=s(41862);let o=i().forwardRef(({className:e,variant:a="primary",size:s="md",loading:r=!1,glow:i=!1,icon:l,children:o,disabled:c,...m},u)=>{let x=c||r;return(0,t.jsxs)("button",{className:(0,n.cn)("relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none",{primary:"btn-primary",secondary:"btn-secondary",success:"btn-success",danger:"btn-danger",outline:"btn-outline",quick:"btn-quick"}[a],{sm:"btn-sm",md:"px-6 py-3",lg:"btn-lg"}[s],i&&"quick"!==a&&"btn-glow",x&&"opacity-50 cursor-not-allowed hover:shadow-none",e),disabled:x,ref:u,...m,children:[(0,t.jsx)("span",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]"}),(0,t.jsxs)("span",{className:"relative flex items-center justify-center gap-2",children:[r?(0,t.jsx)(d.A,{className:"h-4 w-4 animate-spin btn-icon"}):l?(0,t.jsx)("span",{className:"btn-icon",children:l}):null,o]})]})});o.displayName="AnimatedButton";let c=e=>(0,t.jsx)(o,{variant:"primary",...e}),m=e=>(0,t.jsx)(o,{variant:"secondary",...e}),u=e=>(0,t.jsx)(o,{variant:"danger",...e});var x=s(43649),p=s(11860),h=s(63143),g=s(48730),v=s(5336),j=s(88233),b=s(29523);function f({user:e,onEdit:a,onToggleStatus:s,onDelete:i}){let[l,n]=(0,r.useState)(!1);return l?(0,t.jsxs)("div",{className:"flex items-center gap-1 bg-red-50 border border-red-200 rounded-md p-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-xs text-red-700 font-medium",children:"Eliminare?"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1 ml-2",children:[(0,t.jsx)(b.$,{size:"sm",variant:"destructive",onClick:e=>{e.preventDefault(),e.stopPropagation(),n(!1),i()},className:"h-6 px-2 text-xs",children:"S\xec"}),(0,t.jsx)(b.$,{size:"sm",variant:"outline",onClick:e=>{e.preventDefault(),e.stopPropagation(),n(!1)},className:"h-6 px-2 text-xs",children:(0,t.jsx)(p.A,{className:"h-3 w-3"})})]})]}):(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),a()},type:"button",className:"p-1.5 rounded-md hover:bg-blue-50 transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1","aria-label":`Modifica utente ${e.username}`,children:(0,t.jsx)(h.A,{className:"h-4 w-4 text-blue-600 hover:text-blue-700"})}),(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Modifica utente"})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:a=>{a.preventDefault(),a.stopPropagation(),(!e.abilitato||window.confirm(`Sei sicuro di voler disabilitare l'utente "${e.username}"?

L'utente non potr\xe0 pi\xf9 accedere al sistema.`))&&s()},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-slate-50 focus:ring-slate-500"}`,"aria-label":e.abilitato?`Disabilita utente ${e.username}`:`Abilita utente ${e.username}`,children:e.abilitato?(0,t.jsx)(g.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"}):(0,t.jsx)(v.A,{className:"h-4 w-4 text-green-500 hover:text-green-600"})}),"owner"!==e.ruolo&&(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:e.abilitato?"Disabilita utente":"Abilita utente"})]}),(0,t.jsxs)("div",{className:"relative group",children:[(0,t.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),n(!0)},disabled:"owner"===e.ruolo,type:"button",className:`p-1.5 rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 ${"owner"===e.ruolo?"opacity-50 cursor-not-allowed":"hover:scale-105 hover:bg-red-50 focus:ring-red-500"}`,"aria-label":`Elimina utente ${e.username}`,children:(0,t.jsx)(j.A,{className:"h-4 w-4 text-red-500 hover:text-red-600"})}),"owner"!==e.ruolo&&(0,t.jsx)("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-slate-900 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10",children:"Elimina utente"})]})]})}var N=s(96834),w=s(89667),y=s(15391),A=s(6211),z=s(56770),k=s(63213),C=s(16189),E=s(62185),S=s(80013),R=s(15079),T=s(56896),_=s(81806),I=s(93613),D=s(58869),M=s(99891),P=s(12597),V=s(13861),L=s(17313),U=s(41550),$=s(62688);let G=(0,$.A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]);var F=s(84027),q=s(96882),Z=s(8819);function B({user:e,onSave:a,onCancel:s}){let[i,n]=(0,r.useState)({username:"",password:"",ruolo:"user",data_scadenza:"",abilitato:!0,ragione_sociale:"",indirizzo:"",nazione:"",email:"",vat:"",referente_aziendale:""}),[d,o]=(0,r.useState)({}),[u,x]=(0,r.useState)(!1),[j,f]=(0,r.useState)(""),[y,A]=(0,r.useState)(""),[z,k]=(0,r.useState)(!1),[C,$]=(0,r.useState)(0),[B,O]=(0,r.useState)({}),[H,X]=(0,r.useState)(!1),J=e=>{let a=0;return e.length>=8&&(a+=25),/[a-z]/.test(e)&&(a+=25),/[A-Z]/.test(e)&&(a+=25),/[0-9]/.test(e)&&(a+=25),/[^A-Za-z0-9]/.test(e)&&(a+=25),Math.min(a,100)},K=(a,s)=>{let t="pending";switch(a){case"username":t=s&&s.length>=3?"valid":"invalid";break;case"password":t=e?!s||s.length>=8?"valid":"invalid":s&&s.length>=8?"valid":"invalid";break;case"ragione_sociale":t=s&&s.length>=2?"valid":"invalid";break;case"email":t=s?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s)?"valid":"invalid":"valid";break;default:t="valid"}return O(e=>({...e,[a]:t})),t},Q=(e,a)=>{n(s=>({...s,[e]:a})),K(e,a),"password"===e&&$(J(a)),d[e]&&o(a=>({...a,[e]:""})),y&&A(""),j&&f("")},W=()=>{let a=(0,_.GN)({username:i.username,password:e?void 0:i.password,ragione_sociale:i.ragione_sociale,email:i.email,vat:i.vat,indirizzo:i.indirizzo,nazione:i.nazione,referente_aziendale:i.referente_aziendale});return o(a.errors),a.isValid},Y=async s=>{s.preventDefault();let t=`user-form-${e?.id_utente||"new"}-${Date.now()}`;if(!(0,_.Eb)(t,5,6e4))return void f("Troppi tentativi. Riprova tra un minuto.");if(W()){x(!0),f("");try{let s,t={...i};e||(t.ruolo="user"),e&&!t.password.trim()&&delete t.password,t.data_scadenza&&(t.data_scadenza=t.data_scadenza),s=e?await E.dG.updateUser(e.id_utente,t):await E.dG.createUser(t),A(e?"Utente aggiornato con successo!":"Nuovo utente creato con successo!"),setTimeout(()=>{a(s)},1500)}catch(e){f(e.response?.data?.detail||e.message||"Errore durante il salvataggio dell'utente")}finally{x(!1)}}},ee=({status:e})=>"valid"===e?(0,t.jsx)(v.A,{className:"h-4 w-4 text-green-500"}):"invalid"===e?(0,t.jsx)(I.A,{className:"h-4 w-4 text-red-500"}):null;return(0,t.jsxs)(l.Zp,{className:"shadow-lg",children:[(0,t.jsx)(l.aR,{className:"bg-gradient-to-r from-blue-50 to-indigo-50 border-b",children:(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsx)("div",{className:"p-2 bg-blue-100 rounded-lg",children:e?(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600"}):(0,t.jsx)(D.A,{className:"h-5 w-5 text-blue-600"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)(l.ZB,{className:"text-xl",children:e?`Modifica Utente: ${e.username}`:"Crea Nuovo Utente Standard"}),(0,t.jsx)(l.BT,{children:e?"Aggiorna le informazioni dell'utente esistente":"Inserisci i dati per creare un nuovo utente nel sistema"})]})]})}),(0,t.jsxs)(l.Wu,{className:"p-6",children:[j&&(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,t.jsx)(I.A,{className:"h-5 w-5 text-red-600 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-red-600",children:j})]}),y&&(0,t.jsxs)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6 flex items-center gap-3 animate-in slide-in-from-top-2",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 text-green-600 flex-shrink-0"}),(0,t.jsx)("p",{className:"text-green-600",children:y})]}),(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-sm text-slate-600 mb-2",children:[(0,t.jsx)("span",{children:"Completamento form"}),(0,t.jsx)("span",{children:H?"✓ Completo":"In corso..."})]}),(0,t.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-500 ${H?"bg-green-500":"bg-blue-500"}`,style:{width:`${H?100:60}%`}})})]}),(0,t.jsxs)("form",{onSubmit:Y,className:"space-y-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(M.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Credenziali di Accesso"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"username",className:"flex items-center gap-2",children:["Username *",(0,t.jsx)(ee,{status:B.username||"pending"})]}),(0,t.jsx)("div",{className:"relative",children:(0,t.jsx)(w.p,{id:"username",value:i.username,onChange:e=>Q("username",e.target.value),disabled:u,className:`${d.username?"border-red-500":"valid"===B.username?"border-green-500":""} transition-colors duration-200`,placeholder:"Inserisci username univoco"})}),d.username&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(I.A,{className:"h-3 w-3"}),d.username]}),"valid"===B.username&&!d.username&&(0,t.jsxs)("p",{className:"text-sm text-green-600 flex items-center gap-1",children:[(0,t.jsx)(v.A,{className:"h-3 w-3"}),"Username valido"]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"password",className:"flex items-center gap-2",children:[e?"Nuova Password (lascia vuoto per non modificare)":"Password *",(0,t.jsx)(ee,{status:B.password||"pending"})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(w.p,{id:"password",type:z?"text":"password",value:i.password,onChange:e=>Q("password",e.target.value),disabled:u,className:`${d.password?"border-red-500":"valid"===B.password?"border-green-500":""} pr-10 transition-colors duration-200`,placeholder:e?"Lascia vuoto per mantenere la password attuale":"Inserisci password sicura"}),(0,t.jsx)(b.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>k(!z),disabled:u,children:z?(0,t.jsx)(P.A,{className:"h-4 w-4 text-gray-400"}):(0,t.jsx)(V.A,{className:"h-4 w-4 text-gray-400"})})]}),d.password&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(I.A,{className:"h-3 w-3"}),d.password]}),(0,t.jsx)(()=>i.password?(0,t.jsxs)("div",{className:"mt-2 space-y-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,t.jsx)("span",{className:"text-slate-600",children:"Forza password:"}),(0,t.jsx)("span",{className:`font-medium ${C<50?"text-red-600":C<75?"text-yellow-600":"text-green-600"}`,children:C<25?"Molto debole":C<50?"Debole":C<75?"Media":C<100?"Forte":"Molto forte"})]}),(0,t.jsx)("div",{className:"w-full bg-slate-200 rounded-full h-2",children:(0,t.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${C<50?"bg-red-500":C<75?"bg-yellow-500":"bg-green-500"}`,style:{width:`${C}%`}})})]}):null,{})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(L.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Informazioni Aziendali"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"ragione_sociale",className:"flex items-center gap-2",children:["Ragione Sociale *",(0,t.jsx)(ee,{status:B.ragione_sociale||"pending"})]}),(0,t.jsx)(w.p,{id:"ragione_sociale",value:i.ragione_sociale,onChange:e=>Q("ragione_sociale",e.target.value),disabled:u,className:`${d.ragione_sociale?"border-red-500":"valid"===B.ragione_sociale?"border-green-500":""} transition-colors duration-200`,placeholder:"Nome dell'azienda o organizzazione"}),d.ragione_sociale&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(I.A,{className:"h-3 w-3"}),d.ragione_sociale]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"email",className:"flex items-center gap-2",children:[(0,t.jsx)(U.A,{className:"h-4 w-4"}),"Email",(0,t.jsx)(ee,{status:B.email||"pending"})]}),(0,t.jsx)(w.p,{id:"email",type:"email",value:i.email,onChange:e=>Q("email",e.target.value),disabled:u,className:`${d.email?"border-red-500":"valid"===B.email?"border-green-500":""} transition-colors duration-200`,placeholder:"<EMAIL>"}),d.email&&(0,t.jsxs)("p",{className:"text-sm text-red-600 flex items-center gap-1",children:[(0,t.jsx)(I.A,{className:"h-3 w-3"}),d.email]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"indirizzo",className:"flex items-center gap-2",children:[(0,t.jsx)(G,{className:"h-4 w-4"}),"Indirizzo"]}),(0,t.jsx)(w.p,{id:"indirizzo",value:i.indirizzo,onChange:e=>Q("indirizzo",e.target.value),disabled:u,placeholder:"Via, numero civico, citt\xe0",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"nazione",children:"Nazione"}),(0,t.jsx)(w.p,{id:"nazione",value:i.nazione,onChange:e=>Q("nazione",e.target.value),disabled:u,placeholder:"Italia",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"vat",children:"Partita IVA"}),(0,t.jsx)(w.p,{id:"vat",value:i.vat,onChange:e=>Q("vat",e.target.value),disabled:u,placeholder:"*************",className:"transition-colors duration-200"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"referente_aziendale",children:"Referente Aziendale"}),(0,t.jsx)(w.p,{id:"referente_aziendale",value:i.referente_aziendale,onChange:e=>Q("referente_aziendale",e.target.value),disabled:u,placeholder:"Nome e cognome del referente",className:"transition-colors duration-200"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2 pb-2 border-b border-slate-200",children:[(0,t.jsx)(F.A,{className:"h-5 w-5 text-blue-600"}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-slate-900",children:"Configurazioni Account"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"data_scadenza",className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),"Data Scadenza"]}),(0,t.jsx)(w.p,{id:"data_scadenza",type:"date",value:i.data_scadenza,onChange:e=>Q("data_scadenza",e.target.value),disabled:u,className:"transition-colors duration-200"}),(0,t.jsx)("p",{className:"text-xs text-slate-500",children:"Lascia vuoto per account senza scadenza"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(S.J,{htmlFor:"ruolo",children:"Ruolo Utente"}),e?(0,t.jsxs)(R.l6,{value:i.ruolo,onValueChange:e=>Q("ruolo",e),disabled:u,children:[(0,t.jsx)(R.bq,{className:"transition-colors duration-200",children:(0,t.jsx)(R.yv,{})}),(0,t.jsxs)(R.gC,{children:[(0,t.jsx)(R.eb,{value:"user",children:"User Standard"}),(0,t.jsx)(R.eb,{value:"cantieri_user",children:"Cantieri User"})]})]}):(0,t.jsxs)("div",{className:"px-3 py-2 bg-blue-50 border border-blue-200 rounded-md text-sm text-blue-700 flex items-center gap-2",children:[(0,t.jsx)(N.E,{variant:"outline",className:"bg-blue-100 text-blue-700",children:"User Standard"}),(0,t.jsx)("span",{children:"Ruolo predefinito per nuovi utenti"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3 p-4 bg-slate-50 rounded-lg",children:[(0,t.jsx)(T.S,{id:"abilitato",checked:i.abilitato,onCheckedChange:e=>Q("abilitato",e),disabled:u||e&&"owner"===e.ruolo}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)(S.J,{htmlFor:"abilitato",className:"font-medium",children:"Account Abilitato"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"L'utente pu\xf2 accedere al sistema e utilizzare le funzionalit\xe0"})]}),i.abilitato?(0,t.jsx)(N.E,{className:"bg-green-100 text-green-700",children:"Attivo"}):(0,t.jsx)(N.E,{variant:"outline",className:"bg-red-100 text-red-700",children:"Disabilitato"})]})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center gap-4 pt-8 border-t border-slate-200",children:[(0,t.jsx)("div",{className:"text-sm text-slate-600",children:H?(0,t.jsxs)("span",{className:"flex items-center gap-1 text-green-600",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),"Form completato correttamente"]}):(0,t.jsxs)("span",{className:"flex items-center gap-1",children:[(0,t.jsx)(q.A,{className:"h-4 w-4"}),"Completa i campi obbligatori per continuare"]})}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsx)(m,{type:"button",onClick:s,disabled:u,icon:(0,t.jsx)(p.A,{className:"h-4 w-4"}),className:"min-w-[120px]",children:"Annulla"}),(0,t.jsx)(c,{type:"submit",loading:u,disabled:!H,icon:(0,t.jsx)(Z.A,{className:"h-4 w-4"}),glow:H,className:"min-w-[120px]",children:u?"Salvataggio...":e?"Aggiorna Utente":"Crea Utente"})]})]})]})]})]})}var O=s(61611),H=s(78122);function X(){let[e,a]=(0,r.useState)(null),[s,i]=(0,r.useState)(!1),[n,o]=(0,r.useState)(""),m=async()=>{i(!0),o("");try{let e=await E.dG.getDatabaseData();a(e)}catch(e){o(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati del database")}finally{i(!1)}},u=(e,a,s)=>{if(!a||0===a.length)return(0,t.jsxs)("div",{className:"text-center py-4 text-slate-500 border rounded-lg",children:["Nessun dato disponibile per ",s]});let r=Object.keys(a[0]);return(0,t.jsxs)("div",{className:"border rounded-lg overflow-hidden mb-6",children:[(0,t.jsxs)("div",{className:"bg-slate-100 px-4 py-3 border-b",children:[(0,t.jsx)("h4",{className:"font-medium text-slate-900",children:s}),(0,t.jsxs)("p",{className:"text-sm text-slate-600",children:["Totale record: ",a.length]})]}),(0,t.jsx)("div",{className:"overflow-x-auto max-h-96",children:(0,t.jsxs)(A.XI,{children:[(0,t.jsx)(A.A0,{className:"sticky top-0 bg-slate-50",children:(0,t.jsx)(A.Hj,{children:r.map(e=>(0,t.jsx)(A.nd,{className:"font-medium",children:e},e))})}),(0,t.jsx)(A.BF,{children:a.map((e,a)=>(0,t.jsx)(A.Hj,{children:r.map(a=>(0,t.jsx)(A.nA,{className:"font-mono text-sm",children:null!==e[a]&&void 0!==e[a]?String(e[a]):(0,t.jsx)("span",{className:"text-slate-400",children:"NULL"})},a))},a))})]})})]})},x=[{key:"users",title:"Utenti",description:"Tutti gli utenti del sistema"},{key:"cantieri",title:"Cantieri",description:"Tutti i cantieri/progetti"},{key:"cavi",title:"Cavi",description:"Tutti i cavi installati"},{key:"parco_cavi",title:"Bobine",description:"Tutte le bobine del parco cavi"},{key:"strumenti_certificati",title:"Strumenti",description:"Strumenti certificati"},{key:"certificazioni_cavi",title:"Certificazioni",description:"Certificazioni dei cavi"}];return(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(O.A,{className:"h-5 w-5"}),"Visualizzazione Database Raw"]}),(0,t.jsx)(c,{size:"sm",onClick:m,loading:s,icon:(0,t.jsx)(H.A,{className:"h-4 w-4"}),children:"Aggiorna"})]})}),(0,t.jsxs)(l.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(V.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Visualizzazione Raw del Database"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Questa sezione mostra i dati grezzi delle tabelle del database. Utile per debugging e analisi dei dati."})]})]})}),s?(0,t.jsxs)("div",{className:"flex items-center justify-center py-12",children:[(0,t.jsx)(d.A,{className:"h-8 w-8 animate-spin mr-3"}),(0,t.jsx)("span",{className:"text-lg",children:"Caricamento dati database..."})]}):n?(0,t.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-6",children:[(0,t.jsx)("p",{className:"text-red-600 font-medium",children:"Errore durante il caricamento:"}),(0,t.jsx)("p",{className:"text-red-600",children:n})]}):e?(0,t.jsxs)("div",{className:"space-y-8",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Visualizzazione completa di tutte le tabelle del database. I dati sono mostrati in formato raw per debugging e analisi."})}),x.map(a=>e[a.key]&&(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"mb-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-slate-900",children:a.title}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:a.description})]}),u(a.key,e[a.key],a.title)]},a.key)),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-slate-900 mb-2",children:"Riepilogo Database"}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:x.map(a=>(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-slate-600",children:[a.title,":"]}),(0,t.jsxs)("span",{className:"font-medium",children:[e[a.key]?e[a.key].length:0," record"]})]},a.key))})]})]}):(0,t.jsx)("div",{className:"text-center py-12 text-slate-500",children:"Nessun dato disponibile"})]})]})}let J=(0,$.A)("rotate-ccw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);function K(){let{user:e}=(0,k.A)(),[a,s]=(0,r.useState)(""),[i,n]=(0,r.useState)(!1),[d,o]=(0,r.useState)(""),[c,p]=(0,r.useState)(!1),[h,v]=(0,r.useState)(!1),[f,N]=(0,r.useState)(""),[y,A]=(0,r.useState)(""),[z,C]=(0,r.useState)(10),[R,_]=(0,r.useState)(!1),[I,D]=(0,r.useState)(!1),L=async()=>{if(!I)return void N("Devi completare il countdown di sicurezza");v(!0),N(""),A("");try{if(!d.trim())throw Error("Password amministratore richiesta");await E.dG.resetDatabase(),A("Database resettato con successo! Tutti i dati sono stati eliminati."),s(""),n(!1),o(""),D(!1),_(!1),C(10)}catch(e){N(e.response?.data?.detail||e.message||"Errore durante il reset del database")}finally{v(!1)}},U="RESET DATABASE"===a&&i&&d.trim()&&!h&&!R,$=I&&!h;return(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2 text-red-600",children:[(0,t.jsx)(J,{className:"h-5 w-5"}),"Reset Database"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(x.A,{className:"h-6 w-6 text-red-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-bold text-red-900 text-lg",children:"⚠️ ATTENZIONE - OPERAZIONE IRREVERSIBILE"}),(0,t.jsxs)("div",{className:"text-red-700 mt-2 space-y-2",children:[(0,t.jsx)("p",{className:"font-medium",children:"Questa operazione eliminer\xe0 PERMANENTEMENTE tutti i dati dal database:"}),(0,t.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-sm",children:[(0,t.jsx)("li",{children:"Tutti gli utenti (eccetto l'amministratore principale)"}),(0,t.jsx)("li",{children:"Tutti i cantieri e i progetti"}),(0,t.jsx)("li",{children:"Tutti i cavi installati"}),(0,t.jsx)("li",{children:"Tutte le bobine del parco cavi"}),(0,t.jsx)("li",{children:"Tutti i comandi e le certificazioni"}),(0,t.jsx)("li",{children:"Tutti i report e i dati di produttivit\xe0"})]}),(0,t.jsx)("p",{className:"font-bold text-red-800 mt-3",children:"NON \xc8 POSSIBILE RECUPERARE I DATI DOPO IL RESET!"})]})]})]})}),f&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:f})}),y&&(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-green-600",children:y})}),(0,t.jsxs)("div",{className:"space-y-4 border-t pt-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-semibold text-slate-900 mb-4",children:"Conferma Reset Database"}),(0,t.jsx)("p",{className:"text-sm text-slate-600 mb-4",children:"Per procedere con il reset, devi confermare l'operazione seguendo questi passaggi:"})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"confirm-text",className:"text-sm font-medium",children:["1. Digita esattamente: ",(0,t.jsx)("code",{className:"bg-slate-100 px-2 py-1 rounded text-red-600 font-bold",children:"RESET DATABASE"})]}),(0,t.jsx)(w.p,{id:"confirm-text",value:a,onChange:e=>s(e.target.value),placeholder:"Digita: RESET DATABASE",disabled:h||R,className:"RESET DATABASE"===a?"border-green-500":""})]}),(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)(T.S,{id:"confirm-checkbox",checked:i,onCheckedChange:n,disabled:h||R}),(0,t.jsx)(S.J,{htmlFor:"confirm-checkbox",className:"text-sm leading-relaxed",children:"2. Confermo di aver compreso che questa operazione eliminer\xe0 TUTTI i dati dal database in modo PERMANENTE e IRREVERSIBILE. Ho effettuato un backup se necessario."})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)(S.J,{htmlFor:"admin-password",className:"text-sm font-medium flex items-center gap-2",children:[(0,t.jsx)(M.A,{className:"h-4 w-4 text-blue-600"}),"3. Inserisci la tua password di amministratore per confermare l'identit\xe0"]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(w.p,{id:"admin-password",type:c?"text":"password",value:d,onChange:e=>o(e.target.value),placeholder:"Password amministratore",disabled:h||R,className:d.trim()?"border-green-500":""}),(0,t.jsx)(b.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0",onClick:()=>p(!c),disabled:h||R,children:c?(0,t.jsx)(P.A,{className:"h-4 w-4"}):(0,t.jsx)(V.A,{className:"h-4 w-4"})})]})]})]}),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-medium text-slate-900 mb-3",children:"Stato Conferma:"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${"RESET DATABASE"===a?"bg-green-500":"bg-red-500"}`}),(0,t.jsxs)("span",{children:["Testo di conferma: ","RESET DATABASE"===a?"✓ Corretto":"✗ Richiesto"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${i?"bg-green-500":"bg-red-500"}`}),(0,t.jsxs)("span",{children:["Checkbox confermata: ",i?"✓ S\xec":"✗ Richiesta"]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:`w-3 h-3 rounded-full ${d.trim()?"bg-green-500":"bg-red-500"}`}),(0,t.jsxs)("span",{children:["Password amministratore: ",d.trim()?"✓ Inserita":"✗ Richiesta"]})]}),R&&(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-orange-50 border border-orange-200 rounded",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)("span",{className:"text-orange-700 font-medium",children:["Countdown di sicurezza: ",z," secondi"]})]}),I&&(0,t.jsxs)("div",{className:"flex items-center gap-2 mt-3 p-2 bg-red-50 border border-red-200 rounded",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsx)("span",{className:"text-red-700 font-medium",children:"⚠️ Pronto per il reset - Ultima possibilit\xe0 di annullare"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[!I&&!R&&(0,t.jsx)(u,{onClick:()=>{"RESET DATABASE"===a&&i&&d.trim()?(_(!0),C(10),D(!1),N("")):N("Completa tutti i passaggi di conferma prima di procedere")},disabled:!U,className:"w-full",size:"lg",icon:(0,t.jsx)(g.A,{className:"h-5 w-5"}),children:"INIZIA COUNTDOWN DI SICUREZZA (10 secondi)"}),R&&(0,t.jsxs)("div",{className:"w-full p-4 bg-orange-50 border-2 border-orange-300 rounded-lg text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-3",children:[(0,t.jsx)(g.A,{className:"h-6 w-6 text-orange-600 animate-pulse"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-orange-700",children:["Countdown: ",z," secondi"]})]}),(0,t.jsx)("p",{className:"text-sm text-orange-600 mt-2",children:"Il pulsante di reset si attiver\xe0 al termine del countdown"})]}),I&&(0,t.jsx)(u,{onClick:L,disabled:!$,className:"w-full animate-pulse",size:"lg",loading:h,icon:(0,t.jsx)(j.A,{className:"h-5 w-5"}),glow:!0,children:h?"RESET IN CORSO...":"\uD83D\uDEA8 RESET DATABASE - ELIMINA TUTTI I DATI \uD83D\uDEA8"}),!U&&!I&&!R&&(0,t.jsx)("p",{className:"text-center text-sm text-slate-500",children:"Completa tutti i passaggi di conferma per iniziare il countdown"}),I&&(0,t.jsx)(m,{onClick:()=>{D(!1),_(!1),C(10)},className:"w-full",size:"lg",disabled:h,children:"ANNULLA RESET"})]})]}),(0,t.jsxs)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4 text-sm",children:[(0,t.jsx)("h5",{className:"font-medium text-blue-900 mb-2",children:"Informazioni Tecniche:"}),(0,t.jsxs)("ul",{className:"text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Il reset manterr\xe0 la struttura delle tabelle"}),(0,t.jsx)("li",{children:"• L'utente amministratore principale verr\xe0 ricreato"}),(0,t.jsx)("li",{children:"• Le configurazioni di sistema verranno ripristinate ai valori di default"}),(0,t.jsx)("li",{children:"• L'operazione pu\xf2 richiedere alcuni minuti per completarsi"})]})]})]})]})}var Q=s(23361);let W=(0,$.A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]]),Y=(0,$.A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]);var ee=s(10022),ea=s(96474);function es(){let[e,a]=(0,r.useState)("categorie");return(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"h-5 w-5"}),"Database Tipologie Cavi"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-6",children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start gap-3",children:[(0,t.jsx)(Q.A,{className:"h-5 w-5 text-blue-600 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-900",children:"Database Enciclopedico Tipologie Cavi"}),(0,t.jsx)("p",{className:"text-sm text-blue-700 mt-1",children:"Gestisci il database delle tipologie di cavi organizzato per categorie, produttori, standard e tipologie specifiche. Questo database serve come riferimento per la classificazione e gestione dei cavi nei progetti."})]})]})}),(0,t.jsxs)(z.tU,{value:e,onValueChange:a,className:"w-full",children:[(0,t.jsxs)(z.j7,{className:"grid w-full grid-cols-4",children:[(0,t.jsxs)(z.Xi,{value:"categorie",className:"flex items-center gap-2",children:[(0,t.jsx)(W,{className:"h-4 w-4"}),"Categorie"]}),(0,t.jsxs)(z.Xi,{value:"produttori",className:"flex items-center gap-2",children:[(0,t.jsx)(Y,{className:"h-4 w-4"}),"Produttori"]}),(0,t.jsxs)(z.Xi,{value:"standard",className:"flex items-center gap-2",children:[(0,t.jsx)(ee.A,{className:"h-4 w-4"}),"Standard"]}),(0,t.jsxs)(z.Xi,{value:"tipologie",className:"flex items-center gap-2",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),"Tipologie"]})]}),(0,t.jsxs)(z.av,{value:"categorie",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Categorie Cavi"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le categorie principali di cavi (es. Energia, Controllo, Strumentazione, ecc.)"})]}),(0,t.jsxs)(b.$,{children:[(0,t.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Nuova Categoria"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(W,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione categorie cavi - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile creare, modificare ed eliminare le categorie di cavi"})]})]}),(0,t.jsxs)(z.av,{value:"produttori",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Produttori"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci l'elenco dei produttori di cavi (es. Prysmian, Nexans, General Cable, ecc.)"})]}),(0,t.jsxs)(b.$,{children:[(0,t.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Nuovo Produttore"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(Y,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione produttori - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire l'anagrafica dei produttori di cavi"})]})]}),(0,t.jsxs)(z.av,{value:"standard",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Standard e Normative"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci gli standard tecnici e le normative (es. CEI, IEC, EN, CENELEC, ecc.)"})]}),(0,t.jsxs)(b.$,{children:[(0,t.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Nuovo Standard"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(ee.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione standard - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire gli standard tecnici e le normative di riferimento"})]})]}),(0,t.jsxs)(z.av,{value:"tipologie",className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold",children:"Tipologie Specifiche"}),(0,t.jsx)("p",{className:"text-sm text-slate-600",children:"Gestisci le tipologie specifiche di cavi con tutte le caratteristiche tecniche"})]}),(0,t.jsxs)(b.$,{children:[(0,t.jsx)(ea.A,{className:"h-4 w-4 mr-2"}),"Nuova Tipologia"]})]}),(0,t.jsxs)("div",{className:"text-center py-12 text-slate-500",children:[(0,t.jsx)(Q.A,{className:"h-12 w-12 mx-auto mb-4 text-slate-400"}),(0,t.jsx)("p",{children:"Gestione tipologie - Da implementare"}),(0,t.jsx)("p",{className:"text-sm mt-2",children:"Qui sar\xe0 possibile gestire le tipologie specifiche con caratteristiche tecniche dettagliate"})]})]})]}),(0,t.jsxs)("div",{className:"bg-slate-50 border border-slate-200 rounded-lg p-4",children:[(0,t.jsx)("h5",{className:"font-medium text-slate-900 mb-2",children:"Struttura Database Tipologie:"}),(0,t.jsxs)("div",{className:"text-sm text-slate-600 space-y-1",children:[(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Categorie:"})," Classificazione principale (Energia, Controllo, Strumentazione, Dati, ecc.)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Produttori:"})," Aziende produttrici con informazioni di contatto"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Standard:"})," Normative tecniche di riferimento (CEI, IEC, EN, CENELEC)"]}),(0,t.jsxs)("p",{children:[(0,t.jsx)("strong",{children:"Tipologie:"})," Specifiche tecniche dettagliate per ogni tipo di cavo"]})]})]})]})]})}var et=s(41312);let er=(0,$.A)("user-plus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]);var ei=s(99270);let el=(0,$.A)("log-in",[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]]);function en(){let e=(0,C.useRouter)(),[a,s]=(0,r.useState)("visualizza-utenti"),[i,n]=(0,r.useState)(""),[o,m]=(0,r.useState)([]),[u,x]=(0,r.useState)([]),[p,g]=(0,r.useState)(!0),[v,j]=(0,r.useState)(""),[b,S]=(0,r.useState)(null),[R,T]=(0,r.useState)({open:!1,message:"",severity:"success"}),{user:_,impersonateUser:I}=(0,k.A)(),D=async()=>{try{if(g(!0),j(""),"visualizza-utenti"===a||"crea-utente"===a||"accedi-come-utente"===a){let e=await E.dG.getUsers();m(e)}else if("cantieri"===a){let e=await E._I.getCantieri();x(e)}}catch(e){j(e.response?.data?.detail||e.message||"Errore durante il caricamento dei dati")}finally{g(!1)}},M=e=>{S(e),s("modifica-utente")},P=async e=>{try{await E.dG.toggleUserStatus(e),D()}catch(e){j(e.response?.data?.detail||"Errore durante la modifica dello stato utente")}},V=async e=>{if(confirm("Sei sicuro di voler eliminare questo utente?"))try{await E.dG.deleteUser(e),D()}catch(e){j(e.response?.data?.detail||"Errore durante l'eliminazione dell'utente")}},L=e=>{S(null),s("visualizza-utenti"),D()},U=()=>{S(null),s("visualizza-utenti")},$=async a=>{try{await I(a.id_utente),"user"===a.ruolo?e.push("/cantieri"):"cantieri_user"===a.ruolo?e.push("/cavi"):e.push("/")}catch(e){j(e.response?.data?.detail||e.message||"Errore durante l'impersonificazione")}},G=e=>{let a="NEUTRAL";switch(e){case"owner":a="PROGRESS";break;case"user":a="INFO";break;case"cantieri_user":a="SUCCESS";break;default:a="NEUTRAL"}let s=(0,y.getSoftColorClasses)(a);return(0,t.jsx)(N.E,{className:s.badge,children:e})},F=(e,a)=>{let s="SUCCESS",r="Attivo",i="●";if(e)if(a){let e=new Date(a),t=new Date;e<t?(s="ERROR",r="Scaduto",i="⚠"):e.getTime()-t.getTime()<6048e5?(s="WARNING",r="In Scadenza",i="⏰"):i="✓"}else i="✓";else s="ERROR",r="Disabilitato",i="●";let l=(0,y.getSoftColorClasses)(s);return(0,t.jsxs)(N.E,{className:`${l.badge} flex items-center gap-1`,children:[(0,t.jsx)("span",{className:"text-xs",role:"img","aria-hidden":"true",children:i}),(0,t.jsx)("span",{children:r})]})},q=o.filter(e=>e.username?.toLowerCase().includes(i.toLowerCase())||e.ragione_sociale?.toLowerCase().includes(i.toLowerCase())||e.email?.toLowerCase().includes(i.toLowerCase()));return _&&"owner"===_.ruolo?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-6",children:(0,t.jsx)("div",{className:"max-w-[90%] mx-auto space-y-6",children:(0,t.jsxs)(z.tU,{value:a,onValueChange:s,className:"w-full",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"bg-white rounded-lg border border-slate-200 shadow-sm p-1",children:(0,t.jsxs)(z.j7,{className:`grid w-full ${b?"grid-cols-5":"grid-cols-4"} gap-1 h-auto bg-transparent p-0`,children:[(0,t.jsxs)(z.Xi,{value:"visualizza-utenti",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(et.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Gestione Utenti"})]}),(0,t.jsxs)(z.Xi,{value:"crea-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(er,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Crea Nuovo Utente"})]}),b&&(0,t.jsxs)(z.Xi,{value:"modifica-utente",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(h.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Modifica Utente"})]}),(0,t.jsxs)(z.Xi,{value:"database-tipologie-cavi",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(Q.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Database Tipologie Cavi"})]}),(0,t.jsxs)(z.Xi,{value:"visualizza-database-raw",className:"admin-tab-trigger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-blue-50 data-[state=active]:text-blue-700 data-[state=active]:border-blue-200 data-[state=active]:shadow-sm hover:bg-slate-50 border border-transparent",children:[(0,t.jsx)(O.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"font-medium",children:"Gestione Dati Avanzata"})]})]})}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-red-50 to-orange-50 rounded-lg border border-red-200 shadow-sm p-1",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-3 px-4 py-2",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),(0,t.jsx)("span",{className:"text-sm font-medium text-red-800",children:"Impostazioni Avanzate e Pericolose"})]}),(0,t.jsx)(z.j7,{className:"h-auto bg-transparent p-0",children:(0,t.jsxs)(z.Xi,{value:"reset-database",className:"admin-tab-danger flex items-center gap-2 px-4 py-3 rounded-md transition-all duration-200 data-[state=active]:bg-red-100 data-[state=active]:text-red-700 data-[state=active]:border-red-300 data-[state=active]:shadow-sm hover:bg-red-100 hover:text-red-700 border border-transparent text-red-600 font-medium",children:[(0,t.jsx)(J,{className:"h-4 w-4"}),(0,t.jsx)("span",{children:"Reset Database"})]})})]})})]}),(0,t.jsxs)(z.av,{value:"visualizza-utenti",className:"space-y-4",children:[v&&(0,t.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:(0,t.jsx)("p",{className:"text-red-600",children:v})}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center gap-2",children:[(0,t.jsx)(et.A,{className:"h-5 w-5 text-blue-600"}),"Lista Utenti"]}),(0,t.jsx)(l.BT,{children:"Gestisci tutti gli utenti del sistema CABLYS"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(ei.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400"}),(0,t.jsx)(w.p,{placeholder:"Cerca per username, email o ragione sociale...",value:i,onChange:e=>n(e.target.value),className:"pl-10 w-80"})]}),(0,t.jsxs)(N.E,{variant:"outline",className:"text-xs",children:[q.length," utenti"]})]})]})}),(0,t.jsx)(l.Wu,{children:(0,t.jsx)("div",{className:"overflow-x-auto",children:(0,t.jsx)("div",{className:"rounded-md border",children:(0,t.jsxs)(A.XI,{children:[(0,t.jsx)(A.A0,{children:(0,t.jsxs)(A.Hj,{children:[(0,t.jsx)(A.nd,{className:"w-[50px] text-center",children:"ID"}),(0,t.jsx)(A.nd,{className:"w-[100px]",children:"Username"}),(0,t.jsx)(A.nd,{className:"w-[80px] text-center",children:"Password"}),(0,t.jsx)(A.nd,{className:"w-[80px] text-center",children:"Ruolo"}),(0,t.jsx)(A.nd,{className:"w-[180px]",children:"Ragione Sociale"}),(0,t.jsx)(A.nd,{className:"w-[160px]",children:"Email"}),(0,t.jsx)(A.nd,{className:"w-[80px] text-center",children:"VAT"}),(0,t.jsx)(A.nd,{className:"w-[80px] text-center",children:"Nazione"}),(0,t.jsx)(A.nd,{className:"w-[120px]",children:"Referente"}),(0,t.jsx)(A.nd,{className:"w-[90px] text-center",children:"Scadenza"}),(0,t.jsx)(A.nd,{className:"w-[80px] text-center",children:"Stato"}),(0,t.jsx)(A.nd,{className:"w-[100px] text-center",children:"Azioni"})]})}),(0,t.jsx)(A.BF,{children:p?(0,t.jsx)(A.Hj,{children:(0,t.jsx)(A.nA,{colSpan:12,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(d.A,{className:"h-4 w-4 animate-spin"}),"Caricamento..."]})})}):0===o.length?(0,t.jsx)(A.Hj,{children:(0,t.jsx)(A.nA,{colSpan:12,className:"text-center py-8 text-slate-500",children:"Nessun utente trovato"})}):q.map(e=>(0,t.jsxs)(A.Hj,{className:"users-table-row",children:[(0,t.jsx)(A.nA,{className:"text-center",children:(0,t.jsxs)(N.E,{variant:"outline",className:"text-xs font-mono",children:["#",e.id_utente]})}),(0,t.jsx)(A.nA,{className:"font-semibold text-slate-900",children:e.username}),(0,t.jsx)(A.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,t.jsx)("div",{className:"flex gap-1",children:[...Array(8)].map((e,a)=>(0,t.jsx)("div",{className:"w-1.5 h-1.5 bg-slate-400 rounded-full"},a))}),(0,t.jsx)("div",{className:`w-2 h-2 rounded-full ml-2 ${e.password_plain?"bg-green-500":"bg-red-500"}`,title:e.password_plain?"Password configurata":"Password non configurata"})]})}),(0,t.jsx)(A.nA,{className:"text-center",children:G(e.ruolo)}),(0,t.jsx)(A.nA,{className:"max-w-[250px] truncate",title:e.ragione_sociale,children:(0,t.jsx)("span",{className:"text-slate-900",children:e.ragione_sociale||"-"})}),(0,t.jsx)(A.nA,{className:"max-w-[200px] truncate text-sm text-slate-600",title:e.email,children:e.email||"-"}),(0,t.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.vat||"-"}),(0,t.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.nazione||"-"}),(0,t.jsx)(A.nA,{className:"max-w-[150px] truncate text-sm text-slate-600",title:e.referente_aziendale,children:e.referente_aziendale||"-"}),(0,t.jsx)(A.nA,{className:"text-center text-sm text-slate-600",children:e.data_scadenza?new Date(e.data_scadenza).toLocaleDateString("it-IT"):"N/A"}),(0,t.jsx)(A.nA,{className:"text-center",children:F(e.abilitato,e.data_scadenza)}),(0,t.jsx)(A.nA,{className:"text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(f,{user:e,onEdit:()=>M(e),onToggleStatus:()=>P(e.id_utente),onDelete:()=>V(e.id_utente)}),(0,t.jsx)(c,{size:"sm",onClick:()=>$(e),disabled:"owner"===e.ruolo||!e.abilitato,className:"px-3 py-1.5 text-xs",icon:(0,t.jsx)(el,{className:"h-3.5 w-3.5"}),children:"Entra"})]})})]},e.id_utente))})]})})})})]})]}),(0,t.jsx)(z.av,{value:"crea-utente",className:"space-y-4",children:(0,t.jsx)(B,{user:null,onSave:L,onCancel:U})}),b&&(0,t.jsx)(z.av,{value:"modifica-utente",className:"space-y-4",children:(0,t.jsx)(B,{user:b,onSave:L,onCancel:U})}),(0,t.jsx)(z.av,{value:"database-tipologie-cavi",className:"space-y-4",children:(0,t.jsx)(es,{})}),(0,t.jsx)(z.av,{value:"visualizza-database-raw",className:"space-y-4",children:(0,t.jsx)(X,{})}),(0,t.jsx)(z.av,{value:"reset-database",className:"space-y-4",children:(0,t.jsx)(K,{})})]})})}):null}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56770:(e,a,s)=>{"use strict";s.d(a,{tU:()=>H,av:()=>K,j7:()=>X,Xi:()=>J});var t=s(60687),r=s(43210),i=s(70569),l=s(11273),n=s(9510),d=s(98599),o=s(96963),c=s(14163),m=s(13495),u=s(65551),x=s(43),p="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[v,j,b]=(0,n.N)(g),[f,N]=(0,l.A)(g,[b]),[w,y]=f(g),A=r.forwardRef((e,a)=>(0,t.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,t.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,t.jsx)(z,{...e,ref:a})})}));A.displayName=g;var z=r.forwardRef((e,a)=>{let{__scopeRovingFocusGroup:s,orientation:l,loop:n=!1,dir:o,currentTabStopId:v,defaultCurrentTabStopId:b,onCurrentTabStopIdChange:f,onEntryFocus:N,preventScrollOnEntryFocus:y=!1,...A}=e,z=r.useRef(null),k=(0,d.s)(a,z),C=(0,x.jH)(o),[E,R]=(0,u.i)({prop:v,defaultProp:b??null,onChange:f,caller:g}),[T,_]=r.useState(!1),I=(0,m.c)(N),D=j(s),M=r.useRef(!1),[P,V]=r.useState(0);return r.useEffect(()=>{let e=z.current;if(e)return e.addEventListener(p,I),()=>e.removeEventListener(p,I)},[I]),(0,t.jsx)(w,{scope:s,orientation:l,dir:C,loop:n,currentTabStopId:E,onItemFocus:r.useCallback(e=>R(e),[R]),onItemShiftTab:r.useCallback(()=>_(!0),[]),onFocusableItemAdd:r.useCallback(()=>V(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>V(e=>e-1),[]),children:(0,t.jsx)(c.sG.div,{tabIndex:T||0===P?-1:0,"data-orientation":l,...A,ref:k,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{M.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let a=!M.current;if(e.target===e.currentTarget&&a&&!T){let a=new CustomEvent(p,h);if(e.currentTarget.dispatchEvent(a),!a.defaultPrevented){let e=D().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),y)}}M.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>_(!1))})})}),k="RovingFocusGroupItem",C=r.forwardRef((e,a)=>{let{__scopeRovingFocusGroup:s,focusable:l=!0,active:n=!1,tabStopId:d,children:m,...u}=e,x=(0,o.B)(),p=d||x,h=y(k,s),g=h.currentTabStopId===p,b=j(s),{onFocusableItemAdd:f,onFocusableItemRemove:N,currentTabStopId:w}=h;return r.useEffect(()=>{if(l)return f(),()=>N()},[l,f,N]),(0,t.jsx)(v.ItemSlot,{scope:s,id:p,focusable:l,active:n,children:(0,t.jsx)(c.sG.span,{tabIndex:g?0:-1,"data-orientation":h.orientation,...u,ref:a,onMouseDown:(0,i.m)(e.onMouseDown,e=>{l?h.onItemFocus(p):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>h.onItemFocus(p)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void h.onItemShiftTab();if(e.target!==e.currentTarget)return;let a=function(e,a,s){var t;let r=(t=e.key,"rtl"!==s?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===a&&["ArrowLeft","ArrowRight"].includes(r))&&!("horizontal"===a&&["ArrowUp","ArrowDown"].includes(r)))return E[r]}(e,h.orientation,h.dir);if(void 0!==a){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let s=b().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===a)s.reverse();else if("prev"===a||"next"===a){"prev"===a&&s.reverse();let t=s.indexOf(e.currentTarget);s=h.loop?function(e,a){return e.map((s,t)=>e[(a+t)%e.length])}(s,t+1):s.slice(t+1)}setTimeout(()=>S(s))}}),children:"function"==typeof m?m({isCurrentTabStop:g,hasTabStop:null!=w}):m})})});C.displayName=k;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e,a=!1){let s=document.activeElement;for(let t of e)if(t===s||(t.focus({preventScroll:a}),document.activeElement!==s))return}var R=s(46059),T="Tabs",[_,I]=(0,l.A)(T,[N]),D=N(),[M,P]=_(T),V=r.forwardRef((e,a)=>{let{__scopeTabs:s,value:r,onValueChange:i,defaultValue:l,orientation:n="horizontal",dir:d,activationMode:m="automatic",...p}=e,h=(0,x.jH)(d),[g,v]=(0,u.i)({prop:r,onChange:i,defaultProp:l??"",caller:T});return(0,t.jsx)(M,{scope:s,baseId:(0,o.B)(),value:g,onValueChange:v,orientation:n,dir:h,activationMode:m,children:(0,t.jsx)(c.sG.div,{dir:h,"data-orientation":n,...p,ref:a})})});V.displayName=T;var L="TabsList",U=r.forwardRef((e,a)=>{let{__scopeTabs:s,loop:r=!0,...i}=e,l=P(L,s),n=D(s);return(0,t.jsx)(A,{asChild:!0,...n,orientation:l.orientation,dir:l.dir,loop:r,children:(0,t.jsx)(c.sG.div,{role:"tablist","aria-orientation":l.orientation,...i,ref:a})})});U.displayName=L;var $="TabsTrigger",G=r.forwardRef((e,a)=>{let{__scopeTabs:s,value:r,disabled:l=!1,...n}=e,d=P($,s),o=D(s),m=Z(d.baseId,r),u=B(d.baseId,r),x=r===d.value;return(0,t.jsx)(C,{asChild:!0,...o,focusable:!l,active:x,children:(0,t.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":x,"aria-controls":u,"data-state":x?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:m,...n,ref:a,onMouseDown:(0,i.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():d.onValueChange(r)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&d.onValueChange(r)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==d.activationMode;x||l||!e||d.onValueChange(r)})})})});G.displayName=$;var F="TabsContent",q=r.forwardRef((e,a)=>{let{__scopeTabs:s,value:i,forceMount:l,children:n,...d}=e,o=P(F,s),m=Z(o.baseId,i),u=B(o.baseId,i),x=i===o.value,p=r.useRef(x);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(R.C,{present:l||x,children:({present:s})=>(0,t.jsx)(c.sG.div,{"data-state":x?"active":"inactive","data-orientation":o.orientation,role:"tabpanel","aria-labelledby":m,hidden:!s,id:u,tabIndex:0,...d,ref:a,style:{...e.style,animationDuration:p.current?"0s":void 0},children:s&&n})})});function Z(e,a){return`${e}-trigger-${a}`}function B(e,a){return`${e}-content-${a}`}q.displayName=F;var O=s(4780);let H=V,X=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(U,{ref:s,className:(0,O.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));X.displayName=U.displayName;let J=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(G,{ref:s,className:(0,O.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));J.displayName=G.displayName;let K=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(q,{ref:s,className:(0,O.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));K.displayName=q.displayName},56896:(e,a,s)=>{"use strict";s.d(a,{S:()=>n});var t=s(60687);s(43210);var r=s(40211),i=s(13964),l=s(4780);function n({className:e,...a}){return(0,t.jsx)(r.bL,{"data-slot":"checkbox",className:(0,l.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",e),...a,children:(0,t.jsx)(r.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(i.A,{className:"size-3.5"})})})}},61611:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63143:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},74075:e=>{"use strict";e.exports=require("zlib")},78122:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},81806:(e,a,s)=>{"use strict";s.d(a,{Eb:()=>p,GN:()=>h});let t=/[<>\"'&\x00-\x1f\x7f-\x9f]/g,r=/(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,i=/(<script|javascript:|vbscript:|onload|onerror|onclick)/gi,l=e=>"string"!=typeof e?"":e.trim().replace(t,"").replace(/\s+/g," ").substring(0,1e3),n=e=>{let a=l(e);return a.length<3?{isValid:!1,error:"Username deve essere almeno 3 caratteri"}:a.length>20?{isValid:!1,error:"Username non pu\xf2 superare 20 caratteri"}:/^[a-zA-Z0-9._-]+$/.test(a)?/^[._-]|[._-]$/.test(a)?{isValid:!1,error:"Username non pu\xf2 iniziare o finire con caratteri speciali"}:{isValid:!0}:{isValid:!1,error:"Username pu\xf2 contenere solo lettere, numeri, punti, underscore e trattini"}},d=e=>{if(!e||e.length<8)return{isValid:!1,error:"Password deve essere almeno 8 caratteri",strength:0};if(e.length>128)return{isValid:!1,error:"Password troppo lunga (max 128 caratteri)",strength:0};let a=0;return(/[a-z]/.test(e)&&a++,/[A-Z]/.test(e)&&a++,/[0-9]/.test(e)&&a++,/[^a-zA-Z0-9]/.test(e)&&a++,e.length>=12&&a++,a<3)?{isValid:!1,error:"Password deve contenere almeno: 1 minuscola, 1 maiuscola, 1 numero o 1 carattere speciale",strength:a}:["password","123456","admin","qwerty","letmein"].some(a=>e.toLowerCase().includes(a))?{isValid:!1,error:"Password troppo comune",strength:a}:{isValid:!0,strength:a}},o=e=>{let a=l(e);return a?a.length>254?{isValid:!1,error:"Email troppo lunga"}:/^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(a)?{isValid:!0}:{isValid:!1,error:"Formato email non valido"}:{isValid:!1,error:"Email \xe8 obbligatoria"}},c=(e,a=255)=>l(e).length>a?{isValid:!1,error:`Testo troppo lungo (max ${a} caratteri)`}:i.test(e)||r.test(e)?{isValid:!1,error:"Contenuto non consentito rilevato"}:{isValid:!0},m=e=>{let a=l(e);return a?a.length<2?{isValid:!1,error:"Ragione sociale troppo corta"}:a.length>100?{isValid:!1,error:"Ragione sociale troppo lunga (max 100 caratteri)"}:/^[a-zA-Z0-9\s\.\-&']+$/.test(a)?{isValid:!0}:{isValid:!1,error:"Ragione sociale contiene caratteri non consentiti"}:{isValid:!1,error:"Ragione sociale \xe8 obbligatoria"}},u=e=>{if(!e)return{isValid:!0};let a=l(e).replace(/\s/g,"");return a.length<8||a.length>15?{isValid:!1,error:"VAT deve essere tra 8 e 15 caratteri"}:/^[A-Z0-9]+$/i.test(a)?{isValid:!0}:{isValid:!1,error:"VAT pu\xf2 contenere solo lettere e numeri"}},x=new Map,p=(e,a,s)=>{let t=Date.now(),r=x.get(e);return!r||t>r.resetTime?(x.set(e,{count:1,resetTime:t+s}),!0):!(r.count>=a)&&(r.count++,!0)},h=e=>{let a={},s=n(e.username);if(s.isValid||(a.username=s.error),e.password){let s=d(e.password);s.isValid||(a.password=s.error)}let t=m(e.ragione_sociale);if(t.isValid||(a.ragione_sociale=t.error),e.email){let s=o(e.email);s.isValid||(a.email=s.error)}if(e.vat){let s=u(e.vat);s.isValid||(a.vat=s.error)}if(e.indirizzo){let s=c(e.indirizzo,200);s.isValid||(a.indirizzo=s.error)}if(e.nazione){let s=c(e.nazione,50);s.isValid||(a.nazione=s.error)}if(e.referente_aziendale){let s=c(e.referente_aziendale,100);s.isValid||(a.referente_aziendale=s.error)}return{isValid:0===Object.keys(a).length,errors:a}}},83997:e=>{"use strict";e.exports=require("tty")},84027:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88091:(e,a,s)=>{Promise.resolve().then(s.bind(s,1132))},88233:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},94735:e=>{"use strict";e.exports=require("events")},96474:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96504:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=s(65239),r=s(48088),i=s(88170),l=s.n(i),n=s(30893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(a,d);let o={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,1132)),"C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\CMS\\webapp-nextjs\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\CMS\\webapp-nextjs\\src\\app\\admin\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},96882:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},99891:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(62688).A)("shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])}};var a=require("../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[447,991,658,462,400,818,639,109],()=>s(96504));module.exports=t})();