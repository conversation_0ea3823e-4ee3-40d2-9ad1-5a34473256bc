
import psycopg2
from datetime import datetime
from typing import Tuple, Any, Callable, Optional
from functools import wraps
from contextlib import contextmanager
import logging
from modules.utils import ValidazioneCampi, StatoBobina
from modules.database_pg import Database, database_connection

# Configurazione del logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def db_operation(func):
    """Decorator per gestire le operazioni del database e il logging degli errori."""
    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except psycopg2.Error as e:
            logging.error(f"❌ Errore SQL durante {func.__name__}: {e}")
            return None
        except Exception as e:
            logging.error(f"❌ Errore durante {func.__name__}: {e}")
            return None
    return wrapper

class ParcoCavi:

    CAMPI_MODIFICABILI = [
        ("tipologia", "Tipologia"),
        ("n_conduttori", "N° Conduttori"),
        ("sezione", "Sezione"),
        ("metri_totali", "Metri totali"),
        ("ubicazione_bobina", "Ubicazione"),
        ("fornitore", "Fornitore"),
        ("n_DDT", "N° DDT"),
        ("data_DDT", "Data DDT")
    ]

    def __init__(self):
        """Inizializza la classe ParcoCavi."""
        pass

    @contextmanager
    def db_transaction(self):
        """Context manager per gestire le transazioni del database."""
        conn = None
        try:
            conn = database_connection()
            yield conn.cursor()
            conn.commit()
        except Exception as e:
            if conn:
                conn.rollback()
            raise e

    @db_operation
    def get_ultimo_numero_bobina(self, id_cantiere: int) -> int:
        """Recupera l'ultimo numero bobina utilizzato per un cantiere specifico."""
        with self.db_transaction() as cursor:
            cursor.execute('''
                SELECT MAX(numero_bobina)
                FROM parco_cavi
                WHERE id_cantiere = %s
            ''', (id_cantiere,))
            result = cursor.fetchone()
            return int(result[0]) if result and result[0] else 0

    @db_operation
    def verifica_id_bobina_esistente(self, id_bobina: str) -> bool:
        """Verifica se un ID_BOBINA esiste già nel database."""
        with self.db_transaction() as cursor:
            cursor.execute('SELECT 1 FROM parco_cavi WHERE ID_BOBINA = %s', (id_bobina,))
            return cursor.fetchone() is not None

    def genera_id_bobina(self, id_cantiere: int, usa_numero_progressivo: bool = True) -> tuple[str, str]:
        """Genera un nuovo ID_BOBINA e numero_bobina per un cantiere specifico."""
        ultimo_numero = self.get_ultimo_numero_bobina(id_cantiere)
        nuovo_numero = ultimo_numero + 1
        numero_bobina = str(nuovo_numero)

        return (
            f"C{id_cantiere}_B{numero_bobina}" if usa_numero_progressivo else "",
            numero_bobina
        )

    def _handle_input(self, prompt: str, validator_func: Callable, field_name: str, optional: bool = False, exit_on_q: bool = False) -> Tuple[bool, Any]:
        """Gestisce l'input dell'utente con validazione."""
        while True:
            value = input(prompt).strip()
            if exit_on_q and value.lower() == 'q':
                print("\n❌ Operazione annullata dall'utente")
                return False, None
            if optional and not value:
                return True, ""
            risultato = validator_func(value)
            if not risultato.valido:
                print(f"❌ {risultato.messaggio}")
                if exit_on_q:
                    print("Inserisci un valore valido o 'q' per uscire")
                return False, None
            return True, risultato.valore

    DATE_FORMAT = "%Y-%m-%d"

    def _handle_date_input(self, prompt: str, optional: bool = True, current_value: str = None) -> Tuple[bool, str]:
        """Gestisce l'input di date con validazione.

        Args:
            prompt: Messaggio da mostrare all'utente
            optional: Se True, il campo può essere lasciato vuoto
            current_value: Valore attuale della data (per modifiche)

        Returns:
            Tuple[bool, str]: (successo, valore)
        """
        value = input(prompt).strip()

        # Se l'input è vuoto
        if not value:
            if optional:
                # Se è una modifica e abbiamo un valore attuale, mantienilo
                if current_value:
                    return True, current_value
                # Altrimenti usa la data odierna
                return True, datetime.now().strftime(self.DATE_FORMAT)
            else:
                print("❌ Questo campo è obbligatorio")
                return False, ""

        # Se l'utente vuole uscire
        if value.lower() == 'q':
            return False, ""

        # Valida il formato della data
        try:
            datetime.strptime(value, self.DATE_FORMAT)
            return True, value
        except ValueError:
            print(f"❌ Formato data non valido. Utilizzare {self.DATE_FORMAT}")
            return False, ""

    @db_operation
    def crea_bobina(self, id_cantiere: int) -> bool:
        """Gestisce l'input utente per la creazione di una nuova bobina associata a un cantiere."""
        print("\nINSERIMENTO NUOVA BOBINA")
        print("-" * 30)

        # Ottieni la configurazione e determina se usare numero progressivo
        config_value, usa_numero_progressivo = self._get_bobina_config(id_cantiere)

        # Genera e verifica l'ID bobina
        id_bobina, numero_bobina = self._genera_e_verifica_id_bobina(id_cantiere, usa_numero_progressivo)
        if not id_bobina:  # L'utente ha annullato l'operazione
            return False

        # Raccogli tutti i dati della bobina
        dati_bobina = self._collect_bobina_data(id_cantiere, id_bobina, numero_bobina, config_value)
        if not dati_bobina:  # L'utente ha annullato l'operazione
            return False

        # Inserisci la bobina nel database
        return self._insert_bobina_to_db(dati_bobina)

    # --- Metodi helper privati ---

    def _get_bobina_config(self, id_cantiere: int) -> tuple[str, bool]:
        """Gestisce la configurazione per la generazione degli ID bobina."""
        with self.db_transaction() as cursor:
            cursor.execute('''
                SELECT COUNT(*) as count, configurazione
                FROM parco_cavi
                WHERE id_cantiere = %s
                LIMIT 1
            ''', (id_cantiere,))
            result = cursor.fetchone()
            is_first_insert = result['count'] == 0

            if is_first_insert:
                while True:
                    config_input = input("Vuoi utilizzare ID_BOBINA uguale al numero progressivo? (s/n): ").lower()
                    if config_input in ['s', 'n']:
                        usa_numero_progressivo = config_input == 's'
                        config_value = 's' if usa_numero_progressivo else 'n'
                        return config_value, usa_numero_progressivo
                    print("❌ Inserire 's' per sì o 'n' per no")
            else:
                cursor.execute('SELECT configurazione FROM parco_cavi WHERE id_cantiere = %s LIMIT 1', (id_cantiere,))
                result = cursor.fetchone()
                config_value = result[0] if result and result[0] is not None else 's'
                usa_numero_progressivo = config_value == 's'
                return config_value, usa_numero_progressivo

    def _genera_e_verifica_id_bobina(self, id_cantiere: int, usa_numero_progressivo: bool) -> tuple[str, str]:
        """Genera e verifica l'ID bobina in base alla configurazione."""
        id_bobina, numero_bobina = self.genera_id_bobina(id_cantiere, usa_numero_progressivo)

        if not usa_numero_progressivo:
            while True:
                id_input = input("Inserisci ID_BOBINA: ").strip()
                if id_input.lower() == 'q':
                    print("\n❌ Operazione annullata dall'utente")
                    return "", ""
                if not id_input:
                    print("❌ L'ID_BOBINA è obbligatorio. Inserisci un valore valido o 'q' per uscire")
                    continue

                risultato = ValidazioneCampi.valida_campo_base(id_input)
                if not risultato.valido:
                    print(f"❌ {risultato.messaggio}")
                    print("Inserisci un valore valido o 'q' per uscire")
                    continue

                id_completo = f"C{id_cantiere}_B{id_input}"
                if self.verifica_id_bobina_esistente(id_completo):
                    print("❌ ID_BOBINA già esistente nel database")
                    continue

                id_bobina = id_completo
                break
        else:
            print(f"ID_BOBINA generato: {id_bobina}")
            print(f"Numero bobina: {numero_bobina}")

        return id_bobina, numero_bobina

    def _collect_field_data(self, field_name: str, validator_func: Callable,
                            prompt: str, current_value: str = None,
                            optional: bool = False, exit_on_q: bool = True) -> Tuple[bool, Any]:
        """Raccoglie e valida un singolo campo dall'utente.

        Args:
            field_name: Nome del campo
            validator_func: Funzione di validazione
            prompt: Messaggio da mostrare all'utente
            current_value: Valore attuale (per modifiche)
            optional: Se True, il campo può essere lasciato vuoto
            exit_on_q: Se True, l'utente può premere 'q' per uscire

        Returns:
            Tuple[bool, Any]: (successo, valore)
        """
        # Se stiamo modificando un campo esistente, mostra il valore attuale
        if current_value is not None:
            full_prompt = f"{prompt} [{current_value}]: "
        else:
            full_prompt = f"{prompt}: "

        return self._handle_input(full_prompt, validator_func, field_name, optional, exit_on_q)

    def _collect_bobina_data(self, id_cantiere: int, id_bobina: str, numero_bobina: str, config_value: str) -> dict:
        """Raccoglie e valida tutti i dati della bobina dall'utente."""
        # Utility
        success, utility = self._collect_field_data(
            "utility",
            ValidazioneCampi.valida_campo_base,
            "Utility"
        )
        if not success:
            return {}

        # Tipologia
        success, tipologia = self._collect_field_data(
            "tipologia",
            ValidazioneCampi.valida_campo_base,
            "Tipologia"
        )
        if not success:
            return {}

        # Numero conduttori
        success, n_conduttori = self._handle_input(
            "Numero conduttori: ",
            lambda x: ValidazioneCampi.valida_numero(x, "Numero conduttori"),
            "n_conduttori",
            exit_on_q=True
        )
        if not success:
            return {}

        # Sezione
        success, sezione = self._handle_input(
            "Sezione: ",
            ValidazioneCampi.valida_campo_testo,
            "sezione",
            exit_on_q=True
        )
        if not success:
            return {}

        # Metri totali
        while True:
            metri_input = input("Metri totali: ").strip()
            if metri_input.lower() == 'q':
                return {}
            if not metri_input:
                print("❌ I metri teorici sono obbligatori")
                print("Inserisci un valore valido o 'q' per uscire")
                continue
            try:
                metri_totali = float(metri_input)
                if metri_totali <= 0:
                    print("❌ I metri totali devono essere maggiori di zero")
                    print("Inserisci un valore valido o 'q' per uscire")
                    continue
                break
            except ValueError:
                print("❌ Inserire un valore numerico valido")
                print("Inserisci un valore valido o 'q' per uscire")
                continue

        # Ubicazione
        success, ubicazione = self._handle_input(
            "Ubicazione bobina: ",
            ValidazioneCampi.valida_campo_base,
            "ubicazione",
            exit_on_q=True
        )
        if not success:
            return {}

        # Fornitore
        success, fornitore = self._handle_input(
            "Fornitore: ",
            ValidazioneCampi.valida_campo_base,
            "fornitore",
            exit_on_q=True
        )
        if not success:
            return {}

        # Numero DDT
        success, n_ddt = self._handle_input(
            "Numero DDT: ",
            ValidazioneCampi.valida_campo_base,
            "n_ddt",
            exit_on_q=True
        )
        if not success:
            return {}

        # Data DDT
        success, data_ddt = self._handle_date_input(
            "Data DDT (formato YYYY-MM-DD, invio per data odierna): "
        )
        if not success:
            return {}

        return {
            'ID_BOBINA': id_bobina,
            'numero_bobina': numero_bobina,
            'utility': utility,
            'tipologia': tipologia,
            'n_conduttori': n_conduttori,
            'sezione': sezione,
            'metri_totali': metri_totali,
            'metri_residui': metri_totali,
            'stato_bobina': StatoBobina.DISPONIBILE.value,
            'ubicazione_bobina': ubicazione,
            'fornitore': fornitore,
            'n_DDT': n_ddt,
            'data_DDT': data_ddt,
            'configurazione': config_value,
            'id_cantiere': id_cantiere
        }

    def _insert_bobina_to_db(self, dati_bobina: dict) -> bool:
        """Esegue l'inserimento della bobina nel database."""
        try:
            with database_connection() as conn:
                cursor = conn.cursor()

                # Esegui tutte le operazioni in una singola transazione
                try:
                    placeholders = ', '.join(['%s' for _ in dati_bobina])
                    columns = ', '.join(dati_bobina.keys())
                    values = tuple(dati_bobina.values())
                    cursor.execute(f'''
                        INSERT INTO parco_cavi ({columns})
                        VALUES ({placeholders})
                    ''', values)

                    conn.commit()
                    print("\n✅ Bobina inserita con successo!")
                    return True
                except Exception as e:
                    conn.rollback()
                    raise e
        except Exception as e:
            logging.error(f"Errore durante l'inserimento della bobina: {e}")
            print("\n❌ Errore durante l'inserimento della bobina")
            return False

    def visualizza_parco_cavi(self, id_cantiere: int, filtro: str = None) -> bool:
        """Visualizza tutte le bobine nel parco cavi."""
        try:
            with database_connection() as conn:
                cursor = conn.cursor()

                # Query base
                query = '''
                    SELECT * FROM parco_cavi
                    WHERE id_cantiere = %s
                '''

                # Aggiungi filtri se necessario
                params = [id_cantiere]
                if filtro:
                    query += ' AND (ID_BOBINA LIKE %s OR utility LIKE %s OR tipologia LIKE %s)'
                    filtro_param = f'%{filtro}%'
                    params.extend([filtro_param, filtro_param, filtro_param])

                # Ordina per numero_bobina
                query += ' ORDER BY numero_bobina'

                cursor.execute(query, params)
                bobine = cursor.fetchall()

                if not bobine:
                    print("\nNessuna bobina trovata per questo cantiere.")
                    return False

                print("\nBOBINE DISPONIBILI:")
                print("-" * 130)
                print(f"{'Num':<8} {'ID_BOBINA':<15} {'Utility':<15} {'Tipologia':<15} "
                      f"{'N.Cond':<8} {'Sezione':<15} {'Tot(m)':<8} {'Res(m)':<8} {'Ubicazione':<15} "
                      f"{'Fornitore':<15} {'DDT':<10} {'Stato':<12}")
                print("-" * 130)

                for bobina in bobine:
                    # Estrai solo la parte dopo "_B" dall'ID_BOBINA
                    id_bobina_display = bobina['ID_BOBINA'].split('_B', 1)[1] if '_B' in bobina['ID_BOBINA'] else \
                    bobina['ID_BOBINA']

                    print(
                        f"{bobina['numero_bobina']:<8} {id_bobina_display:<15} {bobina['utility']:<15} {bobina['tipologia']:<15} "
                        f"{bobina['n_conduttori']:<8} {bobina['sezione']:<15} {bobina['metri_totali']:<8} "
                        f"{bobina['metri_residui']:<8} {bobina['ubicazione_bobina']:<15} "
                        f"{bobina['fornitore']:<15} {bobina['n_DDT']:<10} {bobina['stato_bobina']:<12}")
        except Exception as e:
            logging.error(f"Errore durante la visualizzazione del parco cavi: {e}")
            print(f"\n❌ Errore durante la visualizzazione del parco cavi: {e}")
            return False

        return True

    @db_operation
    def elimina_bobina(self, parte_y: str, id_cantiere: int) -> bool:
        """Elimina una bobina dal parco cavi.

        Args:
            parte_y: Solo la parte Y dell'ID bobina (formato Cx_By)
            id_cantiere: ID del cantiere

        Returns:
            bool: True se l'eliminazione è avvenuta con successo, False altrimenti
        """
        try:
            with self.db_transaction() as cursor:
                # Costruisci l'ID completo della bobina
                id_bobina = f"C{id_cantiere}_B{parte_y}"

                # Cerca la bobina per ID_BOBINA
                cursor.execute('''
                    SELECT ID_BOBINA, numero_bobina, metri_totali, metri_residui
                    FROM parco_cavi
                    WHERE ID_BOBINA = %s AND id_cantiere = %s
                ''', (id_bobina, id_cantiere))
                result = cursor.fetchone()

                if not result:
                    print(f"\n❌ Bobina con ID {parte_y} non trovata per questo cantiere")
                    return False

                numero_bobina = result['numero_bobina']

                # Verifica se la bobina è stata utilizzata (metri residui < metri totali)
                if result['metri_residui'] < result['metri_totali']:
                    print(f"\n❌ Impossibile eliminare la bobina {parte_y}: è già stata utilizzata")
                    return False

                # Chiedi conferma all'utente
                conferma = input(f"\n⚠️ Sei sicuro di voler eliminare la bobina {parte_y}? (s/n): ").lower()
                if conferma != 's':
                    print("\nOperazione annullata.")
                    return False

                # Procedi con l'eliminazione
                cursor.execute('DELETE FROM parco_cavi WHERE ID_BOBINA = %s', (id_bobina,))

                print(f"\n✅ Bobina {parte_y} eliminata con successo!")
                return True
        except Exception as e:
            logging.error(f"Errore durante l'eliminazione della bobina: {e}")
            print(f"\n❌ Errore durante l'eliminazione della bobina: {e}")
            return False

    @db_operation
    def modifica_bobina(self, id_cantiere: int) -> bool:
        """Modifica una bobina nel parco cavi"""
        try:
            # Visualizza tutte le bobine del cantiere senza filtro per stato
            print("\n=== Bobine del cantiere ===")
            self.visualizza_parco_cavi(id_cantiere)

            # Input e modifica
            y_part = input("\nInserisci ID bobina da modificare: ").strip()
            if not y_part:
                return False

            id_bobina = f"C{id_cantiere}_B{y_part}"

            # Usa la funzione complementare per ottenere i dati della bobina
            bobina = self._get_bobina_by_id(id_bobina, id_cantiere)

            if not bobina:
                print(f"\n❌ Bobina {y_part} non trovata")
                return False

            # Verifica che la bobina sia disponibile
            if bobina['stato_bobina'] != 'Disponibile':
                print(f"\n❌ Impossibile modificare la bobina {y_part}: non è disponibile (stato: {bobina['stato_bobina']})")
                return False

            # Ottieni i nuovi valori usando la funzione complementare
            nuovi_valori = self._get_nuovi_valori(bobina)

            # Verifica se ci sono modifiche
            # Verifica se ci sono modifiche
            updates = {k: v for k, v in nuovi_valori.items() if v != bobina[k]}

            if updates:
                # Se si modifica metri_totali, aggiorna automaticamente metri_residui
                if 'metri_totali' in updates:
                    try:
                        metri_totali_nuovi = float(updates['metri_totali'])

                        # Per bobine DISPONIBILI, i metri residui DEVONO essere uguali ai metri totali
                        updates['metri_residui'] = metri_totali_nuovi

                        # Verifica che i metri totali siano > 0
                        if metri_totali_nuovi <= 0:
                            print("\n❌ Errore: i metri totali devono essere maggiori di zero")
                            return False

                    except ValueError:
                        print("\n❌ Errore: il valore inserito per i metri totali non è valido")
                        return False

                # Esegui l'aggiornamento nel database
                with self.db_transaction() as cursor:
                    # Utilizziamo psycopg2.sql per costruire la query in modo sicuro
                    from psycopg2 import sql

                    # Costruisci la parte SET della query
                    set_items = []
                    for k in updates.keys():
                        set_items.append(sql.SQL("{} = %s").format(sql.Identifier(k)))

                    set_clause = sql.SQL(", ").join(set_items)

                    # Costruisci la query completa
                    query = sql.SQL('''
                        UPDATE parco_cavi
                        SET {}
                        WHERE ID_BOBINA = %s AND id_cantiere = %s
                    ''').format(set_clause)

                    # Esegui la query con i parametri
                    cursor.execute(query, (*updates.values(), id_bobina, id_cantiere))

                print("\n✅ Modifica salvata!")
            else:
                print("\n⏹ Nessuna modifica effettuata")

            return True

        except Exception as e:
            logging.error(f"Errore modifica bobina: {str(e)}")
            print(f"\n❌ Errore: {str(e)}")
            return False

    def _get_bobina_by_id(self, id_bobina: str, id_cantiere: int) -> Optional[dict]:
        """Recupera una bobina dal database come dizionario.

        Args:
            id_bobina: ID nel formato 'Cx_By' (es: 'C1_B5')
            id_cantiere: ID numerico del cantiere

        Returns:
            dict: Dati bobina se trovata, None altrimenti
        """
        try:
            with self.db_transaction() as cursor:
                cursor.execute('''
                    SELECT * FROM parco_cavi
                    WHERE ID_BOBINA = %s AND id_cantiere = %s
                ''', (id_bobina, id_cantiere))

                row = cursor.fetchone()
                if not row:
                    return None

                # Conversione sicura a dizionario
                return dict(zip([col[0] for col in cursor.description], row))

        except Exception as e:
            logging.error(f"Errore durante il fetch di {id_bobina}: {str(e)}")
            return None

    def _get_nuovi_valori(self, bobina: dict) -> dict:
        """Richiede all'utente i nuovi valori per i campi modificabili."""
        print("\nInserisci i nuovi valori (lascia vuoto per mantenere il valore attuale):")

        # Utility
        success, utility = self._collect_field_data(
            "utility",
            ValidazioneCampi.valida_campo_base,
            "Utility",
            current_value=bobina['utility'],
            optional=True
        )
        if not success:
            return {}

        # Tipologia
        success, tipologia = self._collect_field_data(
            "tipologia",
            ValidazioneCampi.valida_campo_base,
            "Tipologia",
            current_value=bobina['tipologia'],
            optional=True
        )
        if not success:
            return {}

        # Numero conduttori
        success, n_conduttori = self._handle_input(
            f"Numero conduttori [{bobina['n_conduttori']}]: ",
            lambda x: ValidazioneCampi.valida_numero(x, "Numero conduttori"),
            "n_conduttori",
            optional=True,
            exit_on_q=True
        )
        if not success:
            return {}

        # Sezione
        success, sezione = self._handle_input(
            f"Sezione [{bobina['sezione']}]: ",
            lambda x: ValidazioneCampi.valida_numero(x, "Sezione"),
            "sezione",
            optional=True,
            exit_on_q=True
        )
        if not success:
            return {}

        # Metri totali (solo per bobine non utilizzate)
        metri_totali = bobina['metri_totali']
        if bobina['metri_residui'] == bobina['metri_totali']:
            while True:
                metri_input = input(f"Metri totali [{bobina['metri_totali']}]: ").strip()
                if not metri_input:
                    break  # Mantieni il valore originale
                if metri_input.lower() == 'q':
                    return {}
                try:
                    new_metri_totali = float(metri_input)
                    if new_metri_totali <= 0:
                        print("❌ I metri totali devono essere maggiori di zero")
                        continue
                    metri_totali = new_metri_totali
                    break
                except ValueError:
                    print("❌ Inserire un valore numerico valido")
                    continue

        # Ubicazione
        success, ubicazione = self._handle_input(
            f"Ubicazione bobina [{bobina['ubicazione_bobina']}]: ",
            ValidazioneCampi.valida_campo_base,
            "ubicazione",
            optional=True,
            exit_on_q=True
        )
        if not success:
            return {}

        # Fornitore
        success, fornitore = self._handle_input(
            f"Fornitore [{bobina['fornitore']}]: ",
            ValidazioneCampi.valida_campo_base,
            "fornitore",
            optional=True,
            exit_on_q=True
        )
        if not success:
            return {}

        # Numero DDT
        success, n_ddt = self._handle_input(
            f"Numero DDT [{bobina['n_DDT']}]: ",
            ValidazioneCampi.valida_campo_base,
            "n_ddt",
            optional=True,
            exit_on_q=True
        )
        if not success:
            return {}

        # Data DDT
        success, data_ddt = self._handle_date_input(
            f"Data DDT (formato YYYY-MM-DD) [{bobina['data_DDT']}]: ",
            optional=True,
            current_value=bobina['data_DDT']
        )
        if not success:
            return {}

        # Costruisci il dizionario dei nuovi valori
        nuovi_valori = {
            'utility': utility if utility != "" else bobina['utility'],
            'tipologia': tipologia if tipologia != "" else bobina['tipologia'],
            'n_conduttori': n_conduttori if n_conduttori != "" else bobina['n_conduttori'],
            'sezione': sezione if sezione != "" else bobina['sezione'],
            'metri_totali': metri_totali,
            'ubicazione_bobina': ubicazione if ubicazione != "" else bobina['ubicazione_bobina'],
            'fornitore': fornitore if fornitore != "" else bobina['fornitore'],
            'n_DDT': n_ddt if n_ddt != "" else bobina['n_DDT'],
            'data_DDT': data_ddt if data_ddt != "" else bobina['data_DDT']
        }

        # Se i metri totali sono stati modificati, aggiorna anche i metri residui
        if metri_totali != bobina['metri_totali']:
            nuovi_valori['metri_residui'] = metri_totali

        return nuovi_valori


