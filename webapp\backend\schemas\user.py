from pydantic import BaseModel
from typing import Optional
from datetime import date

class UserBase(BaseModel):
    """Schema base per gli utenti."""
    username: str
    ruolo: str
    data_scadenza: Optional[date] = None
    abilitato: bool = True

    # Nuovi campi per la gestione aziendale
    ragione_sociale: Optional[str] = None
    indirizzo: Optional[str] = None
    nazione: Optional[str] = None
    email: Optional[str] = None  # Usando str invece di EmailStr per maggiore flessibilità
    vat: Optional[str] = None  # Partita IVA
    referente_aziendale: Optional[str] = None

    # Campi per comunicazione
    telefono: Optional[str] = None
    cellulare: Optional[str] = None

class UserCreate(UserBase):
    """Schema per la creazione di un utente."""
    password: str

class UserUpdate(BaseModel):
    """Schema per l'aggiornamento di un utente."""
    username: Optional[str] = None
    password: Optional[str] = None
    ruolo: Optional[str] = None
    data_scadenza: Optional[date] = None
    abilitato: Optional[bool] = None

    # Nuovi campi per la gestione aziendale
    ragione_sociale: Optional[str] = None
    indirizzo: Optional[str] = None
    nazione: Optional[str] = None
    email: Optional[str] = None
    vat: Optional[str] = None
    referente_aziendale: Optional[str] = None

    # Campi per comunicazione
    telefono: Optional[str] = None
    cellulare: Optional[str] = None

class UserInDB(UserBase):
    """Schema per un utente nel database."""
    id_utente: int
    password: Optional[str] = None
    password_plain: Optional[str] = None  # Campo per la password in chiaro
    created_by: Optional[int] = None

    class Config:
        orm_mode = True

# Alias per UserInDB per compatibilità con il codice esistente
class User(UserInDB):
    """Schema per un utente completo."""
    token_data: Optional[dict] = None  # Per memorizzare i dati del token
