from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Any, Optional

from backend.database import get_db
from backend.models.user import User
from backend.models.certificazione_cavo import CertificazioneCavo
from backend.models.prova_dettagliata import ProvaDettagliata
from backend.schemas.prova_dettagliata import (
    ProvaDettagliataCreate,
    ProvaDettagliataUpdate,
    ProvaDettagliataResponse,
    ProvaDettagliataListResponse
)
from backend.api.auth import get_current_active_user

router = APIRouter()

@router.get("/{cantiere_id}/certificazioni/{certificazione_id}/prove", response_model=List[ProvaDettagliataListResponse])
def get_prove_certificazione(
    cantiere_id: int,
    certificazione_id: int,
    tipo_prova: Optional[str] = Query(None, description="Filtro per tipo di prova"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera tutte le prove dettagliate di una certificazione.
    """
    # Verifica che la certificazione esista e appartenga al cantiere
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()
    
    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Query per ottenere le prove
    query = db.query(ProvaDettagliata).filter(
        ProvaDettagliata.id_certificazione == certificazione_id
    )
    
    if tipo_prova:
        query = query.filter(ProvaDettagliata.tipo_prova == tipo_prova)
    
    prove = query.all()
    return prove

@router.post("/{cantiere_id}/certificazioni/{certificazione_id}/prove", response_model=ProvaDettagliataResponse)
def create_prova(
    cantiere_id: int,
    certificazione_id: int,
    prova_in: ProvaDettagliataCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Crea una nuova prova dettagliata per una certificazione.
    """
    # Verifica che la certificazione esista e appartenga al cantiere
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()
    
    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Crea la nuova prova
    prova = ProvaDettagliata(
        id_certificazione=certificazione_id,
        **prova_in.dict()
    )
    
    db.add(prova)
    db.commit()
    db.refresh(prova)

    return prova

@router.get("/{cantiere_id}/certificazioni/{certificazione_id}/prove/{prova_id}", response_model=ProvaDettagliataResponse)
def get_prova(
    cantiere_id: int,
    certificazione_id: int,
    prova_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Recupera i dettagli di una prova dettagliata specifica.
    """
    # Verifica che la certificazione esista e appartenga al cantiere
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()
    
    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Recupera la prova
    prova = db.query(ProvaDettagliata).filter(
        ProvaDettagliata.id_prova == prova_id,
        ProvaDettagliata.id_certificazione == certificazione_id
    ).first()

    if not prova:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prova con ID {prova_id} non trovata nella certificazione {certificazione_id}"
        )

    return prova

@router.put("/{cantiere_id}/certificazioni/{certificazione_id}/prove/{prova_id}", response_model=ProvaDettagliataResponse)
def update_prova(
    cantiere_id: int,
    certificazione_id: int,
    prova_id: int,
    prova_update: ProvaDettagliataUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Aggiorna una prova dettagliata esistente.
    """
    # Verifica che la certificazione esista e appartenga al cantiere
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()
    
    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Recupera la prova
    prova = db.query(ProvaDettagliata).filter(
        ProvaDettagliata.id_prova == prova_id,
        ProvaDettagliata.id_certificazione == certificazione_id
    ).first()

    if not prova:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prova con ID {prova_id} non trovata nella certificazione {certificazione_id}"
        )

    # Aggiorna i campi forniti
    update_data = prova_update.dict(exclude_unset=True)
    if update_data:
        for field, value in update_data.items():
            setattr(prova, field, value)
        
        db.commit()
        db.refresh(prova)

    return prova

@router.delete("/{cantiere_id}/certificazioni/{certificazione_id}/prove/{prova_id}")
def delete_prova(
    cantiere_id: int,
    certificazione_id: int,
    prova_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
) -> Any:
    """
    Elimina una prova dettagliata.
    """
    # Verifica che la certificazione esista e appartenga al cantiere
    certificazione = db.query(CertificazioneCavo).filter(
        CertificazioneCavo.id_certificazione == certificazione_id,
        CertificazioneCavo.id_cantiere == cantiere_id
    ).first()
    
    if not certificazione:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Certificazione con ID {certificazione_id} non trovata nel cantiere {cantiere_id}"
        )

    # Recupera la prova
    prova = db.query(ProvaDettagliata).filter(
        ProvaDettagliata.id_prova == prova_id,
        ProvaDettagliata.id_certificazione == certificazione_id
    ).first()

    if not prova:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Prova con ID {prova_id} non trovata nella certificazione {certificazione_id}"
        )

    db.delete(prova)
    db.commit()

    return {"message": "Prova eliminata con successo"}

@router.get("/tipi-prova")
def get_tipi_prova() -> Any:
    """
    Restituisce i tipi di prova disponibili.
    """
    return {
        "tipi_prova": [
            {
                "codice": "ESAME_VISTA",
                "nome": "Esame a Vista",
                "descrizione": "Controllo visivo del cavo e delle connessioni"
            },
            {
                "codice": "CONTINUITA",
                "nome": "Prova di Continuità",
                "descrizione": "Test di continuità dei conduttori"
            },
            {
                "codice": "ISOLAMENTO",
                "nome": "Prova di Isolamento",
                "descrizione": "Test di resistenza di isolamento"
            },
            {
                "codice": "RIGIDITA",
                "nome": "Prova di Rigidità Dielettrica",
                "descrizione": "Test di rigidità dielettrica"
            },
            {
                "codice": "SEQUENZA_FASI",
                "nome": "Controllo Sequenza Fasi",
                "descrizione": "Verifica della sequenza delle fasi"
            },
            {
                "codice": "ALTRO",
                "nome": "Altra Prova",
                "descrizione": "Prova specifica non standard"
            }
        ]
    }
