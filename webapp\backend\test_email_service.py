#!/usr/bin/env python3
"""
Script di Test per il Servizio Email
Testa l'invio di email per reset password e notifiche.
"""

import os
import sys
from pathlib import Path

# Aggiungi il path del backend
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Carica le variabili d'ambiente dal file .env
try:
    from dotenv import load_dotenv
    env_file = backend_dir / '.env'
    if env_file.exists():
        load_dotenv(env_file)
        print(f"✅ Caricato file .env da: {env_file}")
    else:
        print(f"⚠️  File .env non trovato in: {env_file}")
except ImportError:
    print("⚠️  python-dotenv non installato, usando variabili d'ambiente di sistema")

def test_email_configuration():
    """Testa la configurazione email."""
    print("🔧 Test Configurazione Email")
    print("=" * 50)
    
    try:
        from core.email_config import email_config_manager
        
        config = email_config_manager.get_current_config()
        provider = email_config_manager.current_provider
        
        print(f"✅ Provider: {provider}")
        print(f"✅ Host: {config.host}")
        print(f"✅ Port: {config.port}")
        print(f"✅ From Email: {config.from_email}")
        print(f"✅ From Name: {config.from_name}")
        
        # Controlla se le credenziali sono configurate
        if not config.username or not config.password:
            print("⚠️  ATTENZIONE: Username o password non configurati")
            print("   Configura le variabili d'ambiente nel file .env")
            return False
            
        if not config.from_email:
            print("⚠️  ATTENZIONE: Email mittente non configurata")
            return False
            
        print("✅ Configurazione email valida")
        return True
        
    except Exception as e:
        print(f"❌ Errore configurazione: {e}")
        return False

def test_email_templates():
    """Testa i template email."""
    print("\n📧 Test Template Email")
    print("=" * 50)
    
    try:
        from core.email_config import email_template_manager
        
        # Test template reset password
        reset_template = email_template_manager.get_password_reset_template()
        if reset_template and len(reset_template) > 100:
            print("✅ Template reset password caricato")
        else:
            print("❌ Template reset password non valido")
            return False
            
        # Test template notifica cambio password
        change_template = email_template_manager.get_password_changed_template()
        if change_template and len(change_template) > 100:
            print("✅ Template cambio password caricato")
        else:
            print("❌ Template cambio password non valido")
            return False
            
        print("✅ Tutti i template sono validi")
        return True
        
    except Exception as e:
        print(f"❌ Errore template: {e}")
        return False

def test_email_sending():
    """Testa l'invio email (modalità test)."""
    print("\n📤 Test Invio Email")
    print("=" * 50)
    
    try:
        from core.email_service import EmailService
        
        email_service = EmailService()
        
        # Test email reset password
        print("📧 Test email reset password...")
        success = email_service.send_password_reset_email(
            to_email="<EMAIL>",
            reset_link="https://example.com/reset?token=test123",
            user_name="Test User",
            user_type="utente"
        )
        
        if success:
            print("✅ Email reset password inviata con successo")
        else:
            print("❌ Errore invio email reset password")
            return False
            
        # Test email notifica cambio password
        print("📧 Test email notifica cambio password...")
        success = email_service.send_password_changed_notification(
            to_email="<EMAIL>",
            user_name="Test User",
            user_type="utente"
        )
        
        if success:
            print("✅ Email notifica cambio password inviata con successo")
        else:
            print("❌ Errore invio email notifica")
            return False
            
        print("✅ Tutti i test di invio completati")
        return True
        
    except Exception as e:
        print(f"❌ Errore invio email: {e}")
        return False

def main():
    """Esegue tutti i test."""
    print("🧪 TEST SERVIZIO EMAIL CMS")
    print("=" * 60)
    
    # Controlla variabili d'ambiente
    print("🔍 Controllo Variabili d'Ambiente")
    print("-" * 30)
    
    required_vars = [
        'EMAIL_PROVIDER',
        'GMAIL_USERNAME' if os.getenv('EMAIL_PROVIDER', 'gmail') == 'gmail' else 'OUTLOOK_USERNAME',
        'GMAIL_APP_PASSWORD' if os.getenv('EMAIL_PROVIDER', 'gmail') == 'gmail' else 'OUTLOOK_PASSWORD'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Variabili mancanti: {', '.join(missing_vars)}")
        print("   Copia .env.example in .env e configura le variabili")
        print("   Oppure imposta EMAIL_TESTING_MODE=true per test senza invio reale")
    else:
        print("✅ Tutte le variabili d'ambiente sono configurate")
    
    # Esegui test
    tests = [
        test_email_configuration,
        test_email_templates,
        test_email_sending
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Errore durante il test: {e}")
            results.append(False)
    
    # Risultati finali
    print("\n📊 RISULTATI FINALI")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"✅ Tutti i test superati ({passed}/{total})")
        print("🎉 Il servizio email è configurato correttamente!")
        return True
    else:
        print(f"❌ Test falliti: {total - passed}/{total}")
        print("🔧 Controlla la configurazione e riprova")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
