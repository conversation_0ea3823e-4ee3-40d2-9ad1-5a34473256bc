{"version": 3, "file": "static/css/main.55a59b69.css", "mappings": "AAAA,kBACE,wBAAyB,CACzB,+BACF,CAEA,mCAOE,uBAAwB,CAHxB,gBAAiB,CAHjB,eAAgB,CAIhB,iBAAkB,CAHlB,iBAAkB,CAClB,kBAAmB,CAGnB,oBAEF,CAGA,sDACE,YACF,CAEA,kCAEE,cAAe,CAIf,eAAgB,CADhB,iBAAkB,CADlB,cAAe,CADf,gBAAiB,CAFjB,mBAMF,CAEA,mCAEE,WAAY,CADZ,YAEF,CAGA,uCACE,cAAe,CACf,eAAgB,CAChB,gBACF,CAGA,qBAEE,8BAAwC,CADxC,cAEF,CAGA,wCACE,wBACF,CAGA,iCACE,wBAAyB,CAEzB,+BAAgC,CADhC,eAEF,CAGA,kCACE,wBAAyB,CACzB,eACF,CAGA,mDACE,gBAAiB,CACjB,gBACF,CAGA,uCACE,cACF,CCzEA,eACE,YACF,CAEA,iCACE,gBAAiB,CACjB,eAAgB,CAChB,kBACF,CAEA,+BAIE,iBAAkB,CAFlB,cAAe,CAGf,eAAgB,CAFhB,gBAAiB,CAFjB,mBAKF,CAEA,oCACE,wBAAyB,CAEzB,wBAAyB,CACzB,eAAgB,CAFhB,aAGF,CAEA,0CACE,wBAAyB,CACzB,eACF,CAEA,mDACE,wBAAyB,CACzB,aACF,CAEA,yDACE,wBACF,CAEA,iDACE,wBAAyB,CACzB,aACF,CAEA,uDACE,wBACF,CAEA,6BACE,iBAAkB,CAClB,8BAAwC,CACxC,8BACF,CAEA,mCACE,8BACF,CAEA,oCACE,YACF,CAEA,oCAEE,YAAa,CACb,6BAA8B,CAF9B,gBAGF,CAEA,gCAEE,kBAAmB,CADnB,YAAa,CAEb,kBACF,CAEA,iDAGE,aAAc,CAFd,gBAAiB,CACjB,gBAEF,CAEA,kDACE,gBAAiB,CACjB,eACF,CAEA,gCACE,iBACF,CAEA,oCACE,wBAAyB,CAEzB,gBAAiB,CACjB,eAAgB,CAFhB,iBAGF,CAEA,sCACE,iBACF,CAEA,sCAEE,wBAAyB,CADzB,iBAEF,CAEA,kCACE,kBACF,CAEA,mCACE,gBACF,CAEA,sCACE,iBACF,CAEA,uCACE,iBACF,CCvHA,WACE,YACF,CAEA,6BACE,gBAAiB,CACjB,eAAgB,CAChB,kBACF,CAEA,2BAIE,iBAAkB,CAFlB,cAAe,CAGf,eAAgB,CAFhB,gBAAiB,CAFjB,mBAKF,CAEA,gCACE,wBAAyB,CAEzB,wBAAyB,CACzB,eAAgB,CAFhB,aAGF,CAEA,sCACE,wBAAyB,CACzB,eACF,CAEA,+BACE,aACF,CAEA,qCACE,wBACF,CAEA,0BACE,iBAAkB,CAClB,8BACF,CAEA,yBACE,wBAAyB,CACzB,+BACF,CAEA,wBAEE,cAAe,CACf,eAAgB,CAChB,iBAAkB,CAHlB,mBAIF,CAEA,qCACE,aAAc,CACd,eACF,CAEA,yBACE,iBAAkB,CAClB,8BAAwC,CACxC,8BACF,CAEA,+BACE,8BACF,CAEA,gCACE,YACF,CAEA,gCAEE,YAAa,CACb,6BAA8B,CAF9B,gBAGF,CAEA,wBAEE,kBAAmB,CADnB,YAAa,CAEb,kBACF,CAEA,yCAGE,aAAc,CAFd,gBAAiB,CACjB,gBAEF,CAEA,0CACE,gBAAiB,CACjB,eACF,CAEA,mCACE,iBAAkB,CAClB,8BAAwC,CACxC,kBACF,CAEA,0BACE,uBAAyB,CACzB,gBACF,CAEA,8BACE,wBACF,CAEA,8BAIE,+BAAgC,CAFhC,aAAc,CADd,eAAgB,CAEhB,iBAEF,CAEA,8BAEE,+BAAgC,CADhC,iBAEF,CAEA,mCACE,wBACF,CAEA,0BACE,iBAAkB,CAClB,kBACF,CAEA,qCACE,aACF,CCxIA,UASE,kCAAmC,CACnC,iCAAkC,CAJlC,mIAEY,CAPZ,QAAS,CACT,SASF,CAEA,gBATE,WAAY,CACZ,iBAAkB,CAFlB,UAcF,CAEA,KACE,uEAEF", "sources": ["components/TopNavbar.css", "pages/UserPage.css", "pages/cavi/CaviPage.css", "index.css"], "sourcesContent": [".excel-style-menu {\n  background-color: #f8f9fa;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.excel-style-menu .MuiToolbar-root {\n  min-height: 60px;\n  padding-left: 24px;\n  padding-right: 24px;\n  flex-wrap: nowrap;\n  overflow-x: hidden;\n  scrollbar-width: none; /* Firefox */\n  -ms-overflow-style: none; /* IE and Edge */\n}\n\n/* Hide scrollbar for Chrome, Safari and Opera */\n.excel-style-menu .MuiToolbar-root::-webkit-scrollbar {\n  display: none;\n}\n\n.excel-style-menu .MuiButton-root {\n  text-transform: none;\n  font-size: 1rem;\n  padding: 8px 16px;\n  min-width: auto;\n  margin-right: 10px;\n  font-weight: 500;\n}\n\n.excel-style-menu .MuiDivider-root {\n  margin: 0 8px;\n  height: 30px;\n}\n\n/* Stile per i menu a tendina */\n.excel-style-submenu .MuiMenuItem-root {\n  font-size: 1rem;\n  min-height: 42px;\n  padding: 8px 20px;\n}\n\n/* Stile per i sottomenu */\n.excel-style-submenu {\n  margin-top: 2px;\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);\n}\n\n/* Effetto hover sui pulsanti */\n.excel-style-menu .MuiButton-root:hover {\n  background-color: #e9ecef;\n}\n\n/* Stile per il pulsante attivo */\n.excel-style-menu .active-button {\n  background-color: #e9ecef;\n  font-weight: 600;\n  border-bottom: 2px solid #1976d2;\n}\n\n/* Stile per il menu item attivo */\n.excel-style-submenu .active-item {\n  background-color: #e9ecef;\n  font-weight: 500;\n}\n\n/* Stile per le icone nei pulsanti */\n.excel-style-menu .MuiButton-root .MuiSvgIcon-root {\n  font-size: 1.3rem;\n  margin-right: 6px;\n}\n\n/* Stile per il testo utente nella barra */\n.excel-style-menu .MuiTypography-body2 {\n  font-size: 1rem;\n}\n", "/* Stile per la pagina dei cantieri */\n.cantieri-page {\n  padding: 20px;\n}\n\n.cantieri-page .MuiTypography-h4 {\n  font-size: 1.8rem;\n  font-weight: 500;\n  margin-bottom: 16px;\n}\n\n.cantieri-page .MuiButton-root {\n  text-transform: none;\n  font-size: 1rem;\n  padding: 8px 16px;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.cantieri-page .MuiButton-contained {\n  background-color: #f8f9fa;\n  color: #212529;\n  border: 1px solid #dee2e6;\n  box-shadow: none;\n}\n\n.cantieri-page .MuiButton-contained:hover {\n  background-color: #e9ecef;\n  box-shadow: none;\n}\n\n.cantieri-page .MuiButton-contained.primary-button {\n  background-color: #f8f9fa;\n  color: #212529;\n}\n\n.cantieri-page .MuiButton-contained.primary-button:hover {\n  background-color: #e9ecef;\n}\n\n.cantieri-page .MuiButton-contained.error-button {\n  background-color: #f8f9fa;\n  color: #dc3545;\n}\n\n.cantieri-page .MuiButton-contained.error-button:hover {\n  background-color: #e9ecef;\n}\n\n.cantieri-page .MuiCard-root {\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: box-shadow 0.3s ease;\n}\n\n.cantieri-page .MuiCard-root:hover {\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n\n.cantieri-page .MuiCardContent-root {\n  padding: 16px;\n}\n\n.cantieri-page .MuiCardActions-root {\n  padding: 8px 16px;\n  display: flex;\n  justify-content: space-between;\n}\n\n.cantieri-page .cantiere-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.cantieri-page .cantiere-header .MuiSvgIcon-root {\n  font-size: 1.5rem;\n  margin-right: 8px;\n  color: #6c757d;\n}\n\n.cantieri-page .cantiere-header .MuiTypography-h6 {\n  font-size: 1.2rem;\n  font-weight: 500;\n}\n\n.cantieri-page .MuiDialog-paper {\n  border-radius: 4px;\n}\n\n.cantieri-page .MuiDialogTitle-root {\n  background-color: #f8f9fa;\n  padding: 16px 24px;\n  font-size: 1.2rem;\n  font-weight: 500;\n}\n\n.cantieri-page .MuiDialogContent-root {\n  padding: 20px 24px;\n}\n\n.cantieri-page .MuiDialogActions-root {\n  padding: 16px 24px;\n  background-color: #f8f9fa;\n}\n\n.cantieri-page .MuiTextField-root {\n  margin-bottom: 16px;\n}\n\n.cantieri-page .MuiInputLabel-root {\n  font-size: 0.95rem;\n}\n\n.cantieri-page .MuiOutlinedInput-root {\n  border-radius: 4px;\n}\n\n.cantieri-page .MuiOutlinedInput-input {\n  padding: 12px 14px;\n}\n", "/* Stile per le pagine di gestione cavi */\n.cavi-page {\n  padding: 20px;\n}\n\n.cavi-page .MuiTypography-h4 {\n  font-size: 1.8rem;\n  font-weight: 500;\n  margin-bottom: 16px;\n}\n\n.cavi-page .MuiButton-root {\n  text-transform: none;\n  font-size: 1rem;\n  padding: 8px 16px;\n  border-radius: 4px;\n  font-weight: 500;\n}\n\n.cavi-page .MuiButton-contained {\n  background-color: #f8f9fa;\n  color: #212529;\n  border: 1px solid #dee2e6;\n  box-shadow: none;\n}\n\n.cavi-page .MuiButton-contained:hover {\n  background-color: #e9ecef;\n  box-shadow: none;\n}\n\n.cavi-page .MuiIconButton-root {\n  color: #6c757d;\n}\n\n.cavi-page .MuiIconButton-root:hover {\n  background-color: #e9ecef;\n}\n\n.cavi-page .MuiPaper-root {\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n}\n\n.cavi-page .MuiTabs-root {\n  background-color: #f8f9fa;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.cavi-page .MuiTab-root {\n  text-transform: none;\n  font-size: 1rem;\n  font-weight: 500;\n  padding: 12px 16px;\n}\n\n.cavi-page .MuiTab-root.Mui-selected {\n  color: #212529;\n  font-weight: 600;\n}\n\n.cavi-page .MuiCard-root {\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  transition: box-shadow 0.3s ease;\n}\n\n.cavi-page .MuiCard-root:hover {\n  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n}\n\n.cavi-page .MuiCardContent-root {\n  padding: 16px;\n}\n\n.cavi-page .MuiCardActions-root {\n  padding: 8px 16px;\n  display: flex;\n  justify-content: space-between;\n}\n\n.cavi-page .cavo-header {\n  display: flex;\n  align-items: center;\n  margin-bottom: 16px;\n}\n\n.cavi-page .cavo-header .MuiSvgIcon-root {\n  font-size: 1.5rem;\n  margin-right: 8px;\n  color: #6c757d;\n}\n\n.cavi-page .cavo-header .MuiTypography-h6 {\n  font-size: 1.2rem;\n  font-weight: 500;\n}\n\n.cavi-page .MuiTableContainer-root {\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  margin-bottom: 16px;\n}\n\n.cavi-page .MuiTable-root {\n  border-collapse: separate;\n  border-spacing: 0;\n}\n\n.cavi-page .MuiTableHead-root {\n  background-color: #f8f9fa;\n}\n\n.cavi-page .MuiTableCell-head {\n  font-weight: 600;\n  color: #212529;\n  padding: 12px 16px;\n  border-bottom: 2px solid #dee2e6;\n}\n\n.cavi-page .MuiTableCell-body {\n  padding: 12px 16px;\n  border-bottom: 1px solid #dee2e6;\n}\n\n.cavi-page .MuiTableRow-root:hover {\n  background-color: #f8f9fa;\n}\n\n.cavi-page .MuiAlert-root {\n  border-radius: 4px;\n  margin-bottom: 16px;\n}\n\n.cavi-page .MuiCircularProgress-root {\n  color: #6c757d;\n}\n", "html, body {\n  margin: 0;\n  padding: 0;\n  width: 100%;\n  height: 100%;\n  overflow-x: hidden; /* Previene scrollbar orizzontale a livello globale */\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n#root {\n  width: 100%;\n  height: 100%;\n  overflow-x: hidden;\n}\n\ncode {\n  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',\n    monospace;\n}\n"], "names": [], "sourceRoot": ""}