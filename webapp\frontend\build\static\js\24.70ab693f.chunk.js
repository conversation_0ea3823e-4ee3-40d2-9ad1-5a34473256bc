"use strict";(self.webpackChunkcms_frontend=self.webpackChunkcms_frontend||[]).push([[24,894],{1894:(e,a,t)=>{t.d(a,{apiService:()=>s});var i=t(8816),r=t(173),o=t(561),n=t(7079),l=t(9678),c=t(2043),d=t(184);const s={login:i.A.login,logout:i.A.logout,getCurrentUser:i.A.getCurrentUser,getCantieri:r.A.getMyCantieri,getCantiere:r.A.getCantiere,createCantiere:r.A.createCantiere,deleteCantiere:r.A.deleteCantiere,getCavi:o.A.getCavi,getCavo:o.A.getCavo,createCavo:o.A.createCavo,updateCavo:o.A.updateCavo,deleteCavo:o.A.deleteCavo,aggiornaCavo:o.A.aggiornaCavo,getCertificazioni:n.A.getCertificazioni,getCertificazione:n.A.getCertificazione,createCertificazione:n.A.createCertificazione,updateCertificazione:n.A.updateCertificazione,deleteCertificazione:n.A.deleteCertificazione,getStrumenti:n.A.getStrumenti,createStrumento:n.A.createStrumento,updateStrumento:n.A.updateStrumento,deleteStrumento:n.A.deleteStrumento,getParcoCavi:l.A.getParcoCavi,createBobina:l.A.createBobina,updateBobina:l.A.updateBobina,deleteBobina:l.A.deleteBobina,generateTemplate:c.default.generateTemplate,importExcel:c.default.importExcel,getReports:d.A.getReports}},6024:(e,a,t)=>{t.r(a),t.d(a,{default:()=>z});var i=t(5043),r=t(3336),o=t(5865),n=t(7254),l=t(8903),c=t(7784),d=t(6446),s=t(1906),u=t(1794),m=t(7141),b=t(1894),g=t(579);const z=function(e){let{cantiereId:a,strumento:t,onSuccess:z,onCancel:A}=e;const[v,x]=(0,i.useState)({nome:"",marca:"",modello:"",numero_serie:"",data_calibrazione:"",data_scadenza_calibrazione:"",certificato_calibrazione:"",note:""}),[C,h]=(0,i.useState)(!1),[f,p]=(0,i.useState)("");(0,i.useEffect)((()=>{t&&x({nome:t.nome||"",marca:t.marca||"",modello:t.modello||"",numero_serie:t.numero_serie||"",data_calibrazione:t.data_calibrazione||"",data_scadenza_calibrazione:t.data_scadenza_calibrazione||"",certificato_calibrazione:t.certificato_calibrazione||"",note:t.note||""})}),[t]);const _=(e,a)=>{x((t=>({...t,[e]:a})))};return(0,g.jsxs)(r.A,{sx:{p:3},children:[(0,g.jsx)(o.A,{variant:"h6",gutterBottom:!0,children:t?"Modifica Strumento":"Nuovo Strumento"}),f&&(0,g.jsx)(n.A,{severity:"error",sx:{mb:2},children:f}),(0,g.jsx)("form",{onSubmit:async e=>{if(e.preventDefault(),v.nome.trim()?v.marca.trim()?v.modello.trim()?v.numero_serie.trim()?v.data_calibrazione?v.data_scadenza_calibrazione?!(new Date(v.data_scadenza_calibrazione)<=new Date(v.data_calibrazione))||(p("La data di scadenza deve essere successiva alla data di calibrazione"),0):(p("La data di scadenza calibrazione \xe8 obbligatoria"),0):(p("La data di calibrazione \xe8 obbligatoria"),0):(p("Il numero di serie \xe8 obbligatorio"),0):(p("Il modello dello strumento \xe8 obbligatorio"),0):(p("La marca dello strumento \xe8 obbligatoria"),0):(p("Il nome dello strumento \xe8 obbligatorio"),0))try{h(!0),p("");const e={nome:v.nome.trim(),marca:v.marca.trim(),modello:v.modello.trim(),numero_serie:v.numero_serie.trim(),data_calibrazione:v.data_calibrazione,data_scadenza_calibrazione:v.data_scadenza_calibrazione,certificato_calibrazione:v.certificato_calibrazione.trim()||null,note:v.note.trim()||null};t?(await b.apiService.updateStrumento(a,t.id_strumento,e),z("Strumento aggiornato con successo")):(await b.apiService.createStrumento(a,e),z("Strumento creato con successo"))}catch(o){var i,r;console.error("Errore nel salvataggio:",o),p((null===(i=o.response)||void 0===i||null===(r=i.data)||void 0===r?void 0:r.detail)||"Errore nel salvataggio dello strumento")}finally{h(!1)}},children:(0,g.jsxs)(l.Ay,{container:!0,spacing:3,children:[(0,g.jsx)(l.Ay,{item:!0,xs:12,children:(0,g.jsx)(o.A,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,children:"Informazioni Strumento"})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,g.jsx)(c.A,{label:"Nome Strumento",value:v.nome,onChange:e=>_("nome",e.target.value),fullWidth:!0,required:!0,placeholder:"es. Multimetro, Tester di isolamento, ecc."})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,g.jsx)(c.A,{label:"Marca",value:v.marca,onChange:e=>_("marca",e.target.value),fullWidth:!0,required:!0,placeholder:"es. Fluke, Megger, ecc."})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,g.jsx)(c.A,{label:"Modello",value:v.modello,onChange:e=>_("modello",e.target.value),fullWidth:!0,required:!0,placeholder:"es. 1587, MIT1025, ecc."})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,g.jsx)(c.A,{label:"Numero di Serie",value:v.numero_serie,onChange:e=>_("numero_serie",e.target.value),fullWidth:!0,required:!0,placeholder:"Numero di serie univoco"})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,children:(0,g.jsx)(o.A,{variant:"subtitle2",color:"text.secondary",gutterBottom:!0,sx:{mt:2},children:"Calibrazione"})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,g.jsx)(c.A,{label:"Data Calibrazione",type:"date",value:v.data_calibrazione,onChange:e=>_("data_calibrazione",e.target.value),fullWidth:!0,required:!0,InputLabelProps:{shrink:!0},inputProps:{max:(new Date).toISOString().split("T")[0]}})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,md:6,children:(0,g.jsx)(c.A,{label:"Data Scadenza Calibrazione",type:"date",value:v.data_scadenza_calibrazione,onChange:e=>_("data_scadenza_calibrazione",e.target.value),fullWidth:!0,required:!0,InputLabelProps:{shrink:!0},inputProps:{min:v.data_calibrazione}})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,children:(0,g.jsx)(c.A,{label:"Percorso Certificato di Calibrazione",value:v.certificato_calibrazione,onChange:e=>_("certificato_calibrazione",e.target.value),fullWidth:!0,placeholder:"Percorso del file del certificato (opzionale)",helperText:"Percorso relativo o assoluto del file del certificato di calibrazione"})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,children:(0,g.jsx)(c.A,{label:"Note",value:v.note,onChange:e=>_("note",e.target.value),fullWidth:!0,multiline:!0,rows:3,placeholder:"Note aggiuntive sullo strumento (opzionale)"})}),(0,g.jsx)(l.Ay,{item:!0,xs:12,children:(0,g.jsxs)(d.A,{sx:{display:"flex",gap:2,justifyContent:"flex-end",mt:2},children:[(0,g.jsx)(s.A,{variant:"outlined",startIcon:(0,g.jsx)(u.A,{}),onClick:A,disabled:C,children:"Annulla"}),(0,g.jsx)(s.A,{type:"submit",variant:"contained",startIcon:(0,g.jsx)(m.A,{}),disabled:C,children:C?"Salvataggio...":"Salva Strumento"})]})})]})})]})}}}]);
//# sourceMappingURL=24.70ab693f.chunk.js.map