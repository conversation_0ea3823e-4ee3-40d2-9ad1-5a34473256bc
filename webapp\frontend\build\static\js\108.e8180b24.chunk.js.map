{"version": 3, "file": "static/js/108.e8180b24.chunk.js", "mappings": "iNAGA,MAOA,EAPyBA,IACvB,MAAMC,EAAMC,EAAAA,OAAa,CAAC,GAI1B,OAHAA,EAAAA,WAAgB,KACdD,EAAIE,QAAUH,CAAK,IAEdC,EAAIE,OAAO,E,wBCCpB,SAASC,EAAgBC,GACvB,MAAmC,qBAArBA,EAAOC,UAA4BD,EAAOC,UAAU,OAAOC,QAAQ,mBAAoB,IAAMF,CAC7G,CAoCA,SAASG,EAAUC,EAAOC,GACxB,IAAK,IAAIC,EAAI,EAAGA,EAAIF,EAAMG,OAAQD,GAAK,EACrC,GAAID,EAAKD,EAAME,IACb,OAAOA,EAGX,OAAQ,CACV,CACA,MAAME,EA3CC,WAA0C,IAAbC,EAAMC,UAAAH,OAAA,QAAAI,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAC5C,MAAM,cACJE,GAAgB,EAAI,WACpBC,GAAa,EAAI,MACjBC,EAAK,UACLC,EAAY,MAAK,UACjBC,EAAS,KACTC,GAAO,GACLR,EACJ,MAAO,CAACS,EAAOC,KAGT,IAHW,WACfC,EAAU,eACVC,GACDF,EACKG,EAAQL,EAAOG,EAAWH,OAASG,EACnCP,IACFS,EAAQA,EAAMC,eAEZX,IACFU,EAAQvB,EAAgBuB,IAE1B,MAAME,EAAmBF,EAAkBJ,EAAQO,QAAOC,IACxD,IAAIC,GAAaX,GAAaK,GAAgBK,GAO9C,OANIb,IACFc,EAAYA,EAAUJ,eAEpBX,IACFe,EAAY5B,EAAgB4B,IAET,UAAdZ,EAAqD,IAA7BY,EAAUC,QAAQN,GAAeK,EAAUC,QAAQN,IAAU,CAAC,IAR9DJ,EAUjC,MAAwB,kBAAVJ,EAAqBU,EAAgBK,MAAM,EAAGf,GAASU,CAAe,CAExF,CAW6BM,GAIvBC,EAAkCC,IACtC,IAAIC,EACJ,OAA8B,OAAvBD,EAAWlC,UAAmF,OAA7DmC,EAAwBD,EAAWlC,QAAQoC,oBAAyB,EAASD,EAAsBE,SAASC,SAASC,eAAe,EAExKC,EAAyB,GAq6B/B,QAp6BA,SAAyBC,GACvB,MAAM,kCAEJC,EAAoCT,EAA+B,yBAEnEU,EAA2B,MAAK,aAChCC,GAAe,EAAK,cACpBC,GAAgB,EAAK,WACrBC,GAAa,EAAK,aAClBC,GAAe,EAAK,YACpBC,GAAeP,EAAMQ,SAAQ,cAC7BC,GAAgB,EAAK,cACrBC,EAAgB,kBAAiB,aACjCC,GAAeX,EAAMY,SAAWb,EAAyB,MAAI,iBAC7Dc,GAAmB,EAAK,qBACxBC,GAAuB,EACvBC,SAAUC,EAAY,uBACtBC,GAAyB,EAAK,gBAC9BC,GAAkB,EAAK,cACvBC,EAAgBlD,EAAoB,sBACpCmD,GAAwB,EAAK,SAC7BZ,GAAW,EAAK,kBAChBa,EAAiB,aACjBC,EACAxC,eAAgByC,EAAqBpC,IACnC,IAAIqC,EACJ,OAAyC,OAAjCA,EAAgBrC,EAAOsC,OAAiBD,EAAgBrC,CAAM,EACvE,QACDuC,EAAO,kBACPC,GAAqB3B,EAAMQ,SAC3BoB,GAAIC,EAAM,mBACVC,GAAqB,EACrBjD,WAAYkD,EAAc,qBAC1BC,EAAuBA,CAAC7C,EAAQ/B,IAAU+B,IAAW/B,EAAK,SAC1DwD,GAAW,EAAK,SAChBqB,EAAQ,QACRC,EAAO,kBACPC,EAAiB,cACjBC,EAAa,OACbC,EACAC,KAAMC,EAAQ,YACdC,GAAc,EAAK,QACnB7D,EAAO,SACP8D,GAAW,EAAK,cAChBC,GAAiB1C,EAAMQ,SACvBpD,MAAOuF,GACL3C,EACE4B,GAAKgB,EAAAA,EAAAA,GAAMf,GACjB,IAAI/C,EAAiByC,EACrBzC,EAAiBK,IACf,MAAM0D,EAActB,EAAmBpC,GACvC,MAA2B,kBAAhB0D,EAKFC,OAAOD,GAETA,CAAW,EAEpB,MAAME,EAAczF,EAAAA,QAAa,GAC3B0F,GAAa1F,EAAAA,QAAa,GAC1B2F,GAAW3F,EAAAA,OAAa,MACxBmC,GAAanC,EAAAA,OAAa,OACzB4F,GAAUC,IAAe7F,EAAAA,SAAe,OACxC8F,GAAYC,IAAiB/F,EAAAA,UAAgB,GAC9CgG,GAAqBlD,EAAgB,GAAK,EAC1CmD,GAAsBjG,EAAAA,OAAagG,KAClClG,GAAOoG,KAAiBC,EAAAA,EAAAA,GAAc,CAC3CC,WAAYf,EACZgB,QAAShD,EACTiD,KAAMlD,KAED7B,GAAYgF,KAAsBJ,EAAAA,EAAAA,GAAc,CACrDC,WAAY3B,EACZ4B,QAAS,GACTC,KAAMlD,EACNoD,MAAO,gBAEFC,GAASC,IAAc1G,EAAAA,UAAe,GACvC2G,GAAkB3G,EAAAA,aAAkB,CAAC4G,EAAOC,KAIhD,KADyBvD,EAAWxD,GAAMY,OAASmG,EAASnG,OAAsB,OAAbmG,KAC3C5D,EACxB,OAEF,IAAI6D,EACJ,GAAIxD,EACFwD,EAAgB,QACX,GAAgB,MAAZD,EACTC,EAAgB,OACX,CACL,MAAMvB,EAAc/D,EAAeqF,GACnCC,EAAuC,kBAAhBvB,EAA2BA,EAAc,EAClE,CACIhE,KAAeuF,IAGnBP,GAAmBO,GACfhC,GACFA,EAAc8B,EAAOE,EAAe,SACtC,GACC,CAACtF,EAAgBD,GAAY+B,EAAUwB,EAAeyB,GAAoBtD,EAAanD,MACnFkF,GAAM+B,KAAgBZ,EAAAA,EAAAA,GAAc,CACzCC,WAAYnB,EACZoB,SAAS,EACTC,KAAMlD,EACNoD,MAAO,UAEFQ,GAAeC,IAAoBjH,EAAAA,UAAe,GACnDkH,IAA6B5D,GAAqB,MAATxD,IAAiByB,KAAeC,EAAe1B,IACxFqH,GAAYnC,KAASG,EACrBxD,GAAkBwF,GAAYtD,EAAcxC,EAAQO,QAAOC,IAC3DiC,KAA0BR,EAAWxD,GAAQ,CAACA,KAAQsH,MAAKC,GAAqB,OAAXA,GAAmB3C,EAAqB7C,EAAQwF,OAO3H,CACE9F,WAAY2F,IAA6BF,GAAgB,GAAKzF,GAC9DC,mBACG,GACC8F,GAAgBC,EAAiB,CACrC5F,mBACA7B,SACAyB,gBAEFvB,EAAAA,WAAgB,KACd,MAAMwH,EAAc1H,KAAUwH,GAAcxH,MACxC2G,KAAYe,GAKZtE,IAAasE,GAGjBb,GAAgB,KAAM7G,GAAM,GAC3B,CAACA,GAAO6G,GAAiBF,GAASa,GAAcxH,MAAOoD,IAC1D,MAAMuE,GAAmBzC,IAAQrD,GAAgBjB,OAAS,IAAMyE,EAS1DuC,IAAWC,EAAAA,EAAAA,IAAiBC,KACZ,IAAhBA,EACFjC,GAAS1F,QAAQ4H,QAEjBjC,GAASkC,cAAc,oBAAoBF,OAAgBC,OAC7D,IAIF7H,EAAAA,WAAgB,KACVsD,GAAYwC,GAAahG,GAAMY,OAAS,IAC1CqF,IAAe,GACf2B,IAAU,GACZ,GACC,CAAC5H,GAAOwD,EAAUwC,GAAY4B,KA+BjC,MAAMK,IAAsBJ,EAAAA,EAAAA,IAAiBK,IAIvC,IAJwC,MAC5CpB,EAAK,MACLqB,EAAK,OACLC,EAAS,QACVF,EAYC,GAXA/B,GAAoBhG,QAAUgI,GAGf,IAAXA,EACFtC,GAAS1F,QAAQkI,gBAAgB,yBAEjCxC,GAAS1F,QAAQmI,aAAa,wBAAyB,GAAG9D,YAAa2D,KAErEpD,GACFA,EAAkB+B,GAAkB,IAAXqB,EAAe,KAAOtG,GAAgBsG,GAAQC,IAEpE/F,GAAWlC,QACd,OAEF,MAAMoI,EAAOlG,GAAWlC,QAAQ6H,cAAc,mBAAmBlF,aAC7DyF,IACFA,EAAKC,UAAUC,OAAO,GAAG3F,aACzByF,EAAKC,UAAUC,OAAO,GAAG3F,mBAE3B,IAAI4F,EAAcrG,GAAWlC,QAM7B,GALgD,YAA5CkC,GAAWlC,QAAQwI,aAAa,UAClCD,EAAcrG,GAAWlC,QAAQoC,cAAcyF,cAAc,sBAI1DU,EACH,OAEF,IAAe,IAAXP,EAEF,YADAO,EAAYE,UAAY,GAG1B,MAAM7G,EAASM,GAAWlC,QAAQ6H,cAAc,uBAAuBG,OACvE,GAAKpG,IAGLA,EAAOyG,UAAUK,IAAI,GAAG/F,aACT,aAAXsF,GACFrG,EAAOyG,UAAUK,IAAI,GAAG/F,kBAQtB4F,EAAYI,aAAeJ,EAAYK,cAA2B,UAAXX,GAAiC,UAAXA,GAAoB,CACnG,MAAMY,EAAUjH,EACVkH,EAAeP,EAAYK,aAAeL,EAAYE,UACtDM,EAAgBF,EAAQG,UAAYH,EAAQI,aAC9CF,EAAgBD,EAClBP,EAAYE,UAAYM,EAAgBR,EAAYK,aAC3CC,EAAQG,UAAYH,EAAQI,cAAgB9E,EAAU,IAAM,GAAKoE,EAAYE,YACtFF,EAAYE,UAAYI,EAAQG,UAAYH,EAAQI,cAAgB9E,EAAU,IAAM,GAExF,KAEI+E,IAAyBxB,EAAAA,EAAAA,IAAiByB,IAK1C,IAL2C,MAC/CxC,EAAK,KACLyC,EAAI,UACJC,EAAY,OAAM,OAClBpB,EAAS,QACVkB,EACC,IAAKjC,GACH,OAEF,MAgCMoC,EArIR,SAA0BtB,EAAOqB,GAC/B,IAAKnH,GAAWlC,SAAWgI,EAAQ,GAAKA,GAAStG,GAAgBjB,OAC/D,OAAQ,EAEV,IAAI8I,EAAYvB,EAChB,OAAa,CACX,MAAMpG,EAASM,GAAWlC,QAAQ6H,cAAc,uBAAuB0B,OAGjEC,GAAoB9F,KAAkC9B,GAAUA,EAAO4B,UAAqD,SAAzC5B,EAAO4G,aAAa,kBAC7G,GAAI5G,GAAUA,EAAO6H,aAAa,cAAgBD,EAEhD,OAAOD,EAaT,GAPEA,EADgB,SAAdF,GACWE,EAAY,GAAK7H,GAAgBjB,QAEjC8I,EAAY,EAAI7H,GAAgBjB,QAAUiB,GAAgBjB,OAKrE8I,IAAcvB,EAChB,OAAQ,CAEZ,CACF,CAwGoB0B,CAhCGC,MACnB,MAAMC,EAAWlI,GAAgBjB,OAAS,EAC1C,GAAa,UAAT2I,EACF,OAAOrD,GAET,GAAa,UAATqD,EACF,OAAO,EAET,GAAa,QAATA,EACF,OAAOQ,EAET,MAAMC,EAAW7D,GAAoBhG,QAAUoJ,EAC/C,OAAIS,EAAW,GACK,IAAdA,GAAmBtF,GACb,EAENZ,IAAoD,IAAjCqC,GAAoBhG,SAAkB8J,KAAKC,IAAIX,GAAQ,EACrE,EAEFQ,EAELC,EAAWD,EACTC,IAAaD,EAAW,GAAKrF,GACvB,EAENZ,GAAmBmG,KAAKC,IAAIX,GAAQ,EAC/BQ,EAEF,EAEFC,CAAQ,EAEkBF,GAAgBN,GAQnD,GAPAvB,GAAoB,CAClBE,MAAOsB,EACPrB,SACAtB,UAIE/D,GAAyB,UAATwG,EAClB,IAAmB,IAAfE,EACF5D,GAAS1F,QAAQH,MAAQyB,OACpB,CACL,MAAMM,EAASL,EAAeG,GAAgB4H,IAC9C5D,GAAS1F,QAAQH,MAAQ+B,EAKX,IADAA,EAAOH,cAAcK,QAAQR,GAAWG,gBACnCH,GAAWb,OAAS,GACrCiF,GAAS1F,QAAQgK,kBAAkB1I,GAAWb,OAAQmB,EAAOnB,OAEjE,CACF,IAkBIwJ,GAAuBlK,EAAAA,aAAkB,KAC7C,IAAKmH,GACH,OAKF,MAAMgD,EAvBkCC,MAMxC,IAAqC,IAAjCnE,GAAoBhG,SAAkBqH,GAAc3F,iBAAmB2F,GAAc3F,gBAAgBjB,SAAWiB,GAAgBjB,QAAU4G,GAAc/F,aAAeA,KAAe+B,EAAWxD,GAAMY,SAAW4G,GAAcxH,MAAMY,QAAU4G,GAAcxH,MAAMuK,OAAM,CAACC,EAAK7J,IAAMe,EAAe1B,GAAMW,MAAQe,EAAe8I,MALjTC,EAKqUjD,GAAcxH,MAL3UuH,EAKkVvH,IAJ9VyK,EAAS/I,EAAe+I,GAAU,OAClClD,EAAS7F,EAAe6F,GAAU,MAGqU,CACtX,MAAMmD,EAA4BlD,GAAc3F,gBAAgBsE,GAAoBhG,SACpF,GAAIuK,EACF,OAAOlK,EAAUqB,IAAiBE,GACzBL,EAAeK,KAAYL,EAAegJ,IAGvD,CAZoBC,IAACF,EAAQlD,EAa7B,OAAQ,CAAC,EAS8B+C,GACvC,IAAwC,IAApCD,EAEF,YADAlE,GAAoBhG,QAAUkK,GAGhC,MAAMO,EAAYpH,EAAWxD,GAAM,GAAKA,GAGxC,GAA+B,IAA3B6B,GAAgBjB,QAA6B,MAAbgK,GAMpC,GAAKvI,GAAWlC,QAKhB,GAAiB,MAAbyK,EAqBAzE,GAAoBhG,SAAW0B,GAAgBjB,OAAS,EAC1DqH,GAAoB,CAClBE,MAAOtG,GAAgBjB,OAAS,IAMpCqH,GAAoB,CAClBE,MAAOhC,GAAoBhG,cA9B7B,CACE,MAAM0K,EAAgBhJ,GAAgBsE,GAAoBhG,SAG1D,GAAIqD,GAAYqH,IAAwF,IAAvErK,EAAUR,IAAOwK,GAAO5F,EAAqBiG,EAAeL,KAC3F,OAEF,MAAMM,EAAYtK,EAAUqB,IAAiBkJ,GAAcnG,EAAqBmG,EAAYH,MACzE,IAAfE,EACFzB,GAAuB,CACrBE,KAAM,UAGRtB,GAAoB,CAClBE,MAAO2C,GAIb,OA5BEzB,GAAuB,CACrBE,KAAM,SAwCR,GAGD,CAEH1H,GAAgBjB,QAGhB4C,GAAmBxD,GAAOgE,EAAuBqF,GAAwBpB,GAAqBZ,GAAW5F,GAAY+B,IAC/GwH,IAAmBnD,EAAAA,EAAAA,IAAiBoD,KACxCC,EAAAA,EAAAA,GAAO7I,GAAY4I,GACdA,GAGLb,IAAsB,IAcxBlK,EAAAA,WAAgB,KACdkK,IAAsB,GACrB,CAACA,KACJ,MAAMe,GAAarE,IACb5B,KAGJ+B,IAAa,GACbE,IAAiB,GACblC,GACFA,EAAO6B,GACT,EAEIsE,GAAcA,CAACtE,EAAOsB,KACrBlD,KAGL+B,IAAa,GACTnC,GACFA,EAAQgC,EAAOsB,GACjB,EAEIiD,GAAcA,CAACvE,EAAOC,EAAUqB,EAAQkD,KAC5C,GAAI9H,GACF,GAAIxD,GAAMY,SAAWmG,EAASnG,QAAUZ,GAAMuK,OAAM,CAACC,EAAK7J,IAAM6J,IAAQzD,EAASpG,KAC/E,YAEG,GAAIX,KAAU+G,EACnB,OAEElC,GACFA,EAASiC,EAAOC,EAAUqB,EAAQkD,GAEpClF,GAAcW,EAAS,EAEnBwE,GAAUrL,EAAAA,QAAa,GACvBsL,GAAiB,SAAC1E,EAAO/E,GAA4D,IAAvB0J,EAAM1K,UAAAH,OAAA,QAAAI,IAAAD,UAAA,GAAAA,UAAA,GAAG,UACvEqH,EAD2CrH,UAAAH,OAAA,QAAAI,IAAAD,UAAA,GAAAA,UAAA,GAAG,eAE9CgG,EAAWhF,EACf,GAAIyB,EAAU,CACZuD,EAAW2E,MAAMC,QAAQ3L,IAASA,GAAMkC,QAAU,GAOlD,MAAM4I,EAAYtK,EAAUuG,GAAU6D,GAAahG,EAAqB7C,EAAQ6I,MAC7D,IAAfE,EACF/D,EAAS6E,KAAK7J,GACM,aAAX0J,IACT1E,EAAS8E,OAAOf,EAAW,GAC3B1C,EAAS,eAEb,CACAvB,GAAgBC,EAAOC,GACvBsE,GAAYvE,EAAOC,EAAUqB,EAAQ,CACnCrG,WAEG2B,GAA0BoD,IAAUA,EAAMgF,SAAYhF,EAAMiF,UAC/DX,GAAYtE,EAAOsB,KAEA,IAAjBlF,GAA0C,UAAjBA,GAA4BqI,GAAQpL,SAA4B,UAAjB+C,IAA6BqI,GAAQpL,UAC/G0F,GAAS1F,QAAQ6L,MAErB,EAqBMC,GAAiBA,CAACnF,EAAO0C,KAC7B,IAAKhG,EACH,OAEiB,KAAf/B,IACF2J,GAAYtE,EAAO,eAErB,IAAIoF,EAAUlG,IACM,IAAhBA,GACiB,KAAfvE,IAAmC,aAAd+H,IACvB0C,EAAUlM,GAAMY,OAAS,IAG3BsL,GAAyB,SAAd1C,EAAuB,GAAK,EACnC0C,EAAU,IACZA,EAAU,GAERA,IAAYlM,GAAMY,SACpBsL,GAAW,IAGfA,EAzCF,SAAuB/D,EAAOqB,GAC5B,IAAe,IAAXrB,EACF,OAAQ,EAEV,IAAIuB,EAAYvB,EAChB,OAAa,CAEX,GAAkB,SAAdqB,GAAwBE,IAAc1J,GAAMY,QAAwB,aAAd4I,IAA2C,IAAfE,EACpF,OAAQ,EAEV,MAAM3H,EAAS+D,GAASkC,cAAc,oBAAoB0B,OAG1D,GAAK3H,GAAWA,EAAO6H,aAAa,cAAe7H,EAAO4B,UAAqD,SAAzC5B,EAAO4G,aAAa,iBAGxF,OAAOe,EAFPA,GAA2B,SAAdF,EAAuB,GAAK,CAI7C,CACF,CAsBY2C,CAAcD,EAAS1C,GACjCvD,GAAciG,GACdtE,GAASsE,EAAQ,EAEbE,GAActF,IAClBnB,EAAYxF,SAAU,EACtBsG,GAAmB,IACfzB,GACFA,EAAc8B,EAAO,GAAI,SAE3BuE,GAAYvE,EAAOtD,EAAW,GAAK,KAAM,QAAQ,EAE7C6I,GAAgBC,GAASxF,IAI7B,GAHIwF,EAAMC,WACRD,EAAMC,UAAUzF,IAEdA,EAAM0F,uBAGU,IAAhBxG,KAAyE,IAApD,CAAC,YAAa,cAAc/D,QAAQ6E,EAAM2F,OACjExG,IAAe,GACf2B,IAAU,IAIQ,MAAhBd,EAAM4F,OACR,OAAQ5F,EAAM2F,KACZ,IAAK,OACCpF,IAAa9C,IAEfuC,EAAM6F,iBACNtD,GAAuB,CACrBE,KAAM,QACNC,UAAW,OACXpB,OAAQ,WACRtB,WAGJ,MACF,IAAK,MACCO,IAAa9C,IAEfuC,EAAM6F,iBACNtD,GAAuB,CACrBE,KAAM,MACNC,UAAW,WACXpB,OAAQ,WACRtB,WAGJ,MACF,IAAK,SAEHA,EAAM6F,iBACNtD,GAAuB,CACrBE,MAhlBK,EAilBLC,UAAW,WACXpB,OAAQ,WACRtB,UAEFqE,GAAWrE,GACX,MACF,IAAK,WAEHA,EAAM6F,iBACNtD,GAAuB,CACrBE,KA3lBK,EA4lBLC,UAAW,OACXpB,OAAQ,WACRtB,UAEFqE,GAAWrE,GACX,MACF,IAAK,YAEHA,EAAM6F,iBACNtD,GAAuB,CACrBE,KAAM,EACNC,UAAW,OACXpB,OAAQ,WACRtB,UAEFqE,GAAWrE,GACX,MACF,IAAK,UAEHA,EAAM6F,iBACNtD,GAAuB,CACrBE,MAAO,EACPC,UAAW,WACXpB,OAAQ,WACRtB,UAEFqE,GAAWrE,GACX,MACF,IAAK,YACHmF,GAAenF,EAAO,YACtB,MACF,IAAK,aACHmF,GAAenF,EAAO,QACtB,MACF,IAAK,QACH,IAAqC,IAAjCX,GAAoBhG,SAAkBkH,GAAW,CACnD,MAAMtF,EAASF,GAAgBsE,GAAoBhG,SAC7CwD,IAAWM,GAAoBA,EAAkBlC,GAIvD,GADA+E,EAAM6F,iBACFhJ,EACF,OAEF6H,GAAe1E,EAAO/E,EAAQ,gBAG1BgB,GACF8C,GAAS1F,QAAQgK,kBAAkBtE,GAAS1F,QAAQH,MAAMY,OAAQiF,GAAS1F,QAAQH,MAAMY,OAE7F,MAAWwC,GAA2B,KAAf3B,KAAmD,IAA9B2F,KACtC5D,GAEFsD,EAAM6F,iBAERnB,GAAe1E,EAAOrF,GAAY,eAAgB,aAEpD,MACF,IAAK,SACC4F,IAEFP,EAAM6F,iBAEN7F,EAAM8F,kBACNxB,GAAYtE,EAAO,WACVzD,IAAiC,KAAf5B,IAAqB+B,GAAYxD,GAAMY,OAAS,KAE3EkG,EAAM6F,iBAEN7F,EAAM8F,kBACNR,GAAYtF,IAEd,MACF,IAAK,YAEH,GAAItD,IAAa6B,GAA2B,KAAf5D,IAAqBzB,GAAMY,OAAS,EAAG,CAClE,MAAMuH,GAAwB,IAAhBnC,GAAoBhG,GAAMY,OAAS,EAAIoF,GAC/Ce,EAAW/G,GAAMkC,QACvB6E,EAAS8E,OAAO1D,EAAO,GACvBkD,GAAYvE,EAAOC,EAAU,eAAgB,CAC3ChF,OAAQ/B,GAAMmI,IAElB,CACA,MACF,IAAK,SAEH,GAAI3E,IAAa6B,GAA2B,KAAf5D,IAAqBzB,GAAMY,OAAS,IAAqB,IAAhBoF,GAAmB,CACvF,MAAMmC,EAAQnC,GACRe,EAAW/G,GAAMkC,QACvB6E,EAAS8E,OAAO1D,EAAO,GACvBkD,GAAYvE,EAAOC,EAAU,eAAgB,CAC3ChF,OAAQ/B,GAAMmI,IAElB,EAIN,EAEI0E,GAAc/F,IAClBF,IAAW,GACPxB,IAAgBO,EAAYxF,SAC9BgL,GAAWrE,EACb,EAEIgG,GAAahG,IAEbjE,EAAkCR,IACpCwD,GAAS1F,QAAQ4H,SAGnBnB,IAAW,GACXhB,GAAWzF,SAAU,EACrBwF,EAAYxF,SAAU,EAClB8C,IAA+C,IAAjCkD,GAAoBhG,SAAkBkH,GACtDmE,GAAe1E,EAAOjF,GAAgBsE,GAAoBhG,SAAU,QAC3D8C,GAAcG,GAA2B,KAAf3B,GACnC+J,GAAe1E,EAAOrF,GAAY,OAAQ,YACjC0B,GACT0D,GAAgBC,EAAO9G,IAEzBoL,GAAYtE,EAAO,QAAO,EAEtBiG,GAAoBjG,IACxB,MAAMC,EAAWD,EAAMkG,OAAOhN,MAC1ByB,KAAesF,IACjBN,GAAmBM,GACnBI,IAAiB,GACbnC,GACFA,EAAc8B,EAAOC,EAAU,UAGlB,KAAbA,EACGtD,GAAqBD,GACxB6H,GAAYvE,EAAO,KAAM,SAG3BqE,GAAWrE,EACb,EAEImG,GAAwBnG,IAC5B,MAAMqB,EAAQ+E,OAAOpG,EAAMqG,cAAcxE,aAAa,sBAClDxC,GAAoBhG,UAAYgI,GAClCF,GAAoB,CAClBnB,QACAqB,QACAC,OAAQ,SAEZ,EAEIgF,GAAyBtG,IAC7BmB,GAAoB,CAClBnB,QACAqB,MAAO+E,OAAOpG,EAAMqG,cAAcxE,aAAa,sBAC/CP,OAAQ,UAEVmD,GAAQpL,SAAU,CAAI,EAElBkN,GAAoBvG,IACxB,MAAMqB,EAAQ+E,OAAOpG,EAAMqG,cAAcxE,aAAa,sBACtD6C,GAAe1E,EAAOjF,GAAgBsG,GAAQ,gBAC9CoD,GAAQpL,SAAU,CAAK,EAEnBmN,GAAkBnF,GAASrB,IAC/B,MAAMC,EAAW/G,GAAMkC,QACvB6E,EAAS8E,OAAO1D,EAAO,GACvBkD,GAAYvE,EAAOC,EAAU,eAAgB,CAC3ChF,OAAQ/B,GAAMmI,IACd,EAEEoF,GAAuBzG,IACvB5B,GACFkG,GAAYtE,EAAO,eAEnBqE,GAAWrE,EACb,EAII0G,GAAkB1G,IAEjBA,EAAMqG,cAAc3K,SAASsE,EAAMkG,SAGpClG,EAAMkG,OAAOrE,aAAa,QAAUnE,GACtCsC,EAAM6F,gBACR,EAIIc,GAAc3G,IAEbA,EAAMqG,cAAc3K,SAASsE,EAAMkG,UAGxCnH,GAAS1F,QAAQ4H,QACbzC,GAAiBM,GAAWzF,SAAW0F,GAAS1F,QAAQuN,aAAe7H,GAAS1F,QAAQwN,iBAAmB,GAC7G9H,GAAS1F,QAAQyN,SAEnBhI,GAAWzF,SAAU,EAAK,EAEtB0N,GAAuB/G,IACtBlD,GAAgC,KAAfnC,IAAsByD,IAC1CqI,GAAqBzG,EACvB,EAEF,IAAIgH,GAAQ1K,GAAY3B,GAAWb,OAAS,EAC5CkN,GAAQA,KAAUtK,EAAWxD,GAAMY,OAAS,EAAc,OAAVZ,IAChD,IAAI+N,GAAiBlM,GACrB,GAAIyC,EAAS,CAEK,IAAI0J,IAEpBD,GAAiBlM,GAAgBoM,QAAO,CAACC,EAAKnM,EAAQoG,KACpD,MAAMgG,EAAQ7J,EAAQvC,GAkBtB,OAjBImM,EAAItN,OAAS,GAAKsN,EAAIA,EAAItN,OAAS,GAAGuN,QAAUA,EAClDD,EAAIA,EAAItN,OAAS,GAAGW,QAAQqK,KAAK7J,GASjCmM,EAAItC,KAAK,CACPa,IAAKtE,EACLA,QACAgG,QACA5M,QAAS,CAACQ,KAGPmM,CAAG,GACT,GACL,CAIA,OAHItK,GAAgB+C,IAClBmG,KAEK,CACLsB,aAAc,eAAC9B,EAAKvL,UAAAH,OAAA,QAAAI,IAAAD,UAAA,GAAAA,UAAA,GAAG,CAAC,EAAC,OAAKsN,EAAAA,EAAAA,GAAS,CACrC,YAAa1G,GAAmB,GAAGnD,YAAe,MACjD8H,EAAO,CACRC,UAAWF,GAAcC,GACzBgC,YAAad,GACbe,QAASd,IACT,EACFe,mBAAoBA,KAAA,CAClBhK,GAAI,GAAGA,UACPiK,QAASjK,IAEXkK,cAAeA,KAAA,CACblK,KACAxE,MAAOyB,GACPkN,OAAQ7B,GACR8B,QAAS/B,GACThI,SAAUkI,GACVuB,YAAaT,GAGb,wBAAyBxG,GAAY,GAAK,KAC1C,oBAAqBtE,EAAe,OAAS,OAC7C,gBAAiB4E,GAAmB,GAAGnD,iBAAexD,EACtD,gBAAiB2G,GAGjB5E,aAAc,MACd9C,IAAK4F,GACLgJ,eAAgB,OAChBC,WAAY,QACZC,KAAM,WACNpL,SAAUC,IAEZoL,cAAeA,KAAA,CACbC,UAAW,EACXC,KAAM,SACNX,QAASnC,KAEX+C,uBAAwBA,KAAA,CACtBF,UAAW,EACXC,KAAM,SACNX,QAAShB,KAEX6B,YAAaC,IAAA,IAAC,MACZlH,GACDkH,EAAA,OAAKhB,EAAAA,EAAAA,GAAS,CACb5B,IAAKtE,EACL,iBAAkBA,EAClB8G,UAAW,IACT5J,GAAY,CACdiK,SAAUhC,GAAgBnF,IAC1B,EACFoH,gBAAiBA,KAAA,CACfR,KAAM,UACNvK,GAAI,GAAGA,YACP,kBAAmB,GAAGA,UACtBvE,IAAK+K,GACLsD,YAAaxH,IAEXA,EAAM6F,gBAAgB,IAG1B6C,eAAgBC,IAGV,IAHW,MACftH,EAAK,OACLpG,GACD0N,EACC,IAAIC,EACJ,MAAMC,GAAYnM,EAAWxD,GAAQ,CAACA,KAAQsH,MAAKC,GAAoB,MAAVA,GAAkB3C,EAAqB7C,EAAQwF,KACtG5D,IAAWM,GAAoBA,EAAkBlC,GACvD,MAAO,CACL0K,IAA+E,OAAzEiD,EAAgC,MAAhBxL,OAAuB,EAASA,EAAanC,IAAmB2N,EAAgBhO,EAAeK,GACrHkN,UAAW,EACXF,KAAM,SACNvK,GAAI,GAAGA,YAAa2D,IACpByH,YAAa3C,GACbsB,QAASlB,GACTwC,aAAczC,GACd,oBAAqBjF,EACrB,gBAAiBxE,EACjB,gBAAiBgM,EAClB,EAEHnL,KACA/C,cACAzB,SACA8N,SACAgC,SAAUzI,IAAavB,GACvBuB,aACAV,QAASA,KAA2B,IAAhBX,GACpBF,YACAC,eACAC,cACA+H,kBAEJ,E,gECj+BO,SAASgC,EAA6BC,GAC3C,OAAOC,EAAAA,EAAAA,IAAqB,mBAAoBD,EAClD,EAC6BE,EAAAA,EAAAA,GAAuB,mBAAoB,CAAC,OAAQ,eAAgB,eAAgB,UAAW,QAAS,W,aCDrI,MAAMC,EAAY,CAAC,YAAa,QAAS,YAAa,iBAAkB,gBAAiB,SAuBnFC,GAAoBC,EAAAA,EAAAA,IAAO,KAAM,CACrC7J,KAAM,mBACNwJ,KAAM,OACNM,kBAAmBA,CAAC1N,EAAO2N,KACzB,MAAM,WACJC,GACE5N,EACJ,MAAO,CAAC2N,EAAOE,KAA2B,YAArBD,EAAWE,OAAuBH,EAAO,SAAQI,EAAAA,EAAAA,GAAWH,EAAWE,WAAYF,EAAWI,gBAAkBL,EAAOM,QAASL,EAAWM,OAASP,EAAOO,OAAQN,EAAWO,eAAiBR,EAAOS,OAAO,GAP5MX,EASvB7O,IAAA,IAAC,MACFyP,EAAK,WACLT,GACDhP,EAAA,OAAK6M,EAAAA,EAAAA,GAAS,CACb6C,UAAW,aACXC,WAAY,OACZC,UAAW,OACXV,OAAQO,EAAMI,MAAQJ,GAAOK,QAAQC,KAAKC,UAC1CC,WAAYR,EAAMS,WAAWD,WAC7BE,WAAYV,EAAMS,WAAWE,iBAC7BC,SAAUZ,EAAMS,WAAWI,QAAQ,KACb,YAArBtB,EAAWE,OAAuB,CACnCA,OAAQO,EAAMI,MAAQJ,GAAOK,QAAQS,QAAQC,MACvB,YAArBxB,EAAWE,OAAuB,CACnCA,MAAO,YACLF,EAAWI,gBAAkB,CAC/BqB,YAAa,GACbC,aAAc,IACb1B,EAAWM,OAAS,CACrBmB,YAAa,KACXzB,EAAWO,eAAiB,CAC9BoB,SAAU,SACVC,IAAK,EACLC,OAAQ,EACRC,iBAAkBrB,EAAMI,MAAQJ,GAAOK,QAAQiB,WAAWC,OAC1D,IACIC,EAA6BvS,EAAAA,YAAiB,SAAuBwS,EAASzS,GAClF,MAAM2C,GAAQ+P,EAAAA,EAAAA,GAAgB,CAC5B/P,MAAO8P,EACPlM,KAAM,sBAEF,UACFoM,EAAS,MACTlC,EAAQ,UAAS,UACjBmC,EAAY,KAAI,eAChBjC,GAAiB,EAAK,cACtBG,GAAgB,EAAK,MACrBD,GAAQ,GACNlO,EACJ0J,GAAQwG,EAAAA,EAAAA,GAA8BlQ,EAAOuN,GACzCK,GAAanC,EAAAA,EAAAA,GAAS,CAAC,EAAGzL,EAAO,CACrC8N,QACAmC,YACAjC,iBACAG,gBACAD,UAEIiC,EArEkBvC,KACxB,MAAM,QACJuC,EAAO,MACPrC,EAAK,eACLE,EAAc,MACdE,EAAK,cACLC,GACEP,EACEwC,EAAQ,CACZvC,KAAM,CAAC,OAAkB,YAAVC,GAAuB,SAAQC,EAAAA,EAAAA,GAAWD,MAAWE,GAAkB,UAAWE,GAAS,SAAUC,GAAiB,WAEvI,OAAOkC,EAAAA,EAAAA,GAAeD,EAAOjD,EAA8BgD,EAAQ,EA0DnDG,CAAkB1C,GAClC,OAAoB2C,EAAAA,EAAAA,KAAK/C,GAAmB/B,EAAAA,EAAAA,GAAS,CACnD+E,GAAIP,EACJD,WAAWS,EAAAA,EAAAA,GAAKN,EAAQtC,KAAMmC,GAC9B3S,IAAKA,EACLuQ,WAAYA,GACXlE,GACL,IACAmG,EAAca,sBAAuB,EAgDrC,U,8FCzIO,SAASC,EAA4BvD,GAC1C,OAAOC,EAAAA,EAAAA,IAAqB,kBAAmBD,EACjD,CACA,MACA,GAD4BE,EAAAA,EAAAA,GAAuB,kBAAmB,CAAC,OAAQ,WAAY,YAAa,UAAW,eAAgB,MAAO,eAAgB,gBAAiB,eAAgB,eAAgB,YAAa,QAAS,eAAgB,eAAgB,iBAAkB,iBAAkB,qBAAsB,SAAU,sBAAuB,QAAS,UAAW,UAAW,YAAa,SAAU,aAAc,Y,ICD5ZsD,EAAYC,E,UAChB,MAAMtD,EAAY,CAAC,eAAgB,gBAAiB,aAAc,eAAgB,YAAa,YAAa,YAAa,cAAe,gBAAiB,YAAa,YAAa,kBAAmB,eAAgB,mBAAoB,uBAAwB,WAAY,yBAA0B,kBAAmB,gBAAiB,gBAAiB,wBAAyB,iBAAkB,WAAY,YAAa,mBAAoB,oBAAqB,eAAgB,iBAAkB,uBAAwB,UAAW,oBAAqB,KAAM,qBAAsB,aAAc,YAAa,mBAAoB,eAAgB,UAAW,cAAe,WAAY,gBAAiB,WAAY,UAAW,oBAAqB,gBAAiB,SAAU,OAAQ,cAAe,WAAY,UAAW,iBAAkB,kBAAmB,YAAa,WAAY,cAAe,cAAe,eAAgB,aAAc,gBAAiB,OAAQ,YAAa,SACz8BuD,EAAa,CAAC,OACdC,EAAa,CAAC,OACdC,EAAa,CAAC,OA2DVC,GAAmBxD,EAAAA,EAAAA,IAAO,MAAO,CACrC7J,KAAM,kBACNwJ,KAAM,OACNM,kBAAmBA,CAAC1N,EAAO2N,KACzB,MAAM,WACJC,GACE5N,GACE,UACJkR,EAAS,aACTC,EAAY,aACZC,EAAY,aACZC,EAAY,KACZC,GACE1D,EACJ,MAAO,CAAC,CACN,CAAC,MAAM2D,EAAoBC,OAAQ7D,EAAO6D,KACzC,CACD,CAAC,MAAMD,EAAoBC,OAAQ7D,EAAO,WAAUI,EAAAA,EAAAA,GAAWuD,OAC9D,CACD,CAAC,MAAMC,EAAoBE,aAAc9D,EAAO8D,WAC/C,CACD,CAAC,MAAMF,EAAoBxS,SAAU4O,EAAO5O,OAC3C,CACD,CAAC,MAAMwS,EAAoBxS,SAAUsS,GAAgB1D,EAAO0D,cAC3D1D,EAAOE,KAAMqD,GAAavD,EAAOuD,UAAWE,GAAgBzD,EAAOyD,aAAcD,GAAgBxD,EAAOwD,aAAa,GAxBnG1D,CA0BtB,CACD,CAAC,KAAK8D,EAAoBxN,YAAYwN,EAAoBG,kBAAmB,CAC3EC,WAAY,WAGd,yBAA0B,CACxB,CAAC,YAAYJ,EAAoBG,kBAAmB,CAClDC,WAAY,YAGhB,CAAC,MAAMJ,EAAoBC,OAAQ,CACjCI,OAAQ,EACRC,SAAU,oBAEZ,CAAC,MAAMN,EAAoBE,aAAc,CACvC,CAAC,IAAIF,EAAoBH,mBAAmBG,EAAoBJ,iBAAkB,CAChF7B,aAAc,IAEhB,CAAC,IAAIiC,EAAoBH,gBAAgBG,EAAoBJ,iBAAkB,CAC7E7B,aAAc,IAEhB,CAAC,MAAMiC,EAAoBxS,SAAU,CACnC+S,MAAO,EACPC,SAAU,KAGd,CAAC,MAAMC,EAAAA,EAAanE,QAAS,CAC3BoE,cAAe,EACf,oBAAqB,CACnBC,QAAS,oBAGb,CAAC,MAAMF,EAAAA,EAAanE,QAAQsE,EAAAA,EAAiBC,aAAc,CACzD,CAAC,MAAMJ,EAAAA,EAAajT,SAAU,CAC5BmT,QAAS,kBAGb,CAAC,MAAMG,EAAAA,EAAqBxE,QAAS,CACnCqE,QAAS,EACT,CAAC,IAAIX,EAAoBH,mBAAmBG,EAAoBJ,iBAAkB,CAChF7B,aAAc,IAEhB,CAAC,IAAIiC,EAAoBH,gBAAgBG,EAAoBJ,iBAAkB,CAC7E7B,aAAc,IAEhB,CAAC,MAAMiC,EAAoBxS,SAAU,CACnCmT,QAAS,uBAEX,CAAC,MAAMX,EAAoBe,gBAAiB,CAC1CC,MAAO,IAGX,CAAC,MAAMF,EAAAA,EAAqBxE,QAAQsE,EAAAA,EAAiBC,aAAc,CAGjEI,WAAY,EACZP,cAAe,EACf5C,YAAa,EACb,CAAC,MAAMkC,EAAoBxS,SAAU,CACnCmT,QAAS,wBAGb,CAAC,MAAMO,EAAAA,EAAmB5E,QAAS,CACjC2E,WAAY,GACZnD,YAAa,EACb,CAAC,IAAIkC,EAAoBH,mBAAmBG,EAAoBJ,iBAAkB,CAChF7B,aAAc,IAEhB,CAAC,IAAIiC,EAAoBH,gBAAgBG,EAAoBJ,iBAAkB,CAC7E7B,aAAc,IAEhB,CAAC,MAAMmD,EAAAA,EAAmB1T,SAAU,CAClCmT,QAAS,WAEX,CAAC,MAAMX,EAAoBe,gBAAiB,CAC1CC,MAAO,IAGX,CAAC,MAAME,EAAAA,EAAmB5E,QAAQsE,EAAAA,EAAiBC,aAAc,CAC/DH,cAAe,EACf,CAAC,MAAMQ,EAAAA,EAAmB1T,SAAU,CAClCmT,QAAS,cAGb,CAAC,MAAMC,EAAAA,EAAiBO,eAAgB,CACtCF,WAAY,GAEd,CAAC,MAAMC,EAAAA,EAAmB5E,QAAQsE,EAAAA,EAAiBO,eAAgB,CACjEF,WAAY,EACZP,cAAe,EACf,CAAC,MAAMV,EAAoBxS,SAAU,CACnCyT,WAAY,GACZP,cAAe,KAGnB,CAAC,MAAMQ,EAAAA,EAAmB5E,QAAQsE,EAAAA,EAAiBO,eAAeP,EAAAA,EAAiBC,aAAc,CAC/F,CAAC,MAAMb,EAAoBxS,SAAU,CACnCyT,WAAY,EACZP,cAAe,IAGnB,CAAC,MAAMV,EAAoBxS,SAAU,CACnC4T,SAAU,EACVC,aAAc,WACdC,QAAS,GAEXC,SAAU,CAAC,CACT9S,MAAO,CACLkR,WAAW,GAEb6B,MAAO,CACLjB,MAAO,SAER,CACD9R,MAAO,CACLsR,KAAM,SAERyB,MAAO,CACL,CAAC,MAAMxB,EAAoBC,OAAQ,CACjCI,OAAQ,EACRC,SAAU,sBAGb,CACD7R,MAAO,CACLqR,cAAc,GAEhB0B,MAAO,CACL,CAAC,MAAMxB,EAAoBxS,SAAU,CACnC8T,QAAS,KAGZ,CACD7S,MAAO,CACLY,UAAU,GAEZmS,MAAO,CACL,CAAC,MAAMxB,EAAoBE,aAAc,CACvCuB,SAAU,aAKZC,GAA2BxF,EAAAA,EAAAA,IAAO,MAAO,CAC7C7J,KAAM,kBACNwJ,KAAM,eACNM,kBAAmBA,CAAC1N,EAAO2N,IAAWA,EAAO2E,cAHd7E,CAI9B,CAED8B,SAAU,WACVgD,MAAO,EACP/C,IAAK,MACL0D,UAAW,uBAEPC,GAA6B1F,EAAAA,EAAAA,IAAO2F,EAAAA,EAAY,CACpDxP,KAAM,kBACNwJ,KAAM,iBACNM,kBAAmBA,CAAC1N,EAAO2N,IAAWA,EAAO+D,gBAHZjE,CAIhC,CACD4F,aAAc,EACdnB,QAAS,EACTP,WAAY,WAER2B,GAA6B7F,EAAAA,EAAAA,IAAO2F,EAAAA,EAAY,CACpDxP,KAAM,kBACNwJ,KAAM,iBACNM,kBAAmBA,CAAA9O,EAEhB+O,KAAM,IAFW,WAClBC,GACDhP,EAAA,OAAa6M,EAAAA,EAAAA,GAAS,CAAC,EAAGkC,EAAO4F,eAAgB3F,EAAWnJ,WAAakJ,EAAO6F,mBAAmB,GALnE/F,CAMhC,CACDyE,QAAS,EACTmB,aAAc,EACdP,SAAU,CAAC,CACT9S,MAAO,CACLyE,WAAW,GAEbsO,MAAO,CACLG,UAAW,sBAIXO,IAAqBhG,EAAAA,EAAAA,IAAOiG,EAAAA,EAAQ,CACxC9P,KAAM,kBACNwJ,KAAM,SACNM,kBAAmBA,CAAC1N,EAAO2N,KACzB,MAAM,WACJC,GACE5N,EACJ,MAAO,CAAC,CACN,CAAC,MAAMuR,EAAoBpS,UAAWwO,EAAOxO,QAC5CwO,EAAOgG,OAAQ/F,EAAWgG,eAAiBjG,EAAOkG,oBAAoB,GATlDpG,EAWxBnI,IAAA,IAAC,MACF+I,GACD/I,EAAA,MAAM,CACLmK,QAASpB,EAAMI,MAAQJ,GAAOoB,OAAOqE,MACrChB,SAAU,CAAC,CACT9S,MAAO,CACL4T,eAAe,GAEjBb,MAAO,CACLxD,SAAU,cAGf,IACKwE,IAAoBtG,EAAAA,EAAAA,IAAOuG,EAAAA,EAAO,CACtCpQ,KAAM,kBACNwJ,KAAM,QACNM,kBAAmBA,CAAC1N,EAAO2N,IAAWA,EAAOiC,OAHrBnC,EAIvB/G,IAAA,IAAC,MACF2H,GACD3H,EAAA,OAAK+E,EAAAA,EAAAA,GAAS,CAAC,EAAG4C,EAAMS,WAAWmF,MAAO,CACzCC,SAAU,QACV,IACIC,IAAsB1G,EAAAA,EAAAA,IAAO,MAAO,CACxC7J,KAAM,kBACNwJ,KAAM,UACNM,kBAAmBA,CAAC1N,EAAO2N,IAAWA,EAAOyG,SAHnB3G,EAIzBhB,IAAA,IAAC,MACF4B,GACD5B,EAAA,MAAM,CACLqB,OAAQO,EAAMI,MAAQJ,GAAOK,QAAQC,KAAKC,UAC1CsD,QAAS,YACV,IACKmC,IAAwB5G,EAAAA,EAAAA,IAAO,MAAO,CAC1C7J,KAAM,kBACNwJ,KAAM,YACNM,kBAAmBA,CAAC1N,EAAO2N,IAAWA,EAAO2G,WAHjB7G,EAI3BZ,IAAA,IAAC,MACFwB,GACDxB,EAAA,MAAM,CACLiB,OAAQO,EAAMI,MAAQJ,GAAOK,QAAQC,KAAKC,UAC1CsD,QAAS,YACV,IACKqC,IAAsB9G,EAAAA,EAAAA,IAAO,MAAO,CACxC7J,KAAM,kBACNwJ,KAAM,UACNM,kBAAmBA,CAAC1N,EAAO2N,IAAWA,EAAO6G,SAHnB/G,EAIzBgH,IAAA,IAAC,MACFpG,GACDoG,EAAA,MAAM,CACLjG,UAAW,OACXoD,OAAQ,EACRM,QAAS,QACTwC,UAAW,OACXR,SAAU,OACV3E,SAAU,WACV,CAAC,MAAMgC,EAAoBpS,UAAW,CACpCwV,UAAW,GACXC,QAAS,OACTV,SAAU,SACVW,eAAgB,aAChBC,WAAY,SACZC,OAAQ,UACRvC,WAAY,EACZlE,UAAW,aACX0G,QAAS,IACTC,wBAAyB,cACzBhD,cAAe,EACf5C,YAAa,GACbC,aAAc,GACd,CAACjB,EAAM6G,YAAYC,GAAG,OAAQ,CAC5BR,UAAW,QAEb,CAAC,KAAKpD,EAAoBxN,WAAY,CACpC2L,iBAAkBrB,EAAMI,MAAQJ,GAAOK,QAAQ0G,OAAOC,MAEtD,uBAAwB,CACtB3F,gBAAiB,gBAGrB,0BAA2B,CACzBmD,SAAUxE,EAAMI,MAAQJ,GAAOK,QAAQ0G,OAAOE,gBAC9CC,cAAe,QAEjB,CAAC,KAAKhE,EAAoBiE,gBAAiB,CACzC9F,iBAAkBrB,EAAMI,MAAQJ,GAAOK,QAAQ0G,OAAOjQ,OAExD,0BAA2B,CACzBuK,gBAAiBrB,EAAMI,KAAO,QAAQJ,EAAMI,KAAKC,QAAQS,QAAQsG,iBAAiBpH,EAAMI,KAAKC,QAAQ0G,OAAOM,oBAAqBC,EAAAA,EAAAA,IAAMtH,EAAMK,QAAQS,QAAQC,KAAMf,EAAMK,QAAQ0G,OAAOM,iBACxL,CAAC,KAAKnE,EAAoBxN,WAAY,CACpC2L,gBAAiBrB,EAAMI,KAAO,QAAQJ,EAAMI,KAAKC,QAAQS,QAAQsG,sBAAsBpH,EAAMI,KAAKC,QAAQ0G,OAAOM,qBAAqBrH,EAAMI,KAAKC,QAAQ0G,OAAOQ,kBAAmBD,EAAAA,EAAAA,IAAMtH,EAAMK,QAAQS,QAAQC,KAAMf,EAAMK,QAAQ0G,OAAOM,gBAAkBrH,EAAMK,QAAQ0G,OAAOQ,cAEjR,uBAAwB,CACtBlG,iBAAkBrB,EAAMI,MAAQJ,GAAOK,QAAQ0G,OAAOrI,WAG1D,CAAC,KAAKwE,EAAoBiE,gBAAiB,CACzC9F,gBAAiBrB,EAAMI,KAAO,QAAQJ,EAAMI,KAAKC,QAAQS,QAAQsG,sBAAsBpH,EAAMI,KAAKC,QAAQ0G,OAAOM,qBAAqBrH,EAAMI,KAAKC,QAAQ0G,OAAOS,kBAAmBF,EAAAA,EAAAA,IAAMtH,EAAMK,QAAQS,QAAQC,KAAMf,EAAMK,QAAQ0G,OAAOM,gBAAkBrH,EAAMK,QAAQ0G,OAAOS,iBAIxR,IACKC,IAAyBrI,EAAAA,EAAAA,IAAOoC,EAAe,CACnDjM,KAAM,kBACNwJ,KAAM,aACNM,kBAAmBA,CAAC1N,EAAO2N,IAAWA,EAAOoI,YAHhBtI,EAI5BuI,IAAA,IAAC,MACF3H,GACD2H,EAAA,MAAM,CACLtG,iBAAkBrB,EAAMI,MAAQJ,GAAOK,QAAQiB,WAAWC,MAC1DJ,KAAM,EACP,IACKyG,IAAsBxI,EAAAA,EAAAA,IAAO,KAAM,CACvC7J,KAAM,kBACNwJ,KAAM,UACNM,kBAAmBA,CAAC1N,EAAO2N,IAAWA,EAAOuI,SAHnBzI,CAIzB,CACDyE,QAAS,EACT,CAAC,MAAMX,EAAoBpS,UAAW,CACpCkQ,YAAa,MAstBjB,GAltBkC/R,EAAAA,YAAiB,SAAsBwS,EAASzS,GAChF,IAAI8Y,EAAuBC,EAAkBC,EAAmBC,EAChE,MAAMtW,GAAQ+P,EAAAA,EAAAA,GAAgB,CAC5B/P,MAAO8P,EACPlM,KAAM,qBAIF,aACFzD,GAAe,EAAK,cACpBC,GAAgB,EAAK,WACrBC,GAAa,EAAK,aAClBC,GAAe,EAAK,UACpBiW,EAAS,UACTvG,EAAS,UACTwG,EAAY5F,IAAeA,GAA0BL,EAAAA,EAAAA,KAAKkG,EAAAA,EAAW,CACnExH,SAAU,WACT,YACH1O,GAAeP,EAAMQ,SAAQ,cAC7BC,GAAgB,EAAK,UACrBiW,EAAY,QAAO,UACnBC,EAAY,QAAO,gBACnBC,EAAkB,CAAC,EAAC,aACpBjW,GAAeX,EAAMY,SAAW,GAAK,MAAI,iBACzCC,GAAmB,EAAK,qBACxBC,GAAuB,EAAK,SAC5BC,GAAW,EAAK,uBAChBE,GAAyB,EAAK,gBAC9BC,GAAkB,EAAK,cACvB0S,GAAgB,EAAK,sBACrBxS,GAAwB,EAAK,eAC7ByV,EAAiB,OAAM,SACvBrW,IAAW,EAAK,UAChB0Q,IAAY,EAAK,iBACjB4F,GAAmBC,GAAQ,IAAIA,IAC/BjY,eAAgByC,GAAkB,QAClCG,GAAO,kBACPC,IAAqB3B,EAAMQ,SAAQ,mBACnCsB,IAAqB,EAAK,UAC1BkV,IAAY,EAAE,iBACdC,GAAmB,KAAI,aACvBC,GAAY,QACZ9C,IAAU,EAAK,YACf+C,GAAc,gBAAU,SACxBvW,IAAW,EAAK,cAChBwW,GAAgB,aAAY,YAC5B5U,IAAc,EAAK,SACnB6U,GAAW,OAAM,eACjBC,GAAiBtD,EAAAA,EAAK,gBACtBuD,GAAkB7D,EAAAA,EAAM,UACxB8D,GAAY3G,IAAuBA,GAAkCN,EAAAA,EAAAA,KAAKkH,EAAAA,EAAmB,CAAC,IAAG,SACjGhV,IAAW,EACXiV,YAAaC,GAAe,YAC5BC,GACAC,aAAcC,GAAgB,WAC9BC,GAAU,cACVrV,IAAiB1C,EAAMQ,SAAQ,KAC/B8Q,GAAO,SAAQ,UACf0G,GAAY,CAAC,GACXhY,EACJ0J,IAAQwG,EAAAA,EAAAA,GAA8BlQ,EAAOuN,IAGzC,aACJ/B,GAAY,cACZM,GAAa,mBACbF,GAAkB,uBAClBW,GAAsB,cACtBH,GAAa,YACbI,GAAW,gBACXG,GAAe,eACfC,GAAc,MACdxP,GAAK,MACL8N,GAAK,SACLgC,GAAQ,GACRtL,GAAE,UACF6C,GAAS,QACTV,GAAO,WACPX,GAAU,SACVF,GAAQ,YACRC,GAAW,WACXtE,GAAU,eACVsM,IACE8M,GAAgBxM,EAAAA,EAAAA,GAAS,CAAC,EAAGzL,EAAO,CACtCU,cAAe,kBAEXyQ,IAAgBtQ,IAAqBE,GAAYmK,KAAUzI,GAC3D2O,KAAiB5Q,KAA+B,IAAnBqW,KAA+C,IAAnBA,GAE7DnL,YAAaT,IACXa,MAEFzO,IAAK6a,IACa,MAAhBhB,GAAuBA,GAAe,CAAC,EACrCiB,GAAmBxL,MAErBtP,IAAKoC,IACH0Y,GACJC,IAAoBlI,EAAAA,EAAAA,GAA8BiI,GAAkBrH,GAChEuH,IAAqBC,EAAAA,EAAAA,GAAW7Y,GAAYyY,IAK5CpZ,GAAiByC,IAJOpC,KAC5B,IAAIqC,EACJ,OAAyC,OAAjCA,EAAgBrC,EAAOsC,OAAiBD,EAAgBrC,CAAM,GAKlEyO,IAAanC,EAAAA,EAAAA,GAAS,CAAC,EAAGzL,EAAO,CACrC4T,gBACA1G,YACAnJ,WACAmN,aACApS,kBACAqS,gBACAC,gBACAC,cAA8B,IAAhBjO,GACdqB,aACA6M,UAEInB,GA3ekBvC,KACxB,MAAM,QACJuC,EAAO,cACPyD,EAAa,SACb1G,EAAQ,QACRnJ,EAAO,UACPmN,EAAS,aACTC,EAAY,aACZC,EAAY,aACZC,EAAY,UACZ5M,EAAS,KACT6M,GACE1D,EACEwC,EAAQ,CACZvC,KAAM,CAAC,OAAQX,GAAY,WAAYnJ,GAAW,UAAWmN,GAAa,YAAaC,GAAgB,eAAgBC,GAAgB,gBACvIK,UAAW,CAAC,aACZ1S,MAAO,CAAC,QAASsS,GAAgB,gBACjCG,IAAK,CAAC,MAAO,WAAUzD,EAAAA,EAAAA,GAAWuD,MAClCgB,aAAc,CAAC,gBACfZ,eAAgB,CAAC,kBACjB6B,eAAgB,CAAC,iBAAkB9O,GAAa,sBAChDkP,OAAQ,CAAC,SAAUC,GAAiB,uBACpChE,MAAO,CAAC,SACR4E,QAAS,CAAC,WACVJ,QAAS,CAAC,WACVE,UAAW,CAAC,aACZnV,OAAQ,CAAC,UACT4W,WAAY,CAAC,cACbG,QAAS,CAAC,YAEZ,OAAO7F,EAAAA,EAAAA,GAAeD,EAAOO,EAA6BR,EAAQ,EA6clDG,CAAkB1C,IAClC,IAAI2K,GACJ,GAAI3X,IAAYxD,GAAMY,OAAS,EAAG,CAChC,MAAMwa,EAAwBC,IAAUhN,EAAAA,EAAAA,GAAS,CAC/CuE,UAAWG,GAAQqB,IACnBzQ,YACCyL,GAAYiM,IAEbF,GADER,GACeA,GAAW3a,GAAOob,EAAuB5K,IAEzCxQ,GAAMsb,KAAI,CAACvZ,EAAQoG,KAClC,MAAMoT,EAAwBH,EAAsB,CAChDjT,WAEF,IACEsE,GACE8O,EACJC,GAAiB1I,EAAAA,EAAAA,GAA8ByI,EAAuB5H,GACxE,OAAoBR,EAAAA,EAAAA,KAAKsI,EAAAA,GAAMpN,EAAAA,EAAAA,GAAS,CACtChK,MAAO3C,GAAeK,GACtBmS,KAAMA,IACLsH,EAAgBrC,GAAY1M,EAAI,GAGzC,CACA,GAAImN,IAAa,GAAKlO,MAAMC,QAAQwP,IAAiB,CACnD,MAAMxB,EAAOwB,GAAeva,OAASgZ,IAChCjT,IAAWgT,EAAO,IACrBwB,GAAiBA,GAAetP,OAAO,EAAG+N,IAC1CuB,GAAevP,MAAmBuH,EAAAA,EAAAA,KAAK,OAAQ,CAC7CP,UAAWG,GAAQqB,IACnBsH,SAAUhC,GAAiBC,IAC1BwB,GAAeva,SAEtB,CACA,MAYM0Z,GAAcC,IAZOc,KAAuBM,EAAAA,EAAAA,MAAM,KAAM,CAC5DD,SAAU,EAAcvI,EAAAA,EAAAA,KAAKuF,GAAwB,CACnD9F,UAAWG,GAAQ4F,WACnBnI,WAAYA,GACZqC,UAAW,MACX6I,SAAUL,EAAOlN,SACFgF,EAAAA,EAAAA,KAAK0F,GAAqB,CACzCjG,UAAWG,GAAQ+F,QACnBtI,WAAYA,GACZkL,SAAUL,EAAOK,aAElBL,EAAO5O,MAYJgO,GAAeC,IAVOkB,EAACC,EAAQ9Z,KAEnC,MAAM,IACF0K,GACEoP,EACJC,GAAahJ,EAAAA,EAAAA,GAA8B+I,EAAQjI,GACrD,OAAoBT,EAAAA,EAAAA,KAAK,MAAM9E,EAAAA,EAAAA,GAAS,CAAC,EAAGyN,EAAY,CACtDJ,SAAUha,GAAeK,KACvB0K,EAAI,GAGJsP,GAAmBA,CAACha,EAAQoG,KAChC,MAAM6T,EAAcxM,GAAe,CACjCzN,SACAoG,UAEF,OAAOsS,IAAapM,EAAAA,EAAAA,GAAS,CAAC,EAAG2N,EAAa,CAC5CpJ,UAAWG,GAAQhR,SACjBA,EAAQ,CACV4N,SAAUqM,EAAY,iBACtB7T,QACA1G,eACC+O,GAAW,EAEVyL,GAAgF,OAArDlD,EAAwB6B,GAAUtG,gBAA0ByE,EAAwBS,EAAgBlF,eAC/H4H,GAAyD,OAAvClD,EAAmB4B,GAAUpI,OAAiBwG,EAAmBQ,EAAgBhH,MACnG2J,GAA4D,OAAzClD,EAAoB2B,GAAUrE,QAAkB0C,EAAoBO,EAAgBjD,OACvG6F,GAAgF,OAArDlD,EAAwB0B,GAAUzE,gBAA0B+C,EAAwBM,EAAgBrD,eACrI,OAAoBwF,EAAAA,EAAAA,MAAMzb,EAAAA,SAAgB,CACxCwb,SAAU,EAAcvI,EAAAA,EAAAA,KAAKU,GAAkBxF,EAAAA,EAAAA,GAAS,CACtDpO,IAAKA,EACL2S,WAAWS,EAAAA,EAAAA,GAAKN,GAAQtC,KAAMmC,GAC9BpC,WAAYA,IACXpC,GAAa9B,IAAQ,CACtBoP,SAAUlB,GAAY,CACpBhW,MACAb,WACAmQ,WAAW,EACXI,KAAe,UAATA,GAAmB,aAAUlT,EACnCqb,gBAAiB7N,KACjB8N,YAAYjO,EAAAA,EAAAA,GAAS,CACnBpO,IAAK8F,GACL6M,UAAWG,GAAQsB,UACnB8G,kBACA5M,QAASzH,IACHA,EAAMkG,SAAWlG,EAAMqG,eACzBU,GAAqB/G,EACvB,IAEAiN,IAAgBC,KAAiB,CACnCkB,cAA2ByG,EAAAA,EAAAA,MAAM9F,EAA0B,CACzDjD,UAAWG,GAAQmC,aACnB1E,WAAYA,GACZkL,SAAU,CAAC3H,IAA4BZ,EAAAA,EAAAA,KAAK4C,GAA4B1H,EAAAA,EAAAA,GAAS,CAAC,EAAGW,KAAiB,CACpG,aAAcsK,EACdiD,MAAOjD,EACP9I,WAAYA,IACXyL,GAAyB,CAC1BrJ,WAAWS,EAAAA,EAAAA,GAAKN,GAAQuB,eAA2C,MAA3B2H,QAAkC,EAASA,GAAwBrJ,WAC3G8I,SAAUtC,KACN,KAAMpF,IAA4Bb,EAAAA,EAAAA,KAAK+C,GAA4B7H,EAAAA,EAAAA,GAAS,CAAC,EAAGc,KAA0B,CAC9GxL,SAAUA,EACV,aAAc0D,GAAYkS,EAAYU,GACtCsC,MAAOlV,GAAYkS,EAAYU,GAC/BzJ,WAAYA,IACX4L,GAAyB,CAC1BxJ,WAAWS,EAAAA,EAAAA,GAAKN,GAAQoD,eAA2C,MAA3BiG,QAAkC,EAASA,GAAwBxJ,WAC3G8I,SAAUtB,MACN,UAGVoC,YAAYnO,EAAAA,EAAAA,GAAS,CACnBuE,UAAWG,GAAQpR,MACnBgC,WACA0B,aACCqJ,WAEF5I,IAAwBqN,EAAAA,EAAAA,KAAKkD,IAAoBhI,EAAAA,EAAAA,GAAS,CAC7D+E,GAAI+G,GACJ3D,cAAeA,EACfb,MAAO,CACLjB,MAAO5O,GAAWA,GAAS2W,YAAc,MAE3CjM,WAAYA,GACZzB,KAAM,eACNjJ,SAAUA,GACVZ,KAAMmC,IACL8U,GAAiB,CAClBvJ,WAAWS,EAAAA,EAAAA,GAAKN,GAAQwD,OAA2B,MAAnB4F,QAA0B,EAASA,GAAgBvJ,WACnF8I,UAAuBC,EAAAA,EAAAA,MAAMhF,IAAmBtI,EAAAA,EAAAA,GAAS,CACvDmC,WAAYA,GACZ4C,GAAI8G,IACHgC,GAAgB,CACjBtJ,WAAWS,EAAAA,EAAAA,GAAKN,GAAQP,MAAyB,MAAlB0J,QAAyB,EAASA,GAAetJ,WAChF8I,SAAU,CAAC1E,IAAqC,IAA1BjJ,GAAenN,QAA4BuS,EAAAA,EAAAA,KAAK4D,GAAqB,CACzFnE,UAAWG,GAAQiE,QACnBxG,WAAYA,GACZkL,SAAU3B,KACP,KAAgC,IAA1BhM,GAAenN,QAAiBwC,IAAa4T,GASnD,MAT0E7D,EAAAA,EAAAA,KAAK8D,GAAuB,CACzGrE,UAAWG,GAAQmE,UACnB1G,WAAYA,GACZzB,KAAM,eACNT,YAAaxH,IAEXA,EAAM6F,gBAAgB,EAExB+O,SAAU1B,KACDjM,GAAenN,OAAS,GAAiBuS,EAAAA,EAAAA,KAAKgE,IAAqB9I,EAAAA,EAAAA,GAAS,CACrF+E,GAAIyG,GACJjH,UAAWG,GAAQqE,QACnB5G,WAAYA,IACXwK,GAAmBlB,GAAc,CAClC7Z,IAAKgb,GACLS,SAAU3N,GAAeuN,KAAI,CAACvZ,EAAQoG,IAChC7D,GACKgW,GAAY,CACjB7N,IAAK1K,EAAO0K,IACZ0B,MAAOpM,EAAOoM,MACduN,SAAU3Z,EAAOR,QAAQ+Z,KAAI,CAACoB,EAASC,IAAWZ,GAAiBW,EAAS3a,EAAOoG,MAAQwU,OAGxFZ,GAAiBha,EAAQoG,QAE9B,YAEJ,OAEV,G", "sources": ["../node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js", "../node_modules/@mui/material/useAutocomplete/useAutocomplete.js", "../node_modules/@mui/material/ListSubheader/listSubheaderClasses.js", "../node_modules/@mui/material/ListSubheader/ListSubheader.js", "../node_modules/@mui/material/Autocomplete/autocompleteClasses.js", "../node_modules/@mui/material/Autocomplete/Autocomplete.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst usePreviousProps = value => {\n  const ref = React.useRef({});\n  React.useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n};\nexport default usePreviousProps;", "'use client';\n\n/* eslint-disable no-constant-condition */\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_setRef as setRef, unstable_useEventCallback as useEventCallback, unstable_useControlled as useControlled, unstable_useId as useId, usePreviousProps } from '@mui/utils';\n\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\n// Give up on IE11 support for this feature\nfunction stripDiacritics(string) {\n  return typeof string.normalize !== 'undefined' ? string.normalize('NFD').replace(/[\\u0300-\\u036f]/g, '') : string;\n}\nexport function createFilterOptions(config = {}) {\n  const {\n    ignoreAccents = true,\n    ignoreCase = true,\n    limit,\n    matchFrom = 'any',\n    stringify,\n    trim = false\n  } = config;\n  return (options, {\n    inputValue,\n    getOptionLabel\n  }) => {\n    let input = trim ? inputValue.trim() : inputValue;\n    if (ignoreCase) {\n      input = input.toLowerCase();\n    }\n    if (ignoreAccents) {\n      input = stripDiacritics(input);\n    }\n    const filteredOptions = !input ? options : options.filter(option => {\n      let candidate = (stringify || getOptionLabel)(option);\n      if (ignoreCase) {\n        candidate = candidate.toLowerCase();\n      }\n      if (ignoreAccents) {\n        candidate = stripDiacritics(candidate);\n      }\n      return matchFrom === 'start' ? candidate.indexOf(input) === 0 : candidate.indexOf(input) > -1;\n    });\n    return typeof limit === 'number' ? filteredOptions.slice(0, limit) : filteredOptions;\n  };\n}\n\n// To replace with .findIndex() once we stop IE11 support.\nfunction findIndex(array, comp) {\n  for (let i = 0; i < array.length; i += 1) {\n    if (comp(array[i])) {\n      return i;\n    }\n  }\n  return -1;\n}\nconst defaultFilterOptions = createFilterOptions();\n\n// Number of options to jump in list box when `Page Up` and `Page Down` keys are used.\nconst pageSize = 5;\nconst defaultIsActiveElementInListbox = listboxRef => {\n  var _listboxRef$current$p;\n  return listboxRef.current !== null && ((_listboxRef$current$p = listboxRef.current.parentElement) == null ? void 0 : _listboxRef$current$p.contains(document.activeElement));\n};\nconst MULTIPLE_DEFAULT_VALUE = [];\nfunction useAutocomplete(props) {\n  const {\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_isActiveElementInListbox = defaultIsActiveElementInListbox,\n    // eslint-disable-next-line @typescript-eslint/naming-convention\n    unstable_classNamePrefix = 'Mui',\n    autoComplete = false,\n    autoHighlight = false,\n    autoSelect = false,\n    blurOnSelect = false,\n    clearOnBlur = !props.freeSolo,\n    clearOnEscape = false,\n    componentName = 'useAutocomplete',\n    defaultValue = props.multiple ? MULTIPLE_DEFAULT_VALUE : null,\n    disableClearable = false,\n    disableCloseOnSelect = false,\n    disabled: disabledProp,\n    disabledItemsFocusable = false,\n    disableListWrap = false,\n    filterOptions = defaultFilterOptions,\n    filterSelectedOptions = false,\n    freeSolo = false,\n    getOptionDisabled,\n    getOptionKey,\n    getOptionLabel: getOptionLabelProp = option => {\n      var _option$label;\n      return (_option$label = option.label) != null ? _option$label : option;\n    },\n    groupBy,\n    handleHomeEndKeys = !props.freeSolo,\n    id: idProp,\n    includeInputInList = false,\n    inputValue: inputValueProp,\n    isOptionEqualToValue = (option, value) => option === value,\n    multiple = false,\n    onChange,\n    onClose,\n    onHighlightChange,\n    onInputChange,\n    onOpen,\n    open: openProp,\n    openOnFocus = false,\n    options,\n    readOnly = false,\n    selectOnFocus = !props.freeSolo,\n    value: valueProp\n  } = props;\n  const id = useId(idProp);\n  let getOptionLabel = getOptionLabelProp;\n  getOptionLabel = option => {\n    const optionLabel = getOptionLabelProp(option);\n    if (typeof optionLabel !== 'string') {\n      if (process.env.NODE_ENV !== 'production') {\n        const erroneousReturn = optionLabel === undefined ? 'undefined' : `${typeof optionLabel} (${optionLabel})`;\n        console.error(`MUI: The \\`getOptionLabel\\` method of ${componentName} returned ${erroneousReturn} instead of a string for ${JSON.stringify(option)}.`);\n      }\n      return String(optionLabel);\n    }\n    return optionLabel;\n  };\n  const ignoreFocus = React.useRef(false);\n  const firstFocus = React.useRef(true);\n  const inputRef = React.useRef(null);\n  const listboxRef = React.useRef(null);\n  const [anchorEl, setAnchorEl] = React.useState(null);\n  const [focusedTag, setFocusedTag] = React.useState(-1);\n  const defaultHighlighted = autoHighlight ? 0 : -1;\n  const highlightedIndexRef = React.useRef(defaultHighlighted);\n  const [value, setValueState] = useControlled({\n    controlled: valueProp,\n    default: defaultValue,\n    name: componentName\n  });\n  const [inputValue, setInputValueState] = useControlled({\n    controlled: inputValueProp,\n    default: '',\n    name: componentName,\n    state: 'inputValue'\n  });\n  const [focused, setFocused] = React.useState(false);\n  const resetInputValue = React.useCallback((event, newValue) => {\n    // retain current `inputValue` if new option isn't selected and `clearOnBlur` is false\n    // When `multiple` is enabled, `newValue` is an array of all selected items including the newly selected item\n    const isOptionSelected = multiple ? value.length < newValue.length : newValue !== null;\n    if (!isOptionSelected && !clearOnBlur) {\n      return;\n    }\n    let newInputValue;\n    if (multiple) {\n      newInputValue = '';\n    } else if (newValue == null) {\n      newInputValue = '';\n    } else {\n      const optionLabel = getOptionLabel(newValue);\n      newInputValue = typeof optionLabel === 'string' ? optionLabel : '';\n    }\n    if (inputValue === newInputValue) {\n      return;\n    }\n    setInputValueState(newInputValue);\n    if (onInputChange) {\n      onInputChange(event, newInputValue, 'reset');\n    }\n  }, [getOptionLabel, inputValue, multiple, onInputChange, setInputValueState, clearOnBlur, value]);\n  const [open, setOpenState] = useControlled({\n    controlled: openProp,\n    default: false,\n    name: componentName,\n    state: 'open'\n  });\n  const [inputPristine, setInputPristine] = React.useState(true);\n  const inputValueIsSelectedValue = !multiple && value != null && inputValue === getOptionLabel(value);\n  const popupOpen = open && !readOnly;\n  const filteredOptions = popupOpen ? filterOptions(options.filter(option => {\n    if (filterSelectedOptions && (multiple ? value : [value]).some(value2 => value2 !== null && isOptionEqualToValue(option, value2))) {\n      return false;\n    }\n    return true;\n  }),\n  // we use the empty string to manipulate `filterOptions` to not filter any options\n  // i.e. the filter predicate always returns true\n  {\n    inputValue: inputValueIsSelectedValue && inputPristine ? '' : inputValue,\n    getOptionLabel\n  }) : [];\n  const previousProps = usePreviousProps({\n    filteredOptions,\n    value,\n    inputValue\n  });\n  React.useEffect(() => {\n    const valueChange = value !== previousProps.value;\n    if (focused && !valueChange) {\n      return;\n    }\n\n    // Only reset the input's value when freeSolo if the component's value changes.\n    if (freeSolo && !valueChange) {\n      return;\n    }\n    resetInputValue(null, value);\n  }, [value, resetInputValue, focused, previousProps.value, freeSolo]);\n  const listboxAvailable = open && filteredOptions.length > 0 && !readOnly;\n  if (process.env.NODE_ENV !== 'production') {\n    if (value !== null && !freeSolo && options.length > 0) {\n      const missingValue = (multiple ? value : [value]).filter(value2 => !options.some(option => isOptionEqualToValue(option, value2)));\n      if (missingValue.length > 0) {\n        console.warn([`MUI: The value provided to ${componentName} is invalid.`, `None of the options match with \\`${missingValue.length > 1 ? JSON.stringify(missingValue) : JSON.stringify(missingValue[0])}\\`.`, 'You can use the `isOptionEqualToValue` prop to customize the equality test.'].join('\\n'));\n      }\n    }\n  }\n  const focusTag = useEventCallback(tagToFocus => {\n    if (tagToFocus === -1) {\n      inputRef.current.focus();\n    } else {\n      anchorEl.querySelector(`[data-tag-index=\"${tagToFocus}\"]`).focus();\n    }\n  });\n\n  // Ensure the focusedTag is never inconsistent\n  React.useEffect(() => {\n    if (multiple && focusedTag > value.length - 1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n  }, [value, multiple, focusedTag, focusTag]);\n  function validOptionIndex(index, direction) {\n    if (!listboxRef.current || index < 0 || index >= filteredOptions.length) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      const option = listboxRef.current.querySelector(`[data-option-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      const nextFocusDisabled = disabledItemsFocusable ? false : !option || option.disabled || option.getAttribute('aria-disabled') === 'true';\n      if (option && option.hasAttribute('tabindex') && !nextFocusDisabled) {\n        // The next option is available\n        return nextFocus;\n      }\n\n      // The next option is disabled, move to the next element.\n      // with looped index\n      if (direction === 'next') {\n        nextFocus = (nextFocus + 1) % filteredOptions.length;\n      } else {\n        nextFocus = (nextFocus - 1 + filteredOptions.length) % filteredOptions.length;\n      }\n\n      // We end up with initial index, that means we don't have available options.\n      // All of them are disabled\n      if (nextFocus === index) {\n        return -1;\n      }\n    }\n  }\n  const setHighlightedIndex = useEventCallback(({\n    event,\n    index,\n    reason = 'auto'\n  }) => {\n    highlightedIndexRef.current = index;\n\n    // does the index exist?\n    if (index === -1) {\n      inputRef.current.removeAttribute('aria-activedescendant');\n    } else {\n      inputRef.current.setAttribute('aria-activedescendant', `${id}-option-${index}`);\n    }\n    if (onHighlightChange) {\n      onHighlightChange(event, index === -1 ? null : filteredOptions[index], reason);\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n    const prev = listboxRef.current.querySelector(`[role=\"option\"].${unstable_classNamePrefix}-focused`);\n    if (prev) {\n      prev.classList.remove(`${unstable_classNamePrefix}-focused`);\n      prev.classList.remove(`${unstable_classNamePrefix}-focusVisible`);\n    }\n    let listboxNode = listboxRef.current;\n    if (listboxRef.current.getAttribute('role') !== 'listbox') {\n      listboxNode = listboxRef.current.parentElement.querySelector('[role=\"listbox\"]');\n    }\n\n    // \"No results\"\n    if (!listboxNode) {\n      return;\n    }\n    if (index === -1) {\n      listboxNode.scrollTop = 0;\n      return;\n    }\n    const option = listboxRef.current.querySelector(`[data-option-index=\"${index}\"]`);\n    if (!option) {\n      return;\n    }\n    option.classList.add(`${unstable_classNamePrefix}-focused`);\n    if (reason === 'keyboard') {\n      option.classList.add(`${unstable_classNamePrefix}-focusVisible`);\n    }\n\n    // Scroll active descendant into view.\n    // Logic copied from https://www.w3.org/WAI/content-assets/wai-aria-practices/patterns/combobox/examples/js/select-only.js\n    // In case of mouse clicks and touch (in mobile devices) we avoid scrolling the element and keep both behaviors same.\n    // Consider this API instead once it has a better browser support:\n    // .scrollIntoView({ scrollMode: 'if-needed', block: 'nearest' });\n    if (listboxNode.scrollHeight > listboxNode.clientHeight && reason !== 'mouse' && reason !== 'touch') {\n      const element = option;\n      const scrollBottom = listboxNode.clientHeight + listboxNode.scrollTop;\n      const elementBottom = element.offsetTop + element.offsetHeight;\n      if (elementBottom > scrollBottom) {\n        listboxNode.scrollTop = elementBottom - listboxNode.clientHeight;\n      } else if (element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0) < listboxNode.scrollTop) {\n        listboxNode.scrollTop = element.offsetTop - element.offsetHeight * (groupBy ? 1.3 : 0);\n      }\n    }\n  });\n  const changeHighlightedIndex = useEventCallback(({\n    event,\n    diff,\n    direction = 'next',\n    reason = 'auto'\n  }) => {\n    if (!popupOpen) {\n      return;\n    }\n    const getNextIndex = () => {\n      const maxIndex = filteredOptions.length - 1;\n      if (diff === 'reset') {\n        return defaultHighlighted;\n      }\n      if (diff === 'start') {\n        return 0;\n      }\n      if (diff === 'end') {\n        return maxIndex;\n      }\n      const newIndex = highlightedIndexRef.current + diff;\n      if (newIndex < 0) {\n        if (newIndex === -1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap && highlightedIndexRef.current !== -1 || Math.abs(diff) > 1) {\n          return 0;\n        }\n        return maxIndex;\n      }\n      if (newIndex > maxIndex) {\n        if (newIndex === maxIndex + 1 && includeInputInList) {\n          return -1;\n        }\n        if (disableListWrap || Math.abs(diff) > 1) {\n          return maxIndex;\n        }\n        return 0;\n      }\n      return newIndex;\n    };\n    const nextIndex = validOptionIndex(getNextIndex(), direction);\n    setHighlightedIndex({\n      index: nextIndex,\n      reason,\n      event\n    });\n\n    // Sync the content of the input with the highlighted option.\n    if (autoComplete && diff !== 'reset') {\n      if (nextIndex === -1) {\n        inputRef.current.value = inputValue;\n      } else {\n        const option = getOptionLabel(filteredOptions[nextIndex]);\n        inputRef.current.value = option;\n\n        // The portion of the selected suggestion that has not been typed by the user,\n        // a completion string, appears inline after the input cursor in the textbox.\n        const index = option.toLowerCase().indexOf(inputValue.toLowerCase());\n        if (index === 0 && inputValue.length > 0) {\n          inputRef.current.setSelectionRange(inputValue.length, option.length);\n        }\n      }\n    }\n  });\n  const getPreviousHighlightedOptionIndex = () => {\n    const isSameValue = (value1, value2) => {\n      const label1 = value1 ? getOptionLabel(value1) : '';\n      const label2 = value2 ? getOptionLabel(value2) : '';\n      return label1 === label2;\n    };\n    if (highlightedIndexRef.current !== -1 && previousProps.filteredOptions && previousProps.filteredOptions.length !== filteredOptions.length && previousProps.inputValue === inputValue && (multiple ? value.length === previousProps.value.length && previousProps.value.every((val, i) => getOptionLabel(value[i]) === getOptionLabel(val)) : isSameValue(previousProps.value, value))) {\n      const previousHighlightedOption = previousProps.filteredOptions[highlightedIndexRef.current];\n      if (previousHighlightedOption) {\n        return findIndex(filteredOptions, option => {\n          return getOptionLabel(option) === getOptionLabel(previousHighlightedOption);\n        });\n      }\n    }\n    return -1;\n  };\n  const syncHighlightedIndex = React.useCallback(() => {\n    if (!popupOpen) {\n      return;\n    }\n\n    // Check if the previously highlighted option still exists in the updated filtered options list and if the value and inputValue haven't changed\n    // If it exists and the value and the inputValue haven't changed, just update its index, otherwise continue execution\n    const previousHighlightedOptionIndex = getPreviousHighlightedOptionIndex();\n    if (previousHighlightedOptionIndex !== -1) {\n      highlightedIndexRef.current = previousHighlightedOptionIndex;\n      return;\n    }\n    const valueItem = multiple ? value[0] : value;\n\n    // The popup is empty, reset\n    if (filteredOptions.length === 0 || valueItem == null) {\n      changeHighlightedIndex({\n        diff: 'reset'\n      });\n      return;\n    }\n    if (!listboxRef.current) {\n      return;\n    }\n\n    // Synchronize the value with the highlighted index\n    if (valueItem != null) {\n      const currentOption = filteredOptions[highlightedIndexRef.current];\n\n      // Keep the current highlighted index if possible\n      if (multiple && currentOption && findIndex(value, val => isOptionEqualToValue(currentOption, val)) !== -1) {\n        return;\n      }\n      const itemIndex = findIndex(filteredOptions, optionItem => isOptionEqualToValue(optionItem, valueItem));\n      if (itemIndex === -1) {\n        changeHighlightedIndex({\n          diff: 'reset'\n        });\n      } else {\n        setHighlightedIndex({\n          index: itemIndex\n        });\n      }\n      return;\n    }\n\n    // Prevent the highlighted index to leak outside the boundaries.\n    if (highlightedIndexRef.current >= filteredOptions.length - 1) {\n      setHighlightedIndex({\n        index: filteredOptions.length - 1\n      });\n      return;\n    }\n\n    // Restore the focus to the previous index.\n    setHighlightedIndex({\n      index: highlightedIndexRef.current\n    });\n    // Ignore filteredOptions (and options, isOptionEqualToValue, getOptionLabel) not to break the scroll position\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [\n  // Only sync the highlighted index when the option switch between empty and not\n  filteredOptions.length,\n  // Don't sync the highlighted index with the value when multiple\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  multiple ? false : value, filterSelectedOptions, changeHighlightedIndex, setHighlightedIndex, popupOpen, inputValue, multiple]);\n  const handleListboxRef = useEventCallback(node => {\n    setRef(listboxRef, node);\n    if (!node) {\n      return;\n    }\n    syncHighlightedIndex();\n  });\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (!inputRef.current || inputRef.current.nodeName !== 'INPUT') {\n        if (inputRef.current && inputRef.current.nodeName === 'TEXTAREA') {\n          console.warn([`A textarea element was provided to ${componentName} where input was expected.`, `This is not a supported scenario but it may work under certain conditions.`, `A textarea keyboard navigation may conflict with Autocomplete controls (for example enter and arrow keys).`, `Make sure to test keyboard navigation and add custom event handlers if necessary.`].join('\\n'));\n        } else {\n          console.error([`MUI: Unable to find the input element. It was resolved to ${inputRef.current} while an HTMLInputElement was expected.`, `Instead, ${componentName} expects an input element.`, '', componentName === 'useAutocomplete' ? 'Make sure you have bound getInputProps correctly and that the normal ref/effect resolutions order is guaranteed.' : 'Make sure you have customized the input component correctly.'].join('\\n'));\n        }\n      }\n    }, [componentName]);\n  }\n  React.useEffect(() => {\n    syncHighlightedIndex();\n  }, [syncHighlightedIndex]);\n  const handleOpen = event => {\n    if (open) {\n      return;\n    }\n    setOpenState(true);\n    setInputPristine(true);\n    if (onOpen) {\n      onOpen(event);\n    }\n  };\n  const handleClose = (event, reason) => {\n    if (!open) {\n      return;\n    }\n    setOpenState(false);\n    if (onClose) {\n      onClose(event, reason);\n    }\n  };\n  const handleValue = (event, newValue, reason, details) => {\n    if (multiple) {\n      if (value.length === newValue.length && value.every((val, i) => val === newValue[i])) {\n        return;\n      }\n    } else if (value === newValue) {\n      return;\n    }\n    if (onChange) {\n      onChange(event, newValue, reason, details);\n    }\n    setValueState(newValue);\n  };\n  const isTouch = React.useRef(false);\n  const selectNewValue = (event, option, reasonProp = 'selectOption', origin = 'options') => {\n    let reason = reasonProp;\n    let newValue = option;\n    if (multiple) {\n      newValue = Array.isArray(value) ? value.slice() : [];\n      if (process.env.NODE_ENV !== 'production') {\n        const matches = newValue.filter(val => isOptionEqualToValue(option, val));\n        if (matches.length > 1) {\n          console.error([`MUI: The \\`isOptionEqualToValue\\` method of ${componentName} does not handle the arguments correctly.`, `The component expects a single value to match a given option but found ${matches.length} matches.`].join('\\n'));\n        }\n      }\n      const itemIndex = findIndex(newValue, valueItem => isOptionEqualToValue(option, valueItem));\n      if (itemIndex === -1) {\n        newValue.push(option);\n      } else if (origin !== 'freeSolo') {\n        newValue.splice(itemIndex, 1);\n        reason = 'removeOption';\n      }\n    }\n    resetInputValue(event, newValue);\n    handleValue(event, newValue, reason, {\n      option\n    });\n    if (!disableCloseOnSelect && (!event || !event.ctrlKey && !event.metaKey)) {\n      handleClose(event, reason);\n    }\n    if (blurOnSelect === true || blurOnSelect === 'touch' && isTouch.current || blurOnSelect === 'mouse' && !isTouch.current) {\n      inputRef.current.blur();\n    }\n  };\n  function validTagIndex(index, direction) {\n    if (index === -1) {\n      return -1;\n    }\n    let nextFocus = index;\n    while (true) {\n      // Out of range\n      if (direction === 'next' && nextFocus === value.length || direction === 'previous' && nextFocus === -1) {\n        return -1;\n      }\n      const option = anchorEl.querySelector(`[data-tag-index=\"${nextFocus}\"]`);\n\n      // Same logic as MenuList.js\n      if (!option || !option.hasAttribute('tabindex') || option.disabled || option.getAttribute('aria-disabled') === 'true') {\n        nextFocus += direction === 'next' ? 1 : -1;\n      } else {\n        return nextFocus;\n      }\n    }\n  }\n  const handleFocusTag = (event, direction) => {\n    if (!multiple) {\n      return;\n    }\n    if (inputValue === '') {\n      handleClose(event, 'toggleInput');\n    }\n    let nextTag = focusedTag;\n    if (focusedTag === -1) {\n      if (inputValue === '' && direction === 'previous') {\n        nextTag = value.length - 1;\n      }\n    } else {\n      nextTag += direction === 'next' ? 1 : -1;\n      if (nextTag < 0) {\n        nextTag = 0;\n      }\n      if (nextTag === value.length) {\n        nextTag = -1;\n      }\n    }\n    nextTag = validTagIndex(nextTag, direction);\n    setFocusedTag(nextTag);\n    focusTag(nextTag);\n  };\n  const handleClear = event => {\n    ignoreFocus.current = true;\n    setInputValueState('');\n    if (onInputChange) {\n      onInputChange(event, '', 'clear');\n    }\n    handleValue(event, multiple ? [] : null, 'clear');\n  };\n  const handleKeyDown = other => event => {\n    if (other.onKeyDown) {\n      other.onKeyDown(event);\n    }\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (focusedTag !== -1 && ['ArrowLeft', 'ArrowRight'].indexOf(event.key) === -1) {\n      setFocusedTag(-1);\n      focusTag(-1);\n    }\n\n    // Wait until IME is settled.\n    if (event.which !== 229) {\n      switch (event.key) {\n        case 'Home':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'start',\n              direction: 'next',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'End':\n          if (popupOpen && handleHomeEndKeys) {\n            // Prevent scroll of the page\n            event.preventDefault();\n            changeHighlightedIndex({\n              diff: 'end',\n              direction: 'previous',\n              reason: 'keyboard',\n              event\n            });\n          }\n          break;\n        case 'PageUp':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -pageSize,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'PageDown':\n          // Prevent scroll of the page\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: pageSize,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowDown':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: 1,\n            direction: 'next',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowUp':\n          // Prevent cursor move\n          event.preventDefault();\n          changeHighlightedIndex({\n            diff: -1,\n            direction: 'previous',\n            reason: 'keyboard',\n            event\n          });\n          handleOpen(event);\n          break;\n        case 'ArrowLeft':\n          handleFocusTag(event, 'previous');\n          break;\n        case 'ArrowRight':\n          handleFocusTag(event, 'next');\n          break;\n        case 'Enter':\n          if (highlightedIndexRef.current !== -1 && popupOpen) {\n            const option = filteredOptions[highlightedIndexRef.current];\n            const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n\n            // Avoid early form validation, let the end-users continue filling the form.\n            event.preventDefault();\n            if (disabled) {\n              return;\n            }\n            selectNewValue(event, option, 'selectOption');\n\n            // Move the selection to the end.\n            if (autoComplete) {\n              inputRef.current.setSelectionRange(inputRef.current.value.length, inputRef.current.value.length);\n            }\n          } else if (freeSolo && inputValue !== '' && inputValueIsSelectedValue === false) {\n            if (multiple) {\n              // Allow people to add new values before they submit the form.\n              event.preventDefault();\n            }\n            selectNewValue(event, inputValue, 'createOption', 'freeSolo');\n          }\n          break;\n        case 'Escape':\n          if (popupOpen) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClose(event, 'escape');\n          } else if (clearOnEscape && (inputValue !== '' || multiple && value.length > 0)) {\n            // Avoid Opera to exit fullscreen mode.\n            event.preventDefault();\n            // Avoid the Modal to handle the event.\n            event.stopPropagation();\n            handleClear(event);\n          }\n          break;\n        case 'Backspace':\n          // Remove the value on the left of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0) {\n            const index = focusedTag === -1 ? value.length - 1 : focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        case 'Delete':\n          // Remove the value on the right of the \"cursor\"\n          if (multiple && !readOnly && inputValue === '' && value.length > 0 && focusedTag !== -1) {\n            const index = focusedTag;\n            const newValue = value.slice();\n            newValue.splice(index, 1);\n            handleValue(event, newValue, 'removeOption', {\n              option: value[index]\n            });\n          }\n          break;\n        default:\n      }\n    }\n  };\n  const handleFocus = event => {\n    setFocused(true);\n    if (openOnFocus && !ignoreFocus.current) {\n      handleOpen(event);\n    }\n  };\n  const handleBlur = event => {\n    // Ignore the event when using the scrollbar with IE11\n    if (unstable_isActiveElementInListbox(listboxRef)) {\n      inputRef.current.focus();\n      return;\n    }\n    setFocused(false);\n    firstFocus.current = true;\n    ignoreFocus.current = false;\n    if (autoSelect && highlightedIndexRef.current !== -1 && popupOpen) {\n      selectNewValue(event, filteredOptions[highlightedIndexRef.current], 'blur');\n    } else if (autoSelect && freeSolo && inputValue !== '') {\n      selectNewValue(event, inputValue, 'blur', 'freeSolo');\n    } else if (clearOnBlur) {\n      resetInputValue(event, value);\n    }\n    handleClose(event, 'blur');\n  };\n  const handleInputChange = event => {\n    const newValue = event.target.value;\n    if (inputValue !== newValue) {\n      setInputValueState(newValue);\n      setInputPristine(false);\n      if (onInputChange) {\n        onInputChange(event, newValue, 'input');\n      }\n    }\n    if (newValue === '') {\n      if (!disableClearable && !multiple) {\n        handleValue(event, null, 'clear');\n      }\n    } else {\n      handleOpen(event);\n    }\n  };\n  const handleOptionMouseMove = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    if (highlightedIndexRef.current !== index) {\n      setHighlightedIndex({\n        event,\n        index,\n        reason: 'mouse'\n      });\n    }\n  };\n  const handleOptionTouchStart = event => {\n    setHighlightedIndex({\n      event,\n      index: Number(event.currentTarget.getAttribute('data-option-index')),\n      reason: 'touch'\n    });\n    isTouch.current = true;\n  };\n  const handleOptionClick = event => {\n    const index = Number(event.currentTarget.getAttribute('data-option-index'));\n    selectNewValue(event, filteredOptions[index], 'selectOption');\n    isTouch.current = false;\n  };\n  const handleTagDelete = index => event => {\n    const newValue = value.slice();\n    newValue.splice(index, 1);\n    handleValue(event, newValue, 'removeOption', {\n      option: value[index]\n    });\n  };\n  const handlePopupIndicator = event => {\n    if (open) {\n      handleClose(event, 'toggleInput');\n    } else {\n      handleOpen(event);\n    }\n  };\n\n  // Prevent input blur when interacting with the combobox\n  const handleMouseDown = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    if (event.target.getAttribute('id') !== id) {\n      event.preventDefault();\n    }\n  };\n\n  // Focus the input when interacting with the combobox\n  const handleClick = event => {\n    // Prevent focusing the input if click is anywhere outside the Autocomplete\n    if (!event.currentTarget.contains(event.target)) {\n      return;\n    }\n    inputRef.current.focus();\n    if (selectOnFocus && firstFocus.current && inputRef.current.selectionEnd - inputRef.current.selectionStart === 0) {\n      inputRef.current.select();\n    }\n    firstFocus.current = false;\n  };\n  const handleInputMouseDown = event => {\n    if (!disabledProp && (inputValue === '' || !open)) {\n      handlePopupIndicator(event);\n    }\n  };\n  let dirty = freeSolo && inputValue.length > 0;\n  dirty = dirty || (multiple ? value.length > 0 : value !== null);\n  let groupedOptions = filteredOptions;\n  if (groupBy) {\n    // used to keep track of key and indexes in the result array\n    const indexBy = new Map();\n    let warn = false;\n    groupedOptions = filteredOptions.reduce((acc, option, index) => {\n      const group = groupBy(option);\n      if (acc.length > 0 && acc[acc.length - 1].group === group) {\n        acc[acc.length - 1].options.push(option);\n      } else {\n        if (process.env.NODE_ENV !== 'production') {\n          if (indexBy.get(group) && !warn) {\n            console.warn(`MUI: The options provided combined with the \\`groupBy\\` method of ${componentName} returns duplicated headers.`, 'You can solve the issue by sorting the options with the output of `groupBy`.');\n            warn = true;\n          }\n          indexBy.set(group, true);\n        }\n        acc.push({\n          key: index,\n          index,\n          group,\n          options: [option]\n        });\n      }\n      return acc;\n    }, []);\n  }\n  if (disabledProp && focused) {\n    handleBlur();\n  }\n  return {\n    getRootProps: (other = {}) => _extends({\n      'aria-owns': listboxAvailable ? `${id}-listbox` : null\n    }, other, {\n      onKeyDown: handleKeyDown(other),\n      onMouseDown: handleMouseDown,\n      onClick: handleClick\n    }),\n    getInputLabelProps: () => ({\n      id: `${id}-label`,\n      htmlFor: id\n    }),\n    getInputProps: () => ({\n      id,\n      value: inputValue,\n      onBlur: handleBlur,\n      onFocus: handleFocus,\n      onChange: handleInputChange,\n      onMouseDown: handleInputMouseDown,\n      // if open then this is handled imperatively so don't let react override\n      // only have an opinion about this when closed\n      'aria-activedescendant': popupOpen ? '' : null,\n      'aria-autocomplete': autoComplete ? 'both' : 'list',\n      'aria-controls': listboxAvailable ? `${id}-listbox` : undefined,\n      'aria-expanded': listboxAvailable,\n      // Disable browser's suggestion that might overlap with the popup.\n      // Handle autocomplete but not autofill.\n      autoComplete: 'off',\n      ref: inputRef,\n      autoCapitalize: 'none',\n      spellCheck: 'false',\n      role: 'combobox',\n      disabled: disabledProp\n    }),\n    getClearProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handleClear\n    }),\n    getPopupIndicatorProps: () => ({\n      tabIndex: -1,\n      type: 'button',\n      onClick: handlePopupIndicator\n    }),\n    getTagProps: ({\n      index\n    }) => _extends({\n      key: index,\n      'data-tag-index': index,\n      tabIndex: -1\n    }, !readOnly && {\n      onDelete: handleTagDelete(index)\n    }),\n    getListboxProps: () => ({\n      role: 'listbox',\n      id: `${id}-listbox`,\n      'aria-labelledby': `${id}-label`,\n      ref: handleListboxRef,\n      onMouseDown: event => {\n        // Prevent blur\n        event.preventDefault();\n      }\n    }),\n    getOptionProps: ({\n      index,\n      option\n    }) => {\n      var _getOptionKey;\n      const selected = (multiple ? value : [value]).some(value2 => value2 != null && isOptionEqualToValue(option, value2));\n      const disabled = getOptionDisabled ? getOptionDisabled(option) : false;\n      return {\n        key: (_getOptionKey = getOptionKey == null ? void 0 : getOptionKey(option)) != null ? _getOptionKey : getOptionLabel(option),\n        tabIndex: -1,\n        role: 'option',\n        id: `${id}-option-${index}`,\n        onMouseMove: handleOptionMouseMove,\n        onClick: handleOptionClick,\n        onTouchStart: handleOptionTouchStart,\n        'data-option-index': index,\n        'aria-disabled': disabled,\n        'aria-selected': selected\n      };\n    },\n    id,\n    inputValue,\n    value,\n    dirty,\n    expanded: popupOpen && anchorEl,\n    popupOpen,\n    focused: focused || focusedTag !== -1,\n    anchorEl,\n    setAnchorEl,\n    focusedTag,\n    groupedOptions\n  };\n}\nexport default useAutocomplete;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListSubheaderUtilityClass(slot) {\n  return generateUtilityClass('MuiListSubheader', slot);\n}\nconst listSubheaderClasses = generateUtilityClasses('MuiListSubheader', ['root', 'colorPrimary', 'colorInherit', 'gutters', 'inset', 'sticky']);\nexport default listSubheaderClasses;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"disableGutters\", \"disableSticky\", \"inset\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport composeClasses from '@mui/utils/composeClasses';\nimport styled from '../styles/styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport capitalize from '../utils/capitalize';\nimport { getListSubheaderUtilityClass } from './listSubheaderClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    color,\n    disableGutters,\n    inset,\n    disableSticky\n  } = ownerState;\n  const slots = {\n    root: ['root', color !== 'default' && `color${capitalize(color)}`, !disableGutters && 'gutters', inset && 'inset', !disableSticky && 'sticky']\n  };\n  return composeClasses(slots, getListSubheaderUtilityClass, classes);\n};\nconst ListSubheaderRoot = styled('li', {\n  name: 'MuiListSubheader',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, ownerState.color !== 'default' && styles[`color${capitalize(ownerState.color)}`], !ownerState.disableGutters && styles.gutters, ownerState.inset && styles.inset, !ownerState.disableSticky && styles.sticky];\n  }\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  boxSizing: 'border-box',\n  lineHeight: '48px',\n  listStyle: 'none',\n  color: (theme.vars || theme).palette.text.secondary,\n  fontFamily: theme.typography.fontFamily,\n  fontWeight: theme.typography.fontWeightMedium,\n  fontSize: theme.typography.pxToRem(14)\n}, ownerState.color === 'primary' && {\n  color: (theme.vars || theme).palette.primary.main\n}, ownerState.color === 'inherit' && {\n  color: 'inherit'\n}, !ownerState.disableGutters && {\n  paddingLeft: 16,\n  paddingRight: 16\n}, ownerState.inset && {\n  paddingLeft: 72\n}, !ownerState.disableSticky && {\n  position: 'sticky',\n  top: 0,\n  zIndex: 1,\n  backgroundColor: (theme.vars || theme).palette.background.paper\n}));\nconst ListSubheader = /*#__PURE__*/React.forwardRef(function ListSubheader(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiListSubheader'\n  });\n  const {\n      className,\n      color = 'default',\n      component = 'li',\n      disableGutters = false,\n      disableSticky = false,\n      inset = false\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    disableGutters,\n    disableSticky,\n    inset\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ListSubheaderRoot, _extends({\n    as: component,\n    className: clsx(classes.root, className),\n    ref: ref,\n    ownerState: ownerState\n  }, other));\n});\nListSubheader.muiSkipListHighlight = true;\nprocess.env.NODE_ENV !== \"production\" ? ListSubheader.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'default'\n   */\n  color: PropTypes.oneOf(['default', 'inherit', 'primary']),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the List Subheader will not have gutters.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * If `true`, the List Subheader will not stick to the top during scroll.\n   * @default false\n   */\n  disableSticky: PropTypes.bool,\n  /**\n   * If `true`, the List Subheader is indented.\n   * @default false\n   */\n  inset: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListSubheader;", "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAutocompleteUtilityClass(slot) {\n  return generateUtilityClass('MuiAutocomplete', slot);\n}\nconst autocompleteClasses = generateUtilityClasses('MuiAutocomplete', ['root', 'expanded', 'fullWidth', 'focused', 'focusVisible', 'tag', 'tagSizeSmall', 'tagSizeMedium', 'hasPopupIcon', 'hasClearIcon', 'inputRoot', 'input', 'inputFocused', 'endAdornment', 'clearIndicator', 'popupIndicator', 'popupIndicatorOpen', 'popper', 'popperDisablePortal', 'paper', 'listbox', 'loading', 'noOptions', 'option', 'groupLabel', 'groupUl']);\nexport default autocompleteClasses;", "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _ClearIcon, _ArrowDropDownIcon;\nconst _excluded = [\"autoComplete\", \"autoHighlight\", \"autoSelect\", \"blurOnSelect\", \"ChipProps\", \"className\", \"clearIcon\", \"clearOnBlur\", \"clearOnEscape\", \"clearText\", \"closeText\", \"componentsProps\", \"defaultValue\", \"disableClearable\", \"disableCloseOnSelect\", \"disabled\", \"disabledItemsFocusable\", \"disableListWrap\", \"disablePortal\", \"filterOptions\", \"filterSelectedOptions\", \"forcePopupIcon\", \"freeSolo\", \"fullWidth\", \"getLimitTagsText\", \"getOptionDisabled\", \"getOptionKey\", \"getOptionLabel\", \"isOptionEqualToValue\", \"groupBy\", \"handleHomeEndKeys\", \"id\", \"includeInputInList\", \"inputValue\", \"limitTags\", \"ListboxComponent\", \"ListboxProps\", \"loading\", \"loadingText\", \"multiple\", \"noOptionsText\", \"onChange\", \"onClose\", \"onHighlightChange\", \"onInputChange\", \"onOpen\", \"open\", \"openOnFocus\", \"openText\", \"options\", \"PaperComponent\", \"PopperComponent\", \"popupIcon\", \"readOnly\", \"renderGroup\", \"renderInput\", \"renderOption\", \"renderTags\", \"selectOnFocus\", \"size\", \"slotProps\", \"value\"],\n  _excluded2 = [\"ref\"],\n  _excluded3 = [\"key\"],\n  _excluded4 = [\"key\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport useAutocomplete, { createFilterOptions } from '../useAutocomplete';\nimport Popper from '../Popper';\nimport ListSubheader from '../ListSubheader';\nimport Paper from '../Paper';\nimport IconButton from '../IconButton';\nimport Chip from '../Chip';\nimport inputClasses from '../Input/inputClasses';\nimport inputBaseClasses from '../InputBase/inputBaseClasses';\nimport outlinedInputClasses from '../OutlinedInput/outlinedInputClasses';\nimport filledInputClasses from '../FilledInput/filledInputClasses';\nimport ClearIcon from '../internal/svg-icons/Close';\nimport ArrowDropDownIcon from '../internal/svg-icons/ArrowDropDown';\nimport { styled } from '../zero-styled';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport autocompleteClasses, { getAutocompleteUtilityClass } from './autocompleteClasses';\nimport capitalize from '../utils/capitalize';\nimport useForkRef from '../utils/useForkRef';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    disablePortal,\n    expanded,\n    focused,\n    fullWidth,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused,\n    popupOpen,\n    size\n  } = ownerState;\n  const slots = {\n    root: ['root', expanded && 'expanded', focused && 'focused', fullWidth && 'fullWidth', hasClearIcon && 'hasClearIcon', hasPopupIcon && 'hasPopupIcon'],\n    inputRoot: ['inputRoot'],\n    input: ['input', inputFocused && 'inputFocused'],\n    tag: ['tag', `tagSize${capitalize(size)}`],\n    endAdornment: ['endAdornment'],\n    clearIndicator: ['clearIndicator'],\n    popupIndicator: ['popupIndicator', popupOpen && 'popupIndicatorOpen'],\n    popper: ['popper', disablePortal && 'popperDisablePortal'],\n    paper: ['paper'],\n    listbox: ['listbox'],\n    loading: ['loading'],\n    noOptions: ['noOptions'],\n    option: ['option'],\n    groupLabel: ['groupLabel'],\n    groupUl: ['groupUl']\n  };\n  return composeClasses(slots, getAutocompleteUtilityClass, classes);\n};\nconst AutocompleteRoot = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    const {\n      fullWidth,\n      hasClearIcon,\n      hasPopupIcon,\n      inputFocused,\n      size\n    } = ownerState;\n    return [{\n      [`& .${autocompleteClasses.tag}`]: styles.tag\n    }, {\n      [`& .${autocompleteClasses.tag}`]: styles[`tagSize${capitalize(size)}`]\n    }, {\n      [`& .${autocompleteClasses.inputRoot}`]: styles.inputRoot\n    }, {\n      [`& .${autocompleteClasses.input}`]: styles.input\n    }, {\n      [`& .${autocompleteClasses.input}`]: inputFocused && styles.inputFocused\n    }, styles.root, fullWidth && styles.fullWidth, hasPopupIcon && styles.hasPopupIcon, hasClearIcon && styles.hasClearIcon];\n  }\n})({\n  [`&.${autocompleteClasses.focused} .${autocompleteClasses.clearIndicator}`]: {\n    visibility: 'visible'\n  },\n  /* Avoid double tap issue on iOS */\n  '@media (pointer: fine)': {\n    [`&:hover .${autocompleteClasses.clearIndicator}`]: {\n      visibility: 'visible'\n    }\n  },\n  [`& .${autocompleteClasses.tag}`]: {\n    margin: 3,\n    maxWidth: 'calc(100% - 6px)'\n  },\n  [`& .${autocompleteClasses.inputRoot}`]: {\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4\n    },\n    [`& .${autocompleteClasses.input}`]: {\n      width: 0,\n      minWidth: 30\n    }\n  },\n  [`& .${inputClasses.root}`]: {\n    paddingBottom: 1,\n    '& .MuiInput-input': {\n      padding: '4px 4px 4px 0px'\n    }\n  },\n  [`& .${inputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    [`& .${inputClasses.input}`]: {\n      padding: '2px 4px 3px 0'\n    }\n  },\n  [`& .${outlinedInputClasses.root}`]: {\n    padding: 9,\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4 + 9\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4 + 9\n    },\n    [`& .${autocompleteClasses.input}`]: {\n      padding: '7.5px 4px 7.5px 5px'\n    },\n    [`& .${autocompleteClasses.endAdornment}`]: {\n      right: 9\n    }\n  },\n  [`& .${outlinedInputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    // Don't specify paddingRight, as it overrides the default value set when there is only\n    // one of the popup or clear icon as the specificity is equal so the latter one wins\n    paddingTop: 6,\n    paddingBottom: 6,\n    paddingLeft: 6,\n    [`& .${autocompleteClasses.input}`]: {\n      padding: '2.5px 4px 2.5px 8px'\n    }\n  },\n  [`& .${filledInputClasses.root}`]: {\n    paddingTop: 19,\n    paddingLeft: 8,\n    [`.${autocompleteClasses.hasPopupIcon}&, .${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 26 + 4 + 9\n    },\n    [`.${autocompleteClasses.hasPopupIcon}.${autocompleteClasses.hasClearIcon}&`]: {\n      paddingRight: 52 + 4 + 9\n    },\n    [`& .${filledInputClasses.input}`]: {\n      padding: '7px 4px'\n    },\n    [`& .${autocompleteClasses.endAdornment}`]: {\n      right: 9\n    }\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.sizeSmall}`]: {\n    paddingBottom: 1,\n    [`& .${filledInputClasses.input}`]: {\n      padding: '2.5px 4px'\n    }\n  },\n  [`& .${inputBaseClasses.hiddenLabel}`]: {\n    paddingTop: 8\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.hiddenLabel}`]: {\n    paddingTop: 0,\n    paddingBottom: 0,\n    [`& .${autocompleteClasses.input}`]: {\n      paddingTop: 16,\n      paddingBottom: 17\n    }\n  },\n  [`& .${filledInputClasses.root}.${inputBaseClasses.hiddenLabel}.${inputBaseClasses.sizeSmall}`]: {\n    [`& .${autocompleteClasses.input}`]: {\n      paddingTop: 8,\n      paddingBottom: 9\n    }\n  },\n  [`& .${autocompleteClasses.input}`]: {\n    flexGrow: 1,\n    textOverflow: 'ellipsis',\n    opacity: 0\n  },\n  variants: [{\n    props: {\n      fullWidth: true\n    },\n    style: {\n      width: '100%'\n    }\n  }, {\n    props: {\n      size: 'small'\n    },\n    style: {\n      [`& .${autocompleteClasses.tag}`]: {\n        margin: 2,\n        maxWidth: 'calc(100% - 4px)'\n      }\n    }\n  }, {\n    props: {\n      inputFocused: true\n    },\n    style: {\n      [`& .${autocompleteClasses.input}`]: {\n        opacity: 1\n      }\n    }\n  }, {\n    props: {\n      multiple: true\n    },\n    style: {\n      [`& .${autocompleteClasses.inputRoot}`]: {\n        flexWrap: 'wrap'\n      }\n    }\n  }]\n});\nconst AutocompleteEndAdornment = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'EndAdornment',\n  overridesResolver: (props, styles) => styles.endAdornment\n})({\n  // We use a position absolute to support wrapping tags.\n  position: 'absolute',\n  right: 0,\n  top: '50%',\n  transform: 'translate(0, -50%)'\n});\nconst AutocompleteClearIndicator = styled(IconButton, {\n  name: 'MuiAutocomplete',\n  slot: 'ClearIndicator',\n  overridesResolver: (props, styles) => styles.clearIndicator\n})({\n  marginRight: -2,\n  padding: 4,\n  visibility: 'hidden'\n});\nconst AutocompletePopupIndicator = styled(IconButton, {\n  name: 'MuiAutocomplete',\n  slot: 'PopupIndicator',\n  overridesResolver: ({\n    ownerState\n  }, styles) => _extends({}, styles.popupIndicator, ownerState.popupOpen && styles.popupIndicatorOpen)\n})({\n  padding: 2,\n  marginRight: -2,\n  variants: [{\n    props: {\n      popupOpen: true\n    },\n    style: {\n      transform: 'rotate(180deg)'\n    }\n  }]\n});\nconst AutocompletePopper = styled(Popper, {\n  name: 'MuiAutocomplete',\n  slot: 'Popper',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [{\n      [`& .${autocompleteClasses.option}`]: styles.option\n    }, styles.popper, ownerState.disablePortal && styles.popperDisablePortal];\n  }\n})(({\n  theme\n}) => ({\n  zIndex: (theme.vars || theme).zIndex.modal,\n  variants: [{\n    props: {\n      disablePortal: true\n    },\n    style: {\n      position: 'absolute'\n    }\n  }]\n}));\nconst AutocompletePaper = styled(Paper, {\n  name: 'MuiAutocomplete',\n  slot: 'Paper',\n  overridesResolver: (props, styles) => styles.paper\n})(({\n  theme\n}) => _extends({}, theme.typography.body1, {\n  overflow: 'auto'\n}));\nconst AutocompleteLoading = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Loading',\n  overridesResolver: (props, styles) => styles.loading\n})(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '14px 16px'\n}));\nconst AutocompleteNoOptions = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'NoOptions',\n  overridesResolver: (props, styles) => styles.noOptions\n})(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary,\n  padding: '14px 16px'\n}));\nconst AutocompleteListbox = styled('div', {\n  name: 'MuiAutocomplete',\n  slot: 'Listbox',\n  overridesResolver: (props, styles) => styles.listbox\n})(({\n  theme\n}) => ({\n  listStyle: 'none',\n  margin: 0,\n  padding: '8px 0',\n  maxHeight: '40vh',\n  overflow: 'auto',\n  position: 'relative',\n  [`& .${autocompleteClasses.option}`]: {\n    minHeight: 48,\n    display: 'flex',\n    overflow: 'hidden',\n    justifyContent: 'flex-start',\n    alignItems: 'center',\n    cursor: 'pointer',\n    paddingTop: 6,\n    boxSizing: 'border-box',\n    outline: '0',\n    WebkitTapHighlightColor: 'transparent',\n    paddingBottom: 6,\n    paddingLeft: 16,\n    paddingRight: 16,\n    [theme.breakpoints.up('sm')]: {\n      minHeight: 'auto'\n    },\n    [`&.${autocompleteClasses.focused}`]: {\n      backgroundColor: (theme.vars || theme).palette.action.hover,\n      // Reset on touch devices, it doesn't add specificity\n      '@media (hover: none)': {\n        backgroundColor: 'transparent'\n      }\n    },\n    '&[aria-disabled=\"true\"]': {\n      opacity: (theme.vars || theme).palette.action.disabledOpacity,\n      pointerEvents: 'none'\n    },\n    [`&.${autocompleteClasses.focusVisible}`]: {\n      backgroundColor: (theme.vars || theme).palette.action.focus\n    },\n    '&[aria-selected=\"true\"]': {\n      backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / ${theme.vars.palette.action.selectedOpacity})` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity),\n      [`&.${autocompleteClasses.focused}`]: {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.hoverOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.hoverOpacity),\n        // Reset on touch devices, it doesn't add specificity\n        '@media (hover: none)': {\n          backgroundColor: (theme.vars || theme).palette.action.selected\n        }\n      },\n      [`&.${autocompleteClasses.focusVisible}`]: {\n        backgroundColor: theme.vars ? `rgba(${theme.vars.palette.primary.mainChannel} / calc(${theme.vars.palette.action.selectedOpacity} + ${theme.vars.palette.action.focusOpacity}))` : alpha(theme.palette.primary.main, theme.palette.action.selectedOpacity + theme.palette.action.focusOpacity)\n      }\n    }\n  }\n}));\nconst AutocompleteGroupLabel = styled(ListSubheader, {\n  name: 'MuiAutocomplete',\n  slot: 'GroupLabel',\n  overridesResolver: (props, styles) => styles.groupLabel\n})(({\n  theme\n}) => ({\n  backgroundColor: (theme.vars || theme).palette.background.paper,\n  top: -8\n}));\nconst AutocompleteGroupUl = styled('ul', {\n  name: 'MuiAutocomplete',\n  slot: 'GroupUl',\n  overridesResolver: (props, styles) => styles.groupUl\n})({\n  padding: 0,\n  [`& .${autocompleteClasses.option}`]: {\n    paddingLeft: 24\n  }\n});\nexport { createFilterOptions };\nconst Autocomplete = /*#__PURE__*/React.forwardRef(function Autocomplete(inProps, ref) {\n  var _slotProps$clearIndic, _slotProps$paper, _slotProps$popper, _slotProps$popupIndic;\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiAutocomplete'\n  });\n\n  /* eslint-disable @typescript-eslint/no-unused-vars */\n  const {\n      autoComplete = false,\n      autoHighlight = false,\n      autoSelect = false,\n      blurOnSelect = false,\n      ChipProps,\n      className,\n      clearIcon = _ClearIcon || (_ClearIcon = /*#__PURE__*/_jsx(ClearIcon, {\n        fontSize: \"small\"\n      })),\n      clearOnBlur = !props.freeSolo,\n      clearOnEscape = false,\n      clearText = 'Clear',\n      closeText = 'Close',\n      componentsProps = {},\n      defaultValue = props.multiple ? [] : null,\n      disableClearable = false,\n      disableCloseOnSelect = false,\n      disabled = false,\n      disabledItemsFocusable = false,\n      disableListWrap = false,\n      disablePortal = false,\n      filterSelectedOptions = false,\n      forcePopupIcon = 'auto',\n      freeSolo = false,\n      fullWidth = false,\n      getLimitTagsText = more => `+${more}`,\n      getOptionLabel: getOptionLabelProp,\n      groupBy,\n      handleHomeEndKeys = !props.freeSolo,\n      includeInputInList = false,\n      limitTags = -1,\n      ListboxComponent = 'ul',\n      ListboxProps,\n      loading = false,\n      loadingText = 'Loading…',\n      multiple = false,\n      noOptionsText = 'No options',\n      openOnFocus = false,\n      openText = 'Open',\n      PaperComponent = Paper,\n      PopperComponent = Popper,\n      popupIcon = _ArrowDropDownIcon || (_ArrowDropDownIcon = /*#__PURE__*/_jsx(ArrowDropDownIcon, {})),\n      readOnly = false,\n      renderGroup: renderGroupProp,\n      renderInput,\n      renderOption: renderOptionProp,\n      renderTags,\n      selectOnFocus = !props.freeSolo,\n      size = 'medium',\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  /* eslint-enable @typescript-eslint/no-unused-vars */\n\n  const {\n    getRootProps,\n    getInputProps,\n    getInputLabelProps,\n    getPopupIndicatorProps,\n    getClearProps,\n    getTagProps,\n    getListboxProps,\n    getOptionProps,\n    value,\n    dirty,\n    expanded,\n    id,\n    popupOpen,\n    focused,\n    focusedTag,\n    anchorEl,\n    setAnchorEl,\n    inputValue,\n    groupedOptions\n  } = useAutocomplete(_extends({}, props, {\n    componentName: 'Autocomplete'\n  }));\n  const hasClearIcon = !disableClearable && !disabled && dirty && !readOnly;\n  const hasPopupIcon = (!freeSolo || forcePopupIcon === true) && forcePopupIcon !== false;\n  const {\n    onMouseDown: handleInputMouseDown\n  } = getInputProps();\n  const {\n    ref: externalListboxRef\n  } = ListboxProps != null ? ListboxProps : {};\n  const _getListboxProps = getListboxProps(),\n    {\n      ref: listboxRef\n    } = _getListboxProps,\n    otherListboxProps = _objectWithoutPropertiesLoose(_getListboxProps, _excluded2);\n  const combinedListboxRef = useForkRef(listboxRef, externalListboxRef);\n  const defaultGetOptionLabel = option => {\n    var _option$label;\n    return (_option$label = option.label) != null ? _option$label : option;\n  };\n  const getOptionLabel = getOptionLabelProp || defaultGetOptionLabel;\n\n  // If you modify this, make sure to keep the `AutocompleteOwnerState` type in sync.\n  const ownerState = _extends({}, props, {\n    disablePortal,\n    expanded,\n    focused,\n    fullWidth,\n    getOptionLabel,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused: focusedTag === -1,\n    popupOpen,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  let startAdornment;\n  if (multiple && value.length > 0) {\n    const getCustomizedTagProps = params => _extends({\n      className: classes.tag,\n      disabled\n    }, getTagProps(params));\n    if (renderTags) {\n      startAdornment = renderTags(value, getCustomizedTagProps, ownerState);\n    } else {\n      startAdornment = value.map((option, index) => {\n        const _getCustomizedTagProp = getCustomizedTagProps({\n            index\n          }),\n          {\n            key\n          } = _getCustomizedTagProp,\n          customTagProps = _objectWithoutPropertiesLoose(_getCustomizedTagProp, _excluded3);\n        return /*#__PURE__*/_jsx(Chip, _extends({\n          label: getOptionLabel(option),\n          size: size\n        }, customTagProps, ChipProps), key);\n      });\n    }\n  }\n  if (limitTags > -1 && Array.isArray(startAdornment)) {\n    const more = startAdornment.length - limitTags;\n    if (!focused && more > 0) {\n      startAdornment = startAdornment.splice(0, limitTags);\n      startAdornment.push( /*#__PURE__*/_jsx(\"span\", {\n        className: classes.tag,\n        children: getLimitTagsText(more)\n      }, startAdornment.length));\n    }\n  }\n  const defaultRenderGroup = params => /*#__PURE__*/_jsxs(\"li\", {\n    children: [/*#__PURE__*/_jsx(AutocompleteGroupLabel, {\n      className: classes.groupLabel,\n      ownerState: ownerState,\n      component: \"div\",\n      children: params.group\n    }), /*#__PURE__*/_jsx(AutocompleteGroupUl, {\n      className: classes.groupUl,\n      ownerState: ownerState,\n      children: params.children\n    })]\n  }, params.key);\n  const renderGroup = renderGroupProp || defaultRenderGroup;\n  const defaultRenderOption = (props2, option) => {\n    // Need to clearly apply key because of https://github.com/vercel/next.js/issues/55642\n    const {\n        key\n      } = props2,\n      otherProps = _objectWithoutPropertiesLoose(props2, _excluded4);\n    return /*#__PURE__*/_jsx(\"li\", _extends({}, otherProps, {\n      children: getOptionLabel(option)\n    }), key);\n  };\n  const renderOption = renderOptionProp || defaultRenderOption;\n  const renderListOption = (option, index) => {\n    const optionProps = getOptionProps({\n      option,\n      index\n    });\n    return renderOption(_extends({}, optionProps, {\n      className: classes.option\n    }), option, {\n      selected: optionProps['aria-selected'],\n      index,\n      inputValue\n    }, ownerState);\n  };\n  const clearIndicatorSlotProps = (_slotProps$clearIndic = slotProps.clearIndicator) != null ? _slotProps$clearIndic : componentsProps.clearIndicator;\n  const paperSlotProps = (_slotProps$paper = slotProps.paper) != null ? _slotProps$paper : componentsProps.paper;\n  const popperSlotProps = (_slotProps$popper = slotProps.popper) != null ? _slotProps$popper : componentsProps.popper;\n  const popupIndicatorSlotProps = (_slotProps$popupIndic = slotProps.popupIndicator) != null ? _slotProps$popupIndic : componentsProps.popupIndicator;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(AutocompleteRoot, _extends({\n      ref: ref,\n      className: clsx(classes.root, className),\n      ownerState: ownerState\n    }, getRootProps(other), {\n      children: renderInput({\n        id,\n        disabled,\n        fullWidth: true,\n        size: size === 'small' ? 'small' : undefined,\n        InputLabelProps: getInputLabelProps(),\n        InputProps: _extends({\n          ref: setAnchorEl,\n          className: classes.inputRoot,\n          startAdornment,\n          onClick: event => {\n            if (event.target === event.currentTarget) {\n              handleInputMouseDown(event);\n            }\n          }\n        }, (hasClearIcon || hasPopupIcon) && {\n          endAdornment: /*#__PURE__*/_jsxs(AutocompleteEndAdornment, {\n            className: classes.endAdornment,\n            ownerState: ownerState,\n            children: [hasClearIcon ? /*#__PURE__*/_jsx(AutocompleteClearIndicator, _extends({}, getClearProps(), {\n              \"aria-label\": clearText,\n              title: clearText,\n              ownerState: ownerState\n            }, clearIndicatorSlotProps, {\n              className: clsx(classes.clearIndicator, clearIndicatorSlotProps == null ? void 0 : clearIndicatorSlotProps.className),\n              children: clearIcon\n            })) : null, hasPopupIcon ? /*#__PURE__*/_jsx(AutocompletePopupIndicator, _extends({}, getPopupIndicatorProps(), {\n              disabled: disabled,\n              \"aria-label\": popupOpen ? closeText : openText,\n              title: popupOpen ? closeText : openText,\n              ownerState: ownerState\n            }, popupIndicatorSlotProps, {\n              className: clsx(classes.popupIndicator, popupIndicatorSlotProps == null ? void 0 : popupIndicatorSlotProps.className),\n              children: popupIcon\n            })) : null]\n          })\n        }),\n        inputProps: _extends({\n          className: classes.input,\n          disabled,\n          readOnly\n        }, getInputProps())\n      })\n    })), anchorEl ? /*#__PURE__*/_jsx(AutocompletePopper, _extends({\n      as: PopperComponent,\n      disablePortal: disablePortal,\n      style: {\n        width: anchorEl ? anchorEl.clientWidth : null\n      },\n      ownerState: ownerState,\n      role: \"presentation\",\n      anchorEl: anchorEl,\n      open: popupOpen\n    }, popperSlotProps, {\n      className: clsx(classes.popper, popperSlotProps == null ? void 0 : popperSlotProps.className),\n      children: /*#__PURE__*/_jsxs(AutocompletePaper, _extends({\n        ownerState: ownerState,\n        as: PaperComponent\n      }, paperSlotProps, {\n        className: clsx(classes.paper, paperSlotProps == null ? void 0 : paperSlotProps.className),\n        children: [loading && groupedOptions.length === 0 ? /*#__PURE__*/_jsx(AutocompleteLoading, {\n          className: classes.loading,\n          ownerState: ownerState,\n          children: loadingText\n        }) : null, groupedOptions.length === 0 && !freeSolo && !loading ? /*#__PURE__*/_jsx(AutocompleteNoOptions, {\n          className: classes.noOptions,\n          ownerState: ownerState,\n          role: \"presentation\",\n          onMouseDown: event => {\n            // Prevent input blur when interacting with the \"no options\" content\n            event.preventDefault();\n          },\n          children: noOptionsText\n        }) : null, groupedOptions.length > 0 ? /*#__PURE__*/_jsx(AutocompleteListbox, _extends({\n          as: ListboxComponent,\n          className: classes.listbox,\n          ownerState: ownerState\n        }, otherListboxProps, ListboxProps, {\n          ref: combinedListboxRef,\n          children: groupedOptions.map((option, index) => {\n            if (groupBy) {\n              return renderGroup({\n                key: option.key,\n                group: option.group,\n                children: option.options.map((option2, index2) => renderListOption(option2, option.index + index2))\n              });\n            }\n            return renderListOption(option, index);\n          })\n        })) : null]\n      }))\n    })) : null]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Autocomplete.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the portion of the selected suggestion that the user hasn't typed,\n   * known as the completion string, appears inline after the input cursor in the textbox.\n   * The inline completion string is visually highlighted and has a selected state.\n   * @default false\n   */\n  autoComplete: PropTypes.bool,\n  /**\n   * If `true`, the first option is automatically highlighted.\n   * @default false\n   */\n  autoHighlight: PropTypes.bool,\n  /**\n   * If `true`, the selected option becomes the value of the input\n   * when the Autocomplete loses focus unless the user chooses\n   * a different option or changes the character string in the input.\n   *\n   * When using the `freeSolo` mode, the typed value will be the input value\n   * if the Autocomplete loses focus without highlighting an option.\n   * @default false\n   */\n  autoSelect: PropTypes.bool,\n  /**\n   * Control if the input should be blurred when an option is selected:\n   *\n   * - `false` the input is not blurred.\n   * - `true` the input is always blurred.\n   * - `touch` the input is blurred after a touch event.\n   * - `mouse` the input is blurred after a mouse event.\n   * @default false\n   */\n  blurOnSelect: PropTypes.oneOfType([PropTypes.oneOf(['mouse', 'touch']), PropTypes.bool]),\n  /**\n   * Props applied to the [`Chip`](/material-ui/api/chip/) element.\n   */\n  ChipProps: PropTypes.object,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The icon to display in place of the default clear icon.\n   * @default <ClearIcon fontSize=\"small\" />\n   */\n  clearIcon: PropTypes.node,\n  /**\n   * If `true`, the input's text is cleared on blur if no value is selected.\n   *\n   * Set it to `true` if you want to help the user enter a new value.\n   * Set it to `false` if you want to help the user resume their search.\n   * @default !props.freeSolo\n   */\n  clearOnBlur: PropTypes.bool,\n  /**\n   * If `true`, clear all values when the user presses escape and the popup is closed.\n   * @default false\n   */\n  clearOnEscape: PropTypes.bool,\n  /**\n   * Override the default text for the *clear* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Clear'\n   */\n  clearText: PropTypes.string,\n  /**\n   * Override the default text for the *close popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Close'\n   */\n  closeText: PropTypes.string,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  componentsProps: PropTypes.shape({\n    clearIndicator: PropTypes.object,\n    paper: PropTypes.object,\n    popper: PropTypes.object,\n    popupIndicator: PropTypes.object\n  }),\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default props.multiple ? [] : null\n   */\n  defaultValue: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.defaultValue !== undefined && !Array.isArray(props.defaultValue)) {\n      return new Error(['MUI: The Autocomplete expects the `defaultValue` prop to be an array when `multiple={true}` or undefined.', `However, ${props.defaultValue} was provided.`].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * If `true`, the input can't be cleared.\n   * @default false\n   */\n  disableClearable: PropTypes.bool,\n  /**\n   * If `true`, the popup won't close when a value is selected.\n   * @default false\n   */\n  disableCloseOnSelect: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, will allow focus on disabled items.\n   * @default false\n   */\n  disabledItemsFocusable: PropTypes.bool,\n  /**\n   * If `true`, the list box in the popup will not wrap focus.\n   * @default false\n   */\n  disableListWrap: PropTypes.bool,\n  /**\n   * If `true`, the `Popper` content will be under the DOM hierarchy of the parent component.\n   * @default false\n   */\n  disablePortal: PropTypes.bool,\n  /**\n   * A function that determines the filtered options to be rendered on search.\n   *\n   * @default createFilterOptions()\n   * @param {Value[]} options The options to render.\n   * @param {object} state The state of the component.\n   * @returns {Value[]}\n   */\n  filterOptions: PropTypes.func,\n  /**\n   * If `true`, hide the selected options from the list box.\n   * @default false\n   */\n  filterSelectedOptions: PropTypes.bool,\n  /**\n   * Force the visibility display of the popup icon.\n   * @default 'auto'\n   */\n  forcePopupIcon: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.bool]),\n  /**\n   * If `true`, the Autocomplete is free solo, meaning that the user input is not bound to provided options.\n   * @default false\n   */\n  freeSolo: PropTypes.bool,\n  /**\n   * If `true`, the input will take up the full width of its container.\n   * @default false\n   */\n  fullWidth: PropTypes.bool,\n  /**\n   * The label to display when the tags are truncated (`limitTags`).\n   *\n   * @param {number} more The number of truncated tags.\n   * @returns {ReactNode}\n   * @default (more) => `+${more}`\n   */\n  getLimitTagsText: PropTypes.func,\n  /**\n   * Used to determine the disabled state for a given option.\n   *\n   * @param {Value} option The option to test.\n   * @returns {boolean}\n   */\n  getOptionDisabled: PropTypes.func,\n  /**\n   * Used to determine the key for a given option.\n   * This can be useful when the labels of options are not unique (since labels are used as keys by default).\n   *\n   * @param {Value} option The option to get the key for.\n   * @returns {string | number}\n   */\n  getOptionKey: PropTypes.func,\n  /**\n   * Used to determine the string value for a given option.\n   * It's used to fill the input (and the list box options if `renderOption` is not provided).\n   *\n   * If used in free solo mode, it must accept both the type of the options and a string.\n   *\n   * @param {Value} option\n   * @returns {string}\n   * @default (option) => option.label ?? option\n   */\n  getOptionLabel: PropTypes.func,\n  /**\n   * If provided, the options will be grouped under the returned string.\n   * The groupBy value is also used as the text for group headings when `renderGroup` is not provided.\n   *\n   * @param {Value} options The options to group.\n   * @returns {string}\n   */\n  groupBy: PropTypes.func,\n  /**\n   * If `true`, the component handles the \"Home\" and \"End\" keys when the popup is open.\n   * It should move focus to the first option and last option, respectively.\n   * @default !props.freeSolo\n   */\n  handleHomeEndKeys: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide an id it will fall back to a randomly generated one.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the highlight can move to the input.\n   * @default false\n   */\n  includeInputInList: PropTypes.bool,\n  /**\n   * The input value.\n   */\n  inputValue: PropTypes.string,\n  /**\n   * Used to determine if the option represents the given value.\n   * Uses strict equality by default.\n   * ⚠️ Both arguments need to be handled, an option can only match with one value.\n   *\n   * @param {Value} option The option to test.\n   * @param {Value} value The value to test against.\n   * @returns {boolean}\n   */\n  isOptionEqualToValue: PropTypes.func,\n  /**\n   * The maximum number of tags that will be visible when not focused.\n   * Set `-1` to disable the limit.\n   * @default -1\n   */\n  limitTags: integerPropType,\n  /**\n   * The component used to render the listbox.\n   * @default 'ul'\n   */\n  ListboxComponent: PropTypes.elementType,\n  /**\n   * Props applied to the Listbox element.\n   */\n  ListboxProps: PropTypes.object,\n  /**\n   * If `true`, the component is in a loading state.\n   * This shows the `loadingText` in place of suggestions (only if there are no suggestions to show, for example `options` are empty).\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Text to display when in a loading state.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Loading…'\n   */\n  loadingText: PropTypes.node,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   * @default false\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Text to display when there are no options.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'No options'\n   */\n  noOptionsText: PropTypes.node,\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value|Value[]} value The new value of the component.\n   * @param {string} reason One of \"createOption\", \"selectOption\", \"removeOption\", \"blur\" or \"clear\".\n   * @param {string} [details]\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggleInput\"`, `\"escape\"`, `\"selectOption\"`, `\"removeOption\"`, `\"blur\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the highlight option changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value} option The highlighted option.\n   * @param {string} reason Can be: `\"keyboard\"`, `\"auto\"`, `\"mouse\"`, `\"touch\"`.\n   */\n  onHighlightChange: PropTypes.func,\n  /**\n   * Callback fired when the input value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} value The new value of the text input.\n   * @param {string} reason Can be: `\"input\"` (user input), `\"reset\"` (programmatic change), `\"clear\"`.\n   */\n  onInputChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * If `true`, the popup will open on input focus.\n   * @default false\n   */\n  openOnFocus: PropTypes.bool,\n  /**\n   * Override the default text for the *open popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Open'\n   */\n  openText: PropTypes.string,\n  /**\n   * Array of options.\n   */\n  options: PropTypes.array.isRequired,\n  /**\n   * The component used to render the body of the popup.\n   * @default Paper\n   */\n  PaperComponent: PropTypes.elementType,\n  /**\n   * The component used to position the popup.\n   * @default Popper\n   */\n  PopperComponent: PropTypes.elementType,\n  /**\n   * The icon to display in place of the default popup icon.\n   * @default <ArrowDropDownIcon />\n   */\n  popupIcon: PropTypes.node,\n  /**\n   * If `true`, the component becomes readonly. It is also supported for multiple tags where the tag cannot be deleted.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the group.\n   *\n   * @param {AutocompleteRenderGroupParams} params The group to render.\n   * @returns {ReactNode}\n   */\n  renderGroup: PropTypes.func,\n  /**\n   * Render the input.\n   *\n   * @param {object} params\n   * @returns {ReactNode}\n   */\n  renderInput: PropTypes.func.isRequired,\n  /**\n   * Render the option, use `getOptionLabel` by default.\n   *\n   * @param {object} props The props to apply on the li element.\n   * @param {Value} option The option to render.\n   * @param {object} state The state of each option.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderOption: PropTypes.func,\n  /**\n   * Render the selected value.\n   *\n   * @param {Value[]} value The `value` provided to the component.\n   * @param {function} getTagProps A tag props getter.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderTags: PropTypes.func,\n  /**\n   * If `true`, the input's text is selected on focus.\n   * It helps the user clear the selected value.\n   * @default !props.freeSolo\n   */\n  selectOnFocus: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'medium'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['small', 'medium']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    clearIndicator: PropTypes.object,\n    paper: PropTypes.object,\n    popper: PropTypes.object,\n    popupIndicator: PropTypes.object\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The value of the autocomplete.\n   *\n   * The value must have reference equality with the option in order to be selected.\n   * You can customize the equality behavior with the `isOptionEqualToValue` prop.\n   */\n  value: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.value !== undefined && !Array.isArray(props.value)) {\n      return new Error(['MUI: The Autocomplete expects the `value` prop to be an array when `multiple={true}` or undefined.', `However, ${props.value} was provided.`].join('\\n'));\n    }\n    return null;\n  })\n} : void 0;\nexport default Autocomplete;"], "names": ["value", "ref", "React", "current", "stripDiacritics", "string", "normalize", "replace", "findIndex", "array", "comp", "i", "length", "defaultFilterOptions", "config", "arguments", "undefined", "ignoreAccents", "ignoreCase", "limit", "matchFrom", "stringify", "trim", "options", "_ref", "inputValue", "getOptionLabel", "input", "toLowerCase", "filteredOptions", "filter", "option", "candidate", "indexOf", "slice", "createFilterOptions", "defaultIsActiveElementInListbox", "listboxRef", "_listboxRef$current$p", "parentElement", "contains", "document", "activeElement", "MULTIPLE_DEFAULT_VALUE", "props", "unstable_isActiveElementInListbox", "unstable_classNamePrefix", "autoComplete", "autoHighlight", "autoSelect", "blurOnSelect", "clearOnBlur", "freeSolo", "clearOnEscape", "componentName", "defaultValue", "multiple", "disableClearable", "disableCloseOnSelect", "disabled", "disabledProp", "disabledItemsFocusable", "disableListWrap", "filterOptions", "filterSelectedOptions", "getOptionDisabled", "getOption<PERSON>ey", "getOptionLabelProp", "_option$label", "label", "groupBy", "handleHomeEndKeys", "id", "idProp", "includeInputInList", "inputValueProp", "isOptionEqualToValue", "onChange", "onClose", "onHighlightChange", "onInputChange", "onOpen", "open", "openProp", "openOnFocus", "readOnly", "selectOnFocus", "valueProp", "useId", "optionLabel", "String", "ignoreFocus", "firstFocus", "inputRef", "anchorEl", "setAnchorEl", "focusedTag", "setFocusedTag", "defaultHighlighted", "highlightedIndexRef", "setValueState", "useControlled", "controlled", "default", "name", "setInputValueState", "state", "focused", "setFocused", "resetInputValue", "event", "newValue", "newInputValue", "setOpenState", "inputPristine", "setInputPristine", "inputValueIsSelectedValue", "popupOpen", "some", "value2", "previousProps", "usePreviousProps", "valueChange", "listboxAvailable", "focusTag", "useEventCallback", "tagToFocus", "focus", "querySelector", "setHighlightedIndex", "_ref2", "index", "reason", "removeAttribute", "setAttribute", "prev", "classList", "remove", "listboxNode", "getAttribute", "scrollTop", "add", "scrollHeight", "clientHeight", "element", "scrollBottom", "elementBottom", "offsetTop", "offsetHeight", "changeHighlightedIndex", "_ref3", "diff", "direction", "nextIndex", "nextFocus", "nextFocusDisabled", "hasAttribute", "validOptionIndex", "getNextIndex", "maxIndex", "newIndex", "Math", "abs", "setSelectionRange", "syncHighlightedIndex", "previousHighlightedOptionIndex", "getPreviousHighlightedOptionIndex", "every", "val", "value1", "previousHighlightedOption", "isSameValue", "valueItem", "currentOption", "itemIndex", "optionItem", "handleListboxRef", "node", "setRef", "handleOpen", "handleClose", "handleValue", "details", "is<PERSON><PERSON>ch", "selectNewValue", "origin", "Array", "isArray", "push", "splice", "ctrl<PERSON>ey", "metaKey", "blur", "handleFocusTag", "nextTag", "validTagIndex", "handleClear", "handleKeyDown", "other", "onKeyDown", "defaultMuiPrevented", "key", "which", "preventDefault", "stopPropagation", "handleFocus", "handleBlur", "handleInputChange", "target", "handleOptionMouseMove", "Number", "currentTarget", "handleOptionTouchStart", "handleOptionClick", "handleTagDelete", "handlePopupIndicator", "handleMouseDown", "handleClick", "selectionEnd", "selectionStart", "select", "handleInputMouseDown", "dirty", "groupedOptions", "Map", "reduce", "acc", "group", "getRootProps", "_extends", "onMouseDown", "onClick", "getInputLabelProps", "htmlFor", "getInputProps", "onBlur", "onFocus", "autoCapitalize", "spell<PERSON>heck", "role", "getClearProps", "tabIndex", "type", "getPopupIndicatorProps", "getTagProps", "_ref4", "onDelete", "getListboxProps", "getOptionProps", "_ref5", "_getO<PERSON><PERSON>ey", "selected", "onMouseMove", "onTouchStart", "expanded", "getListSubheaderUtilityClass", "slot", "generateUtilityClass", "generateUtilityClasses", "_excluded", "ListSubheaderRoot", "styled", "overridesResolver", "styles", "ownerState", "root", "color", "capitalize", "disableGutters", "gutters", "inset", "disableSticky", "sticky", "theme", "boxSizing", "lineHeight", "listStyle", "vars", "palette", "text", "secondary", "fontFamily", "typography", "fontWeight", "fontWeightMedium", "fontSize", "pxToRem", "primary", "main", "paddingLeft", "paddingRight", "position", "top", "zIndex", "backgroundColor", "background", "paper", "ListSubheader", "inProps", "useDefaultProps", "className", "component", "_objectWithoutPropertiesLoose", "classes", "slots", "composeClasses", "useUtilityClasses", "_jsx", "as", "clsx", "muiSkipListHighlight", "getAutocompleteUtilityClass", "_ClearIcon", "_ArrowDropDownIcon", "_excluded2", "_excluded3", "_excluded4", "AutocompleteRoot", "fullWidth", "hasClearIcon", "hasPopupIcon", "inputFocused", "size", "autocompleteClasses", "tag", "inputRoot", "clearIndicator", "visibility", "margin", "max<PERSON><PERSON><PERSON>", "width", "min<PERSON><PERSON><PERSON>", "inputClasses", "paddingBottom", "padding", "inputBaseClasses", "sizeSmall", "outlinedInputClasses", "endAdornment", "right", "paddingTop", "filledInputClasses", "hidden<PERSON>abel", "flexGrow", "textOverflow", "opacity", "variants", "style", "flexWrap", "AutocompleteEndAdornment", "transform", "AutocompleteClearIndicator", "IconButton", "marginRight", "AutocompletePopupIndicator", "popupIndicator", "popupIndicatorOpen", "AutocompletePopper", "<PERSON><PERSON>", "popper", "disable<PERSON><PERSON><PERSON>", "popperDisablePortal", "modal", "AutocompletePaper", "Paper", "body1", "overflow", "AutocompleteLoading", "loading", "AutocompleteNoOptions", "noOptions", "AutocompleteListbox", "listbox", "_ref6", "maxHeight", "minHeight", "display", "justifyContent", "alignItems", "cursor", "outline", "WebkitTapHighlightColor", "breakpoints", "up", "action", "hover", "disabledOpacity", "pointerEvents", "focusVisible", "mainChannel", "selectedOpacity", "alpha", "hoverOpacity", "focusOpacity", "AutocompleteGroupLabel", "groupLabel", "_ref7", "AutocompleteGroupUl", "groupUl", "_slotProps$clearIndic", "_slotProps$paper", "_slotProps$popper", "_slotProps$popupIndic", "ChipProps", "clearIcon", "ClearIcon", "clearText", "closeText", "componentsProps", "forcePopupIcon", "getLimitTagsText", "more", "limitTags", "ListboxComponent", "ListboxProps", "loadingText", "noOptionsText", "openText", "PaperComponent", "PopperComponent", "popupIcon", "ArrowDropDownIcon", "renderGroup", "renderGroupProp", "renderInput", "renderOption", "renderOptionProp", "renderTags", "slotProps", "useAutocomplete", "externalListboxRef", "_getListboxProps", "otherListboxProps", "combinedListboxRef", "useForkRef", "startAdornment", "getCustomizedTagProps", "params", "map", "_getCustomizedTagProp", "customTagProps", "Chip", "children", "_jsxs", "defaultRenderOption", "props2", "otherProps", "renderListOption", "optionProps", "clearIndicatorSlotProps", "paperSlotProps", "popperSlotProps", "popupIndicatorSlotProps", "InputLabelProps", "InputProps", "title", "inputProps", "clientWidth", "option2", "index2"], "sourceRoot": ""}